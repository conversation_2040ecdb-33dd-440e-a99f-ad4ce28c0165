package io.agora.rtc.ss;

import android.annotation.TargetApi;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentManager;

import io.agora.rtc.ss.aidl.INotification;
import io.agora.rtc.ss.aidl.IScreenSharing;
import io.agora.rtc.ss.impl.ScreenSharingService;
import io.agora.rtc.ss.permissions.InvisibleFragment;
import io.agora.rtc2.video.VideoEncoderConfiguration;

public class ScreenSharingClient {
    private static final String TAG = ScreenSharingClient.class.getSimpleName();
    private static final String TAG_FRAGMENT = "InvisibleFragment";
    private static IScreenSharing mScreenShareSvc;
    private IStateListener mStateListener;
    private static volatile ScreenSharingClient mInstance;

    private Intent projectionIntent;

    private ScreenSharingClient() {
    }

    public static ScreenSharingClient getInstance() {
        if (mInstance == null) {
            synchronized (ScreenSharingClient.class) {
                if (mInstance == null) {
                    mInstance = new ScreenSharingClient();
                }
            }
        }

        return mInstance;
    }

    private final ServiceConnection mScreenShareConn = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder service) {
            mScreenShareSvc = IScreenSharing.Stub.asInterface(service);

            try {
                mScreenShareSvc.registerCallback(mNotification);
                mScreenShareSvc.startShare();
            } catch (RemoteException e) {
                e.printStackTrace();
                Log.e(TAG, Log.getStackTraceString(e));
            }

        }

        public void onServiceDisconnected(ComponentName className) {
            mScreenShareSvc = null;
        }
    };

    private INotification mNotification = new INotification.Stub() {
        /**
         * This is called by the remote service to tell us about error happened.
         * Note that IPC calls are dispatched through a thread
         * pool running in each process, so the code executing here will
         * NOT be running in our main thread like most other things -- so,
         * if to update the UI, we need to use a Handler to hop over there.
         */
        public void onError(int error) {
            Log.e(TAG, "screen sharing service error happened: " + error);
            mStateListener.onError(error);
        }

        public void onTokenWillExpire() {
            Log.d(TAG, "access token for screen sharing service will expire soon");
            mStateListener.onTokenWillExpire();
        }
    };

    /**
     * 开始屏幕共享
     *
     * @param context     activity
     * @param appId       声网的APP ID
     * @param token       共享屏幕时申请的临时TOKEN
     * @param channelName 会议室房间号
     * @param uid         共享屏幕时随机创建的账号
     * @param vec         共享屏幕使用的视频编码的参数
     * @param listener    当用户拒绝了屏幕录制权限时的回调
     */
    public void start(final AppCompatActivity context,
                      final FragmentManager manager,
                      final String appId,
                      final String token,
                      final String channelName,
                      final int uid,
                      final VideoEncoderConfiguration vec,
                      final StartShareListener listener) {
        if (projectionIntent == null) {
            Log.d(TAG, "需要申请权限");
            InvisibleFragment fragment = getOrCreateInvisibleFragment(manager);
            fragment.requestNow(new InvisibleFragment.RequestResult() {
                @Override
                public void onAllow(Intent data) {
                    Log.d(TAG, "用户同意权限");
                    projectionIntent = data;
                    boolean res = bindRemote(context, appId, token, channelName, uid, vec);
                    listener.onStartFinish(res ? StartShareListener.CODE_SUCCESS : StartShareListener.CODE_ERROR_BIND_REMOTE);
                }

                @Override
                public void onReject() {
                    // 系统应用, 不可能权限失败
                    Log.d(TAG, "用户拒绝权限");
                    if (listener != null) {
                        listener.onStartFinish(StartShareListener.CODE_ERROR_REFUSED_PERMISSION);
                    }
                }
            });
        } else {
            boolean res = bindRemote(context, appId, token, channelName, uid, vec);
            listener.onStartFinish(res ? StartShareListener.CODE_SUCCESS : StartShareListener.CODE_ERROR_BIND_REMOTE);
        }

    }

    private boolean bindRemote(AppCompatActivity context,
                               String appId,
                               String token,
                               String channelName,
                               int uid,
                               VideoEncoderConfiguration vec) {
        if (mScreenShareSvc == null) {
            Intent intent = new Intent(context, ScreenSharingService.class);
            intent.putExtra(Constant.APP_ID, appId);
            intent.putExtra(Constant.ACCESS_TOKEN, token);
            intent.putExtra(Constant.CHANNEL_NAME, channelName);
            intent.putExtra(Constant.UID, uid);
            intent.putExtra(Constant.WIDTH, vec.dimensions.width);
            intent.putExtra(Constant.HEIGHT, vec.dimensions.height);
            intent.putExtra(Constant.FRAME_RATE, vec.frameRate);
            intent.putExtra(Constant.BITRATE, vec.bitrate);
            intent.putExtra(Constant.ORIENTATION_MODE, vec.orientationMode.getValue());
            intent.putExtra(Constant.DEGRADATION_PREFER, vec.degradationPrefer.getValue());
            intent.putExtra(Constant.INTENT, projectionIntent);
            boolean bindRes = context.bindService(intent, mScreenShareConn, Context.BIND_AUTO_CREATE);

            if (!bindRes) {
                Log.e(TAG, "绑定分享服务失败!!");
                if (mStateListener != null) {
                    mStateListener.onError(-1);
                }
            }
            return bindRes;
        }
        return false;
    }


    /**
     * 获取/创建不可见的Fragment, 用户申请权限
     *
     * @param aty activity
     * @return 不可见的Fragment, 如果已经申请过,则直接使用该Fragment, 否则创建一个,并添加到Activity中
     */
    private InvisibleFragment getOrCreateInvisibleFragment(FragmentManager fragmentManager) {
        InvisibleFragment fragment = (InvisibleFragment) fragmentManager.findFragmentByTag(TAG_FRAGMENT);
        if (fragment == null) {
            fragment = new InvisibleFragment();
            fragmentManager.beginTransaction().add(fragment, TAG_FRAGMENT).commitNow();
        }
        return fragment;
    }

    @TargetApi(21)
    public void stop(Context context) {
        if (mScreenShareSvc != null) {
            try {
                mScreenShareSvc.stopShare();
                mScreenShareSvc.unregisterCallback(mNotification);
            } catch (Exception e) {
                Log.e(TAG, "stop:", e);
            } finally {
                mScreenShareSvc = null;
            }
        }
        try {
            context.unbindService(mScreenShareConn);
        } catch (Exception exp) {
            Log.w(TAG, "停止分享出错", exp);
        }
    }

    @TargetApi(21)
    public void renewToken(String token) {
        if (mScreenShareSvc != null) {
            try {
                mScreenShareSvc.renewToken(token);
            } catch (RemoteException e) {
                e.printStackTrace();
                Log.e(TAG, Log.getStackTraceString(e));
            }
        } else {
            Log.e(TAG, "screen sharing service not exist");
        }
    }

    @TargetApi(21)
    public void setListener(IStateListener listener) {
        mStateListener = listener;
    }

    public interface IStateListener {
        void onError(int error);

        void onTokenWillExpire();
    }

    public interface RefusedPermissionListener {
        void onRefusedPermission();
    }

    public interface StartShareListener {
        int CODE_SUCCESS = 1;
        int CODE_ERROR_REFUSED_PERMISSION = -1;
        int CODE_ERROR_BIND_REMOTE = -2;

        void onStartFinish(int code);
    }
}
