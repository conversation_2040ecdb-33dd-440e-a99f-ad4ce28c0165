package io.agora.rtc.ss;

import com.czur.czurutils.log.CZURLogConfig;
import com.czur.czurutils.log.CZURLogUtilsKt;

/**
 * Created by 陈丰尧 on 2023/1/5
 */
public class CZURLogBridge {
    public static void logV(String msg) {
        CZURLogUtilsKt.logV(new String[]{msg}, CZURLogConfig.INSTANCE.getConfigTag(), null);
    }

    public static void logD(String msg) {
        CZURLogUtilsKt.logD(new String[]{msg}, CZURLogConfig.INSTANCE.getConfigTag(), null);
    }

    public static void logE(String msg) {
        CZURLogUtilsKt.logE(new String[]{msg}, CZURLogConfig.INSTANCE.getConfigTag(), null);
    }

    public static void logE(String msg, Throwable tr) {
        CZURLogUtilsKt.logE(new String[]{msg}, CZURLogConfig.INSTANCE.getConfigTag(), tr);
    }
}
