package com.czur.starry.device.hdmilib

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import android.content.Context
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner

/**
 * Created by 陈丰尧 on 2021/8/19
 * 申请Camera的工具类
 * 在HDMI没有插入的状态中, 会使用 ImageCapture 来申请相机
 * 在HDMI插入后, 会生成 Preview 并设置HDMI的分辨率,来进行显示
 * @param surfaceProvider: 要将HDMI显示到哪个Surface上
 */
class CameraUtil(
    private val context: Context,
    private val surfaceProvider: Preview.SurfaceProvider,
    private val life: LifecycleOwner,
    private val onOpenCameraFail: (exp: Exception) -> Unit,
) {
    companion object {
        private const val TAG = "HDMI-CameraUtil"
    }

    private lateinit var cameraProvider: ProcessCameraProvider

    // 选择后置相机
    private val cameraSelector by lazy {
        CameraSelector.DEFAULT_BACK_CAMERA
    }
    private val imageCapture by lazy {
        ImageCapture.Builder().build()
    }

    private var resolution = defResolution

    /**
     * 初始化Camera
     */
    fun initCamera(preview: Boolean = false) {
        logTagD(TAG, "initCamera preview:${preview}")
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            logTagD(TAG, "打开Camera")
            cameraProvider = cameraProviderFuture.get()
            try {
                // 在重新绑定之前, 需要解绑之前绑定过的usecase
                cameraProvider.unbindAll()

                // 把useCase绑定到CameraProvider中
                // 只有绑定了, 才会真正去申请相机
                if (preview) {
                    cameraProvider.bindToLifecycle(
                        life, cameraSelector, createPreview()
                    )
                } else {
                    cameraProvider.bindToLifecycle(
                        life, cameraSelector, imageCapture
                    )
                }
            } catch (exc: Exception) {
                onOpenCameraFail(exc)
            }

        }, ContextCompat.getMainExecutor(context))
    }

    /**
     * 更新相机分辨率
     */
    fun updateResolution(resolution: HDMIResolution) {
        if (this.resolution != resolution) {
            logTagD(TAG, "分辨率改变:before:${this.resolution};after:${resolution}")
            this.resolution = resolution
            startPreview()
        }
    }

    /**
     * 重启Camera
     */
    fun restartCamera() {
        stopPreview()
        initCamera()
    }

    /**
     * 开始预览
     */
    fun startPreview() {
        // 开始预览
        logTagI(TAG, "开始预览")
        val preview = createPreview()

        if (this::cameraProvider.isInitialized) {
            cameraProvider.unbindAll()
            cameraProvider.bindToLifecycle(
                life, cameraSelector, preview
            )
        } else {
            initCamera(true)
        }
    }

    /**
     * 停止预览
     */
    fun stopPreview() {
        logTagI(TAG, "停止预览,释放Camera")
        if (this::cameraProvider.isInitialized) {
            cameraProvider.unbindAll()
        }
    }

    /**
     * 生成Preview
     */
    private fun createPreview(): Preview {
        return Preview.Builder()
            .setTargetResolution(resolution.toSize())
            .build()
            .apply {
                setSurfaceProvider(surfaceProvider)
            }
    }

}