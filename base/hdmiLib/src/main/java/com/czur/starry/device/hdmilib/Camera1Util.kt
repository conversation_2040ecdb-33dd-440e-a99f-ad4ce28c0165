package com.czur.starry.device.hdmilib

import android.annotation.SuppressLint
import android.hardware.camera2.CameraAccessException
import android.hardware.camera2.CameraCaptureSession
import android.hardware.camera2.CameraDevice
import android.hardware.camera2.CameraManager
import android.hardware.camera2.CameraMetadata
import android.hardware.camera2.CaptureRequest
import android.os.Handler
import android.os.HandlerThread
import android.util.Size
import android.view.Surface
import androidx.annotation.NonNull
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import rockchip.hardware.hdmi.V1_0.HdmiAudioStatus
import rockchip.hardware.hdmi.V1_0.IHdmi


/**
 * Created by 陈丰尧 on 2022/5/12
 */
class Camera1Util(
    private val cameraView: CameraView,
    private val scope: CoroutineScope,
    private val onOpenCameraFail: (exp: Exception) -> Unit
) {

    companion object {
        private const val TAG = "Camera1Util"
        var previewSize = Size(0, 0)
    }

    private var hdmiDeviceId = "100"

    private var cameraDevice: CameraDevice? = null
    private var mBackgroundHandler: Handler? = null
    private var mBackgroundThread: HandlerThread? = null
    private var captureRequestBuilder: CaptureRequest.Builder? = null
    private var cameraCaptureSessions: CameraCaptureSession? = null
    private var hdmiService: IHdmi? = null

    fun initCamera() {

    }


    fun releaseCamera() {
        logTagV(TAG, "释放Camera")
        if (null != cameraDevice) {
            cameraDevice?.close()
            cameraDevice = null
        }
    }


    @SuppressLint("MissingPermission")
    suspend fun openCamera(service: IHdmi, cameraManager: CameraManager) =
        withContext(Dispatchers.IO) {
            logTagD(TAG, "打开Camera")
            try {
                hdmiService = service
                hdmiDeviceId = service!!.hdmiDeviceId
                val status = service!!.mipiStatus
                logTagD(TAG, "status:" + status.status)
                logTagD(TAG, "width:" + status.width)
                logTagD(TAG, "height:" + status.height)
                logTagD(TAG, "fps:" + status.fps)
                logTagD(TAG, "hdmiDeviceId===$hdmiDeviceId")
                if (hdmiDeviceId.isNullOrEmpty()) {
                    hdmiDeviceId = "100"
                    return@withContext HDMIUtil.HDMIStatus.IDLE
                }
                previewSize = Size(status.width.toInt(), status.height.toInt())
                if (status.status == 0L) {
                    //status.status = 1 // 1 开启 0 未连接 目前测试并不准确，所有显示加载中
                    logTagI(TAG, "HDMI is not ready ==${status.status}")
                    HDMIUtil.HDMIStatus.PREPARE
                } else {
                    if (status.width == 0L || status.height == 0L) {
                        //新出现的情况，底层没有协商好，但是插入状态，status也正确，open也不报错。
                        logTagI(TAG, "status.width == 0L")
                        HDMIUtil.HDMIStatus.PREPARE
                    } else {
                        cameraManager.openCamera(hdmiDeviceId, stateCallback, mBackgroundHandler)
                        HDMIUtil.HDMIStatus.PREVIEW
                    }
                }
            } catch (exp: Exception) {
                onOpenCameraFail(exp)
                HDMIUtil.HDMIStatus.IDLE
            }
        }


    fun enableAudio(isEnable: Boolean = true) {
        val audioStatus = HdmiAudioStatus()
        audioStatus.deviceId = hdmiDeviceId
        //去除判断Mic占用；凯凯沟通确认底层可以处理是否播放声音。当status=1时，结束会议录制会自动切换声音。
        if (isEnable) {
            audioStatus.status = 1
        }else {
            audioStatus.status = 0
        }
        try {
            hdmiService?.onAudioChange(audioStatus)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    fun startBackgroundThread() {
        mBackgroundThread = HandlerThread("Camera Background")
        mBackgroundThread?.start()
        mBackgroundHandler = Handler(mBackgroundThread!!.looper)
    }

    fun stopBackgroundThread() {
        mBackgroundThread?.quitSafely()
        try {
            mBackgroundThread?.join()
            mBackgroundThread = null
            mBackgroundHandler = null
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }
    }

    private val stateCallback: CameraDevice.StateCallback = object : CameraDevice.StateCallback() {
        override fun onOpened(camera: CameraDevice) {
            // This is called when the camera is open
            logTagD(TAG, "onOpened")
            cameraDevice = camera
            createCameraPreview()
            //应凯凯要求，保证camera打开后再打开audio
            enableAudio()
        }

        override fun onDisconnected(camera: CameraDevice) {
            logTagD(TAG, "onDisconnected")
            cameraDevice?.close()
        }

        override fun onError(camera: CameraDevice, error: Int) {
            logTagI(TAG, "onError")
            if (null != cameraDevice) {
                cameraDevice?.close()
                cameraDevice = null
            }
        }
    }

     fun createCameraPreview() {
        try {
            val texture = cameraView.getSurfaceTexture()
            logTagD(TAG, "createCameraPreview")
            texture!!.setDefaultBufferSize(previewSize.width, previewSize.height)
            val surface = Surface(texture)
            captureRequestBuilder =
                cameraDevice!!.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
            captureRequestBuilder?.addTarget(surface)
            cameraDevice!!.createCaptureSession(
                listOf(surface),
                object : CameraCaptureSession.StateCallback() {
                    override fun onConfigured(@NonNull cameraCaptureSession: CameraCaptureSession) {
                        // The camera is already closed
                        if (null == cameraDevice) {
                            return
                        }
                        logTagD(TAG, "onConfigured")
                        // When the session is ready, we start displaying the preview.
                        cameraCaptureSessions = cameraCaptureSession
                        updatePreview()
                    }

                    override fun onConfigureFailed(@NonNull cameraCaptureSession: CameraCaptureSession) {
                        logTagI(TAG, "onConfigureFailed")
                    }

                },
                null
            )
        } catch (e: Exception) {
            logTagD(TAG, "=====e=${e.message}")
            e.printStackTrace()
        }
    }

    private fun updatePreview() {
        if (null == cameraDevice) {
            logTagE(TAG, "updatePreview error, return")
        }
        logTagD(TAG, "updatePreview")
        captureRequestBuilder!!.set(CaptureRequest.CONTROL_MODE, CameraMetadata.CONTROL_MODE_AUTO)
        try {
            cameraCaptureSessions!!.setRepeatingRequest(
                captureRequestBuilder!!.build(),
                null,
                mBackgroundHandler
            )
        } catch (e: CameraAccessException) {
            e.printStackTrace()
        }
    }

    fun releaseCaptureSession() {
        if (null != cameraCaptureSessions) {
            cameraCaptureSessions?.close()
            cameraCaptureSessions = null
        }
    }

}