package com.czur.starry.device.hdmilib

import android.util.Size
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW


private const val DEF_RESOLUTION_WIDTH = 1920
private const val DEF_RESOLUTION_HEIGHT = 1080
private const val TAG = "HDMI-Resolution"

data class HDMIResolution(
    val width: Int, // 分辨率宽
    val height: Int // 分辨率高
) {
    override fun toString(): String {
        return "HDMIResolution(width=$width, height=$height)"
    }
}

fun HDMIResolution.toSize(): Size = Size(width, height)

val defResolution = HDMIResolution(DEF_RESOLUTION_WIDTH, DEF_RESOLUTION_HEIGHT)

fun createResolution(resolutionStr: String): HDMIResolution {
    logTagV(TAG, "解析分辨率:${resolutionStr}")
    val res = resolutionStr.split("x", "p", "i", ignoreCase = true)
    if (res.isNullOrEmpty() || res.size != 3) {
        logTagW(TAG, "解析分辨率错误, 使用默认分辨率:${DEF_RESOLUTION_WIDTH}*${DEF_RESOLUTION_HEIGHT}")
        return defResolution
    }

    val width = res[0].toInt()
    val height = res[1].toInt()
    return HDMIResolution(width, height)
}