package com.czur.starry.device.hdmilib

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.RemoteException
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.otalib.OTAHandler.allMeetingStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import rockchip.hardware.hdmi.V1_0.IHdmi

/**
 * Created by 陈丰尧 on 2022/8/22
 * HDMI检测器
 */
class HDMIMonitor {
    companion object {
        const val TAG = "HDMIMonitor"
        const val ACTION_HDMI_CHANGE = "com.android.action.CZUR_HDMIIN_CHANGED"
        const val ACTION_HDMI_STATE = "com.android.action.CZUR_HDMIIN_EAIRPLAY"
        const val ACTION_HDMI_STATE_KEY = "hdminIn"
        const val EAIRPLAY_PACKAGE_NAME = "com.ecloud.eairplay"
    }

    private var service: IHdmi? = null
    private var hdmiReceiver: HDMIStatusReceiver? = null

    /**
     * 获取当前HDMI的状态
     */
    fun getHDMIIFStatus(): HDMIIFStatus {

        try {
            service = IHdmi.getService(true)
            val status = service!!.mipiStatus.status
            return if (status == 0L) {
                HDMIIFStatus.OUT
            } else {
                HDMIIFStatus.IN
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }

        return HDMIIFStatus.OUT
    }


    /**
     * 注册HDMIReceiver的回调
     */
    fun registerHDMIReceiver(context: Context, hdmiIfStatusListener: (HDMIIFStatus) -> Unit) {
        hdmiReceiver = HDMIStatusReceiver(hdmiIfStatusListener).apply {
            val intentFilter = IntentFilter(ACTION_HDMI_CHANGE)
            context.registerReceiver(this, intentFilter)
        }
    }

    /**
     * 取消注册HDMI的监听
     */
    fun unRegisterHDMIReceiver(context: Context) {
        hdmiReceiver?.let {
            context.unregisterReceiver(it)
            hdmiReceiver = null
        }
    }


    /**
     * 监听HDMI插拔
     */
    private class HDMIStatusReceiver(
        private val hdmiIfStatusListener: (HDMIIFStatus) -> Unit
    ) : BroadcastReceiver() {
        companion object {
            private const val ACTION_HDMI_STATE_KEY = "hdminIn"
        }

        override fun onReceive(context: Context?, intent: Intent?) {
            logTagV(TAG, "收到了HDMI广播:${intent}")
            intent?.let {
                logIntent(intent)
            }
            when (intent?.action) {
                ACTION_HDMI_CHANGE -> {
                    // hdmi 的插入状态 决定切换预览和无信号
                    val hdmiStatus =
                        intent.extras?.getBoolean(ACTION_HDMI_STATE_KEY, false) ?: false
                    logTagV(TAG, "hdmi插入状态hdmiStatus:${hdmiStatus}")
                    logTagV(TAG, "hdmi插入状态allMeetingStatus:${allMeetingStatus}")
                    logTagV(
                        TAG,
                        "hdmi插入状态KEY_HDMI_AIRPLAY_OPEN:${
                            getBooleanSystemProp(
                                KEY_HDMI_AIRPLAY_OPEN,
                                true
                            )
                        }"
                    )
                    val hdmiifStatus = if (hdmiStatus) HDMIIFStatus.IN else HDMIIFStatus.OUT
                    logTagD("song", "hdmiifStatus:${hdmiifStatus}")
                    // 接口状态改变
                    hdmiIfStatusListener(hdmiifStatus)

                }

            }
        }
    }
}

/**
 * 发送广播通知投屏
 */
fun sendBroadCastToMirror(context: Context?, hdmiIn: Boolean) {
    logTagV(HDMIMonitor.TAG, "给宜享发送hdmi广播 hdmiIn:${hdmiIn}")
    val intent = Intent(HDMIMonitor.ACTION_HDMI_STATE).apply {
        putExtra(HDMIMonitor.ACTION_HDMI_STATE_KEY, hdmiIn)
        setPackage(HDMIMonitor.EAIRPLAY_PACKAGE_NAME)
    }
    context?.sendBroadcast(intent)
}

//偶现发送一次广播 宜享未启动。多次发送
suspend fun repeatSendBroadcastToMirror(context: Context?) =
    withContext(Dispatchers.IO) {
        var timeout = 3
        do {
            sendBroadCastToMirror(context, true)
            delay(1000)
            timeout--

        } while (timeout > 0)

    }