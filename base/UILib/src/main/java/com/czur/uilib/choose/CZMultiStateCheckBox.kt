package com.czur.uilib.choose

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PathMeasure
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.uilib.R
import com.czur.uilib.color
import kotlin.math.min

/**
 * Created by 陈丰尧 on 2023/8/4
 */
private const val BORDER_WIDTH = 5F // 边框宽度

private const val ANIM_DURATION = 300L   // 动画时长

class CZMultiStateCheckBox @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val bgColor by color(R.color.cz_checkbox_bg)
    private val checkMarkColor by color(R.color.cz_checkbox_check_mark)

    private val radius: Float by lazy {
        min(width, height) / 4F // 外部圆角
    }
    private val halfCheckWidth
        get() = width / 2F // 半选中状态 宽度
    private val halfCheckRadius: Float by lazy {
        halfCheckWidth / 4F // 半选中状态 中心圆角半径
    }

    // 当前状态
    private var currentState: CheckStatus = CheckStatus.UNCHECKED

    // 目标状态
    private var targetState: CheckStatus? = null

    private val checkMarkPath: Path by lazy(LazyThreadSafetyMode.NONE) {
        initCheckMarkPath()
    }
    private val checkPathMeasure: PathMeasure by lazy(LazyThreadSafetyMode.NONE) {
        PathMeasure(checkMarkPath, false).also {
            markPathLength = it.length
        }
    }
    private var markPathLength = 0F
    private val checkMarkWidth: Float by lazy {
        width / 20F
    }
    private val drawPath = Path()

    private val paint by lazy {
        Paint().apply {
            isAntiAlias = true
        }
    }
    private var animProcess = 0F    // 动画进度(0:OFF, 1:ON)

    private val isAnim      // 是否正在动画中
        get() = animProcess != 0F && animProcess != 1F

    private var currentAnim: ValueAnimator = ValueAnimator.ofFloat().apply {
        duration = ANIM_DURATION
        interpolator = AccelerateDecelerateInterpolator()
        addUpdateListener {
            if (it.isRunning) {
                animProcess = it.animatedValue as Float
                invalidate()
            }
        }
    }

    // listener
    private var onCheckedChangeListener: ((state: CheckStatus, fromUser: Boolean) -> Unit)? = null

    var blockOperation: BlockOperation? = null
    // 半选中状态会自动迁移到的状态
    var halfAutoMoveTo: CheckStatus = CheckStatus.CHECKED

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CZBindClickView)
        val withClickIds = typedArray.getString(R.styleable.CZBindClickView_withClickIDs) ?: ""
        typedArray.recycle()
        if (withClickIds.isNotEmpty()) {
            post {
                bindOtherViewClick(withClickIds)
            }
        }

        setOnDebounceClickListener {
            if (isAnim) return@setOnDebounceClickListener
            val nextStatus = when(currentState) {
                CheckStatus.UNCHECKED -> CheckStatus.CHECKED
                CheckStatus.CHECKED -> CheckStatus.UNCHECKED
                CheckStatus.HALF_CHECKED -> halfAutoMoveTo
            }
            changeCheckedState(nextStatus, useAnim = true, fromUser = true)
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 1. 绘制底色
        paint.style = Paint.Style.FILL
        paint.color = bgColor
        canvas.drawRoundRect(0F, 0F, width.toFloat(), height.toFloat(), radius, radius, paint)

        // 2. 根据状态不同, 绘制不同的内容
        var targetDrawState = targetState ?: currentState.nextAnimState()
        if (targetDrawState == currentState) {
            targetDrawState = currentState.nextAnimState()
        }
        when (currentState to targetDrawState) {
            CheckStatus.UNCHECKED to CheckStatus.CHECKED -> drawUncheckToCheck(animProcess, canvas)
            CheckStatus.UNCHECKED to CheckStatus.HALF_CHECKED -> drawUncheckToHalf(
                animProcess,
                canvas
            )

            CheckStatus.CHECKED to CheckStatus.UNCHECKED -> drawCheckToUncheck(animProcess, canvas)
            CheckStatus.CHECKED to CheckStatus.HALF_CHECKED -> drawCheckToHalf(animProcess, canvas)

            CheckStatus.HALF_CHECKED to CheckStatus.UNCHECKED -> drawHalfToUncheck(
                animProcess,
                canvas
            )

            CheckStatus.HALF_CHECKED to CheckStatus.CHECKED -> drawHalfToCheck(animProcess, canvas)
        }
    }

    private fun drawUncheckToCheck(drawProgress: Float, canvas: Canvas) {
        // 1. 绘制勾
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeJoin = Paint.Join.ROUND
        paint.strokeWidth = checkMarkWidth
        paint.color = checkMarkColor

        drawPath.reset()
        checkPathMeasure.getSegment(0F, markPathLength * drawProgress, drawPath, true)
        canvas.drawPath(drawPath, paint)

        // 2. 绘制遮挡的白框
        paint.style = Paint.Style.FILL
        paint.color = checkMarkColor
        paint.alpha = (255 * (1 - drawProgress)).toInt()
        // 计算遮挡框的宽度
        val coverEdgeMax = width - BORDER_WIDTH
        val coverEdgeMin = width * 0.8F
        val coverEdge = (coverEdgeMax - coverEdgeMin) * (1 - drawProgress) + coverEdgeMin
        if (coverEdge > 0) {
            val radiusMin = radius - BORDER_WIDTH / 2F  // 内部白框的圆角小一些, 好看一些, 不然四个角会比较粗
            val radiusMax = coverEdgeMin / 2F
            val radius = (radiusMax - radiusMin) * (drawProgress) + radiusMin
            canvas.drawRoundRect(
                (width - coverEdge) / 2F,
                (height - coverEdge) / 2F,
                (width + coverEdge) / 2F,
                (height + coverEdge) / 2F,
                radius,
                radius,
                paint
            )
        }
    }

    private fun drawCheckToUncheck(drawProgress: Float, canvas: Canvas) =
        drawUncheckToCheck(1 - drawProgress, canvas)

    private fun drawCheckToHalf(drawProgress: Float, canvas: Canvas) {
        // 绘制白底
        paint.style = Paint.Style.FILL
        paint.color = checkMarkColor
        val coverEdge = width - BORDER_WIDTH
        val drawWhiteRadius = radius - BORDER_WIDTH / 2F
        canvas.drawRoundRect(
            (width - coverEdge) / 2F,
            (height - coverEdge) / 2F,
            (width + coverEdge) / 2F,
            (height + coverEdge) / 2F,
            drawWhiteRadius,
            drawWhiteRadius,
            paint
        )
        // 绘制Half圆角矩形, 从最大到最小
        paint.style = Paint.Style.FILL
        paint.color = bgColor
        val drawHalfMax = width
        val drawHalfMin = halfCheckWidth
        val drawHalfWidth = (drawHalfMax - drawHalfMin) * (1 - drawProgress) + drawHalfMin
        val drawHalfRadiusMax = radius
        val drawHalfRadiusMin = halfCheckRadius
        val drawHalfCheckRadius =
            (drawHalfRadiusMax - drawHalfRadiusMin) * (1 - drawProgress) + drawHalfRadiusMin
        canvas.drawRoundRect(
            (width - drawHalfWidth) / 2F,
            (height - drawHalfWidth) / 2F,
            (width + drawHalfWidth) / 2F,
            (height + drawHalfWidth) / 2F,
            drawHalfCheckRadius,
            drawHalfCheckRadius,
            paint
        )

        // 绘制勾
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeJoin = Paint.Join.ROUND
        paint.strokeWidth = checkMarkWidth
        paint.color = checkMarkColor

        drawPath.reset()
        checkPathMeasure.getSegment(0F, markPathLength * (1 - drawProgress), drawPath, true)
        canvas.drawPath(drawPath, paint)
    }

    private fun drawHalfToCheck(drawProgress: Float, canvas: Canvas) =
        drawCheckToHalf(1 - drawProgress, canvas)

    private fun drawHalfToUncheck(drawProgress: Float, canvas: Canvas) {
        // 绘制白色的圆角矩形
        paint.style = Paint.Style.FILL
        paint.color = checkMarkColor
        val coverEdge = width - BORDER_WIDTH
        val radius = radius - BORDER_WIDTH / 2F
        canvas.drawRoundRect(
            (width - coverEdge) / 2F,
            (height - coverEdge) / 2F,
            (width + coverEdge) / 2F,
            (height + coverEdge) / 2F,
            radius,
            radius,
            paint
        )

        // 绘制半选中的圆角矩形
        paint.style = Paint.Style.FILL
        paint.color = bgColor
        paint.alpha = (255 * (1 - drawProgress)).toInt()
        val drawHalfWidth = halfCheckWidth * (1 - drawProgress)
        canvas.drawRoundRect(
            (width - drawHalfWidth) / 2F,
            (height - drawHalfWidth) / 2F,
            (width + drawHalfWidth) / 2F,
            (height + drawHalfWidth) / 2F,
            halfCheckRadius,
            halfCheckRadius,
            paint
        )

    }

    private fun drawUncheckToHalf(drawProgress: Float, canvas: Canvas) =
        drawHalfToUncheck(1 - drawProgress, canvas)


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 如果不是正方形, 则强制修改成正方形
        val width = MeasureSpec.getSize(widthMeasureSpec)
        val height = MeasureSpec.getSize(heightMeasureSpec)
        if (width == height) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        } else {
            val size = min(width, height)
            setMeasuredDimension(size, size)
        }
    }

    fun setOnCheckedChangeListener(listener: ((state: CheckStatus, fromUser: Boolean) -> Unit)?) {
        onCheckedChangeListener = listener
    }

    fun setChecked(state: CheckStatus, useAnim: Boolean = true) {
        changeCheckedState(state, useAnim, false)
    }


    private fun changeCheckedState(state: CheckStatus, useAnim: Boolean, fromUser: Boolean) {
        if ((targetState ?: currentState) == state) {
            return // 已经是目标状态或正在变成目标状态, 不需要再变
        }
        if (blockOperation?.shouldBlock(state, fromUser) == true) return // 阻止操作

        cancelAnim()
        if (useAnim) {
            targetState = state
            startAnim()
            onCheckedChangeListener?.invoke(state, fromUser)
        } else {
            targetState = null
            animProcess = 0F
            invalidate()
            onCheckedChangeListener?.invoke(state, fromUser)
        }
    }

    private fun bindOtherViewClick(withClickIds: String) {
        val ids = withClickIds.split(",")
        val parent = parent as? ViewGroup ?: return
        ids.forEach {
            val id = resources.getIdentifier(it.trim(), "id", context.packageName)
            if (id != 0) {
                val view = parent.findViewById<View>(id)
                view.setOnDebounceClickListener {
                    performClick()
                }
            }
        }
    }

    private fun initCheckMarkPath(): Path {
        val path = Path()
        val startXPosition = 0.25F
        val startYPosition = 0.45F

        val middleXPosition = 0.45F
        val middleYPosition = 0.65F

        val endXPosition = 0.75F
        val endYPosition = 0.35F

        path.moveTo(width * startXPosition, height * startYPosition)
        path.lineTo(width * middleXPosition, height * middleYPosition)
        path.lineTo(width * endXPosition, height * endYPosition)
        return path
    }


    private fun startAnim() {
        currentAnim.setFloatValues(0F, 1F)
        val listener = object : Animator.AnimatorListener {
            override fun onAnimationEnd(animator: Animator) {
                // 动画结束后, 更新当前状态
                currentState = targetState ?: currentState
                animProcess = 0F
                invalidate()
                currentAnim.removeListener(this)
            }

            override fun onAnimationCancel(animator: Animator) {
                targetState = null
                animProcess = 0F
                currentAnim.removeListener(this)
            }

            override fun onAnimationRepeat(animation: Animator) {}

            override fun onAnimationStart(animator: Animator) {}
        }
        currentAnim.addListener(listener)
        currentAnim.start()
    }

    private fun cancelAnim() {
        if (currentAnim.isRunning) {
            currentAnim.cancel()
        }
    }

    fun setCheckState(checkStatus: CheckStatus, useAnim: Boolean = true) {
        changeCheckedState(checkStatus, useAnim, false)
    }

    interface BlockOperation {
        fun shouldBlock(willBeStatue: CheckStatus, fromUser: Boolean): Boolean
    }

    object BlockUserCheckOperation : BlockOperation {
        override fun shouldBlock(willBeStatue: CheckStatus, fromUser: Boolean): Boolean {
            return willBeStatue == CheckStatus.CHECKED && fromUser
        }
    }

    object BlockUserUnCheckOperation : BlockOperation {
        override fun shouldBlock(willBeStatue: CheckStatus, fromUser: Boolean): Boolean {
            return willBeStatue == CheckStatus.UNCHECKED && fromUser
        }
    }

    enum class CheckStatus {
        CHECKED,        // 选中
        HALF_CHECKED,   // 半选中
        UNCHECKED;      // 未选中

        fun nextAnimState(): CheckStatus {
            return when (this) {
                UNCHECKED -> CHECKED
                CHECKED -> HALF_CHECKED
                HALF_CHECKED -> UNCHECKED
            }
        }

        fun preAnimState(): CheckStatus {
            return when (this) {
                UNCHECKED -> HALF_CHECKED
                CHECKED -> UNCHECKED
                HALF_CHECKED -> CHECKED
            }
        }
    }
}