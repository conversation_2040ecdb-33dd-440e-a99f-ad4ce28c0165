package com.czur.uilib.choose

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.czur.starry.device.baselib.utils.ConstraintUtil
import com.czur.uilib.R

/**
 * Created by 陈丰尧 on 2024/7/24
 */
private const val ID_OFF_SET: Int = 1000

class CZTabBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), View.OnClickListener {
    private var titles: List<String> = emptyList()
    private var constraintUtil: ConstraintUtil? = null
    var selIndex = 0
        private set
    private var listener: ((selPos: Int) -> Unit)? = null

    init {
        constraintUtil = ConstraintUtil(this)
        updateViews()
    }

    fun setTitles(titles: List<String>, lastSelect: Int = 0) {
        this.titles = titles
        updateViews()
        selIndex = lastSelect
        updateSelUI(selIndex)
    }

    /**
     * 设置标签改变监听
     *
     * @param listener 选中标题改变的监听, 如果传入null,则表示清理回调
     */
    fun setOnSelChangeListener(listener: ((selPos: Int) -> Unit)?) {
        this.listener = listener
    }

    private fun updateViews() {
        removeAllViews()
        if (titles.isEmpty()) {
            return
        }

        for (i in titles.indices) {
            val title = titles[i]
            val textView = LayoutInflater.from(context)
                .inflate(R.layout.widget_item_cz_tab_bar, this, false) as TextView
            textView.id = generateViewId()

            textView.tag = i + ID_OFF_SET
            textView.setOnClickListener(this)

            textView.text = title
            // 鼠标事件
            textView.setOnHoverListener { _: View?, event: MotionEvent ->
                if (selIndex == i) {
                    return@setOnHoverListener false
                }
                when (event.action) {
                    MotionEvent.ACTION_HOVER_ENTER -> {
                        textView.setTextColor(
                            resources.getColor(
                                R.color.text_content_title_hover,
                                null
                            )
                        )
                        return@setOnHoverListener true
                    }

                    MotionEvent.ACTION_HOVER_EXIT -> {
                        textView.setTextColor(resources.getColor(R.color.text_content_title, null))
                        return@setOnHoverListener true
                    }
                }
                false
            }

            addView(textView)
        }

        val begin = constraintUtil!!.begin()

        begin.commit()
        for (i in titles.indices) {
            if (i == 0) {
                begin.leftToLeftOf(getChildAt(i).id, ConstraintSet.PARENT_ID)
            } else {
                begin.leftToRightOf(getChildAt(i).id, getChildAt(i - 1).id)
            }

            if (titles.size > 1) {
                if (i == titles.size - 1) {
                    begin.rightToRightOf(getChildAt(i).id, ConstraintSet.PARENT_ID)
                } else {
                    begin.rightToLeftOf(getChildAt(i).id, getChildAt(i + 1).id)
                }
            }
            updateNormalUI(i)
        }
        begin.chainHorizontal(getChildAt(0).id, ConstraintSet.CHAIN_SPREAD_INSIDE)
        begin.commit()
    }

    private fun updateSelUI(selIndex: Int) {
        val textView = getChildAt(selIndex) as TextView
        textView.setTextColor(resources.getColor(R.color.white, null))
        textView.background =
            AppCompatResources.getDrawable(context, R.drawable.bg_cz_tab_bar_selected)
    }

    private fun updateNormalUI(normalIndex: Int) {
        val textView = getChildAt(normalIndex) as TextView
        textView.setTextColor(resources.getColor(R.color.text_content_title, null))
        textView.background = null
    }

    override fun onClick(v: View) {
        val id = v.tag as Int
        val clickIndex = id - ID_OFF_SET
        if (clickIndex != selIndex) {
            updateNormalUI(selIndex)
            updateSelUI(clickIndex)
            listener?.invoke(clickIndex)
        }

        selIndex = clickIndex
    }


    fun setTextViewBar(id: Int) {
        val clickIndex = id - ID_OFF_SET
        if (clickIndex != selIndex) {
            updateNormalUI(selIndex)
            updateSelUI(clickIndex)
            listener?.invoke(clickIndex)
        }

        selIndex = clickIndex
    }
}