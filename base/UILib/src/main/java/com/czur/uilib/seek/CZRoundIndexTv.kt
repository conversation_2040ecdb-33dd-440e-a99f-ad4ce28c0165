package com.czur.uilib.seek

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.Gravity
import androidx.appcompat.widget.AppCompatTextView
import com.czur.uilib.R

/**
 * Created by 陈丰尧 on 2023/8/3
 * 圆角的索引TextView
 */
class CZRoundIndexTv @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var bgColor: Int
    private var textColor: Int

    private val paint: Paint by lazy(LazyThreadSafetyMode.NONE) {
        Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
        }
    }

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CZRoundIndexTv)
        bgColor = ta.getColor(R.styleable.CZRoundIndexTv_czRoundIndexTvBgColor, Color.WHITE)
        textColor = ta.getColor(R.styleable.CZRoundIndexTv_czRoundIndexTvTextColor, Color.BLACK)
        ta.recycle()

        includeFontPadding = false
        setTextColor(textColor)
        gravity = Gravity.CENTER
    }

    /**
     * 更新颜色
     */
    fun updateColor(newBgColor: Int = bgColor, newTextColor: Int = textColor) {
        this.bgColor = newBgColor
        this.textColor = newTextColor
        setTextColor(textColor)
        invalidate()
    }

    fun updateColorRes(newBgColorRes: Int, newTextColorRes: Int) {
        updateColor(context.getColor(newBgColorRes), context.getColor(newTextColorRes))
    }

    override fun setText(text: CharSequence?, type: BufferType?) {
        // 只显示第一个字符
        var textToSet = text
        if (text != null && text.length > 1) {
            textToSet = text.subSequence(0, 1)
        }
        super.setText(textToSet, type)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // 设置为正方形
        val width = measuredWidth
        val height = measuredHeight
        if (width != height) {
            val size = width.coerceAtLeast(height)
            setMeasuredDimension(size, size)
        }
    }

    override fun onDraw(canvas: Canvas) {
        // 绘制圆
        paint.color = bgColor
        canvas.drawCircle(width / 2F, height / 2F, width / 2F, paint)
        super.onDraw(canvas)
    }

}