package com.czur.uilib

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity.CENTER_VERTICAL
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.Glide
import com.czur.starry.device.baselib.tips.setTextAndTip
import com.czur.starry.device.baselib.utils.getString

/**
 * Created by 陈丰尧 on 2023/8/11
 */
class CZMenuItem @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val iconIv: ImageView by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.menuIconIv)
    }
    private val titleTv: TextView by lazy(LazyThreadSafetyMode.NONE) {
        findViewById(R.id.menuTitleTv)
    }

    private val dividerTop: View by lazy(LazyThreadSafetyMode.NONE) { findViewById(R.id.dividerTop) }
    private val dividerBottom: View by lazy(LazyThreadSafetyMode.NONE) { findViewById(R.id.dividerBottom) }
    var dividerType: DividerType = DividerType.NONE
        set(value) {
            field = value
            when (value) {
                DividerType.NONE -> {
                    dividerTop.visibility = View.GONE
                    dividerBottom.visibility = View.GONE
                }

                DividerType.TOP -> {
                    dividerTop.visibility = View.VISIBLE
                    dividerBottom.visibility = View.GONE
                }

                DividerType.BOTTOM -> {
                    dividerTop.visibility = View.GONE
                    dividerBottom.visibility = View.VISIBLE
                }

                DividerType.BOTH -> {
                    dividerTop.visibility = View.VISIBLE
                    dividerBottom.visibility = View.VISIBLE
                }
            }
        }

    enum class DividerType {
        NONE, TOP, BOTTOM, BOTH
    }

    init {
        inflate(context, R.layout.widget_cz_menu_item, this)


        val ta = context.obtainStyledAttributes(attrs, R.styleable.CZMenuItem)
        val iconDrawable = ta.getDrawable(R.styleable.CZMenuItem_czMenuIcon)
        val iconTitle = ta.getString(R.styleable.CZMenuItem_czMenuTitle)
        ta.recycle()

        iconDrawable?.let {
            iconIv.setImageDrawable(it)
        }
        iconTitle?.let {
            titleTv.text = it
        }
    }

    fun setIconRes(res: Int) {
        iconIv.setImageResource(res)
    }

    fun setIconUrl(url: String?, placeholderRes: Int? = null) {
        val builder = Glide.with(this)
            .load(url)
        if (placeholderRes != null) {
            builder.placeholder(placeholderRes)
        }
        builder.into(iconIv)
    }

    fun setTitle(title: String) {
        titleTv.text = title
    }

    fun setTitleRes(res: Int) {
        titleTv.setText(res)
    }

    fun setTitleAndTip(title: String) {
        titleTv.setTextAndTip(title)
    }

    fun setTitleResAndTip(res: Int) {
        titleTv.setTextAndTip(getString(res))
    }
}