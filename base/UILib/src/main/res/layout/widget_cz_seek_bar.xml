<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage"
    android:clipChildren="false">

    <ImageView
        android:id="@+id/seekDownIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_cz_seek_bar_down"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.seek.CZSeekBarCore
        android:id="@+id/seekBarCore"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/seekDownIv"
        app:layout_constraintRight_toLeftOf="@id/seekUpIv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginLeft="0px"
        app:layout_goneMarginRight="0px" />

    <ImageView
        android:id="@+id/seekUpIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_cz_seek_bar_up"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>