package android.util;

import androidx.annotation.NonNull;

import org.jetbrains.annotations.Nullable;

import java.io.PrintWriter;
import java.io.Writer;

/**
 * Lightweight wrapper around {@link PrintWriter} that automatically indents
 * newlines based on internal state. It also automatically wraps long lines
 * based on given line length.
 * <p>
 * Delays writing indent until first actual write on a newline, enabling indent
 * modification after newline.
 *
 * @hide
 */
public class IndentingPrintWriter extends PrintWriter {

    public IndentingPrintWriter(@NonNull Writer writer) {
        this(writer, "  ", -1);
    }

    public IndentingPrintWriter(@NonNull Writer writer, @NonNull String singleIndent) {
        this(writer, singleIndent, null, -1);
    }

    public IndentingPrintWriter(@NonNull Writer writer, @NonNull String singleIndent,
                                String prefix) {
        this(writer, singleIndent, prefix, -1);
    }

    public IndentingPrintWriter(@NonNull Writer writer, @NonNull String singleIndent,
                                int wrapLength) {
        this(writer, singleIndent, null, wrapLength);
    }

    public IndentingPrintWriter(@NonNull Writer writer, @NonNull String singleIndent,
                                @Nullable String prefix, int wrapLength) {
        super(writer);

    }

    /**
     * Overrides the indent set in the constructor for the next printed line.
     *
     * @deprecated Use the "prefix" constructor parameter
     * @hide
     */
    @NonNull
    @Deprecated
    public IndentingPrintWriter setIndent(@NonNull String indent) {
        return this;
    }

    /**
     * Overrides the indent set in the constructor with {@code singleIndent} repeated {@code indent}
     * times.
     *
     * @deprecated Use the "prefix" constructor parameter
     * @hide
     */
    @NonNull
    @Deprecated
    public IndentingPrintWriter setIndent(int indent) {
        return this;
    }

    /**
     * Increases the indent starting with the next printed line.
     */
    @NonNull
    public IndentingPrintWriter increaseIndent() {
        return this;
    }

    /**
     * Decreases the indent starting with the next printed line.
     */
    @NonNull
    public IndentingPrintWriter decreaseIndent() {
        return this;
    }

    /**
     * Prints a key-value pair.
     */
    @NonNull
    public IndentingPrintWriter print(@NonNull String key, @Nullable Object value) {
        return this;
    }

    /**
     * Prints a key-value pair, using hexadecimal format for the value.
     */
    @NonNull
    public IndentingPrintWriter printHexInt(@NonNull String key, int value) {
        return this;
    }


}
