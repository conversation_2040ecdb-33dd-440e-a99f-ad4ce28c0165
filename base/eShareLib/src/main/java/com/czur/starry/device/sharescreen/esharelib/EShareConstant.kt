package com.czur.starry.device.sharescreen.esharelib

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale

/** PIN码模式：自定义  */
const val PIN_CODE_MODE_CUSTOM = 0

/** PIN码模式：随机  */
const val PIN_CODE_MODE_RANDOM = 1

/** PIN码模式：关闭  */
const val PIN_CODE_MODE_DISABLE = 2

/** PIN码模式：编码6位数字  */
const val PIN_CODE_MODE_ENCRYPT_6N = 3

/** PIN码模式：编码8位字母  */
const val PIN_CODE_MODE_ENCRYPT_8C = 4

/**
 * callbackkey值
 */
const val E_SHARE_CALLBACK_PIN_CODE = "eshare_pin_code"
const val E_SHARE_CALLBACK_QRCODE = "eshare_qrcode_info"
const val E_SHARE_CALLBACK_PIN_MODE = "eshare_pin_code_mode"
const val E_SHARE_CALLBACK_MULTI_SCREEN_MODE = "eshare_multi_screen_mode"
const val E_SHARE_CALLBACK_AIR_PLAY_ENABLE = "eshare_client_airplay_enable"
const val E_SHARE_CALLBACK_CHROMECAST_ENABLE = "eshare_chromecast_enable"
const val E_SHARE_CALLBACK_NETWORK = "eshare_network_info"
const val KEY_AUDIO_ENABLE = "key_audio_enable"
const val E_SHARE_CALLBACK_MIRACAST_ENABLE = "eshare_miracast_enable"
const val E_SHARE_DEVICE_NAME = "eshare_device_name"    // 投屏名称
const val E_SHARE_CALLBACK_DLNA_ENABLE = "eshare_dlna_enable"   // DLNA

const val E_SHARE_BYOM_CAMERA_RUNNING = "eshare_byom_running"    // byom camera运行状态
const val E_SHARE_BYOM_AUDIO_RUNNING = "eshare_audio_running"    // byom audio运行状态

const val E_SHARE_DEVICE_NAME_MAX_LENGTH = 19   // 最多输入19个字符(Miracast限制)

const val DEF_E_SHARE_NAME = "StarryHub"


/**
 * clientType值
 */
enum class EShareClientType(val value: Int) {
    RECEIVER(-2),
    UNKNOWN(-1),
    WINDOWS(0),
    ANDROID(1),
    IOS(2),
    CHROME(4),
    MAC_OS(5),
    LINUX(6),
    AIRPLAY(10),
    MIRACAST(11),
    DLNA(20);


    companion object {
        fun create(value: Int): EShareClientType {
            return when (value) {
                RECEIVER.value -> RECEIVER
                UNKNOWN.value -> UNKNOWN
                WINDOWS.value -> WINDOWS
                ANDROID.value -> ANDROID
                IOS.value -> IOS
                CHROME.value -> CHROME
                MAC_OS.value -> MAC_OS
                LINUX.value -> LINUX
                AIRPLAY.value -> AIRPLAY
                MIRACAST.value -> MIRACAST
                DLNA.value -> DLNA
                else -> UNKNOWN
            }
        }
    }
}

/**
 * 智能满屏功能
 */
enum class CastFullscreenValue(val value: Int) {
    ENABLE(1),
    DISABLE(0);

    fun toBoolean(): Boolean {
        return when (this) {
            ENABLE -> true
            else -> false
        }
    }

    companion object {
        fun create(value: Int): CastFullscreenValue {
            return if (value == ENABLE.value) ENABLE else DISABLE
        }
    }
}

/**
 * 宜享设备名禁止输入的字符
 */
val deviceNameBlockChars = listOf(
    '\\',
    '/',
    ':',
    '*',
    '?',
    '\"',
    '<',
    '>',
    '|',
    '&',
    '%',
)

// 投屏二维码 对应的下载地址
const val E_SHARE_QR_CODE_URL = "https://czur.com/app/czur"

// 二维码签名的网址
val shareQRCodeUrl: String by lazy {
    when (Constants.starryHWInfo.salesLocale) {
        StarryDevLocale.Mainland -> "https://c.czur.com/m/a/Upass9e84M#czur"
        StarryDevLocale.Overseas -> "https://c.czur.com/m/a/sD2hRen4OO#czur"
    }
}

enum class ESharePairState(val value: Int) {
    UNKNOWN(0),  /** 未知 */
    NORECORD(1), /** 无配对记录 */
    PAIRING(2),  /** 正在配对中 */
    SUCCESS(3),  /** 配对成功 */
    FAILURE(4);  /** 配对失败 */
}
