package com.czur.starry.device.sharescreen.esharelib.ui

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.KeyEvent
import androidx.lifecycle.whenResumed
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.czur.starry.device.sharescreen.esharelib.databinding.ActivityAirplayRequestBinding
import com.czur.starry.device.sharescreen.esharelib.entity.AirplayReqInfo
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2022/2/23
 */
class AirPlayRequestDialog : CZViewBindingAty<ActivityAirplayRequestBinding>() {
    companion object {
        private const val TAG = "AirPlayRequestDialog"
        const val KEY_CLIENT_IP = "clientIP"
        const val KEY_CLIENT_NAME = "clientName"
        const val KEY_CLIENT_LIST = "clientList"

        const val AIR_PLAY_ATY_TAG = "AirPlayRequestDialog"

        private const val REQ_TIME_OUT = 9 * ONE_SECOND
    }

    override val atyTag: String = AIR_PLAY_ATY_TAG

    private val airPlayClientList = ArrayList<AirplayReqInfo>()
    private var currentIp: String = ""
    private val systemManager by lazy { SystemManagerProxy() }

    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(this)
    }

    override fun initWindow() {
        super.initWindow()
        if (Constants.starryHWInfo.hasTouchScreen) {
            logTagV(TAG, "触控屏,添加偏移量")
            window.apply {
                val params = attributes
                params.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun handlePreIntent(preIntent: Intent) {
        if (preIntent.hasExtra(KEY_CLIENT_LIST)) {
            val list = preIntent.getParcelableArrayListExtra<AirplayReqInfo>(KEY_CLIENT_LIST)
            list?.let {
                airPlayClientList.addAll(list)
            }
        } else {
            val requestIp = preIntent.getStringExtra(KEY_CLIENT_IP) ?: return
            val requestName = preIntent.getStringExtra(KEY_CLIENT_NAME) ?: ""
            airPlayClientList.add(AirplayReqInfo(requestName, requestIp))
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlePreIntent(intent)
    }

    override fun ActivityAirplayRequestBinding.initBindingViews() {
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出

        if (airPlayClientList.isEmpty()) {
            finish()
            return
        }

        val (requestName, requestIp) = airPlayClientList.removeAt(0)
        currentIp = requestIp

        val info = getString(R.string.str_airplay_request, requestName)
        airplayInfTv.text = info

        airplayCancelBtn.setOnClickListener {
            logTagD(TAG, "拒绝接入airplay")
            eShareServerSDK.refuseScreenCast(requestIp, 7)
            checkOrFinish()
        }

        airplayConfirmBtn.setOnClickListener {
            logTagD(TAG, "同意接入airplay")
            eShareServerSDK.allowScreenCast(requestIp, 7)
            if (systemManager.needPerfConstraint(SystemManagerProxy.PerfConstraintScene.CAST)) {
                logTagW(TAG, "无线投屏 需要性能约束")
                NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                    put(getString(R.string.toast_pref_constraint))
                }
            } else {
                logTagV(TAG, "无线投屏 不需要性能约束")
            }
            checkOrFinish()
        }
    }

    private fun checkOrFinish() {
        if (airPlayClientList.isEmpty()) {
            logTagI(TAG, "已处理了所有AirPlay请求, 正常退出")
            finish()
            return
        }

        // 还有排队中的请求
        MainScope().launch {
            logTagD(TAG, "启动下一个AirPlay请求")
            finish()
            delay(ONE_SECOND)
            startActivity(
                Intent(
                    this@AirPlayRequestDialog,
                    AirPlayRequestDialog::class.java
                ).apply {
                    putExtra(KEY_CLIENT_LIST, airPlayClientList)
                })
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        eShareServerSDK.registerCallback(object : SimpleEShareCallback() {
            override fun cancelRequestAlert(
                clientIp: String?,
                clientName: String?,
                clientType: Int
            ) {
                super.cancelRequestAlert(clientIp, clientName, clientType)
                if (clientIp == currentIp) {
                    logTagD(TAG, "airPlay:${clientIp} 取消申请")
                    checkOrFinish()
                }
            }
        })

        launch {
            whenResumed {
                delay(REQ_TIME_OUT)
                logTagI(TAG, "超时自动拒绝")
                binding.airplayCancelBtn.performClick()
            }
        }

    }

    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            logTagD(TAG, "拦截返回键")
            binding.airplayCancelBtn.performClick() // 相当于点击了拒绝按钮
            return true
        }
        return super.onInterceptKeyDown(keyCode, event)
    }
}