package com.czur.starry.device.sharescreen.esharelib.net

import com.czur.starry.device.baselib.BuildConfig
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.sharescreen.esharelib.entity.GuideVideoEntity

/**
 * Created by 陈丰尧 on 2023/5/12
 */
interface IShareScreenService {
    @MiaoHttpGet("/api/common/videos")
    fun getGuideVideoEntity(
        @MiaoHttpParam("version") version: Int = BuildConfig.CZUR_APP_VERSION_CODE,
        type: Class<GuideVideoEntity> = GuideVideoEntity::class.java
    ): MiaoHttpEntity<GuideVideoEntity>
}