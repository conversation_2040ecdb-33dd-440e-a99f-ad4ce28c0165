package com.czur.starry.device.sharescreen.esharelib.entity

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.sharescreen.esharelib.R
import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 2023/5/12
 */
data class GuideVideoEntity(
    val ios: GuidVideoItem,
    val android: GuidVideoItem,
    val windows: GuidVideoItem,
    @SerializedName("clickdrop")
    val clickDrop: GuidVideoItem,
    val dlna: GuidVideoItem,
    val share: GuidVideoItem,
    val p2p: GuidVideoItem,
    val linux: GuidVideoItem
)

data class GuidVideoItem(
    val name: String,
    val url: String,
    @SerializedName("duration")
    val durationInSec: Int,
)

private const val VIDEO_URL_PATH = "/player/"

enum class NoNetworkGuidVideoItem(
    val nameRes: Int,
    val url: String,
    val textSize: Float
) {
    APPLE(
        R.string.title_guide_no_network_apple,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_APPLE}",
        16f
    ),
    ANDROID(
        R.string.title_guide_no_network_android,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_ANDROID}",
        16f
    ),
    WINDOWS(
        R.string.title_guide_no_network_windows,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_WINDOWS}",
        16f
    ),
    VIDEO_APP(
        R.string.title_guide_no_network_dlna,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_DLNA}",
        16f
    ),
    CZUR_SHARE(
        R.string.title_guide_no_network_czur_share,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_SHARE}",
        16f
    ),
    P2P(
        R.string.title_guide_no_network_p2p,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_P2P}",
        16f
    ),
    CLICK_DROP(
        R.string.title_guide_no_network_click_drop,
        "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_CLICK_DROP}",
        16f
    ),
    LINUX(
    R.string.title_guide_no_network_linux,
    "${Constants.BASE_URL_SHARE_V2}${VIDEO_URL_PATH}${Constants.VIDEO_ID_LINUX}",
    16f
    ),;
}

