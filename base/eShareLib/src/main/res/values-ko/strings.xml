<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">무선 화면 캐스팅 코드</string>
    <string name="str_use_guide">휴대전화나 컴퓨터에서 무선 화면 캐스팅하려면 어떻게 하나요?</string>
    <string name="str_ethernet_ssid">케이블 연결</string>
    <string name="str_info_ip">IP: %s</string>
    <string name="str_info_ssid_label">현재 네트워크</string>
    <string name="float_tip_close">닫기</string>
    <string name="str_guide_float_title">휴대전화나 컴퓨터에서 무선 화면 캐스팅하려면 어떻게 하나요?</string>
    <string name="str_guide_apple_1">휴대전화나 컴퓨터가 반드시 StarryHub와 같은 네트워크에 연결되어 있어야 합니다.</string>
    <string name="str_guide_apple_2">iPhone/iPad/mac(AirPlay를 지원)에서 화면 미러링 기능을 엽니다.</string>
    <string name="str_guide_apple_3">%s을(를) 선택해 화면 캐스팅을 시작합니다</string>
    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_guide_android_download">QR 코드를 스캔해 \"%s\" 앱을 다운로드합니다.</string>
    <string name="str_guide_android_1">휴대전화가 StarryHub와 같은 네트워크에 연결되어 있어야 합니다.</string>
    <string name="str_guide_pc_1">컴퓨터가 반드시 StarryHub와 같은 Wi-Fi 네트워크에 연결되어 있어야 합니다.</string>
    <string name="str_guide_pc_2">Windows 컴퓨터 키보드에서 WIN+K를 누른 다음 %s을(를) 선택해 화면 캐스팅을 시작합니다.</string>
    <string name="str_guide_pc_3">이 순서에 따라 진행했는데도 화면 캐스팅을 할 수 없거나 StarryHub를 찾을 수 없는 경우, 또는 컴퓨터가 Miracast를 지원하지 않는 경우 www.czur.com에서 %1$s(를) 다운로드한 후 %1$s에서 화면을 캐스팅하면 됩니다.</string>
    <string name="str_guide_recommend">★최우선 권장. </string>
    <string name="str_guide_click_drop_1">StarryHub 뒷면의 USB 포트에 ClickDrop을 삽입하고 페어링이 끝날 때까지 기다립니다.</string>
    <string name="str_guide_click_drop_2">ClickDrop을 컴퓨터에 연결하면 ClickDrop의 LED 표시등이 깜박입니다. 표시등이 켜진 상태로 깜박임이 멈출 때까지 기다립니다.</string>
    <string name="str_guide_click_drop_3">ClickDrop의 버튼을 눌러 화면 캐스팅을 시작합니다.</string>
    <string name="str_guide_click_drop_hint">플러그 &amp; 플레이. 인터넷 연결이 필요합니다.</string>
    <string name="btn_guide_click_drop_buy">구매</string>
    <string name="title_setting">설정</string>
    <string name="str_device_name">디스플레이 이름</string>
    <string name="item_setting_pin_code">암호</string>
    <string name="item_setting_multiple_display">여러 개의 화면을 동시에 캐스팅합니다</string>
    <string name="item_setting_smart_full_screen">전체 화면</string>
    <string name="item_setting_ask_before_casting">화면 캐스팅 확인</string>
    <string name="item_setting_airplay_visibility">AirPlay 감지됨</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">암호창</string>
    <string name="item_setting_device_name_float_window">StarryHub 이름 플로팅 창</string>
    <string name="setting_options_enable">켜기</string>
    <string name="setting_options_disable">끄기</string>
    <string name="title_miracast_hint">알림</string>
    <string name="str_cancel">취소</string>
    <string name="str_miracast_open">켜기</string>
    <string name="str_no_network_ssid">인터넷 연결 없음</string>
    <string name="str_qrcode_float_title">스캔해 시작</string>
    <string name="str_miracast_disable_hint">Miracast를 사용하려면 Wi-Fi를 켜야 합니다.</string>
    <string name="str_air_play_disable_hint">AirPlay를 사용하려면 인터넷 연결이 필요합니다.</string>
    <string name="str_dlna_disable_hint">DLNA를 사용하려면 인터넷 연결이 필요합니다.</string>
    <string name="str_airplay_request">방금 %s(으)로부터 화면 캐스팅 요청을 받았습니다. 수락할까요?</string>
    <string name="str_airplay_request_reject">아니오</string>
    <string name="str_airplay_request_agree">예</string>
    <string name="str_guide_category_apple">macOS &amp; iOS</string>
    <string name="str_guide_category_android">Android 전화기/태블릿</string>
    <string name="str_guide_category_windows">Windows OS 컴퓨터</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_title_apple">macOS &amp; iOS</string>
    <string name="str_guide_title_android">Android 전화기/태블릿</string>
    <string name="str_guide_title_windows">Windows OS 컴퓨터</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>
    <string name="str_guide_sub_title_harmony">전화기/태블릿</string>
    <string name="str_guide_item_click_drop_title">ClickDrop\n무선 화면 캐스팅 게이트웨이</string>
    <string name="str_qr_code_guide">CZUR Share 앱\n 캐스팅 QR 코드</string>
    <string name="str_qr_code_guide_overseas">CZUR 앱 무선 화면 \n 캐스팅 QR 코드</string>
    <string name="title_buy_click_drop">ClickDrop</string>
    <string name="str_device_count_hint">최대 4개까지 화면 캐스팅.</string>
    <string name="str_float_tips_device_count_hint">동시에 디바이스 최대 4대까지 무선 화면 캐스팅합니다.</string>
    <string name="str_device_name_illegal">유효하지 않은 화면 캐스팅 이름입니다. 설정에서 변경하세요.</string>
    <string name="str_title_eshare_screen_projection">화면 캐스팅할 앱을 다운로드합니다.</string>
    <string name="str_title_direct_screen_projection">화면을 직접 캐스팅합니다.</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">AirPlay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS / macOS</string>
    <string name="str_more_setting">설정 더 보기</string>
    <string name="str_qrcode_label">스캔해 다운로드하거나\n화면을 캐스트합니다</string>
    <string name="str_airplay_hint">AirPlay를 사용하려면 인터넷 연결이 필요합니다</string>
    <string name="str_ask_before_casting_hint">프로젝션 화면을 받기 전에 확인이 필요합니다.</string>
    <string name="str_guide_no_network">네트워크를 감지할 수 없습니다, 시청할 코드를 스캔하세요.</string>
    <string name="title_guide_no_network_apple">Apple OS용 무선 화면 캐스팅 튜토리얼</string>
    <string name="title_guide_no_network_windows">Windows OS용 무선 화면 캐스팅 튜토리얼</string>
    <string name="title_guide_no_network_click_drop">ClickDrop용 무선 화면 캐스팅 튜토리얼</string>
    <string name="title_guide_no_network_android">Android OS용 무선 스크린 캐스팅 튜토리얼</string>
    <string name="str_guide_android_2">휴대폰의 바로가기 메뉴를 열거나 설정으로 가서 \"스크린 캐스팅\" 옵션(예: 무선 스크린 캐스팅, 무선 디스플레이, 화면 공유, 화면 투영, 다중 화면 상호 작용 등)을 찾습니다. 무선 스크린 캐스팅 기능 활성화. </string>
    <string name="str_guide_android_3">【%S】를 선택하여 화면 전송을 시작합니다.</string>
    <string name="str_guide_android_4">위에 언급한 방법으로 무선 스크린 캐스팅을 실현할 수 없을 수도 있습니다. 사용자의 휴대폰이 Miracast 프로토콜을 지원하지 않을 수 있거나 호환성 문제로 인한 것이기 때문입니다. 위 방법 가운데 하나로 StarryHub를 찾을 수 없는 경우, www.czur.com에서 \'CZUR Share\' 앱을 다운로드하고 대신 \'CZUR Share\' 앱에서 화면을 전송합니다.</string>
    <string name="str_guide_linux_1">사용자 장치와 StarryHub가 동일한 Wi-Fi 네트워크에 있는지 확실히 확인합니다.</string>
    <string name="str_guide_linux_2">\'Google Chrome\' 브라우저를 열고 더 많은 옵션을 볼 수 있도록 브라우저 왼쪽 상단에 있는 \' ⋮ \' 버튼을 클릭한 다음 \'전송...\'을 선택하세요.</string>
    <string name="str_guide_linux_3">【%S】를 선택하여 화면 전송을 시작합니다.</string>
    <string name="str_guide_video_app_1">사용자 장치와 StarryHub가 동일한 Wi-Fi 네트워크에 있는지 확실히 확인합니다.</string>
    <string name="str_guide_video_app_2">비디오 앱의 [ICON] 버튼을 클릭합니다.</string>
    <string name="str_guide_video_app_3">【%s （  DLNA  ）】를 선택하여 비디오 화면 전송을 시작합니다.</string>
    <string name="str_guide_video_app_3_oversea">【%s 】를 선택하여 비디오 화면 전송을 시작합니다.</string>
    <string name="str_guide_czur_share_1">사용자 장치와 StarryHub가 동일한 Wi-Fi 네트워크에 있는지 확실히 확인합니다.</string>
    <string name="str_guide_czur_share_2">\'CZUR Share\' 앱을 열고 【%s】를 선택한 다음, \'연결\'을 클릭합니다.</string>
    <string name="str_guide_czur_share_3">[ICON]을 클릭하여 화면 전송을 시작합니다.</string>
    <string name="str_guide_czur_share_4">  www.czur.com에서 %1$s를 다운로드하거나 사용자 휴대폰\\의 앱스토어에서 “%1$s”를 검색 및 설치한 다음 화면을 전송합니다.</string>
    <string name="str_guide_czur_share_4_store">앱스토어</string>
    <string name="str_guide_p2p_1">사용자 휴대폰이나 컴퓨터를 StarryHub의 액세스 포인트(Wi-Fi 핫스팟명 %s, 비밀번호 변경자007)에 연결합니다.</string>
    <string name="str_guide_p2p_2">“휴대폰 지원 방법: Airplay, Miracast, CZUR Share, DLNA(로컬 파일만 해당), 무선 스크린 캐스팅 게이트웨이. 컴퓨터 지원 방법: Airplay, Miracast, CZUR Share, 무선 스크린 캐스팅 게이트웨이.”</string>
    <string name="str_guide_remark_label">참고</string>
    <string name="str_guide_remark_czur_share">www.czur.com에서 \'CZUR Share\' 앱을 다운로드하거나, 또는 스크린 캐스팅을 위해 \'CZUR Share\'를 사용할 수 있도록 앱 스토어에서 \'CZUR Share\' 앱을 검색합니다.</string>
    <string name="str_guide_remark_p2p">활성화 목적으로 최초 사용 시에 Wi-Fi 연결이 필요합니다.</string>
    <string name="str_eshare_quit_title">화면 전송을 중지하시겠습니까?</string>
    <string name="dialog_normal_cancel">아니요</string>
    <string name="dialog_normal_confirm">네</string>
    <string name="str_guide_category_linux">Linux 시스템</string>
    <string name="str_guide_category_video_app">비디오 앱</string>
    <string name="str_guide_category_p2p">P2P 스크린 캐스팅.</string>
    <string name="str_guide_title_linux">Linux 시스템(Chromecast)</string>
    <string name="str_guide_title_video_app">비디오 앱</string>
    <string name="str_guide_title_p2p">P2P 스크린 캐스팅.</string>
    <string name="str_buy_click_drop_hint">QR 코드를 스캔해서 구매합니다</string>
    <string name="str_float_tips_device_content_hint">DLNA는 제외됩니다.</string>
    <string name="str_p2p_hint">P2P 스크린 캐스팅이 더 안정적입니다.</string>
    <string name="str_p2p_hint_summary">사용자 컴퓨터/휴대폰을 다음의 Wi-Fi 핫스팟에 연결해 주십시오.</string>
    <string name="str_p2p_hint_pwd">비밀번호 %s</string>
    <string name="str_title_hdmi">HDMI 화면 공유</string>
    <string name="title_guide_no_network_linux">Linux OS용 무선 스크린 캐스팅 튜토리얼</string>
    <string name="title_guide_no_network_dlna">비디오 앱용 무선 스크린 캐스팅 튜토리얼</string>
    <string name="title_guide_no_network_czur_share">CZUR Share 앱용 무선 스크린 캐스팅 튜토리얼</string>
    <string name="title_guide_no_network_p2p">P2P용 무선 스크린 캐스팅 튜토리얼</string>
    <string name="str_float_tips_device_peripheral_mode_hint">주변장치 모드 및 화면 공유를 동시에 적용할 수 있습니다.</string>
    <string name="str_guide_video_app_2_mainland">화상 회의 앱의 TV 아이콘을 클릭합니다.</string>
    <string name="str_float_tips_device_content_hint_army">DLNA 제외</string>
</resources>
