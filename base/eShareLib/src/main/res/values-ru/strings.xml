<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">Код беспроводной трансляции экрана</string>
    <string name="str_use_guide">Как выполнить беспроводную трансляцию экрана с \nпомощью мобильного телефона или компьютера?</string>
    <string name="str_ethernet_ssid">Проводное подключение</string>
    <string name="str_info_ip">IP: %s</string>
    <string name="str_info_ssid_label">Текущая сеть</string>
    <string name="float_tip_close">Закрыть</string>
    <string name="str_guide_float_title">Как выполнить беспроводную трансляцию экрана с помощью мобильного телефона или компьютера?</string>
    <string name="str_guide_apple_1">Убедитесь, что мобильный телефон или компьютер находятся в той же сети, что и StarryHub.</string>
    <string name="str_guide_apple_2">Функция Open Screen Mirroring на iPhone/iPad/mac (также поддерживается AirPlay)</string>
    <string name="str_guide_apple_3">Выберите %s, чтобы начать трансляцию экрана.</string>
    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_guide_android_download">Отсканируйте QR-код, чтобы загрузить приложение «%s»</string>
    <string name="str_guide_android_1">Убедитесь, что телефон находится в той же сети, что и StarryHub.</string>
    <string name="str_guide_pc_1">Убедитесь, что компьютер подключен к той же сети Wi-Fi, что и StarryHub.</string>
    <string name="str_guide_pc_2">Нажмите WIN+K на клавиатуре компьютера Windows, выберите %s, начните трансляцию экрана.</string>
    <string name="str_guide_pc_3">Если после этой операции вы не можете транслировать экран, не можете найти StarryHub или ваш компьютер не поддерживает Miracast, вы можете загрузить %1$s с сайта www.czur.com и транслировать экран с %1$s.</string>
    <string name="str_guide_recommend">★ Наиболее рекомендуемые. </string>
    <string name="str_guide_android_2">Откройте контекстное меню телефона или перейдите в настройки, чтобы найти опцию «Трансляция экрана» (например, беспроводная трансляция экрана, беспроводной дисплей, совместное использование экрана, проекция экрана, взаимодействие с несколькими экранами и т. д.). Включите функцию беспроводной трансляции экрана. </string>
    <string name="str_guide_android_3">Выберите 【%s】, чтобы начать трансляцию экрана.</string>
    <string name="str_guide_android_4">Возможно, что вышеупомянутые методы не позволяют выполнять беспроводную трансляцию экрана. Ваш телефон может не поддерживать протокол Miracast или есть проблемы с совместимостью. Если вы не можете найти StarryHub ни одним из вышеперечисленных способов, загрузите приложение «CZUR Share» с сайта www.czur.com и вместо этого транслируйте экран из приложения «CZUR Share».</string>
    <string name="str_guide_linux_1">Убедитесь, что ваше устройство и StarryHub находятся в одной Wi-Fi-сети.</string>
    <string name="str_guide_linux_2">Откройте браузер Google Chrome, нажмите кнопку \’⋮\’ в верхнем левом углу браузера, чтобы открыть дополнительные параметры, затем выберите \’Cast...\’.</string>
    <string name="str_guide_linux_3">Выберите 【%s】, чтобы начать трансляцию экрана.</string>
    <string name="str_guide_video_app_1">Убедитесь, что ваше устройство и StarryHub находятся в одной Wi-Fi-сети.</string>
    <string name="str_guide_video_app_2">Нажмите кнопку [ICON] видеоприложения.</string>
    <string name="str_guide_video_app_3">Выберите 【%s（DLNA）】, чтобы начать трансляцию видео с экрана.</string>
    <string name="str_guide_video_app_3_oversea">Выберите 【%s】, чтобы начать трансляцию видео с экрана.</string>
    <string name="str_guide_czur_share_1">Убедитесь, что ваше устройство и StarryHub находятся в одной Wi-Fi-сети.</string>
    <string name="str_guide_czur_share_2">Откройте приложение \’CZUR Share\’, выберите 【%s】, нажмите \'Подключиться\'.</string>
    <string name="str_guide_czur_share_3">Нажмите [ЗНАЧОК], чтобы начать трансляцию экрана.</string>
    <string name="str_guide_czur_share_4">  Вы можете загрузить %1$s с сайта www.czur.com или найти и установить «%1$s» из App Store на телефоне, а затем транслировать экран.</string>
    <string name="str_guide_czur_share_4_store">APP Store</string>
    <string name="str_guide_p2p_1">Подключите свой мобильный телефон или компьютер к точке доступа StarryHub (имя точки доступа Wi-Fi %s, смена пароля007).</string>
    <string name="str_guide_p2p_2">«Методы, поддерживаемые мобильным телефоном: Airplay, Miracast, CZUR Share, DLNA (только локальные файлы), шлюз для беспроводной трансляции экрана. Методы, поддерживаемые компьютером: Airplay, Miracast, CZUR Share, шлюз для беспроводной трансляции экрана».</string>
    <string name="str_guide_remark_label">Примечание</string>
    <string name="str_guide_remark_czur_share">Вы можете загрузить приложение \’CZUR Share\’ с сайта www.czur.com или выполнить поиск приложения \'CZUR Share\’ в своем магазине приложений, чтобы использовать \'CZUR Share\’ для трансляции экрана.</string>
    <string name="str_guide_remark_p2p">Для активации при первом использовании требуется подключение к Wi-Fi-сети.</string>
    <string name="str_eshare_quit_title">Прекратить трансляцию экрана?</string>
    <string name="dialog_normal_cancel">Нет</string>
    <string name="dialog_normal_confirm">Да</string>
    <string name="str_guide_category_linux">Система Linux</string>
    <string name="str_guide_category_video_app">Видеоприложение</string>
    <string name="str_guide_category_p2p">P2P-трансляция экрана.</string>
    <string name="str_guide_title_linux">Система Linux (Chromecast)</string>
    <string name="str_guide_title_video_app">Видеоприложение</string>
    <string name="str_guide_title_p2p">P2P-трансляция экрана.</string>
    <string name="str_buy_click_drop_hint">Сканируйте QR-код для покупки</string>
    <string name="str_float_tips_device_content_hint">Кроме DLNA</string>
    <string name="str_p2p_hint">P2P-трансляция экрана более стабильна.</string>
    <string name="str_p2p_hint_summary">Пожалуйста, подключите свой компьютер/телефон к следующей точке доступа Wi-Fi.</string>
    <string name="str_p2p_hint_pwd">Пароль %s</string>
    <string name="str_title_hdmi">Трансляция экрана через HDMI</string>
    <string name="title_guide_no_network_android">Учебное пособие по беспроводной трансляции экрана для ОС Android</string>
    <string name="title_guide_no_network_linux">Учебное пособие по беспроводной трансляции экрана для ОС Linux</string>
    <string name="title_guide_no_network_dlna">Учебное пособие по беспроводной трансляции экрана для видеоприложений</string>
    <string name="title_guide_no_network_czur_share">Учебное пособие по беспроводной трансляции экрана для приложения CZUR Share</string>
    <string name="title_guide_no_network_p2p">Учебное пособие по беспроводной трансляции экрана для P2P</string>
    <string name="str_float_tips_device_peripheral_mode_hint">Режим периферии и общий доступ к экрану можно применять одновременно.</string>
    <string name="str_guide_video_app_2_mainland">Нажмите значок телевизора в приложении для видеоконференций.</string>
    <string name="str_guide_click_drop_1">Вставьте ClickDrop в порт USB на задней панели StarryHub, дождитесь завершения процесса сопряжения.</string>
    <string name="str_guide_click_drop_2">Подключите ClickDrop к компьютеру, светодиодный индикатор начнет мигать, дождитесь, когда индикатор начнет гореть непрерывно.</string>
    <string name="str_guide_click_drop_3">Чтобы начать трансляцию экрана, нажмите кнопку на ClickDrop.</string>
    <string name="str_guide_click_drop_hint">Plug &amp; play. Соединение с Интернетом не требуется.</string>
    <string name="btn_guide_click_drop_buy">Покупка</string>
    <string name="title_setting">Настройки</string>
    <string name="str_device_name">Имя дисплея</string>
    <string name="item_setting_pin_code">Пароль</string>
    <string name="item_setting_multiple_display">Одновременная трансляция\nнескольких экранов</string>
    <string name="item_setting_smart_full_screen">Полноэкранный </string>
    <string name="item_setting_ask_before_casting">Подтверждение трансляции экрана</string>
    <string name="item_setting_airplay_visibility">AirPlay видимый</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">Окно пароля</string>
    <string name="item_setting_device_name_float_window">Плавающее окно имени StarryHub</string>
    <string name="setting_options_enable">Вкл.</string>
    <string name="setting_options_disable">Откл.</string>
    <string name="title_miracast_hint">Уведомление</string>
    <string name="str_cancel">Отмена</string>
    <string name="str_miracast_open">Вкл.</string>
    <string name="str_no_network_ssid">Нет подключения к Интернету</string>
    <string name="str_qrcode_float_title">Чтобы начать, выполните сканирование</string>
    <string name="str_miracast_disable_hint">Чтобы использовать Miracast нужно включить Wi-Fi.</string>
    <string name="str_air_play_disable_hint">Для использования AirPlay требуется соединение с Интернетом</string>
    <string name="str_dlna_disable_hint">Для использования DLNA требуется соединение с Интернетом</string>
    <string name="str_airplay_request">Вы получили запрос трансляции экрана от %s. Согласиться?</string>
    <string name="str_airplay_request_reject">Нет</string>
    <string name="str_airplay_request_agree">Да</string>
    <string name="str_guide_category_apple">macOS и iOS</string>
    <string name="str_guide_category_android">Телефон/планшет Android</string>
    <string name="str_guide_category_windows">Компьютер на ОС Windows</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_title_apple">macOS и iOS</string>
    <string name="str_guide_title_android">Телефон/планшет Android</string>
    <string name="str_guide_title_windows">Компьютер на ОС Windows</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>
    <string name="str_guide_sub_title_harmony">телефон / планшет</string>
    <string name="str_guide_item_click_drop_title">ClickDrop\nШлюз беспроводной трансляции экрана</string>
    <string name="str_qr_code_guide">Приложение CZUR Share\n QR-код трансляции</string>
    <string name="str_qr_code_guide_overseas">Беспроводная трансляция экрана CZUR\n QR-код трансляции</string>
    <string name="title_buy_click_drop">ClickDrop</string>
    <string name="str_device_count_hint">Макс. 4 транслируемых экрана.</string>
    <string name="str_float_tips_device_count_hint">Возможна одновременная беспроводная трансляция до четырех экранов.</string>
    <string name="str_device_name_illegal">Это недопустимое имя трансляции экрана. Измените его в настройках.</string>
    <string name="str_title_eshare_screen_projection">Скачайте приложение для трансляции экрана.</string>
    <string name="str_title_direct_screen_projection">Прямая трансляция экрана.</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">Airplay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS / macOS</string>
    <string name="str_more_setting">Дополнительные настройки</string>
    <string name="str_qrcode_label">Отсканируйте для скачивания\nили трансляции экрана</string>
    <string name="str_airplay_hint">Для использования AirPlay требуется соединение с Интернетом</string>
    <string name="str_ask_before_casting_hint">Для приема трансляции экранов требуется подтверждение.</string>
    <string name="str_guide_no_network">Сеть не обнаружена, для просмотра отсканируйте код.</string>
    <string name="title_guide_no_network_apple">Учебное пособие по беспроводной трансляции экрана для ОС Apple</string>
    <string name="title_guide_no_network_windows">Учебное пособие по беспроводной трансляции экрана для ОС Windows</string>
    <string name="title_guide_no_network_click_drop">Учебное пособие по беспроводной трансляции экрана для ClickDrop</string>
    <string name="str_float_tips_device_content_hint_army">За исключением DLNA</string>
</resources>
