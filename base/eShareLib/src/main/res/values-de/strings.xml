<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">Wi-Fi-Screencastingcode</string>
    <string name="str_use_guide">So nutzen Sie Wi-Fi-Screencasting von \neinem Smartphone oder Computer aus</string>
    <string name="str_ethernet_ssid">Kabelverbindung</string>
    <string name="str_info_ip">IP: %s</string>
    <string name="str_info_ssid_label">Aktuelles Netzwerk</string>
    <string name="float_tip_close">Schließen</string>
    <string name="str_guide_float_title">So nutzen Sie Wi-Fi-Screencasting von einem Smartphone oder Computer aus</string>
    <string name="str_guide_apple_1"><PERSON><PERSON><PERSON>e <PERSON>, dass das Smartphone bzw. der Computer in demselben Netzwerk sind, wie der StarryHub.</string>
    <string name="str_guide_apple_2">Öffnen Sie die Funktion zur Bildschirmspiegelung auf dem iPhone/iPad/mac (unterstützt auch AirPlay)</string>
    <string name="str_guide_apple_3">%s wählen, um Screencasting zu starten</string>
    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_guide_android_download">Zum Herunterladen der %s-App QR-Code scannen</string>
    <string name="str_guide_android_1">Stellen Sie sicher, dass das Smartphone in demselben Netzwerk ist, wie der StarryHub.</string>
    <string name="str_guide_pc_1">Stellen Sie sicher, dass der Computer in demselben WLAN ist, wie der StarryHub.</string>
    <string name="str_guide_pc_2">Drücken Sie unter Windows die Tastenkombination WIN+K und wählen Sie %s, um das Screencasting zu starten.</string>
    <string name="str_guide_pc_3">Wenn sich das Screencasting nicht auf diese Weise starten lässt, Sie den StarryHub nicht finden können oder der Computer Miracast nicht unterstützt, laden Sie %1$s von www.czur.com und Screencasting von %1$s herunter.</string>
    <string name="str_guide_recommend">★ Empfohlen. </string>
    <string name="str_guide_click_drop_1">Legen Sie ClickDrop in den USB-Anschluss auf der Rückseite des StarryHub ein und warten Sie bis das Pairing abgeschlossen ist.</string>
    <string name="str_guide_click_drop_2">Verbinden Sie ClickDrop mit dem Compute und die ClickDrop-LED beginnt zu blinken, Warten Sie, bis die LED konstant leuchtet.</string>
    <string name="str_guide_click_drop_3">Drücken Sie die Taste vorne am ClickDrop, um Screencasting zu starten</string>
    <string name="str_guide_click_drop_hint">Plug&amp;Play. keine Internetverbindung nötig.</string>
    <string name="btn_guide_click_drop_buy">Kaufen</string>
    <string name="title_setting">Einstellungen</string>
    <string name="str_device_name">Anzeigename</string>
    <string name="item_setting_pin_code">Kennwort</string>
    <string name="item_setting_multiple_display">Gleichzeitiges Casting mehrerer \nBildschirme</string>
    <string name="item_setting_smart_full_screen">Vollbildschirm </string>
    <string name="item_setting_ask_before_casting">Screencasting bestätigen</string>
    <string name="item_setting_airplay_visibility">AirPlay sichtbar</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">Kennwort-Fenster</string>
    <string name="item_setting_device_name_float_window">Schwebendes Fenster des \nStarryHub-Namens</string>
    <string name="setting_options_enable">Aktivieren</string>
    <string name="setting_options_disable">Deaktivieren</string>
    <string name="title_miracast_hint">Benachrichtigung</string>
    <string name="str_cancel">Abbrechen</string>
    <string name="str_miracast_open">Aktivieren</string>
    <string name="str_no_network_ssid">Keine Internetverbindung</string>
    <string name="str_qrcode_float_title">Zum Starten scannen</string>
    <string name="str_miracast_disable_hint">Für Miracast muss Wi-Fi eingeschaltet sein.</string>
    <string name="str_air_play_disable_hint">Für AirPlay wird eine Internetverbindung benötigt.</string>
    <string name="str_dlna_disable_hint">Für DLNA wird eine Internetverbindung benötigt.</string>
    <string name="str_airplay_request">Sie haben eine Screencasting-Anfrage von %s erhalten. Möchten Sie sie annehmen?</string>
    <string name="str_airplay_request_reject">Nein</string>
    <string name="str_airplay_request_agree">Ja</string>
    <string name="str_guide_category_apple">macOS &amp; iOS</string>
    <string name="str_guide_category_android">Android Smartphone/Tablet</string>
    <string name="str_guide_category_windows">Windows OS Computer</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_title_apple">macOS &amp; iOS</string>
    <string name="str_guide_title_android">Android Smartphone/Tablet</string>
    <string name="str_guide_title_windows">Windows OS Computer</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>
    <string name="str_guide_sub_title_harmony">Smartphone/Tablet</string>
    <string name="str_guide_item_click_drop_title">ClickDrop\nWi-Fi-Screencasting-Gateway</string>
    <string name="str_qr_code_guide">CZUR Share-App\n Casting-QR-Code</string>
    <string name="str_qr_code_guide_overseas">CZUR App Wi-Fi-\nScreencasting-QR-Code</string>
    <string name="title_buy_click_drop">ClickDrop</string>
    <string name="str_device_count_hint">Gleichzeitiges Casting von bis zu vier Bildschirmen</string>
    <string name="str_float_tips_device_count_hint">Wi-Fi-Screencasting – Gleichzeitiges Casting von für bis zu vier Geräten</string>
    <string name="str_device_name_illegal">Dieser Screencastingname oder ist ungültig. Ändern Sie die Einstellungen,</string>
    <string name="str_title_eshare_screen_projection">App für Screencasting herunterladen</string>
    <string name="str_title_direct_screen_projection">Direktes Screencasting.</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">AirPlay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS/macOS</string>
    <string name="str_more_setting">Weitere Einstellungen</string>
    <string name="str_qrcode_label">Zum herunterladen oder \nfür Screencasting scannen</string>
    <string name="str_airplay_hint">Für AirPlay wird eine Internetverbindung benötigt.</string>
    <string name="str_ask_before_casting_hint">Vor der Annahme von Bildschirmen Erlaubnis einholen.</string>
    <string name="str_guide_no_network">Kein netzwerk erkannt. Zum Ansehen Code scannen.</string>
    <string name="title_guide_no_network_apple">Wi-Fi-Screencasting-Tutorial für Apple</string>
    <string name="title_guide_no_network_windows">Wi-Fi-Screencasting-Tutorial für Windows</string>
    <string name="title_guide_no_network_click_drop">Wi-Fi-Screencasting-Tutorial für ClickDrop</string>
    <string name="str_guide_android_2">Gehen Sie über das Schnellzugriffsmenü oder in den Einstellungen Ihres Smartphones zu der Option zum Screencasting (die Funktionen heißen beispielsweise Wireless-Screen Mirroring, Wireless Display, Bildschirmfreigabe, Bildschirmprojektion, Multiscreen Interaction usw.). Aktivieren Sie auf dem Smartphone diese Option zum Wireless-Screencasting. </string>
    <string name="str_guide_android_3">Wählen Sie 【%s】, um mit der Bildschirmübertragung zu beginnen.</string>
    <string name="str_guide_android_4">Möglicherweise lässt sich unter den genannten Optionen das Wireless-Screencasting in dieser Konfiguration nicht realisieren. Mögliche Ursachen sind, dass Ihr Smartphone das Miracast-Protokoll nicht unterstützt, oder dass ein Kompatibilitätsproblem vorliegt. Wenn Sie mit den angegebenen Verfahren den StarryHub nicht finden können, laden Sie die CZUR Share-App von www.czur.com herunter und teilen Sie den Bildschirm stattdessen über die CZUR Share-App.</string>
    <string name="str_guide_linux_1">Stellen Sie sicher, dass sich Ihr Gerät in demselben WLAN befindet wie der StarryHub.</string>
    <string name="str_guide_linux_2">Öffnen Sie den Browser Google Chrome, klicken Sie in der linken oberen Ecke des Browserfensters auf „ ⋮ “, um weitere Optionen anzuzeigen, und wählen Sie dann die Option „Streamen“.</string>
    <string name="str_guide_linux_3">Wählen Sie 【%s】, um mit der Bildschirmübertragung zu beginnen.</string>
    <string name="str_guide_video_app_1">Stellen Sie sicher, dass sich Ihr Gerät in demselben WLAN befindet wie der StarryHub.</string>
    <string name="str_guide_video_app_2">Klicken Sie auf die Schaltfläche [ICON] in Ihrer Video-App.</string>
    <string name="str_guide_video_app_3">Wählen Sie 【%s（DLNA）】, um den Bildschirm als Video zu übertragen.</string>
    <string name="str_guide_video_app_3_oversea">Wählen Sie 【%s】, um den Bildschirm als Video zu übertragen.</string>
    <string name="str_guide_czur_share_1">Stellen Sie sicher, dass sich Ihr Gerät in demselben WLAN befindet wie der StarryHub.</string>
    <string name="str_guide_czur_share_2">Öffnen Sie die CZUR Share-App, wählen Sie 【%s】 und klicken Sie dann auf „Verbinden“.</string>
    <string name="str_guide_czur_share_3">Wählen Sie [ICON], um mit der Bildschirmübertragung zu beginnen.</string>
    <string name="str_guide_czur_share_4">  Sie können %1$s über unsere Website www.czur.com herunterladen. Sie können %1$s auch im App Store Ihres Smartphones suchen und installieren. Anschließend können Sie Ihren Bildschirm übertragen.</string>
    <string name="str_guide_czur_share_4_store">App Store</string>
    <string name="str_guide_p2p_1">Verbinden Sie Ihr Smartphone oder Ihren Computer mit dem Access Point, mit dem auch der StarryHub verbunden ist (Wi-Fi-Hotspotname %s, Kennwort changer007).</string>
    <string name="str_guide_p2p_2">"Für Smartphones unterstützte Methoden: Airplay, Miracast, CZUR Share, DLNA (nur lokale Dateien), Wireless-Screencasting-Gateway. Für Computer unterstützte Methoden: Airplay, Miracast, CZUR Share, Wireless-Screencasting-Gateway."</string>
    <string name="str_guide_remark_label">Hinweise</string>
    <string name="str_guide_remark_czur_share">Sie können die CZUR Share-App über unsere Website www.czur.com herunterladen. Sie können die CZUR Share-App auch im App Store Ihres Smartphones suchen und installieren. Anschließend können Sie Ihren Bildschirm übertragen.</string>
    <string name="str_guide_remark_p2p">Für die Aktivierung ist bei der ersten Nutzung dieser Funktion eine Wi-Fi-Verbindung erforderlich.</string>
    <string name="str_eshare_quit_title">Möchten Sie das Screencasting deaktivieren?</string>
    <string name="dialog_normal_cancel">Nein</string>
    <string name="dialog_normal_confirm">Ja</string>
    <string name="str_guide_category_linux">Linux-System</string>
    <string name="str_guide_category_video_app">Video-App</string>
    <string name="str_guide_category_p2p">P2P Screen Share.</string>
    <string name="str_guide_title_linux">Linux System (Chromecast)</string>
    <string name="str_guide_title_video_app">Video-App</string>
    <string name="str_guide_title_p2p">P2P Screen Share.</string>
    <string name="str_buy_click_drop_hint">Zum Kaufen QR-Code scannen</string>
    <string name="str_float_tips_device_content_hint">Außer DLNA</string>
    <string name="str_p2p_hint">P2P Screen Share ist stabiler.</string>
    <string name="str_p2p_hint_summary">Bitte verbinden Sie Ihren Computer bzw. Ihr Smartphone mit dem folgenden Wi-Fi-Hotspot.</string>
    <string name="str_p2p_hint_pwd">Kennwort %s</string>
    <string name="str_title_hdmi">HDMI Screen Sharing</string>
    <string name="title_guide_no_network_android">Wireless-Screencasting-Tutorial für Android OS</string>
    <string name="title_guide_no_network_linux">Wireless-Screencasting-Tutorial für Linux OS</string>
    <string name="title_guide_no_network_dlna">Wireless-Screencasting-Tutorial für Video-Apps</string>
    <string name="title_guide_no_network_czur_share">Wireless-Screencasting-Tutorial für die CZUR Share-App</string>
    <string name="title_guide_no_network_p2p">Wireless-Screencasting-Tutorial für P2P</string>
    <string name="str_float_tips_device_peripheral_mode_hint">Der Peripheriebetrieb und die Bildschirmfreigabe können gleichzeitig aktiviert sein.</string>
    <string name="str_guide_video_app_2_mainland">Klicken Sie in der Videomeeting-App auf das TV-Symbol.</string>
    <string name="str_float_tips_device_content_hint_army">Außer DLNA</string>
</resources>
