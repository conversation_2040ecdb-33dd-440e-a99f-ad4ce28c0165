<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="DialogActivity" parent="Theme.AppCompat.Light.Dialog">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <!--    不加这个,dialog在锁屏下有几率弹不出    -->
        <item name="android:windowMinWidthMajor">10%</item>
        <item name="android:windowMinWidthMinor">10%</item>
        <!--    不加这个,dialog在锁屏下有几率弹不出    -->
    </style>

</resources>