<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">ワイヤレス画面キャストコード</string>
    <string name="str_use_guide">携帯電話またはコンピューターから\nどうやってワイヤレス画面キャストできますか？</string>
    <string name="str_ethernet_ssid">有線ネットワーク</string>
    <string name="str_info_ip">IP：%s</string>
    <string name="str_info_ssid_label">現在のネットワーク</string>
    <string name="float_tip_close">閉じる</string>
    <string name="str_guide_float_title">携帯電話またはコンピューターからどうやってワイヤレス画面キャストできますか？</string>
    <string name="str_guide_apple_1">StarryHubと同じWi-Fi環境にあることを確認します。</string>
    <string name="str_guide_apple_2">iPhone/iPad/mac（AirPlayもサポートします）で画面のミラーリング機能を開きます</string>
    <string name="str_guide_apple_3">%sを閉じてから画面キャストを開始してください</string>
    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_guide_android_download">QRコードをスキャンして\"%s\」アプリをダウンロード</string>
    <string name="str_guide_android_1">StarryHubと同じWi-Fi環境にあることを確認します。</string>
    <string name="str_guide_android_2">携帯電話のショートカットメニューを開くか、設定へ進んで「画面キャスト」オプションを見つけてください（例：ワイヤレス画面キャスト、ワイヤレスディスプレイ、画面共有、画面投影、マルチスクリーンインタラクションなど）。ワイヤレス画面キャスト機能で有効にします。 </string>
    <string name="str_guide_android_3">【%s】を選択して画面のキャストを開始します。</string>
    <string name="str_guide_pc_1">StarryHubと同じWi-Fi環境にあることを確認します。</string>
    <string name="str_guide_pc_2">WindowsコンピューターのキーボードでWIN+Kを押し、%sを選択して画面キャストを開始します。</string>
    <string name="str_guide_pc_3">これらの操作を行っても画面をキャストできなかったりStarryHubが見つからない場合、またはコンピューターがMiracastをサポートしない場合は、www.czur.comから%1$sをダウンロードして%1$sから画面をキャストできます。</string>
    <string name="str_guide_recommend">★ 最もおすすめ。 </string>
    <string name="str_guide_linux_1">デバイスとStarryHubが同じWi-Fiネットワークに存在していることを確かめてください。</string>
    <string name="str_guide_linux_2">「Google Chrome」ブラウザーを開き、左上で「 ⋮ 」ボタンをクリックして詳細なオプションを表示し、「キャスト」を選択します。</string>
    <string name="str_guide_linux_3">【%s】を選択して画面のキャストを開始します。</string>
    <string name="str_guide_video_app_1">デバイスとStarryHubが同じWi-Fiネットワークに存在していることを確かめてください。</string>
    <string name="str_guide_video_app_2">ビデオアプリの[ICON]ボタンをクリックします。</string>
    <string name="str_guide_video_app_3">【%s（DLNA）】を選択してビデオ画面のキャストを開始します。</string>
    <string name="str_guide_czur_share_1">デバイスとStarryHubが同じWi-Fiネットワークに存在していることを確かめてください。</string>
    <string name="str_guide_czur_share_2">「CZUR Share」アプリを開き、【%s】を選択し、「接続」をクリックします。</string>
    <string name="str_guide_czur_share_3">[ICON] をクリックして画面のキャストを開始します。</string>
    <string name="str_guide_czur_share_4">  www.czur.comから%1$sをダウンロードするか、携帯電話のApp Storeで「%1$s」を検索してインストールしてから、画面をキャストします。</string>
    <string name="str_guide_czur_share_4_store">APP Store</string>
    <string name="str_guide_p2p_1">携帯電話やコンピューターをStarryHubのアクセスポイント（Wi-Fiホットスポット名 %s、パスワード changer007）に接続します。</string>
    <string name="str_guide_p2p_2">携帯電話がサポートする方法：Airplay、Miracast、CZUR Share、DLNA（ローカルファイルのみ）、ワイヤレス画面キャストゲートウェイ。コンピューターがサポートする方法：Airplay、Miracast、CZUR Share、ワイヤレス画面キャストゲートウェイ。</string>
    <string name="str_guide_remark_label">注</string>
    <string name="str_guide_remark_czur_share">画面キャストで「CZUR Share」を使用する場合は、www.czur.comで「CZUR Share」アプリをダウンロードするか、APP Storeで「CZUR Share」アプリを検索できます。</string>
    <string name="str_guide_remark_p2p">初めて使用する場合は、アクティベーション用にWi-Fi接続が必要です。</string>
    <string name="str_guide_click_drop_1">ClickDropをStarryHubの背面にあるUSBポートへ挿入し、ペアリング完了を待ちます。</string>
    <string name="str_guide_click_drop_2">ClickDropをコンピューターへ接続します。ClickDropのLEDインジケーターが点滅します。点滅が点灯に変わるまでお待ちください。</string>
    <string name="str_guide_click_drop_3">ClickDropのボタンを押して画面キャストを開始します。</string>
    <string name="str_guide_click_drop_hint">プラグアンドプレイ。インターネット接続は不要です。</string>
    <string name="btn_guide_click_drop_buy">購入</string>
    <string name="title_setting">設定</string>
    <string name="str_device_name">デバイス名</string>
    <string name="item_setting_pin_code">パスワード</string>
    <string name="item_setting_multiple_display">複数の画面を同時にキャスト</string>
    <string name="item_setting_smart_full_screen">フルスクリーン </string>
    <string name="item_setting_ask_before_casting">画面キャストの確認</string>
    <string name="item_setting_airplay_visibility">AirPlay表示</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">Windowパスワード</string>
    <string name="item_setting_device_name_float_window">StarryHub名のフローティングウィンドウ</string>
    <string name="setting_options_enable">有効にする</string>
    <string name="setting_options_disable">無効にする</string>
    <string name="title_miracast_hint">通知</string>
    <string name="str_cancel">キャンセル</string>
    <string name="str_miracast_open">有効にする</string>
    <string name="str_no_network_ssid">インターネット接続が存在しません</string>
    <string name="str_qrcode_float_title">スキャンして開始</string>
    <string name="str_miracast_disable_hint">Miracastを使用するにはWi-Fiをオンにする必要があります。</string>
    <string name="str_air_play_disable_hint">AirPlayを使用するにはインターネット接続が必要です</string>
    <string name="str_dlna_disable_hint">DLNAを使用するにはインターネット接続が必要です</string>
    <string name="str_airplay_request">%sから画面キャストのリクエストを受けました。同意しますか？</string>
    <string name="str_airplay_request_reject">いいえ</string>
    <string name="str_airplay_request_agree">はい</string>
    <string name="str_guide_category_apple">macOS &amp; iOS</string>
    <string name="str_guide_category_android">Androidフォン/タブレット</string>
    <string name="str_guide_category_windows">Windows OSコンピューター</string>
    <string name="str_guide_category_linux">Linuxシステム</string>
    <string name="str_guide_category_video_app">ビデオアプリ</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>
    <string name="str_guide_category_p2p">P2P画面キャスト</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_title_apple">macOS &amp; iOS</string>
    <string name="str_guide_title_android">Androidフォン/タブレット</string>
    <string name="str_guide_title_windows">Windows OSコンピューター</string>
    <string name="str_guide_title_linux">Linuxシステム（Chromecast）</string>
    <string name="str_guide_title_video_app">ビデオアプリ</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_p2p">P2P画面キャスト</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>
    <string name="str_guide_sub_title_harmony">電話/タブレット</string>
    <string name="str_guide_item_click_drop_title">ClickDrop\nワイヤレス画面キャストのゲートウェイ</string>
    <string name="str_guide_item_click_drop_title_slogan">プラグアンドドロップ</string>
    <string name="str_qr_code_guide">CZUR Shareアプリ\nキャスト用QRコード</string>
    <string name="str_qr_code_guide_overseas">CZURアプリワイヤレス画面\nキャスト用QRコード</string>
    <string name="title_buy_click_drop">ClickDrop</string>
    <string name="str_buy_click_drop_hint">QRコードをスキャンして\n購入してください</string>
    <string name="str_device_count_hint">キャストは最大4つの画面までです。</string>
    <string name="str_float_tips_device_count_hint">ワイヤレス画面キャストは同時に4デバイスまでです。</string>
    <string name="str_float_tips_device_content_hint">DLNA例外です</string>
    <string name="str_p2p_hint">P2P画面キャストはより安定しています。</string>
    <string name="str_p2p_hint_summary">以下のWi-Fiホットスポットへコンピューター/携帯電話を接続してください。</string>
    <string name="str_p2p_hint_pwd">パスワード %s</string>
    <string name="str_device_name_illegal">この画面キャスト名は無効です。設定で変更してください。</string>
    <string name="str_title_eshare_screen_projection">画面キャストのアプリをダウンロードしてください。</string>
    <string name="str_title_direct_screen_projection">直接画面をキャストします。</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">AirPlay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS / macOS</string>
    <string name="str_more_setting">その他の設定</string>
    <string name="str_qrcode_label">スキャンしてダウンロード\nするか画面をキャストします</string>
    <string name="str_airplay_hint">AirPlayを使用するにはインターネット接続が必要です</string>
    <string name="str_ask_before_casting_hint">投影画面を受信するには確認が必要です。</string>
    <string name="str_guide_no_network">ネットワークが検出されません。コードをスキャンして視聴してください。</string>
    <string name="title_guide_no_network_apple">Apple OSのワイヤレス画面キャストのチュートリアル</string>
    <string name="title_guide_no_network_android">Android OSのワイヤレス画面キャストのチュートリアル</string>
    <string name="title_guide_no_network_windows">Windows OSのワイヤレス画面キャストのチュートリアル</string>
    <string name="title_guide_no_network_click_drop">ClickDropのワイヤレス画面キャストのチュートリアル</string>
    <string name="title_guide_no_network_dlna">ビデオアプリのワイヤレス画面キャストのチュートリアル</string>
    <string name="title_guide_no_network_czur_share">CZUR Shareアプリのワイヤレス画面キャストのチュートリアル</string>
    <string name="title_guide_no_network_p2p">P2Pのワイヤレス画面キャストのチュートリアル</string>
    <string name="str_guide_android_4">上記の方法ではワイヤレス画面キャストを実現できない可能性があります。携帯電話はMiracastプロトコルをサポートしないか、互換性に問題がある可能性があります。上記の方法のいずれでもStarryHubが見つからない場合は、www.czur.comから「CZUR Share」アプリをダウンロードして、「CZUR Share」アプリから画面をキャストしてください。</string>
    <string name="str_guide_video_app_3_oversea">【%s】を選択してビデオ画面のキャストを開始します。</string>
    <string name="str_eshare_quit_title">画面キャストを停止しますか？</string>
    <string name="dialog_normal_cancel">いいえ</string>
    <string name="dialog_normal_confirm">はい</string>
    <string name="str_title_hdmi">HDMI画面共有</string>
    <string name="title_guide_no_network_linux">Linux OSのワイヤレス画面キャストのチュートリアル</string>
    <string name="str_float_tips_device_peripheral_mode_hint">周辺機器モードと画面キャストは同時に適用できます。</string>
    <string name="str_guide_video_app_2_mainland">ビデオ会議アプリのTVアイコンをクリックします。</string>
    <string name="str_float_tips_device_content_hint_army">DLNA以外</string>
</resources>
