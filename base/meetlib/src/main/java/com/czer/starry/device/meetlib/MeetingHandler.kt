package com.czer.starry.device.meetlib

import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.handler.SPContentHandler

/**
 * Created by 陈丰尧 on 2021/8/4
 */

object MeetingHandler : SPContentHandler() {


    const val KEY_LOCAL_MEETING_RECORDING = "localMeetingRecording"
    const val KEY_LOCAL_MEETING_VIDEO_RECORDING = "localMeetingVideoRecording"
    const val KEY_LOCAL_MEETING_RECORD_SHARE = "localMeetingRecordShare"
    const val KEY_LOCAL_MEETING_SCREEN_RECORDING = "localMeetingScreenRecording"
    const val KEY_LOCAL_MEETING_RECORD_STATUS = "local_record_meeting_record_status"  // 本地会议录制状态
    const val KEY_LOCAL_MEETING_RECORD_MODE = "local_record_meeting_record_mode"  // 本地会议录制模式
    private const val TAG = "MeetingHandler"


    override val authority: String = "com.czur.starry.device.localmeetingrecord.LocalMeetingProvider"
    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
            // 是否正在进行本地会议录制
            KEY_LOCAL_MEETING_RECORDING to LiveTrans(localMeetingRecordingLive as MutableLiveData) {
                it.toBoolean()
            },
            // 是否正在进行本地会议录像
            KEY_LOCAL_MEETING_VIDEO_RECORDING to LiveTrans(localMeetingVideoRecordingLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_LOCAL_MEETING_RECORD_STATUS to LiveTrans(localRecordStateLive as MutableLiveData) {
                it.toBoolean()
            },
        )
    }

    // 是否正在进行本地会议录制（包括录音，录像，录屏）
    val localMeetingRecordingLive by lazy { createLive { localMeetingRecording } }
    var localMeetingRecording: Boolean
        set(value) {
            setValue(KEY_LOCAL_MEETING_RECORDING, value)
        }
        get() = getValue(KEY_LOCAL_MEETING_RECORDING, false)

    val localMeetingVideoRecordingLive by lazy { createLive { localMeetingVideoRecording } }
    var localMeetingVideoRecording: Boolean
        set(value) {
            setValue(KEY_LOCAL_MEETING_VIDEO_RECORDING, value)
        }
        get() = getValue(KEY_LOCAL_MEETING_VIDEO_RECORDING, false)


    // 会议录制中是否可以共享屏幕(非录制中,录音,录屏,可以共享屏幕) true 可以共享 false 不可以共享
    val localMeetingRecordShareLive by lazy { createLive { localMeetingRecordCanEShare } }
    var localMeetingRecordCanEShare: Boolean
        set(value) {
            setValue(KEY_LOCAL_MEETING_RECORD_SHARE, value)
        }
        get() = getValue(KEY_LOCAL_MEETING_RECORD_SHARE, true)

    //会议是否录屏
    var localMeetingScreenRecording: Boolean
        set(value) {
            setValue(KEY_LOCAL_MEETING_SCREEN_RECORDING, value)
        }
        get() = getValue(KEY_LOCAL_MEETING_SCREEN_RECORDING, true)

    /**
     * 语音本地会议录制播放状态
     */
    val localRecordStateLive = createLive { stopLocalRecordState }
    var stopLocalRecordState: Boolean
        get() = getValue(KEY_LOCAL_MEETING_RECORD_STATUS, false)
        set(value) = setValue(KEY_LOCAL_MEETING_RECORD_STATUS, value)
    /**
     * 语音本地会议录制播放模式
     */
    var localRecordMode: String
        get() = getValue(KEY_LOCAL_MEETING_RECORD_MODE, "")
        set(value) = setValue(KEY_LOCAL_MEETING_RECORD_MODE, value)
}