package com.czur.starry.device.noticelib.floating

import android.widget.TextView
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.noticelib.R

/**
 * Created by 陈丰尧 on 2021/9/6
 * 简单的消息通知
 */
class SimpleFloatNotice(val msg: String) : FloatNotice() {
    override fun getLayoutId(): Int = R.layout.float_notice_simple

    private val floatNoticeMsgTv: TextView by findView(R.id.floatNoticeMsgTv)

    override fun initView() {
        super.initView()
        floatNoticeMsgTv.text = msg
    }
}