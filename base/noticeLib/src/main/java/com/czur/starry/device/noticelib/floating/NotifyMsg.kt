package com.czur.starry.device.noticelib.floating

import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.noticelib.*

/**
 * Created by 陈丰尧 on 2021/9/6
 */

/**
 * 通知栏显示的消息类型
 */
data class NotifyMsg(
    val id: Long,
    val title: String,
    val type: Int,
    val data: String?,
    val receiveTime: Long
) {
    companion object {
        fun create(noticeMsg: NoticeMsg): NotifyMsg {
            return NotifyMsg(
                noticeMsg.getLong(NOTICE_MSG_ID),
                noticeMsg.getStr(NOTICE_MSG_TITLE),
                noticeMsg.getInt(NOTICE_MSG_TYPE),
                noticeMsg.get(NOTICE_MSG_DATA),
                noticeMsg.getLong(NOTICE_MSG_RECEIVE_TIME)
            )
        }
    }
}