package com.czur.starry.device.noticelib.floating

import com.czur.starry.device.baselib.base.BaseActivity
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.view.floating.FloatFragment
import com.czur.starry.device.baselib.view.floating.FloatShowMode
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2021/9/6
 */
abstract class FloatNotice : FloatFragment(outSideClick = true, showMode = FloatShowMode.REPEAT) {
    companion object {
        private const val MSG_SHOW_TIME = 5 * ONE_SECOND
    }

    override fun onShow() {
        launch {
            delay(MSG_SHOW_TIME)
            // 通知最多显5s后 自动消失
            dismiss()
        }
    }

    override fun show(targetAty: CZBaseAty) {
        show(ByScreenParams.topRight(offSetRight = 46, offSetTop = 60), anim = AnimDirection.RIGHT)
    }
}