com.czur.starry.device.contacts.contactslib
anim abc_fade_in
anim abc_fade_out
anim abc_grow_fade_in_from_bottom
anim abc_popup_enter
anim abc_popup_exit
anim abc_shrink_fade_out_from_bottom
anim abc_slide_in_bottom
anim abc_slide_in_top
anim abc_slide_out_bottom
anim abc_slide_out_top
anim abc_tooltip_enter
anim abc_tooltip_exit
anim anim_alpha_in
anim anim_alpha_out
anim anim_aty_none
anim anim_scale_y_in
anim anim_scale_y_out
anim btn_checkbox_to_checked_box_inner_merged_animation
anim btn_checkbox_to_checked_box_outer_merged_animation
anim btn_checkbox_to_checked_icon_null_animation
anim btn_checkbox_to_unchecked_box_inner_merged_animation
anim btn_checkbox_to_unchecked_check_path_merged_animation
anim btn_checkbox_to_unchecked_icon_null_animation
anim btn_radio_to_off_mtrl_dot_group_animation
anim btn_radio_to_off_mtrl_ring_outer_animation
anim btn_radio_to_off_mtrl_ring_outer_path_animation
anim btn_radio_to_on_mtrl_dot_group_animation
anim btn_radio_to_on_mtrl_ring_outer_animation
anim btn_radio_to_on_mtrl_ring_outer_path_animation
anim design_bottom_sheet_slide_in
anim design_bottom_sheet_slide_out
anim design_snackbar_in
anim design_snackbar_out
anim fragment_fast_out_extra_slow_in
anim mtrl_bottom_sheet_slide_in
anim mtrl_bottom_sheet_slide_out
anim mtrl_card_lowers_interpolator
animator design_appbar_state_list_animator
animator design_fab_hide_motion_spec
animator design_fab_show_motion_spec
animator fragment_close_enter
animator fragment_close_exit
animator fragment_fade_enter
animator fragment_fade_exit
animator fragment_open_enter
animator fragment_open_exit
animator linear_indeterminate_line1_head_interpolator
animator linear_indeterminate_line1_tail_interpolator
animator linear_indeterminate_line2_head_interpolator
animator linear_indeterminate_line2_tail_interpolator
animator mtrl_btn_state_list_anim
animator mtrl_btn_unelevated_state_list_anim
animator mtrl_card_state_list_anim
animator mtrl_chip_state_list_anim
animator mtrl_extended_fab_change_size_collapse_motion_spec
animator mtrl_extended_fab_change_size_expand_motion_spec
animator mtrl_extended_fab_hide_motion_spec
animator mtrl_extended_fab_show_motion_spec
animator mtrl_extended_fab_state_list_animator
animator mtrl_fab_hide_motion_spec
animator mtrl_fab_show_motion_spec
animator mtrl_fab_transformation_sheet_collapse_spec
animator mtrl_fab_transformation_sheet_expand_spec
array baselib_day_of_week
attr SharedValue
attr SharedValueId
attr actionBarDivider
attr actionBarItemBackground
attr actionBarPopupTheme
attr actionBarSize
attr actionBarSplitStyle
attr actionBarStyle
attr actionBarTabBarStyle
attr actionBarTabStyle
attr actionBarTabTextStyle
attr actionBarTheme
attr actionBarWidgetTheme
attr actionButtonStyle
attr actionDropDownStyle
attr actionLayout
attr actionMenuTextAppearance
attr actionMenuTextColor
attr actionModeBackground
attr actionModeCloseButtonStyle
attr actionModeCloseContentDescription
attr actionModeCloseDrawable
attr actionModeCopyDrawable
attr actionModeCutDrawable
attr actionModeFindDrawable
attr actionModePasteDrawable
attr actionModePopupWindowStyle
attr actionModeSelectAllDrawable
attr actionModeShareDrawable
attr actionModeSplitBackground
attr actionModeStyle
attr actionModeTheme
attr actionModeWebSearchDrawable
attr actionOverflowButtonStyle
attr actionOverflowMenuStyle
attr actionProviderClass
attr actionTextColorAlpha
attr actionViewClass
attr activityChooserViewStyle
attr alertDialogButtonGroupStyle
attr alertDialogCenterButtons
attr alertDialogStyle
attr alertDialogTheme
attr alignContent
attr alignItems
attr allowStacking
attr alpha
attr alphabeticModifiers
attr altSrc
attr animateCircleAngleTo
attr animateRelativeTo
attr animate_relativeTo
attr animationMode
attr appBarLayoutStyle
attr applyMotionScene
attr arcMode
attr arrowHeadLength
attr arrowShaftLength
attr attributeName
attr autoCompleteMode
attr autoCompleteTextViewStyle
attr autoSizeMaxTextSize
attr autoSizeMinTextSize
attr autoSizePresetSizes
attr autoSizeStepGranularity
attr autoSizeTextType
attr autoTransition
attr background
attr backgroundColor
attr backgroundInsetBottom
attr backgroundInsetEnd
attr backgroundInsetStart
attr backgroundInsetTop
attr backgroundOverlayColorAlpha
attr backgroundSplit
attr backgroundStacked
attr backgroundTint
attr backgroundTintMode
attr badgeGravity
attr badgeStyle
attr badgeTextColor
attr barLength
attr barrierAllowsGoneWidgets
attr barrierDirection
attr barrierMargin
attr baselib_bg_color
attr baselib_theme
attr baselib_titlebar_color
attr baselib_titlebar_title
attr baselib_titlebar_top_distance
attr baselib_tv_color
attr behavior_autoHide
attr behavior_autoShrink
attr behavior_draggable
attr behavior_expandedOffset
attr behavior_fitToContents
attr behavior_halfExpandedRatio
attr behavior_hideable
attr behavior_overlapTop
attr behavior_peekHeight
attr behavior_saveFlags
attr behavior_skipCollapsed
attr bg_color
attr bl_activated_textColor
attr bl_active_textColor
attr bl_anim_auto_start
attr bl_checkable_drawable
attr bl_checkable_gradient_angle
attr bl_checkable_gradient_centerColor
attr bl_checkable_gradient_centerX
attr bl_checkable_gradient_centerY
attr bl_checkable_gradient_endColor
attr bl_checkable_gradient_gradientRadius
attr bl_checkable_gradient_startColor
attr bl_checkable_gradient_type
attr bl_checkable_gradient_useLevel
attr bl_checkable_solid_color
attr bl_checkable_stroke_color
attr bl_checkable_textColor
attr bl_checked_button_drawable
attr bl_checked_drawable
attr bl_checked_gradient_angle
attr bl_checked_gradient_centerColor
attr bl_checked_gradient_centerX
attr bl_checked_gradient_centerY
attr bl_checked_gradient_endColor
attr bl_checked_gradient_gradientRadius
attr bl_checked_gradient_startColor
attr bl_checked_gradient_type
attr bl_checked_gradient_useLevel
attr bl_checked_solid_color
attr bl_checked_stroke_color
attr bl_checked_textColor
attr bl_corners_bottomLeftRadius
attr bl_corners_bottomRadius
attr bl_corners_bottomRightRadius
attr bl_corners_leftRadius
attr bl_corners_radius
attr bl_corners_rightRadius
attr bl_corners_topLeftRadius
attr bl_corners_topRadius
attr bl_corners_topRightRadius
attr bl_duration
attr bl_duration_item0
attr bl_duration_item1
attr bl_duration_item10
attr bl_duration_item11
attr bl_duration_item12
attr bl_duration_item13
attr bl_duration_item14
attr bl_duration_item2
attr bl_duration_item3
attr bl_duration_item4
attr bl_duration_item5
attr bl_duration_item6
attr bl_duration_item7
attr bl_duration_item8
attr bl_duration_item9
attr bl_enabled_drawable
attr bl_enabled_gradient_angle
attr bl_enabled_gradient_centerColor
attr bl_enabled_gradient_centerX
attr bl_enabled_gradient_centerY
attr bl_enabled_gradient_endColor
attr bl_enabled_gradient_gradientRadius
attr bl_enabled_gradient_startColor
attr bl_enabled_gradient_type
attr bl_enabled_gradient_useLevel
attr bl_enabled_solid_color
attr bl_enabled_stroke_color
attr bl_enabled_textColor
attr bl_expanded_textColor
attr bl_focused_activated
attr bl_focused_drawable
attr bl_focused_gradient_angle
attr bl_focused_gradient_centerColor
attr bl_focused_gradient_centerX
attr bl_focused_gradient_centerY
attr bl_focused_gradient_endColor
attr bl_focused_gradient_gradientRadius
attr bl_focused_gradient_startColor
attr bl_focused_gradient_type
attr bl_focused_gradient_useLevel
attr bl_focused_hovered
attr bl_focused_solid_color
attr bl_focused_stroke_color
attr bl_focused_textColor
attr bl_frame_drawable_item0
attr bl_frame_drawable_item1
attr bl_frame_drawable_item10
attr bl_frame_drawable_item11
attr bl_frame_drawable_item12
attr bl_frame_drawable_item13
attr bl_frame_drawable_item14
attr bl_frame_drawable_item2
attr bl_frame_drawable_item3
attr bl_frame_drawable_item4
attr bl_frame_drawable_item5
attr bl_frame_drawable_item6
attr bl_frame_drawable_item7
attr bl_frame_drawable_item8
attr bl_frame_drawable_item9
attr bl_function
attr bl_gradient_angle
attr bl_gradient_centerColor
attr bl_gradient_centerX
attr bl_gradient_centerY
attr bl_gradient_endColor
attr bl_gradient_gradientRadius
attr bl_gradient_startColor
attr bl_gradient_type
attr bl_gradient_useLevel
attr bl_multi_selector1
attr bl_multi_selector2
attr bl_multi_selector3
attr bl_multi_selector4
attr bl_multi_selector5
attr bl_multi_selector6
attr bl_multi_text_selector1
attr bl_multi_text_selector2
attr bl_multi_text_selector3
attr bl_multi_text_selector4
attr bl_multi_text_selector5
attr bl_multi_text_selector6
attr bl_oneshot
attr bl_padding_bottom
attr bl_padding_left
attr bl_padding_right
attr bl_padding_top
attr bl_position
attr bl_pressed_color
attr bl_pressed_drawable
attr bl_pressed_gradient_angle
attr bl_pressed_gradient_centerColor
attr bl_pressed_gradient_centerX
attr bl_pressed_gradient_centerY
attr bl_pressed_gradient_endColor
attr bl_pressed_gradient_gradientRadius
attr bl_pressed_gradient_startColor
attr bl_pressed_gradient_type
attr bl_pressed_gradient_useLevel
attr bl_pressed_solid_color
attr bl_pressed_stroke_color
attr bl_pressed_textColor
attr bl_ripple_color
attr bl_ripple_enable
attr bl_selected_drawable
attr bl_selected_gradient_angle
attr bl_selected_gradient_centerColor
attr bl_selected_gradient_centerX
attr bl_selected_gradient_centerY
attr bl_selected_gradient_endColor
attr bl_selected_gradient_gradientRadius
attr bl_selected_gradient_startColor
attr bl_selected_gradient_type
attr bl_selected_gradient_useLevel
attr bl_selected_solid_color
attr bl_selected_stroke_color
attr bl_selected_textColor
attr bl_shape
attr bl_shape_alpha
attr bl_size_height
attr bl_size_width
attr bl_solid_color
attr bl_stroke_color
attr bl_stroke_dashGap
attr bl_stroke_dashWidth
attr bl_stroke_position
attr bl_stroke_width
attr bl_text_gradient_endColor
attr bl_text_gradient_orientation
attr bl_text_gradient_startColor
attr bl_unActivated_textColor
attr bl_unActive_textColor
attr bl_unCheckable_drawable
attr bl_unCheckable_gradient_angle
attr bl_unCheckable_gradient_centerColor
attr bl_unCheckable_gradient_centerX
attr bl_unCheckable_gradient_centerY
attr bl_unCheckable_gradient_endColor
attr bl_unCheckable_gradient_gradientRadius
attr bl_unCheckable_gradient_startColor
attr bl_unCheckable_gradient_type
attr bl_unCheckable_gradient_useLevel
attr bl_unCheckable_solid_color
attr bl_unCheckable_stroke_color
attr bl_unCheckable_textColor
attr bl_unChecked_button_drawable
attr bl_unChecked_drawable
attr bl_unChecked_gradient_angle
attr bl_unChecked_gradient_centerColor
attr bl_unChecked_gradient_centerX
attr bl_unChecked_gradient_centerY
attr bl_unChecked_gradient_endColor
attr bl_unChecked_gradient_gradientRadius
attr bl_unChecked_gradient_startColor
attr bl_unChecked_gradient_type
attr bl_unChecked_gradient_useLevel
attr bl_unChecked_solid_color
attr bl_unChecked_stroke_color
attr bl_unChecked_textColor
attr bl_unEnabled_drawable
attr bl_unEnabled_gradient_angle
attr bl_unEnabled_gradient_centerColor
attr bl_unEnabled_gradient_centerX
attr bl_unEnabled_gradient_centerY
attr bl_unEnabled_gradient_endColor
attr bl_unEnabled_gradient_gradientRadius
attr bl_unEnabled_gradient_startColor
attr bl_unEnabled_gradient_type
attr bl_unEnabled_gradient_useLevel
attr bl_unEnabled_solid_color
attr bl_unEnabled_stroke_color
attr bl_unEnabled_textColor
attr bl_unExpanded_textColor
attr bl_unFocused_activated
attr bl_unFocused_drawable
attr bl_unFocused_gradient_angle
attr bl_unFocused_gradient_centerColor
attr bl_unFocused_gradient_centerX
attr bl_unFocused_gradient_centerY
attr bl_unFocused_gradient_endColor
attr bl_unFocused_gradient_gradientRadius
attr bl_unFocused_gradient_startColor
attr bl_unFocused_gradient_type
attr bl_unFocused_gradient_useLevel
attr bl_unFocused_hovered
attr bl_unFocused_solid_color
attr bl_unFocused_stroke_color
attr bl_unFocused_textColor
attr bl_unPressed_drawable
attr bl_unPressed_gradient_angle
attr bl_unPressed_gradient_centerColor
attr bl_unPressed_gradient_centerX
attr bl_unPressed_gradient_centerY
attr bl_unPressed_gradient_endColor
attr bl_unPressed_gradient_gradientRadius
attr bl_unPressed_gradient_startColor
attr bl_unPressed_gradient_type
attr bl_unPressed_gradient_useLevel
attr bl_unPressed_solid_color
attr bl_unPressed_stroke_color
attr bl_unPressed_textColor
attr bl_unSelected_drawable
attr bl_unSelected_gradient_angle
attr bl_unSelected_gradient_centerColor
attr bl_unSelected_gradient_centerX
attr bl_unSelected_gradient_centerY
attr bl_unSelected_gradient_endColor
attr bl_unSelected_gradient_gradientRadius
attr bl_unSelected_gradient_startColor
attr bl_unSelected_gradient_type
attr bl_unSelected_gradient_useLevel
attr bl_unSelected_solid_color
attr bl_unSelected_stroke_color
attr bl_unSelected_textColor
attr bl_unpressed_color
attr blendSrc
attr borderRound
attr borderRoundPercent
attr borderWidth
attr borderlessButtonStyle
attr bottomAppBarStyle
attr bottomNavigationStyle
attr bottomSheetDialogTheme
attr bottomSheetStyle
attr boxBackgroundColor
attr boxBackgroundMode
attr boxCollapsedPaddingTop
attr boxCornerRadiusBottomEnd
attr boxCornerRadiusBottomStart
attr boxCornerRadiusTopEnd
attr boxCornerRadiusTopStart
attr boxStrokeColor
attr boxStrokeErrorColor
attr boxStrokeWidth
attr boxStrokeWidthFocused
attr breathEffect
attr brightness
attr buttonBarButtonStyle
attr buttonBarNegativeButtonStyle
attr buttonBarNeutralButtonStyle
attr buttonBarPositiveButtonStyle
attr buttonBarStyle
attr buttonCompat
attr buttonGravity
attr buttonIconDimen
attr buttonPanelSideLayout
attr buttonStyle
attr buttonStyleSmall
attr buttonTint
attr buttonTintMode
attr cardBackgroundColor
attr cardCornerRadius
attr cardElevation
attr cardForegroundColor
attr cardMaxElevation
attr cardPreventCornerOverlap
attr cardUseCompatPadding
attr cardViewStyle
attr carousel_backwardTransition
attr carousel_emptyViewsBehavior
attr carousel_firstView
attr carousel_forwardTransition
attr carousel_infinite
attr carousel_nextState
attr carousel_previousState
attr carousel_touchUpMode
attr carousel_touchUp_dampeningFactor
attr carousel_touchUp_velocityThreshold
attr chainUseRtl
attr checkMarkCompat
attr checkMarkTint
attr checkMarkTintMode
attr checkboxStyle
attr checkedButton
attr checkedChip
attr checkedIcon
attr checkedIconEnabled
attr checkedIconMargin
attr checkedIconSize
attr checkedIconTint
attr checkedIconVisible
attr checkedTextViewStyle
attr chipBackgroundColor
attr chipCornerRadius
attr chipEndPadding
attr chipGroupStyle
attr chipIcon
attr chipIconEnabled
attr chipIconSize
attr chipIconTint
attr chipIconVisible
attr chipMinHeight
attr chipMinTouchTargetSize
attr chipSpacing
attr chipSpacingHorizontal
attr chipSpacingVertical
attr chipStandaloneStyle
attr chipStartPadding
attr chipStrokeColor
attr chipStrokeWidth
attr chipStyle
attr chipSurfaceColor
attr circleColor
attr circleRadius
attr circularProgressIndicatorStyle
attr circularflow_angles
attr circularflow_defaultAngle
attr circularflow_defaultRadius
attr circularflow_radiusInDP
attr circularflow_viewCenter
attr clearsTag
attr clickAction
attr clockFaceBackgroundColor
attr clockHandColor
attr clockIcon
attr clockNumberTextColor
attr closeIcon
attr closeIconEnabled
attr closeIconEndPadding
attr closeIconSize
attr closeIconStartPadding
attr closeIconTint
attr closeIconVisible
attr closeItemLayout
attr collapseContentDescription
attr collapseIcon
attr collapsedSize
attr collapsedTitleGravity
attr collapsedTitleTextAppearance
attr collapsingToolbarLayoutStyle
attr color
attr colorAccent
attr colorBackgroundFloating
attr colorButtonNormal
attr colorControlActivated
attr colorControlHighlight
attr colorControlNormal
attr colorError
attr colorOnBackground
attr colorOnError
attr colorOnPrimary
attr colorOnPrimarySurface
attr colorOnSecondary
attr colorOnSurface
attr colorPrimary
attr colorPrimaryDark
attr colorPrimarySurface
attr colorPrimaryVariant
attr colorSecondary
attr colorSecondaryVariant
attr colorSurface
attr colorSwitchThumbNormal
attr commitIcon
attr constraintRotate
attr constraintSet
attr constraintSetEnd
attr constraintSetStart
attr constraint_referenced_ids
attr constraint_referenced_tags
attr constraints
attr content
attr contentDescription
attr contentInsetEnd
attr contentInsetEndWithActions
attr contentInsetLeft
attr contentInsetRight
attr contentInsetStart
attr contentInsetStartWithNavigation
attr contentPadding
attr contentPaddingBottom
attr contentPaddingEnd
attr contentPaddingLeft
attr contentPaddingRight
attr contentPaddingStart
attr contentPaddingTop
attr contentScrim
attr contrast
attr controlBackground
attr coordinatorLayoutStyle
attr cornerFamily
attr cornerFamilyBottomLeft
attr cornerFamilyBottomRight
attr cornerFamilyTopLeft
attr cornerFamilyTopRight
attr cornerRadius
attr cornerSize
attr cornerSizeBottomLeft
attr cornerSizeBottomRight
attr cornerSizeTopLeft
attr cornerSizeTopRight
attr counterEnabled
attr counterMaxLength
attr counterOverflowTextAppearance
attr counterOverflowTextColor
attr counterTextAppearance
attr counterTextColor
attr crossfade
attr currentState
attr curveFit
attr customBoolean
attr customColorDrawableValue
attr customColorValue
attr customDimension
attr customFloatValue
attr customIntegerValue
attr customNavigationLayout
attr customPixelDimension
attr customReference
attr customStringValue
attr dayInvalidStyle
attr daySelectedStyle
attr dayStyle
attr dayTodayStyle
attr defaultDuration
attr defaultQueryHint
attr defaultState
attr deltaPolarAngle
attr deltaPolarRadius
attr deriveConstraintsFrom
attr dialogCornerRadius
attr dialogPreferredPadding
attr dialogTheme
attr displayOptions
attr divider
attr dividerDrawable
attr dividerDrawableHorizontal
attr dividerDrawableVertical
attr dividerHorizontal
attr dividerPadding
attr dividerVertical
attr dragDirection
attr dragScale
attr dragThreshold
attr drawPath
attr drawableBottomCompat
attr drawableEndCompat
attr drawableLeftCompat
attr drawableRightCompat
attr drawableSize
attr drawableStartCompat
attr drawableTint
attr drawableTintMode
attr drawableTopCompat
attr drawerArrowStyle
attr dropDownListViewStyle
attr dropdownListPreferredItemHeight
attr duration
attr editSolidColor
attr editTextBackground
attr editTextColor
attr editTextStyle
attr elevation
attr elevationOverlayColor
attr elevationOverlayEnabled
attr emojiCompatEnabled
attr endIconCheckable
attr endIconContentDescription
attr endIconDrawable
attr endIconMode
attr endIconTint
attr endIconTintMode
attr enforceMaterialTheme
attr enforceTextAppearance
attr ensureMinTouchTargetSize
attr errorContentDescription
attr errorEnabled
attr errorIconDrawable
attr errorIconTint
attr errorIconTintMode
attr errorTextAppearance
attr errorTextColor
attr expandActivityOverflowButtonDrawable
attr expanded
attr expandedHintEnabled
attr expandedTitleGravity
attr expandedTitleMargin
attr expandedTitleMarginBottom
attr expandedTitleMarginEnd
attr expandedTitleMarginStart
attr expandedTitleMarginTop
attr expandedTitleTextAppearance
attr extendMotionSpec
attr extendedFloatingActionButtonStyle
attr fabAlignmentMode
attr fabAnimationMode
attr fabCradleMargin
attr fabCradleRoundedCornerRadius
attr fabCradleVerticalOffset
attr fabCustomSize
attr fabSize
attr fastScrollEnabled
attr fastScrollHorizontalThumbDrawable
attr fastScrollHorizontalTrackDrawable
attr fastScrollVerticalThumbDrawable
attr fastScrollVerticalTrackDrawable
attr firstBaselineToTopHeight
attr flexDirection
attr flexWrap
attr float_tips
attr float_tips_theme
attr floatingActionButtonStyle
attr flow_firstHorizontalBias
attr flow_firstHorizontalStyle
attr flow_firstVerticalBias
attr flow_firstVerticalStyle
attr flow_horizontalAlign
attr flow_horizontalBias
attr flow_horizontalGap
attr flow_horizontalStyle
attr flow_lastHorizontalBias
attr flow_lastHorizontalStyle
attr flow_lastVerticalBias
attr flow_lastVerticalStyle
attr flow_maxElementsWrap
attr flow_padding
attr flow_verticalAlign
attr flow_verticalBias
attr flow_verticalGap
attr flow_verticalStyle
attr flow_wrapMode
attr font
attr fontFamily
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFallbackQuery
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontProviderSystemFontFamily
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr foregroundInsidePadding
attr framePosition
attr gapBetweenBars
attr gestureInsetBottomIgnored
attr goIcon
attr grid_columnWeights
attr grid_columns
attr grid_horizontalGaps
attr grid_orientation
attr grid_rowWeights
attr grid_rows
attr grid_skips
attr grid_spans
attr grid_useRtl
attr grid_validateInputs
attr grid_verticalGaps
attr guidelineUseRtl
attr haloColor
attr haloRadius
attr headerLayout
attr height
attr helperText
attr helperTextEnabled
attr helperTextTextAppearance
attr helperTextTextColor
attr hideAnimationBehavior
attr hideMotionSpec
attr hideOnContentScroll
attr hideOnScroll
attr hint
attr hintAnimationEnabled
attr hintEnabled
attr hintTextAppearance
attr hintTextColor
attr homeAsUpIndicator
attr homeLayout
attr horizontalOffset
attr hoveredFocusedTranslationZ
attr icon
attr iconEndPadding
attr iconGravity
attr iconPadding
attr iconSize
attr iconStartPadding
attr iconTint
attr iconTintMode
attr iconifiedByDefault
attr ifTagNotSet
attr ifTagSet
attr imageButtonStyle
attr imagePanX
attr imagePanY
attr imageRotate
attr imageZoom
attr indeterminateAnimationType
attr indeterminateProgressStyle
attr indicatorColor
attr indicatorDirectionCircular
attr indicatorDirectionLinear
attr indicatorInset
attr indicatorSize
attr initialActivityCount
attr insetForeground
attr isLightTheme
attr isMaterialTheme
attr itemBackground
attr itemFillColor
attr itemHorizontalPadding
attr itemHorizontalTranslationEnabled
attr itemIconPadding
attr itemIconSize
attr itemIconTint
attr itemMaxLines
attr itemPadding
attr itemRippleColor
attr itemShapeAppearance
attr itemShapeAppearanceOverlay
attr itemShapeFillColor
attr itemShapeInsetBottom
attr itemShapeInsetEnd
attr itemShapeInsetStart
attr itemShapeInsetTop
attr itemSpacing
attr itemStrokeColor
attr itemStrokeWidth
attr itemTextAppearance
attr itemTextAppearanceActive
attr itemTextAppearanceInactive
attr itemTextColor
attr justifyContent
attr keyPositionType
attr keyboardIcon
attr keylines
attr lStar
attr labelBehavior
attr labelStyle
attr labelVisibilityMode
attr lastBaselineToBottomHeight
attr layout
attr layoutDescription
attr layoutDuringTransition
attr layoutManager
attr layout_alignSelf
attr layout_anchor
attr layout_anchorGravity
attr layout_behavior
attr layout_collapseMode
attr layout_collapseParallaxMultiplier
attr layout_constrainedHeight
attr layout_constrainedWidth
attr layout_constraintBaseline_creator
attr layout_constraintBaseline_toBaselineOf
attr layout_constraintBaseline_toBottomOf
attr layout_constraintBaseline_toTopOf
attr layout_constraintBottom_creator
attr layout_constraintBottom_toBottomOf
attr layout_constraintBottom_toTopOf
attr layout_constraintCircle
attr layout_constraintCircleAngle
attr layout_constraintCircleRadius
attr layout_constraintDimensionRatio
attr layout_constraintEnd_toEndOf
attr layout_constraintEnd_toStartOf
attr layout_constraintGuide_begin
attr layout_constraintGuide_end
attr layout_constraintGuide_percent
attr layout_constraintHeight
attr layout_constraintHeight_default
attr layout_constraintHeight_max
attr layout_constraintHeight_min
attr layout_constraintHeight_percent
attr layout_constraintHorizontal_bias
attr layout_constraintHorizontal_chainStyle
attr layout_constraintHorizontal_weight
attr layout_constraintLeft_creator
attr layout_constraintLeft_toLeftOf
attr layout_constraintLeft_toRightOf
attr layout_constraintRight_creator
attr layout_constraintRight_toLeftOf
attr layout_constraintRight_toRightOf
attr layout_constraintStart_toEndOf
attr layout_constraintStart_toStartOf
attr layout_constraintTag
attr layout_constraintTop_creator
attr layout_constraintTop_toBottomOf
attr layout_constraintTop_toTopOf
attr layout_constraintVertical_bias
attr layout_constraintVertical_chainStyle
attr layout_constraintVertical_weight
attr layout_constraintWidth
attr layout_constraintWidth_default
attr layout_constraintWidth_max
attr layout_constraintWidth_min
attr layout_constraintWidth_percent
attr layout_dodgeInsetEdges
attr layout_editor_absoluteX
attr layout_editor_absoluteY
attr layout_flexBasisPercent
attr layout_flexGrow
attr layout_flexShrink
attr layout_goneMarginBaseline
attr layout_goneMarginBottom
attr layout_goneMarginEnd
attr layout_goneMarginLeft
attr layout_goneMarginRight
attr layout_goneMarginStart
attr layout_goneMarginTop
attr layout_insetEdge
attr layout_keyline
attr layout_marginBaseline
attr layout_maxHeight
attr layout_maxWidth
attr layout_minHeight
attr layout_minWidth
attr layout_optimizationLevel
attr layout_order
attr layout_scrollFlags
attr layout_scrollInterpolator
attr layout_wrapBefore
attr layout_wrapBehaviorInParent
attr liftOnScroll
attr liftOnScrollTargetViewId
attr limitBoundsTo
attr lineHeight
attr lineSpacing
attr linearProgressIndicatorStyle
attr listChoiceBackgroundIndicator
attr listChoiceIndicatorMultipleAnimated
attr listChoiceIndicatorSingleAnimated
attr listDividerAlertDialog
attr listItemLayout
attr listLayout
attr listMenuViewStyle
attr listPopupWindowStyle
attr listPreferredItemHeight
attr listPreferredItemHeightLarge
attr listPreferredItemHeightSmall
attr listPreferredItemPaddingEnd
attr listPreferredItemPaddingLeft
attr listPreferredItemPaddingRight
attr listPreferredItemPaddingStart
attr logo
attr logoDescription
attr materialAlertDialogBodyTextStyle
attr materialAlertDialogTheme
attr materialAlertDialogTitleIconStyle
attr materialAlertDialogTitlePanelStyle
attr materialAlertDialogTitleTextStyle
attr materialButtonOutlinedStyle
attr materialButtonStyle
attr materialButtonToggleGroupStyle
attr materialCalendarDay
attr materialCalendarFullscreenTheme
attr materialCalendarHeaderCancelButton
attr materialCalendarHeaderConfirmButton
attr materialCalendarHeaderDivider
attr materialCalendarHeaderLayout
attr materialCalendarHeaderSelection
attr materialCalendarHeaderTitle
attr materialCalendarHeaderToggleButton
attr materialCalendarMonth
attr materialCalendarMonthNavigationButton
attr materialCalendarStyle
attr materialCalendarTheme
attr materialCalendarYearNavigationButton
attr materialCardViewStyle
attr materialCircleRadius
attr materialClockStyle
attr materialThemeOverlay
attr materialTimePickerStyle
attr materialTimePickerTheme
attr max
attr maxAcceleration
attr maxActionInlineWidth
attr maxButtonHeight
attr maxCharacterCount
attr maxHeight
attr maxImageSize
attr maxLine
attr maxLines
attr maxVelocity
attr maxWidth
attr measureWithLargestChild
attr menu
attr methodName
attr minHeight
attr minHideDelay
attr minSeparation
attr minTouchTargetSize
attr minWidth
attr mock_diagonalsColor
attr mock_label
attr mock_labelBackgroundColor
attr mock_labelColor
attr mock_showDiagonals
attr mock_showLabel
attr motionDebug
attr motionEffect_alpha
attr motionEffect_end
attr motionEffect_move
attr motionEffect_start
attr motionEffect_strict
attr motionEffect_translationX
attr motionEffect_translationY
attr motionEffect_viewTransition
attr motionInterpolator
attr motionPathRotate
attr motionProgress
attr motionStagger
attr motionTarget
attr motion_postLayoutCollision
attr motion_triggerOnCollision
attr moveWhenScrollAtTop
attr multiChoiceItemLayout
attr navigationContentDescription
attr navigationIcon
attr navigationIconTint
attr navigationMode
attr navigationViewStyle
attr nestedScrollFlags
attr nestedScrollViewStyle
attr nestedScrollable
attr number
attr numericModifiers
attr offBgColor
attr onBgColor
attr onCross
attr onHide
attr onNegativeCross
attr onPositiveCross
attr onShow
attr onStateTransition
attr onTouchUp
attr overlapAnchor
attr overlay
attr paddingBottomNoButtons
attr paddingBottomSystemWindowInsets
attr paddingEnd
attr paddingLeftSystemWindowInsets
attr paddingRightSystemWindowInsets
attr paddingStart
attr paddingTopNoTitle
attr panelBackground
attr panelMenuListTheme
attr panelMenuListWidth
attr passwordToggleContentDescription
attr passwordToggleDrawable
attr passwordToggleEnabled
attr passwordToggleTint
attr passwordToggleTintMode
attr pathMotionArc
attr path_percent
attr percentHeight
attr percentWidth
attr percentX
attr percentY
attr perpendicularPath_percent
attr pivotAnchor
attr placeholderText
attr placeholderTextAppearance
attr placeholderTextColor
attr placeholder_emptyVisibility
attr polarRelativeTo
attr popupMenuBackground
attr popupMenuStyle
attr popupTheme
attr popupWindowStyle
attr prefixText
attr prefixTextAppearance
attr prefixTextColor
attr preserveIconSpacing
attr pressedTranslationZ
attr progress
attr progressBarPadding
attr progressBarStyle
attr progress_color
attr quantizeMotionInterpolator
attr quantizeMotionPhase
attr quantizeMotionSteps
attr queryBackground
attr queryHint
attr queryPatterns
attr radioButtonStyle
attr rangeFillColor
attr ratingBarStyle
attr ratingBarStyleIndicator
attr ratingBarStyleSmall
attr reactiveGuide_animateChange
attr reactiveGuide_applyToAllConstraintSets
attr reactiveGuide_applyToConstraintSet
attr reactiveGuide_valueId
attr recyclerViewStyle
attr region_heightLessThan
attr region_heightMoreThan
attr region_widthLessThan
attr region_widthMoreThan
attr reverseLayout
attr rippleColor
attr rotationCenterId
attr round
attr roundPercent
attr saturation
attr scaleFromTextSize
attr scrimAnimationDuration
attr scrimBackground
attr scrimVisibleHeightTrigger
attr searchHintIcon
attr searchIcon
attr searchViewStyle
attr seekBarStyle
attr selectableItemBackground
attr selectableItemBackgroundBorderless
attr selectionRequired
attr selectorSize
attr setsTag
attr shapeAppearance
attr shapeAppearanceLargeComponent
attr shapeAppearanceMediumComponent
attr shapeAppearanceOverlay
attr shapeAppearanceSmallComponent
attr shortcutMatchRequired
attr showAnimationBehavior
attr showAsAction
attr showDelay
attr showDivider
attr showDividerHorizontal
attr showDividerVertical
attr showDividers
attr showMotionSpec
attr showPaths
attr showText
attr showTextColor
attr showTitle
attr show_mode
attr shrinkMotionSpec
attr singleChoiceItemLayout
attr singleLine
attr singleSelection
attr sizePercent
attr sliderStyle
attr snackbarButtonStyle
attr snackbarStyle
attr snackbarTextViewStyle
attr spanCount
attr spinBars
attr spinnerDropDownItemStyle
attr spinnerStyle
attr splitTrack
attr springBoundary
attr springDamping
attr springMass
attr springStiffness
attr springStopThreshold
attr srcCompat
attr ssid_single_line
attr stackFromEnd
attr staggered
attr startIconCheckable
attr startIconContentDescription
attr startIconDrawable
attr startIconTint
attr startIconTintMode
attr stateLabels
attr state_above_anchor
attr state_collapsed
attr state_collapsible
attr state_dragged
attr state_liftable
attr state_lifted
attr statusBarBackground
attr statusBarForeground
attr statusBarScrim
attr strokeColor
attr strokeWidth
attr subMenuArrow
attr submitBackground
attr subtitle
attr subtitleTextAppearance
attr subtitleTextColor
attr subtitleTextStyle
attr suffixText
attr suffixTextAppearance
attr suffixTextColor
attr suggestionRowLayout
attr switchMinWidth
attr switchPadding
attr switchStyle
attr switchTextAppearance
attr tabBackground
attr tabContentStart
attr tabGravity
attr tabIconTint
attr tabIconTintMode
attr tabIndicator
attr tabIndicatorAnimationDuration
attr tabIndicatorAnimationMode
attr tabIndicatorColor
attr tabIndicatorFullWidth
attr tabIndicatorGravity
attr tabIndicatorHeight
attr tabInlineLabel
attr tabMaxWidth
attr tabMinWidth
attr tabMode
attr tabPadding
attr tabPaddingBottom
attr tabPaddingEnd
attr tabPaddingStart
attr tabPaddingTop
attr tabRippleColor
attr tabSelectedTextColor
attr tabStyle
attr tabTextAppearance
attr tabTextColor
attr tabUnboundedRipple
attr targetId
attr targetSeekBar
attr telltales_tailColor
attr telltales_tailScale
attr telltales_velocityMode
attr textAllCaps
attr textAppearanceBody1
attr textAppearanceBody2
attr textAppearanceButton
attr textAppearanceCaption
attr textAppearanceHeadline1
attr textAppearanceHeadline2
attr textAppearanceHeadline3
attr textAppearanceHeadline4
attr textAppearanceHeadline5
attr textAppearanceHeadline6
attr textAppearanceLargePopupMenu
attr textAppearanceLineHeightEnabled
attr textAppearanceListItem
attr textAppearanceListItemSecondary
attr textAppearanceListItemSmall
attr textAppearanceOverline
attr textAppearancePopupMenuHeader
attr textAppearanceSearchResultSubtitle
attr textAppearanceSearchResultTitle
attr textAppearanceSmallPopupMenu
attr textAppearanceSubtitle1
attr textAppearanceSubtitle2
attr textBackground
attr textBackgroundPanX
attr textBackgroundPanY
attr textBackgroundRotate
attr textBackgroundZoom
attr textColorAlertDialogListItem
attr textColorSearchUrl
attr textEndPadding
attr textFillColor
attr textInputLayoutFocusedRectEnabled
attr textInputStyle
attr textLocale
attr textOutlineColor
attr textOutlineThickness
attr textPanX
attr textPanY
attr textStartPadding
attr textureBlurFactor
attr textureEffect
attr textureHeight
attr textureWidth
attr theme
attr themeLineHeight
attr thickness
attr thumbColor
attr thumbElevation
attr thumbRadius
attr thumbStrokeColor
attr thumbStrokeWidth
attr thumbTextPadding
attr thumbTint
attr thumbTintMode
attr tickColor
attr tickColorActive
attr tickColorInactive
attr tickMark
attr tickMarkTint
attr tickMarkTintMode
attr tickVisible
attr tint
attr tintMode
attr title
attr titleEnabled
attr titleMargin
attr titleMarginBottom
attr titleMarginEnd
attr titleMarginStart
attr titleMarginTop
attr titleMargins
attr titleTextAppearance
attr titleTextColor
attr titleTextStyle
attr toolbarId
attr toolbarNavigationButtonStyle
attr toolbarStyle
attr tooltipForegroundColor
attr tooltipFrameBackground
attr tooltipStyle
attr tooltipText
attr touchAnchorId
attr touchAnchorSide
attr touchRegionId
attr track
attr trackColor
attr trackColorActive
attr trackColorInactive
attr trackCornerRadius
attr trackHeight
attr trackThickness
attr trackTint
attr trackTintMode
attr transformPivotTarget
attr transitionDisable
attr transitionEasing
attr transitionFlags
attr transitionPathRotate
attr transitionShapeAppearance
attr triggerId
attr triggerReceiver
attr triggerSlack
attr ttcIndex
attr upDuration
attr useCompatPadding
attr useMaterialThemeColors
attr values
attr verticalOffset
attr viewInflaterClass
attr viewTransitionMode
attr viewTransitionOnCross
attr viewTransitionOnNegativeCross
attr viewTransitionOnPositiveCross
attr visibilityMode
attr voiceIcon
attr warmth
attr waveDecay
attr waveOffset
attr wavePeriod
attr wavePhase
attr waveShape
attr waveVariesBy
attr windowActionBar
attr windowActionBarOverlay
attr windowActionModeOverlay
attr windowFixedHeightMajor
attr windowFixedHeightMinor
attr windowFixedWidthMajor
attr windowFixedWidthMinor
attr windowMinWidthMajor
attr windowMinWidthMinor
attr windowNoTitle
attr yearSelectedStyle
attr yearStyle
attr yearTodayStyle
bool abc_action_bar_embed_tabs
bool abc_allow_stacked_button_bar
bool abc_config_actionMenuItemAllCaps
bool mtrl_btn_textappearance_all_caps
color abc_background_cache_hint_selector_material_dark
color abc_background_cache_hint_selector_material_light
color abc_btn_colored_borderless_text_material
color abc_btn_colored_text_material
color abc_color_highlight_material
color abc_decor_view_status_guard
color abc_decor_view_status_guard_light
color abc_hint_foreground_material_dark
color abc_hint_foreground_material_light
color abc_primary_text_disable_only_material_dark
color abc_primary_text_disable_only_material_light
color abc_primary_text_material_dark
color abc_primary_text_material_light
color abc_search_url_text
color abc_search_url_text_normal
color abc_search_url_text_pressed
color abc_search_url_text_selected
color abc_secondary_text_material_dark
color abc_secondary_text_material_light
color abc_tint_btn_checkable
color abc_tint_default
color abc_tint_edittext
color abc_tint_seek_thumb
color abc_tint_spinner
color abc_tint_switch_track
color accent_material_dark
color accent_material_light
color androidx_core_ripple_material_light
color androidx_core_secondary_text_default_material_light
color background_floating_material_dark
color background_floating_material_light
color background_material_dark
color background_material_light
color base_bg_color
color black
color bright_foreground_disabled_material_dark
color bright_foreground_disabled_material_light
color bright_foreground_inverse_material_dark
color bright_foreground_inverse_material_light
color bright_foreground_material_dark
color bright_foreground_material_light
color button_material_dark
color button_material_light
color call_notification_answer_color
color call_notification_decline_color
color cardview_dark_background
color cardview_light_background
color cardview_shadow_end_color
color cardview_shadow_start_color
color checkbox_themeable_attribute_color
color color_local_pdfbar
color color_tv_file_empty
color design_bottom_navigation_shadow_color
color design_box_stroke_color
color design_dark_default_color_background
color design_dark_default_color_error
color design_dark_default_color_on_background
color design_dark_default_color_on_error
color design_dark_default_color_on_primary
color design_dark_default_color_on_secondary
color design_dark_default_color_on_surface
color design_dark_default_color_primary
color design_dark_default_color_primary_dark
color design_dark_default_color_primary_variant
color design_dark_default_color_secondary
color design_dark_default_color_secondary_variant
color design_dark_default_color_surface
color design_default_color_background
color design_default_color_error
color design_default_color_on_background
color design_default_color_on_error
color design_default_color_on_primary
color design_default_color_on_secondary
color design_default_color_on_surface
color design_default_color_primary
color design_default_color_primary_dark
color design_default_color_primary_variant
color design_default_color_secondary
color design_default_color_secondary_variant
color design_default_color_surface
color design_error
color design_fab_shadow_end_color
color design_fab_shadow_mid_color
color design_fab_shadow_start_color
color design_fab_stroke_end_inner_color
color design_fab_stroke_end_outer_color
color design_fab_stroke_top_inner_color
color design_fab_stroke_top_outer_color
color design_icon_tint
color design_snackbar_background_color
color dialog_bg_color
color dialog_cursor_color
color dialog_editText_bg_color
color dialog_title_color
color dim_foreground_disabled_material_dark
color dim_foreground_disabled_material_light
color dim_foreground_material_dark
color dim_foreground_material_light
color error_color_material_dark
color error_color_material_light
color float_out_color_dark
color float_out_color_img_dark_front
color foreground_material_dark
color foreground_material_light
color fragment_textView_bg_lightBlue
color fragment_textView_bg_normal
color highlighted_text_material_dark
color highlighted_text_material_light
color material_blue_grey_800
color material_blue_grey_900
color material_blue_grey_950
color material_cursor_color
color material_deep_teal_200
color material_deep_teal_500
color material_grey_100
color material_grey_300
color material_grey_50
color material_grey_600
color material_grey_800
color material_grey_850
color material_grey_900
color material_on_background_disabled
color material_on_background_emphasis_high_type
color material_on_background_emphasis_medium
color material_on_primary_disabled
color material_on_primary_emphasis_high_type
color material_on_primary_emphasis_medium
color material_on_surface_disabled
color material_on_surface_emphasis_high_type
color material_on_surface_emphasis_medium
color material_on_surface_stroke
color material_slider_active_tick_marks_color
color material_slider_active_track_color
color material_slider_halo_color
color material_slider_inactive_tick_marks_color
color material_slider_inactive_track_color
color material_slider_thumb_color
color material_timepicker_button_background
color material_timepicker_button_stroke
color material_timepicker_clock_text_color
color material_timepicker_clockface
color material_timepicker_modebutton_tint
color menu_bg_color
color menu_hover_in
color mtrl_bottom_nav_colored_item_tint
color mtrl_bottom_nav_colored_ripple_color
color mtrl_bottom_nav_item_tint
color mtrl_bottom_nav_ripple_color
color mtrl_btn_bg_color_selector
color mtrl_btn_ripple_color
color mtrl_btn_stroke_color_selector
color mtrl_btn_text_btn_bg_color_selector
color mtrl_btn_text_btn_ripple_color
color mtrl_btn_text_color_disabled
color mtrl_btn_text_color_selector
color mtrl_btn_transparent_bg_color
color mtrl_calendar_item_stroke_color
color mtrl_calendar_selected_range
color mtrl_card_view_foreground
color mtrl_card_view_ripple
color mtrl_chip_background_color
color mtrl_chip_close_icon_tint
color mtrl_chip_surface_color
color mtrl_chip_text_color
color mtrl_choice_chip_background_color
color mtrl_choice_chip_ripple_color
color mtrl_choice_chip_text_color
color mtrl_error
color mtrl_fab_bg_color_selector
color mtrl_fab_icon_text_color_selector
color mtrl_fab_ripple_color
color mtrl_filled_background_color
color mtrl_filled_icon_tint
color mtrl_filled_stroke_color
color mtrl_indicator_text_color
color mtrl_navigation_item_background_color
color mtrl_navigation_item_icon_tint
color mtrl_navigation_item_text_color
color mtrl_on_primary_text_btn_text_color_selector
color mtrl_on_surface_ripple_color
color mtrl_outlined_icon_tint
color mtrl_outlined_stroke_color
color mtrl_popupmenu_overlay_color
color mtrl_scrim_color
color mtrl_tabs_colored_ripple_color
color mtrl_tabs_icon_color_selector
color mtrl_tabs_icon_color_selector_colored
color mtrl_tabs_legacy_text_color_selector
color mtrl_tabs_ripple_color
color mtrl_text_btn_text_color_selector
color mtrl_textinput_default_box_stroke_color
color mtrl_textinput_disabled_color
color mtrl_textinput_filled_box_default_background_color
color mtrl_textinput_focused_box_stroke_color
color mtrl_textinput_hovered_box_stroke_color
color notice_read
color notification_action_color_filter
color notification_icon_bg_color
color notification_material_background_media_default_color
color point_yellow
color pop_color
color primary_dark_material_dark
color primary_dark_material_light
color primary_material_dark
color primary_material_light
color primary_text_default_material_dark
color primary_text_default_material_light
color primary_text_disabled_material_dark
color primary_text_disabled_material_light
color radiobutton_themeable_attribute_color
color ripple_material_dark
color ripple_material_light
color secondary_text_default_material_dark
color secondary_text_default_material_light
color secondary_text_disabled_material_dark
color secondary_text_disabled_material_light
color sel_menu_text
color select_color
color switch_thumb_disabled_material_dark
color switch_thumb_disabled_material_light
color switch_thumb_material_dark
color switch_thumb_material_light
color switch_thumb_normal_material_dark
color switch_thumb_normal_material_light
color test_mtrl_calendar_day
color test_mtrl_calendar_day_selected
color thumb_vertical
color tooltip_background_dark
color tooltip_background_light
color transparent
color wallpaper_display_sort_blue
color white
dimen abc_action_bar_content_inset_material
dimen abc_action_bar_content_inset_with_nav
dimen abc_action_bar_default_height_material
dimen abc_action_bar_default_padding_end_material
dimen abc_action_bar_default_padding_start_material
dimen abc_action_bar_elevation_material
dimen abc_action_bar_icon_vertical_padding_material
dimen abc_action_bar_overflow_padding_end_material
dimen abc_action_bar_overflow_padding_start_material
dimen abc_action_bar_stacked_max_height
dimen abc_action_bar_stacked_tab_max_width
dimen abc_action_bar_subtitle_bottom_margin_material
dimen abc_action_bar_subtitle_top_margin_material
dimen abc_action_button_min_height_material
dimen abc_action_button_min_width_material
dimen abc_action_button_min_width_overflow_material
dimen abc_alert_dialog_button_bar_height
dimen abc_alert_dialog_button_dimen
dimen abc_button_inset_horizontal_material
dimen abc_button_inset_vertical_material
dimen abc_button_padding_horizontal_material
dimen abc_button_padding_vertical_material
dimen abc_cascading_menus_min_smallest_width
dimen abc_config_prefDialogWidth
dimen abc_control_corner_material
dimen abc_control_inset_material
dimen abc_control_padding_material
dimen abc_dialog_corner_radius_material
dimen abc_dialog_fixed_height_major
dimen abc_dialog_fixed_height_minor
dimen abc_dialog_fixed_width_major
dimen abc_dialog_fixed_width_minor
dimen abc_dialog_list_padding_bottom_no_buttons
dimen abc_dialog_list_padding_top_no_title
dimen abc_dialog_min_width_major
dimen abc_dialog_min_width_minor
dimen abc_dialog_padding_material
dimen abc_dialog_padding_top_material
dimen abc_dialog_title_divider_material
dimen abc_disabled_alpha_material_dark
dimen abc_disabled_alpha_material_light
dimen abc_dropdownitem_icon_width
dimen abc_dropdownitem_text_padding_left
dimen abc_dropdownitem_text_padding_right
dimen abc_edit_text_inset_bottom_material
dimen abc_edit_text_inset_horizontal_material
dimen abc_edit_text_inset_top_material
dimen abc_floating_window_z
dimen abc_list_item_height_large_material
dimen abc_list_item_height_material
dimen abc_list_item_height_small_material
dimen abc_list_item_padding_horizontal_material
dimen abc_panel_menu_list_width
dimen abc_progress_bar_height_material
dimen abc_search_view_preferred_height
dimen abc_search_view_preferred_width
dimen abc_seekbar_track_background_height_material
dimen abc_seekbar_track_progress_height_material
dimen abc_select_dialog_padding_start_material
dimen abc_star_big
dimen abc_star_medium
dimen abc_star_small
dimen abc_switch_padding
dimen abc_text_size_body_1_material
dimen abc_text_size_body_2_material
dimen abc_text_size_button_material
dimen abc_text_size_caption_material
dimen abc_text_size_display_1_material
dimen abc_text_size_display_2_material
dimen abc_text_size_display_3_material
dimen abc_text_size_display_4_material
dimen abc_text_size_headline_material
dimen abc_text_size_large_material
dimen abc_text_size_medium_material
dimen abc_text_size_menu_header_material
dimen abc_text_size_menu_material
dimen abc_text_size_small_material
dimen abc_text_size_subhead_material
dimen abc_text_size_subtitle_material_toolbar
dimen abc_text_size_title_material
dimen abc_text_size_title_material_toolbar
dimen action_bar_size
dimen appcompat_dialog_background_inset
dimen cardview_compat_inset_shadow
dimen cardview_default_elevation
dimen cardview_default_radius
dimen clock_face_margin_start
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen default_dimension
dimen design_appbar_elevation
dimen design_bottom_navigation_active_item_max_width
dimen design_bottom_navigation_active_item_min_width
dimen design_bottom_navigation_active_text_size
dimen design_bottom_navigation_elevation
dimen design_bottom_navigation_height
dimen design_bottom_navigation_icon_size
dimen design_bottom_navigation_item_max_width
dimen design_bottom_navigation_item_min_width
dimen design_bottom_navigation_label_padding
dimen design_bottom_navigation_margin
dimen design_bottom_navigation_shadow_height
dimen design_bottom_navigation_text_size
dimen design_bottom_sheet_elevation
dimen design_bottom_sheet_modal_elevation
dimen design_bottom_sheet_peek_height_min
dimen design_fab_border_width
dimen design_fab_elevation
dimen design_fab_image_size
dimen design_fab_size_mini
dimen design_fab_size_normal
dimen design_fab_translation_z_hovered_focused
dimen design_fab_translation_z_pressed
dimen design_navigation_elevation
dimen design_navigation_icon_padding
dimen design_navigation_icon_size
dimen design_navigation_item_horizontal_padding
dimen design_navigation_item_icon_padding
dimen design_navigation_max_width
dimen design_navigation_padding_bottom
dimen design_navigation_separator_vertical_padding
dimen design_snackbar_action_inline_max_width
dimen design_snackbar_action_text_color_alpha
dimen design_snackbar_background_corner_radius
dimen design_snackbar_elevation
dimen design_snackbar_extra_spacing_horizontal
dimen design_snackbar_max_width
dimen design_snackbar_min_width
dimen design_snackbar_padding_horizontal
dimen design_snackbar_padding_vertical
dimen design_snackbar_padding_vertical_2lines
dimen design_snackbar_text_size
dimen design_tab_max_width
dimen design_tab_scrollable_min_width
dimen design_tab_text_size
dimen design_tab_text_size_2line
dimen design_textinput_caption_translate_y
dimen disabled_alpha_material_dark
dimen disabled_alpha_material_light
dimen fastscroll_default_thickness
dimen fastscroll_margin
dimen fastscroll_minimum_range
dimen highlight_alpha_material_colored
dimen highlight_alpha_material_dark
dimen highlight_alpha_material_light
dimen hint_alpha_material_dark
dimen hint_alpha_material_light
dimen hint_pressed_alpha_material_dark
dimen hint_pressed_alpha_material_light
dimen item_touch_helper_max_drag_scroll_per_frame
dimen item_touch_helper_swipe_escape_max_velocity
dimen item_touch_helper_swipe_escape_velocity
dimen material_clock_display_padding
dimen material_clock_face_margin_top
dimen material_clock_hand_center_dot_radius
dimen material_clock_hand_padding
dimen material_clock_hand_stroke_width
dimen material_clock_number_text_size
dimen material_clock_period_toggle_height
dimen material_clock_period_toggle_margin_left
dimen material_clock_period_toggle_width
dimen material_clock_size
dimen material_cursor_inset_bottom
dimen material_cursor_inset_top
dimen material_cursor_width
dimen material_emphasis_disabled
dimen material_emphasis_high_type
dimen material_emphasis_medium
dimen material_filled_edittext_font_1_3_padding_bottom
dimen material_filled_edittext_font_1_3_padding_top
dimen material_filled_edittext_font_2_0_padding_bottom
dimen material_filled_edittext_font_2_0_padding_top
dimen material_font_1_3_box_collapsed_padding_top
dimen material_font_2_0_box_collapsed_padding_top
dimen material_helper_text_default_padding_top
dimen material_helper_text_font_1_3_padding_horizontal
dimen material_helper_text_font_1_3_padding_top
dimen material_input_text_to_prefix_suffix_padding
dimen material_text_view_test_line_height
dimen material_text_view_test_line_height_override
dimen material_timepicker_dialog_buttons_margin_top
dimen mtrl_alert_dialog_background_inset_bottom
dimen mtrl_alert_dialog_background_inset_end
dimen mtrl_alert_dialog_background_inset_start
dimen mtrl_alert_dialog_background_inset_top
dimen mtrl_alert_dialog_picker_background_inset
dimen mtrl_badge_horizontal_edge_offset
dimen mtrl_badge_long_text_horizontal_padding
dimen mtrl_badge_radius
dimen mtrl_badge_text_horizontal_edge_offset
dimen mtrl_badge_text_size
dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset
dimen mtrl_badge_toolbar_action_menu_item_vertical_offset
dimen mtrl_badge_with_text_radius
dimen mtrl_bottomappbar_fabOffsetEndMode
dimen mtrl_bottomappbar_fab_bottom_margin
dimen mtrl_bottomappbar_fab_cradle_margin
dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius
dimen mtrl_bottomappbar_fab_cradle_vertical_offset
dimen mtrl_bottomappbar_height
dimen mtrl_btn_corner_radius
dimen mtrl_btn_dialog_btn_min_width
dimen mtrl_btn_disabled_elevation
dimen mtrl_btn_disabled_z
dimen mtrl_btn_elevation
dimen mtrl_btn_focused_z
dimen mtrl_btn_hovered_z
dimen mtrl_btn_icon_btn_padding_left
dimen mtrl_btn_icon_padding
dimen mtrl_btn_inset
dimen mtrl_btn_letter_spacing
dimen mtrl_btn_padding_bottom
dimen mtrl_btn_padding_left
dimen mtrl_btn_padding_right
dimen mtrl_btn_padding_top
dimen mtrl_btn_pressed_z
dimen mtrl_btn_snackbar_margin_horizontal
dimen mtrl_btn_stroke_size
dimen mtrl_btn_text_btn_icon_padding
dimen mtrl_btn_text_btn_padding_left
dimen mtrl_btn_text_btn_padding_right
dimen mtrl_btn_text_size
dimen mtrl_btn_z
dimen mtrl_calendar_action_confirm_button_min_width
dimen mtrl_calendar_action_height
dimen mtrl_calendar_action_padding
dimen mtrl_calendar_bottom_padding
dimen mtrl_calendar_content_padding
dimen mtrl_calendar_day_corner
dimen mtrl_calendar_day_height
dimen mtrl_calendar_day_horizontal_padding
dimen mtrl_calendar_day_today_stroke
dimen mtrl_calendar_day_vertical_padding
dimen mtrl_calendar_day_width
dimen mtrl_calendar_days_of_week_height
dimen mtrl_calendar_dialog_background_inset
dimen mtrl_calendar_header_content_padding
dimen mtrl_calendar_header_content_padding_fullscreen
dimen mtrl_calendar_header_divider_thickness
dimen mtrl_calendar_header_height
dimen mtrl_calendar_header_height_fullscreen
dimen mtrl_calendar_header_selection_line_height
dimen mtrl_calendar_header_text_padding
dimen mtrl_calendar_header_toggle_margin_bottom
dimen mtrl_calendar_header_toggle_margin_top
dimen mtrl_calendar_landscape_header_width
dimen mtrl_calendar_maximum_default_fullscreen_minor_axis
dimen mtrl_calendar_month_horizontal_padding
dimen mtrl_calendar_month_vertical_padding
dimen mtrl_calendar_navigation_bottom_padding
dimen mtrl_calendar_navigation_height
dimen mtrl_calendar_navigation_top_padding
dimen mtrl_calendar_pre_l_text_clip_padding
dimen mtrl_calendar_selection_baseline_to_top_fullscreen
dimen mtrl_calendar_selection_text_baseline_to_bottom
dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen
dimen mtrl_calendar_selection_text_baseline_to_top
dimen mtrl_calendar_text_input_padding_top
dimen mtrl_calendar_title_baseline_to_top
dimen mtrl_calendar_title_baseline_to_top_fullscreen
dimen mtrl_calendar_year_corner
dimen mtrl_calendar_year_height
dimen mtrl_calendar_year_horizontal_padding
dimen mtrl_calendar_year_vertical_padding
dimen mtrl_calendar_year_width
dimen mtrl_card_checked_icon_margin
dimen mtrl_card_checked_icon_size
dimen mtrl_card_corner_radius
dimen mtrl_card_dragged_z
dimen mtrl_card_elevation
dimen mtrl_card_spacing
dimen mtrl_chip_pressed_translation_z
dimen mtrl_chip_text_size
dimen mtrl_edittext_rectangle_top_offset
dimen mtrl_exposed_dropdown_menu_popup_elevation
dimen mtrl_exposed_dropdown_menu_popup_vertical_offset
dimen mtrl_exposed_dropdown_menu_popup_vertical_padding
dimen mtrl_extended_fab_bottom_padding
dimen mtrl_extended_fab_corner_radius
dimen mtrl_extended_fab_disabled_elevation
dimen mtrl_extended_fab_disabled_translation_z
dimen mtrl_extended_fab_elevation
dimen mtrl_extended_fab_end_padding
dimen mtrl_extended_fab_end_padding_icon
dimen mtrl_extended_fab_icon_size
dimen mtrl_extended_fab_icon_text_spacing
dimen mtrl_extended_fab_min_height
dimen mtrl_extended_fab_min_width
dimen mtrl_extended_fab_start_padding
dimen mtrl_extended_fab_start_padding_icon
dimen mtrl_extended_fab_top_padding
dimen mtrl_extended_fab_translation_z_base
dimen mtrl_extended_fab_translation_z_hovered_focused
dimen mtrl_extended_fab_translation_z_pressed
dimen mtrl_fab_elevation
dimen mtrl_fab_min_touch_target
dimen mtrl_fab_translation_z_hovered_focused
dimen mtrl_fab_translation_z_pressed
dimen mtrl_high_ripple_default_alpha
dimen mtrl_high_ripple_focused_alpha
dimen mtrl_high_ripple_hovered_alpha
dimen mtrl_high_ripple_pressed_alpha
dimen mtrl_large_touch_target
dimen mtrl_low_ripple_default_alpha
dimen mtrl_low_ripple_focused_alpha
dimen mtrl_low_ripple_hovered_alpha
dimen mtrl_low_ripple_pressed_alpha
dimen mtrl_min_touch_target_size
dimen mtrl_navigation_elevation
dimen mtrl_navigation_item_horizontal_padding
dimen mtrl_navigation_item_icon_padding
dimen mtrl_navigation_item_icon_size
dimen mtrl_navigation_item_shape_horizontal_margin
dimen mtrl_navigation_item_shape_vertical_margin
dimen mtrl_progress_circular_inset
dimen mtrl_progress_circular_inset_extra_small
dimen mtrl_progress_circular_inset_medium
dimen mtrl_progress_circular_inset_small
dimen mtrl_progress_circular_radius
dimen mtrl_progress_circular_size
dimen mtrl_progress_circular_size_extra_small
dimen mtrl_progress_circular_size_medium
dimen mtrl_progress_circular_size_small
dimen mtrl_progress_circular_track_thickness_extra_small
dimen mtrl_progress_circular_track_thickness_medium
dimen mtrl_progress_circular_track_thickness_small
dimen mtrl_progress_indicator_full_rounded_corner_radius
dimen mtrl_progress_track_thickness
dimen mtrl_shape_corner_size_large_component
dimen mtrl_shape_corner_size_medium_component
dimen mtrl_shape_corner_size_small_component
dimen mtrl_slider_halo_radius
dimen mtrl_slider_label_padding
dimen mtrl_slider_label_radius
dimen mtrl_slider_label_square_side
dimen mtrl_slider_thumb_elevation
dimen mtrl_slider_thumb_radius
dimen mtrl_slider_track_height
dimen mtrl_slider_track_side_padding
dimen mtrl_slider_track_top
dimen mtrl_slider_widget_height
dimen mtrl_snackbar_action_text_color_alpha
dimen mtrl_snackbar_background_corner_radius
dimen mtrl_snackbar_background_overlay_color_alpha
dimen mtrl_snackbar_margin
dimen mtrl_snackbar_message_margin_horizontal
dimen mtrl_snackbar_padding_horizontal
dimen mtrl_switch_thumb_elevation
dimen mtrl_textinput_box_corner_radius_medium
dimen mtrl_textinput_box_corner_radius_small
dimen mtrl_textinput_box_label_cutout_padding
dimen mtrl_textinput_box_stroke_width_default
dimen mtrl_textinput_box_stroke_width_focused
dimen mtrl_textinput_counter_margin_start
dimen mtrl_textinput_end_icon_margin_start
dimen mtrl_textinput_outline_box_expanded_padding
dimen mtrl_textinput_start_icon_margin_end
dimen mtrl_toolbar_default_height
dimen mtrl_tooltip_arrowSize
dimen mtrl_tooltip_cornerSize
dimen mtrl_tooltip_minHeight
dimen mtrl_tooltip_minWidth
dimen mtrl_tooltip_padding
dimen mtrl_transition_shared_axis_slide_distance
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
dimen subtitle_corner_radius
dimen subtitle_outline_width
dimen subtitle_shadow_offset
dimen subtitle_shadow_radius
dimen test_mtrl_calendar_day_cornerSize
dimen tooltip_corner_radius
dimen tooltip_horizontal_padding
dimen tooltip_margin
dimen tooltip_precise_anchor_extra_offset
dimen tooltip_precise_anchor_threshold
dimen tooltip_vertical_padding
dimen tooltip_y_offset_non_touch
dimen tooltip_y_offset_touch
dimen tvWifiSavedTextSize
drawable abc_ab_share_pack_mtrl_alpha
drawable abc_action_bar_item_background_material
drawable abc_btn_borderless_material
drawable abc_btn_check_material
drawable abc_btn_check_material_anim
drawable abc_btn_check_to_on_mtrl_000
drawable abc_btn_check_to_on_mtrl_015
drawable abc_btn_colored_material
drawable abc_btn_default_mtrl_shape
drawable abc_btn_radio_material
drawable abc_btn_radio_material_anim
drawable abc_btn_radio_to_on_mtrl_000
drawable abc_btn_radio_to_on_mtrl_015
drawable abc_btn_switch_to_on_mtrl_00001
drawable abc_btn_switch_to_on_mtrl_00012
drawable abc_cab_background_internal_bg
drawable abc_cab_background_top_material
drawable abc_cab_background_top_mtrl_alpha
drawable abc_control_background_material
drawable abc_dialog_material_background
drawable abc_edit_text_material
drawable abc_ic_ab_back_material
drawable abc_ic_arrow_drop_right_black_24dp
drawable abc_ic_clear_material
drawable abc_ic_commit_search_api_mtrl_alpha
drawable abc_ic_go_search_api_material
drawable abc_ic_menu_copy_mtrl_am_alpha
drawable abc_ic_menu_cut_mtrl_alpha
drawable abc_ic_menu_overflow_material
drawable abc_ic_menu_paste_mtrl_am_alpha
drawable abc_ic_menu_selectall_mtrl_alpha
drawable abc_ic_menu_share_mtrl_alpha
drawable abc_ic_search_api_material
drawable abc_ic_star_black_16dp
drawable abc_ic_star_black_36dp
drawable abc_ic_star_black_48dp
drawable abc_ic_star_half_black_16dp
drawable abc_ic_star_half_black_36dp
drawable abc_ic_star_half_black_48dp
drawable abc_ic_voice_search_api_material
drawable abc_item_background_holo_dark
drawable abc_item_background_holo_light
drawable abc_list_divider_material
drawable abc_list_divider_mtrl_alpha
drawable abc_list_focused_holo
drawable abc_list_longpressed_holo
drawable abc_list_pressed_holo_dark
drawable abc_list_pressed_holo_light
drawable abc_list_selector_background_transition_holo_dark
drawable abc_list_selector_background_transition_holo_light
drawable abc_list_selector_disabled_holo_dark
drawable abc_list_selector_disabled_holo_light
drawable abc_list_selector_holo_dark
drawable abc_list_selector_holo_light
drawable abc_menu_hardkey_panel_mtrl_mult
drawable abc_popup_background_mtrl_mult
drawable abc_ratingbar_indicator_material
drawable abc_ratingbar_material
drawable abc_ratingbar_small_material
drawable abc_scrubber_control_off_mtrl_alpha
drawable abc_scrubber_control_to_pressed_mtrl_000
drawable abc_scrubber_control_to_pressed_mtrl_005
drawable abc_scrubber_primary_mtrl_alpha
drawable abc_scrubber_track_mtrl_alpha
drawable abc_seekbar_thumb_material
drawable abc_seekbar_tick_mark_material
drawable abc_seekbar_track_material
drawable abc_spinner_mtrl_am_alpha
drawable abc_spinner_textfield_background_material
drawable abc_star_black_48dp
drawable abc_star_half_black_48dp
drawable abc_switch_thumb_material
drawable abc_switch_track_mtrl_alpha
drawable abc_tab_indicator_material
drawable abc_tab_indicator_mtrl_alpha
drawable abc_text_cursor_material
drawable abc_text_select_handle_left_mtrl
drawable abc_text_select_handle_left_mtrl_dark
drawable abc_text_select_handle_left_mtrl_light
drawable abc_text_select_handle_middle_mtrl
drawable abc_text_select_handle_middle_mtrl_dark
drawable abc_text_select_handle_middle_mtrl_light
drawable abc_text_select_handle_right_mtrl
drawable abc_text_select_handle_right_mtrl_dark
drawable abc_text_select_handle_right_mtrl_light
drawable abc_textfield_activated_mtrl_alpha
drawable abc_textfield_default_mtrl_alpha
drawable abc_textfield_search_activated_mtrl_alpha
drawable abc_textfield_search_default_mtrl_alpha
drawable abc_textfield_search_material
drawable abc_vector_test
drawable avd_hide_password
drawable avd_show_password
drawable base_lib_ic_eye_close
drawable base_lib_ic_eye_open
drawable baselib_bg_pop_tip
drawable baselib_bg_pop_tip_blue
drawable baselib_bg_pop_tip_light_blue
drawable baselib_icon_back
drawable baselib_icon_msg
drawable baselib_icon_selected
drawable baselib_icon_user
drawable baselib_icon_wifi
drawable baselib_icon_wifi_bad
drawable baselib_icon_wifi_detail
drawable baselib_icon_wifi_lock
drawable baselib_icon_wifi_mid
drawable baselib_icon_wifi_weak
drawable bg_common
drawable bg_pdf_pop_list
drawable bg_pdf_pop_stroke
drawable btn_checkbox_checked_mtrl
drawable btn_checkbox_checked_to_unchecked_mtrl_animation
drawable btn_checkbox_unchecked_mtrl
drawable btn_checkbox_unchecked_to_checked_mtrl_animation
drawable btn_radio_off_mtrl
drawable btn_radio_off_to_on_mtrl_animation
drawable btn_radio_on_mtrl
drawable btn_radio_on_to_off_mtrl_animation
drawable design_bottom_navigation_item_background
drawable design_fab_background
drawable design_ic_visibility
drawable design_ic_visibility_off
drawable design_password_eye
drawable design_snackbar_background
drawable drawable_base_cursor
drawable file_icon_share_checked
drawable file_icon_share_unchecked
drawable ic_app_icon_def
drawable ic_call_answer
drawable ic_call_answer_low
drawable ic_call_answer_video
drawable ic_call_answer_video_low
drawable ic_call_decline
drawable ic_call_decline_low
drawable ic_clock_black_24dp
drawable ic_ethernet_unidentified
drawable ic_file_empty
drawable ic_hook
drawable ic_keyboard_black_24dp
drawable ic_mtrl_checked_circle
drawable ic_mtrl_chip_checked_black
drawable ic_mtrl_chip_checked_circle
drawable ic_mtrl_chip_close_circle
drawable ic_page_down
drawable ic_page_down_dis
drawable ic_page_up
drawable ic_page_up_dis
drawable ic_pdf_pop
drawable ic_preview_back
drawable ic_status_ethernet
drawable ic_status_no_network
drawable ic_toast_fail
drawable ic_toast_success
drawable ic_zoom_in
drawable ic_zoom_in_dis
drawable ic_zoom_out
drawable ic_zoom_out_dis
drawable material_cursor_drawable
drawable material_ic_calendar_black_24dp
drawable material_ic_clear_black_24dp
drawable material_ic_edit_black_24dp
drawable material_ic_keyboard_arrow_left_black_24dp
drawable material_ic_keyboard_arrow_next_black_24dp
drawable material_ic_keyboard_arrow_previous_black_24dp
drawable material_ic_keyboard_arrow_right_black_24dp
drawable material_ic_menu_arrow_down_black_24dp
drawable material_ic_menu_arrow_up_black_24dp
drawable mtrl_dialog_background
drawable mtrl_dropdown_arrow
drawable mtrl_ic_arrow_drop_down
drawable mtrl_ic_arrow_drop_up
drawable mtrl_ic_cancel
drawable mtrl_ic_error
drawable mtrl_popupmenu_background
drawable mtrl_popupmenu_background_dark
drawable mtrl_tabs_default_indicator
drawable navigation_empty_icon
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_oversize_large_icon_bg
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
drawable page_down_enable
drawable page_up_enable
drawable page_zoom_in_enable
drawable page_zoom_out_enable
drawable sel_list_menu
drawable skip_arrow_right
drawable test_custom_background
drawable test_level_drawable
drawable tooltip_frame_dark
drawable tooltip_frame_light
drawable wifi_setting_select
drawable wifi_signal
id BOTTOM_END
id BOTTOM_START
id NO_DEBUG
id SHOW_ALL
id SHOW_PATH
id SHOW_PROGRESS
id TOP_END
id TOP_START
id above
id accelerate
id accessibility_action_clickable_span
id accessibility_custom_action_0
id accessibility_custom_action_1
id accessibility_custom_action_10
id accessibility_custom_action_11
id accessibility_custom_action_12
id accessibility_custom_action_13
id accessibility_custom_action_14
id accessibility_custom_action_15
id accessibility_custom_action_16
id accessibility_custom_action_17
id accessibility_custom_action_18
id accessibility_custom_action_19
id accessibility_custom_action_2
id accessibility_custom_action_20
id accessibility_custom_action_21
id accessibility_custom_action_22
id accessibility_custom_action_23
id accessibility_custom_action_24
id accessibility_custom_action_25
id accessibility_custom_action_26
id accessibility_custom_action_27
id accessibility_custom_action_28
id accessibility_custom_action_29
id accessibility_custom_action_3
id accessibility_custom_action_30
id accessibility_custom_action_31
id accessibility_custom_action_4
id accessibility_custom_action_5
id accessibility_custom_action_6
id accessibility_custom_action_7
id accessibility_custom_action_8
id accessibility_custom_action_9
id action0
id actionDown
id actionDownUp
id actionUp
id action_bar
id action_bar_activity_content
id action_bar_container
id action_bar_root
id action_bar_spinner
id action_bar_subtitle
id action_bar_title
id action_container
id action_context_bar
id action_divider
id action_image
id action_menu_divider
id action_menu_presenter
id action_mode_bar
id action_mode_bar_stub
id action_mode_close_button
id action_text
id actions
id activity_chooser_view_content
id add
id agentweb_webview_id
id alertTitle
id aligned
id allStates
id animateToEnd
id animateToStart
id antiClockwise
id anticipate
id asConfigured
id async
id auto
id autoComplete
id autoCompleteToEnd
id autoCompleteToStart
id axisRelative
id barrier
id baseline
id below
id bestChoice
id blocking
id blue
id blue2
id bottom
id bounce
id buttonPanel
id callMeasure
id cancel_action
id cancel_button
id carryVelocity
id center
id chain
id chain2
id checkbox
id checked
id chip
id chip1
id chip2
id chip3
id chip_group
id chronometer
id circle_center
id clear_text
id clockwise
id closest
id column
id column_reverse
id confirm_button
id constraint
id container
id content
id contentLayout
id contentPanel
id contiguous
id continuousVelocity
id coordinator
id cos
id counterclockwise
id currentState
id custom
id customPanel
id cut
id dark
id dark2
id dark3
id date_picker_actions
id decelerate
id decelerateAndComplete
id decor_content_parent
id default_activity_button
id deltaRelative
id design_bottom_sheet
id design_menu_item_action_area
id design_menu_item_action_area_stub
id design_menu_item_text
id design_navigation_view
id dialog_button
id disjoint
id doubleBtnFloatBtn
id doubleBtnFloatCancelBtn
id doubleBtnFloatConfirmBtn
id doubleBtnFloatContentTv
id doubleBtnFloatNotesContentTv
id doubleBtnFloatTitleTv
id dragAnticlockwise
id dragClockwise
id dragDown
id dragEnd
id dragLeft
id dragRight
id dragStart
id dragUp
id dropdown_menu
id easeIn
id easeInOut
id easeOut
id east
id editText
id edit_query
id edit_text_id
id elastic
id end
id endToStart
id end_padder
id expand_activities_button
id expanded_menu
id fade
id fill
id filled
id fixed
id flex_end
id flex_start
id flip
id floatDarkBg
id floatImgBgView
id floatOutSide
id floating
id floatingBgFrameLayout
id floatingDarkBgView
id floatingDarkFrontView
id forever
id fragment_container_view_tag
id frost
id ghost_view
id ghost_view_holder
id glide_custom_view_target_tag
id gone
id grey
id group_divider
id guideline
id header_title
id hide_ime_id
id holdView
id home
id honorRequest
id horizontal
id horizontal_only
id icon
id icon_group
id ignore
id ignoreRequest
id image
id imgDarkFront
id immediateStop
id included
id indexWordGroup
id info
id invisible
id inward
id italic
id item_touch_helper_previous_elevation
id item_view
id iv_back
id iv_selected
id iv_selected_settings
id iv_wifi_details
id iv_wifi_lock
id iv_wifi_status
id jumpToEnd
id jumpToStart
id labelGroup
id labeled
id largeLabel
id layout
id left
id leftToRight
id light
id lightBlue
id line
id line1
id line3
id linear
id listMode
id list_item
id mainframe_error_container_id
id mainframe_error_viewsub_id
id masked
id match_constraint
id match_parent
id material_clock_display
id material_clock_face
id material_clock_hand
id material_clock_period_am_button
id material_clock_period_pm_button
id material_clock_period_toggle
id material_hour_text_input
id material_hour_tv
id material_label
id material_minute_text_input
id material_minute_tv
id material_textinput_timepicker
id material_timepicker_cancel_button
id material_timepicker_container
id material_timepicker_edit_text
id material_timepicker_mode_button
id material_timepicker_ok_button
id material_timepicker_view
id material_value_index
id media_actions
id message
id middle
id mini
id month_grid
id month_navigation_bar
id month_navigation_fragment_toggle
id month_navigation_next
id month_navigation_previous
id month_title
id motion_base
id mtrl_anchor_parent
id mtrl_calendar_day_selector_frame
id mtrl_calendar_days_of_week
id mtrl_calendar_frame
id mtrl_calendar_main_pane
id mtrl_calendar_months
id mtrl_calendar_selection_frame
id mtrl_calendar_text_input_frame
id mtrl_calendar_year_selector_frame
id mtrl_card_checked_layer_id
id mtrl_child_content_container
id mtrl_internal_children_alpha_tag
id mtrl_motion_snapshot_view
id mtrl_picker_fullscreen
id mtrl_picker_header
id mtrl_picker_header_selection_text
id mtrl_picker_header_title_and_selection
id mtrl_picker_header_toggle
id mtrl_picker_text_input_date
id mtrl_picker_text_input_range_end
id mtrl_picker_text_input_range_start
id mtrl_picker_title_text
id mtrl_view_tag_bottom_padding
id multiply
id navigation_header_container
id neverCompleteToEnd
id neverCompleteToStart
id noState
id none
id normal
id normalDialogCancelBtn
id normalDialogConfirmBtn
id normalDialogContentTv
id normalDialogTitleTv
id north
id notification_background
id notification_main_column
id notification_main_column_container
id notifyBarClockTv
id notifyBarMsgIv
id notifyBarUnReadPoint
id notifyBarUserIv
id notifyBarUserLayer
id notifyBarUserTv
id notifyBarWifiIv
id nowrap
id off
id on
id outline
id outward
id oval
id overshoot
id packed
id parallax
id parent
id parentPanel
id parentRelative
id parent_matrix
id password_toggle
id path
id pathRelative
id percent
id pin
id popup_listview
id position
id postLayout
id progress_bar
id progress_circular
id progress_horizontal
id radial
id radio
id rectangle
id rectangles
id red
id report_drawn
id reverseSawtooth
id right
id rightToLeft
id right_icon
id right_side
id ring
id rounded
id row
id row_index_key
id row_reverse
id rv_words
id save_non_transition_alpha
id save_overlay_view
id sawtooth
id scale
id screen
id scrollIndicatorDown
id scrollIndicatorUp
id scrollView
id scrollable
id search_badge
id search_bar
id search_button
id search_close_btn
id search_edit_frame
id search_go_btn
id search_mag_icon
id search_plate
id search_src_text
id search_voice_btn
id selectSettingView
id select_dialog_listview
id selected
id selection_type
id setting
id sharedValueSet
id sharedValueUnset
id shortcut
id simpleStatusBarWifi
id sin
id singleBtnFloatConfirmBtn
id singleBtnFloatContentTv
id singleBtnFloatTitleTv
id skipped
id slide
id smallLabel
id snackbar_action
id snackbar_text
id south
id space_around
id space_between
id space_evenly
id spacer
id special_effects_controller_view_tag
id spline
id split_action_bar
id spread
id spread_inside
id spring
id square
id src_atop
id src_in
id src_over
id standard
id start
id startHorizontal
id startToEnd
id startVertical
id staticLayout
id staticPostLayout
id status_bar_latest_event_content
id stop
id stretch
id submenuarrow
id submit_area
id sweep
id switchCircleView
id switchOFF
id switchON
id tabMode
id tag_accessibility_actions
id tag_accessibility_clickable_spans
id tag_accessibility_heading
id tag_accessibility_pane_title
id tag_float_tip_custom
id tag_float_tip_xml
id tag_item_img_url
id tag_on_apply_window_listener
id tag_on_receive_content_listener
id tag_on_receive_content_mime_types
id tag_screen_reader_focusable
id tag_state_description
id tag_transition_group
id tag_trigger_time
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id tag_window_insets_animation_callback
id test_checkbox_android_button_tint
id test_checkbox_app_button_tint
id test_radiobutton_android_button_tint
id test_radiobutton_app_button_tint
id text
id text2
id textSpacerNoButtons
id textSpacerNoTitle
id textView
id text_input_end_icon
id text_input_error_icon
id text_input_start_icon
id textinput_counter
id textinput_error
id textinput_helper_text
id textinput_placeholder
id textinput_prefix_text
id textinput_suffix_text
id time
id title
id titleDividerNoCustom
id title_template
id toastImgIv
id toastMsgTv
id top
id topPanel
id touch_outside
id transition_current_scene
id transition_layout_save
id transition_position
id transition_scene_layoutid_cache
id transition_transform
id triangle
id tvWords
id tv_code
id tv_text
id tv_title
id tv_wifi_conneted_state
id tv_wifi_name
id tv_wifi_saved
id tv_wifi_set_portal
id unchecked
id uniform
id unlabeled
id up
id userFlow
id vertical
id vertical_only
id view_offset_helper
id view_transition
id view_tree_lifecycle_owner
id view_tree_on_back_pressed_dispatcher_owner
id view_tree_saved_state_registry_owner
id view_tree_view_model_store_owner
id visible
id visible_removing_fragment_view_tag
id web_parent_layout_id
id west
id white
id white2
id withinBounds
id wrap
id wrap_content
id wrap_content_constrained
id wrap_reverse
id x_left
id x_right
id zero_corner_chip
integer abc_config_activityDefaultDur
integer abc_config_activityShortDur
integer app_bar_elevation_anim_duration
integer bottom_sheet_slide_duration
integer cancel_button_image_alpha
integer config_tooltipAnimTime
integer design_snackbar_text_max_lines
integer design_tab_indicator_anim_duration_ms
integer hide_password_duration
integer mtrl_badge_max_character_count
integer mtrl_btn_anim_delay_ms
integer mtrl_btn_anim_duration_ms
integer mtrl_calendar_header_orientation
integer mtrl_calendar_selection_text_lines
integer mtrl_calendar_year_selector_span
integer mtrl_card_anim_delay_ms
integer mtrl_card_anim_duration_ms
integer mtrl_chip_anim_duration
integer mtrl_tab_indicator_anim_duration_ms
integer show_password_duration
integer status_bar_notification_info_maxnum
interpolator btn_checkbox_checked_mtrl_animation_interpolator_0
interpolator btn_checkbox_checked_mtrl_animation_interpolator_1
interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0
interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1
interpolator btn_radio_to_off_mtrl_animation_interpolator_0
interpolator btn_radio_to_on_mtrl_animation_interpolator_0
interpolator fast_out_slow_in
interpolator mtrl_fast_out_linear_in
interpolator mtrl_fast_out_slow_in
interpolator mtrl_linear
interpolator mtrl_linear_out_slow_in
layout abc_action_bar_title_item
layout abc_action_bar_up_container
layout abc_action_menu_item_layout
layout abc_action_menu_layout
layout abc_action_mode_bar
layout abc_action_mode_close_item_material
layout abc_activity_chooser_view
layout abc_activity_chooser_view_list_item
layout abc_alert_dialog_button_bar_material
layout abc_alert_dialog_material
layout abc_alert_dialog_title_material
layout abc_cascading_menu_item_layout
layout abc_dialog_title_material
layout abc_expanded_menu_layout
layout abc_list_menu_item_checkbox
layout abc_list_menu_item_icon
layout abc_list_menu_item_layout
layout abc_list_menu_item_radio
layout abc_popup_menu_header_item_layout
layout abc_popup_menu_item_layout
layout abc_screen_content_include
layout abc_screen_simple
layout abc_screen_simple_overlay_action_mode
layout abc_screen_toolbar
layout abc_search_dropdown_item_icons_2line
layout abc_search_view
layout abc_select_dialog_material
layout abc_tooltip
layout agentweb_error_page
layout baselib_item_mobile_code
layout baselib_item_word
layout baselib_pop_tip
layout baselib_pop_tip_blue
layout baselib_pop_tip_light_blue
layout baselib_widget_edittextview
layout baselib_widget_notify_bar
layout baselib_widget_title_bar
layout baselib_widget_wifi_item
layout baselib_widget_wordindex
layout common_float_double_btn
layout common_float_install_loading
layout common_float_single_btn
layout custom_dialog
layout design_bottom_navigation_item
layout design_bottom_sheet_dialog
layout design_layout_snackbar
layout design_layout_snackbar_include
layout design_layout_tab_icon
layout design_layout_tab_text
layout design_menu_item_action_area
layout design_navigation_item
layout design_navigation_item_header
layout design_navigation_item_separator
layout design_navigation_item_subheader
layout design_navigation_menu
layout design_navigation_menu_item
layout design_text_input_end_icon
layout design_text_input_start_icon
layout dialog_loading
layout dialog_normal
layout floating_cz_loading
layout fragment_czvb_floating
layout fragment_float
layout ime_base_split_test_activity
layout ime_secondary_split_test_activity
layout material_chip_input_combo
layout material_clock_display
layout material_clock_display_divider
layout material_clock_period_toggle
layout material_clock_period_toggle_land
layout material_clockface_textview
layout material_clockface_view
layout material_radial_view_group
layout material_textinput_timepicker
layout material_time_chip
layout material_time_input
layout material_timepicker
layout material_timepicker_dialog
layout material_timepicker_textinput_display
layout mtrl_alert_dialog
layout mtrl_alert_dialog_actions
layout mtrl_alert_dialog_title
layout mtrl_alert_select_dialog_item
layout mtrl_alert_select_dialog_multichoice
layout mtrl_alert_select_dialog_singlechoice
layout mtrl_calendar_day
layout mtrl_calendar_day_of_week
layout mtrl_calendar_days_of_week
layout mtrl_calendar_horizontal
layout mtrl_calendar_month
layout mtrl_calendar_month_labeled
layout mtrl_calendar_month_navigation
layout mtrl_calendar_months
layout mtrl_calendar_vertical
layout mtrl_calendar_year
layout mtrl_layout_snackbar
layout mtrl_layout_snackbar_include
layout mtrl_picker_actions
layout mtrl_picker_dialog
layout mtrl_picker_fullscreen
layout mtrl_picker_header_dialog
layout mtrl_picker_header_fullscreen
layout mtrl_picker_header_selection_text
layout mtrl_picker_header_title_text
layout mtrl_picker_header_toggle
layout mtrl_picker_text_input_date
layout mtrl_picker_text_input_date_range
layout notification_action
layout notification_action_tombstone
layout notification_media_action
layout notification_media_cancel_action
layout notification_template_big_media
layout notification_template_big_media_custom
layout notification_template_big_media_narrow
layout notification_template_big_media_narrow_custom
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_lines_media
layout notification_template_media
layout notification_template_media_custom
layout notification_template_part_chronometer
layout notification_template_part_time
layout popup_pdf_item
layout popup_pdf_list
layout select_dialog_item_material
layout select_dialog_multichoice_material
layout select_dialog_singlechoice_material
layout support_simple_spinner_dropdown_item
layout test_action_chip
layout test_chip_zero_corner_radius
layout test_design_checkbox
layout test_design_radiobutton
layout test_reflow_chipgroup
layout test_toolbar
layout test_toolbar_custom_background
layout test_toolbar_elevation
layout test_toolbar_surface
layout text_view_with_line_height_from_appearance
layout text_view_with_line_height_from_layout
layout text_view_with_line_height_from_style
layout text_view_with_theme_line_height
layout text_view_without_line_height
layout toast_pdf
layout toast_simple
layout view_cz_loading
layout widget_simple_status_bar
layout widget_switch
plurals mtrl_badge_content_description
string abc_action_bar_home_description
string abc_action_bar_up_description
string abc_action_menu_overflow_description
string abc_action_mode_done
string abc_activity_chooser_view_see_all
string abc_activitychooserview_choose_application
string abc_capital_off
string abc_capital_on
string abc_menu_alt_shortcut_label
string abc_menu_ctrl_shortcut_label
string abc_menu_delete_shortcut_label
string abc_menu_enter_shortcut_label
string abc_menu_function_shortcut_label
string abc_menu_meta_shortcut_label
string abc_menu_shift_shortcut_label
string abc_menu_space_shortcut_label
string abc_menu_sym_shortcut_label
string abc_prepend_shortcut_label
string abc_search_hint
string abc_searchview_description_clear
string abc_searchview_description_query
string abc_searchview_description_search
string abc_searchview_description_submit
string abc_searchview_description_voice
string abc_shareactionprovider_share_with
string abc_shareactionprovider_share_with_application
string abc_toolbar_collapse_description
string agentweb_camera
string agentweb_cancel
string agentweb_click_open
string agentweb_coming_soon_download
string agentweb_continue
string agentweb_current_downloaded_length
string agentweb_current_downloading_progress
string agentweb_default_page_error
string agentweb_download
string agentweb_download_fail
string agentweb_download_task_has_been_exist
string agentweb_file_chooser
string agentweb_file_download
string agentweb_honeycomblow
string agentweb_leave
string agentweb_leave_app_and_go_other_page
string agentweb_loading
string agentweb_max_file_length_limit
string agentweb_message_show_continue
string agentweb_message_show_ssl_error
string agentweb_message_show_ssl_expired
string agentweb_message_show_ssl_hostname_mismatch
string agentweb_message_show_ssl_not_yet_valid
string agentweb_message_show_ssl_untrusted
string agentweb_tips
string agentweb_title_ssl_error
string agentweb_trickter
string androidx_startup
string app_name
string app_name_army_share_screen
string appbar_scrolling_view_behavior
string baselib_alert_dialog_no_prompt
string baselib_alert_dialog_title
string baselib_app_name
string baselib_network_error
string baselib_title_bar_back
string baselib_wifi_connected
string baselib_wifi_connecting
string baselib_wifi_conneted_failed
string baselib_wifi_saved
string baselib_wifi_set_portal
string bottom_sheet_behavior
string bottomsheet_action_expand_halfway
string call_notification_answer_action
string call_notification_answer_video_action
string call_notification_decline_action
string call_notification_hang_up_action
string call_notification_incoming_text
string call_notification_ongoing_text
string call_notification_screening_text
string character_counter_content_description
string character_counter_overflowed_content_description
string character_counter_pattern
string chip_text
string clear_text_end_icon_content_description
string comma
string dialog_byom_camera_mic_occupy_hint
string dialog_normal_cancel
string dialog_normal_confirm
string dialog_normal_know_it
string dialog_normal_title_tips
string dialog_title_newByom_request
string error_icon_content_description
string exposed_dropdown_menu_content_description
string fab_transformation_scrim_behavior
string fab_transformation_sheet_behavior
string float_tip_empty
string float_tip_network_info
string float_tip_notice_msg
string float_tip_personal_center
string func_coming_soon
string hide_bottom_view_on_scroll_behavior
string icon_content_description
string item_view_role_description
string material_clock_display_divider
string material_clock_toggle_content_description
string material_hour_selection
string material_hour_suffix
string material_minute_selection
string material_minute_suffix
string material_slider_range_end
string material_slider_range_start
string material_timepicker_am
string material_timepicker_clock_mode_description
string material_timepicker_hour
string material_timepicker_minute
string material_timepicker_pm
string material_timepicker_select_time
string material_timepicker_text_input_mode_description
string mtrl_badge_numberless_content_description
string mtrl_chip_close_icon_content_description
string mtrl_exceed_max_badge_number_content_description
string mtrl_exceed_max_badge_number_suffix
string mtrl_picker_a11y_next_month
string mtrl_picker_a11y_prev_month
string mtrl_picker_announce_current_selection
string mtrl_picker_cancel
string mtrl_picker_confirm
string mtrl_picker_date_header_selected
string mtrl_picker_date_header_title
string mtrl_picker_date_header_unselected
string mtrl_picker_day_of_week_column_header
string mtrl_picker_invalid_format
string mtrl_picker_invalid_format_example
string mtrl_picker_invalid_format_use
string mtrl_picker_invalid_range
string mtrl_picker_navigate_to_year_description
string mtrl_picker_out_of_range
string mtrl_picker_range_header_only_end_selected
string mtrl_picker_range_header_only_start_selected
string mtrl_picker_range_header_selected
string mtrl_picker_range_header_title
string mtrl_picker_range_header_unselected
string mtrl_picker_save
string mtrl_picker_text_input_date_hint
string mtrl_picker_text_input_date_range_end_hint
string mtrl_picker_text_input_date_range_start_hint
string mtrl_picker_text_input_day_abbr
string mtrl_picker_text_input_month_abbr
string mtrl_picker_text_input_year_abbr
string mtrl_picker_toggle_to_calendar_input_mode
string mtrl_picker_toggle_to_day_selection
string mtrl_picker_toggle_to_text_input_mode
string mtrl_picker_toggle_to_year_selection
string password_toggle_content_description
string path_password_eye
string path_password_eye_mask_strike_through
string path_password_eye_mask_visible
string path_password_strike_through
string screen_short_toast
string search_menu_title
string status_bar_notification_info_overflow
string str_create_temp_success1
string str_device_name_q1
string str_device_name_q1pro
string str_device_name_q1s
string str_device_name_q1s_plus
string str_device_name_q1s_pro
string str_device_name_q2
string str_device_name_q2_pro
string str_device_name_q2_s
string str_device_name_q2_s_plus
string str_device_name_q2_s_pro
string str_device_name_q2_s_pro_max
string str_device_name_studio
string str_device_name_studio_pro
string str_device_name_studio_s
string str_device_name_studio_s_plus
string str_device_name_studio_s_pro
string str_device_name_studio_s_pro_max
string str_friday
string str_monday
string str_notify_bar_not_login
string str_saturday
string str_sunday
string str_thursday
string str_time_unit_hour
string str_time_unit_minute
string str_time_unit_second
string str_tuesday
string str_wednesday
string symbol_ellipsis
string toast_operation_failure
string toast_operation_failure_by_net
string toast_operation_success
string toast_pref_constraint
string tv_empty_file
string voice_change_recovery
string voice_change_wallpaper
string voice_command_close
string voice_command_open
string voice_command_photo
string voice_command_recent_file
string voice_command_record
string voice_command_screen
string voice_command_uninstall
string voice_command_video
string voice_reboot_wifi
string wireless_screen
style AlertDialog_AppCompat
style AlertDialog_AppCompat_Light
style AndroidThemeColorAccentYellow
style Animation_AppCompat_Dialog
style Animation_AppCompat_DropDownUp
style Animation_AppCompat_Tooltip
style Animation_Design_BottomSheetDialog
style Animation_MaterialComponents_BottomSheetDialog
style AppTheme
style AppTheme_NoDisplay
style BaseAppTheme
style BaseLib_CommonDialog
style BaseLib_Dialog_FullScreen
style Base_AlertDialog_AppCompat
style Base_AlertDialog_AppCompat_Light
style Base_Animation_AppCompat_Dialog
style Base_Animation_AppCompat_DropDownUp
style Base_Animation_AppCompat_Tooltip
style Base_CardView
style Base_DialogWindowTitleBackground_AppCompat
style Base_DialogWindowTitle_AppCompat
style Base_MaterialAlertDialog_MaterialComponents_Title_Icon
style Base_MaterialAlertDialog_MaterialComponents_Title_Panel
style Base_MaterialAlertDialog_MaterialComponents_Title_Text
style Base_TextAppearance_AppCompat
style Base_TextAppearance_AppCompat_Body1
style Base_TextAppearance_AppCompat_Body2
style Base_TextAppearance_AppCompat_Button
style Base_TextAppearance_AppCompat_Caption
style Base_TextAppearance_AppCompat_Display1
style Base_TextAppearance_AppCompat_Display2
style Base_TextAppearance_AppCompat_Display3
style Base_TextAppearance_AppCompat_Display4
style Base_TextAppearance_AppCompat_Headline
style Base_TextAppearance_AppCompat_Inverse
style Base_TextAppearance_AppCompat_Large
style Base_TextAppearance_AppCompat_Large_Inverse
style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
style Base_TextAppearance_AppCompat_Medium
style Base_TextAppearance_AppCompat_Medium_Inverse
style Base_TextAppearance_AppCompat_Menu
style Base_TextAppearance_AppCompat_SearchResult
style Base_TextAppearance_AppCompat_SearchResult_Subtitle
style Base_TextAppearance_AppCompat_SearchResult_Title
style Base_TextAppearance_AppCompat_Small
style Base_TextAppearance_AppCompat_Small_Inverse
style Base_TextAppearance_AppCompat_Subhead
style Base_TextAppearance_AppCompat_Subhead_Inverse
style Base_TextAppearance_AppCompat_Title
style Base_TextAppearance_AppCompat_Title_Inverse
style Base_TextAppearance_AppCompat_Tooltip
style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
style Base_TextAppearance_AppCompat_Widget_Button
style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
style Base_TextAppearance_AppCompat_Widget_Button_Colored
style Base_TextAppearance_AppCompat_Widget_Button_Inverse
style Base_TextAppearance_AppCompat_Widget_DropDownItem
style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
style Base_TextAppearance_AppCompat_Widget_Switch
style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
style Base_TextAppearance_MaterialComponents_Badge
style Base_TextAppearance_MaterialComponents_Button
style Base_TextAppearance_MaterialComponents_Headline6
style Base_TextAppearance_MaterialComponents_Subtitle2
style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
style Base_ThemeOverlay_AppCompat
style Base_ThemeOverlay_AppCompat_ActionBar
style Base_ThemeOverlay_AppCompat_Dark
style Base_ThemeOverlay_AppCompat_Dark_ActionBar
style Base_ThemeOverlay_AppCompat_Dialog
style Base_ThemeOverlay_AppCompat_Dialog_Alert
style Base_ThemeOverlay_AppCompat_Light
style Base_ThemeOverlay_MaterialComponents_Dialog
style Base_ThemeOverlay_MaterialComponents_Dialog_Alert
style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework
style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework
style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog
style Base_Theme_AppCompat
style Base_Theme_AppCompat_CompactMenu
style Base_Theme_AppCompat_Dialog
style Base_Theme_AppCompat_DialogWhenLarge
style Base_Theme_AppCompat_Dialog_Alert
style Base_Theme_AppCompat_Dialog_FixedSize
style Base_Theme_AppCompat_Dialog_MinWidth
style Base_Theme_AppCompat_Light
style Base_Theme_AppCompat_Light_DarkActionBar
style Base_Theme_AppCompat_Light_Dialog
style Base_Theme_AppCompat_Light_DialogWhenLarge
style Base_Theme_AppCompat_Light_Dialog_Alert
style Base_Theme_AppCompat_Light_Dialog_FixedSize
style Base_Theme_AppCompat_Light_Dialog_MinWidth
style Base_Theme_MaterialComponents
style Base_Theme_MaterialComponents_Bridge
style Base_Theme_MaterialComponents_CompactMenu
style Base_Theme_MaterialComponents_Dialog
style Base_Theme_MaterialComponents_DialogWhenLarge
style Base_Theme_MaterialComponents_Dialog_Alert
style Base_Theme_MaterialComponents_Dialog_Bridge
style Base_Theme_MaterialComponents_Dialog_FixedSize
style Base_Theme_MaterialComponents_Dialog_MinWidth
style Base_Theme_MaterialComponents_Light
style Base_Theme_MaterialComponents_Light_Bridge
style Base_Theme_MaterialComponents_Light_DarkActionBar
style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge
style Base_Theme_MaterialComponents_Light_Dialog
style Base_Theme_MaterialComponents_Light_DialogWhenLarge
style Base_Theme_MaterialComponents_Light_Dialog_Alert
style Base_Theme_MaterialComponents_Light_Dialog_Bridge
style Base_Theme_MaterialComponents_Light_Dialog_FixedSize
style Base_Theme_MaterialComponents_Light_Dialog_MinWidth
style Base_V14_ThemeOverlay_MaterialComponents_Dialog
style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert
style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog
style Base_V14_Theme_MaterialComponents
style Base_V14_Theme_MaterialComponents_Bridge
style Base_V14_Theme_MaterialComponents_Dialog
style Base_V14_Theme_MaterialComponents_Dialog_Bridge
style Base_V14_Theme_MaterialComponents_Light
style Base_V14_Theme_MaterialComponents_Light_Bridge
style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge
style Base_V14_Theme_MaterialComponents_Light_Dialog
style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge
style Base_V21_ThemeOverlay_AppCompat_Dialog
style Base_V21_Theme_AppCompat
style Base_V21_Theme_AppCompat_Dialog
style Base_V21_Theme_AppCompat_Light
style Base_V21_Theme_AppCompat_Light_Dialog
style Base_V21_Theme_MaterialComponents
style Base_V21_Theme_MaterialComponents_Dialog
style Base_V21_Theme_MaterialComponents_Light
style Base_V21_Theme_MaterialComponents_Light_Dialog
style Base_V22_Theme_AppCompat
style Base_V22_Theme_AppCompat_Light
style Base_V23_Theme_AppCompat
style Base_V23_Theme_AppCompat_Light
style Base_V26_Theme_AppCompat
style Base_V26_Theme_AppCompat_Light
style Base_V26_Widget_AppCompat_Toolbar
style Base_V28_Theme_AppCompat
style Base_V28_Theme_AppCompat_Light
style Base_V7_ThemeOverlay_AppCompat_Dialog
style Base_V7_Theme_AppCompat
style Base_V7_Theme_AppCompat_Dialog
style Base_V7_Theme_AppCompat_Light
style Base_V7_Theme_AppCompat_Light_Dialog
style Base_V7_Widget_AppCompat_AutoCompleteTextView
style Base_V7_Widget_AppCompat_EditText
style Base_V7_Widget_AppCompat_Toolbar
style Base_Widget_AppCompat_ActionBar
style Base_Widget_AppCompat_ActionBar_Solid
style Base_Widget_AppCompat_ActionBar_TabBar
style Base_Widget_AppCompat_ActionBar_TabText
style Base_Widget_AppCompat_ActionBar_TabView
style Base_Widget_AppCompat_ActionButton
style Base_Widget_AppCompat_ActionButton_CloseMode
style Base_Widget_AppCompat_ActionButton_Overflow
style Base_Widget_AppCompat_ActionMode
style Base_Widget_AppCompat_ActivityChooserView
style Base_Widget_AppCompat_AutoCompleteTextView
style Base_Widget_AppCompat_Button
style Base_Widget_AppCompat_ButtonBar
style Base_Widget_AppCompat_ButtonBar_AlertDialog
style Base_Widget_AppCompat_Button_Borderless
style Base_Widget_AppCompat_Button_Borderless_Colored
style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
style Base_Widget_AppCompat_Button_Colored
style Base_Widget_AppCompat_Button_Small
style Base_Widget_AppCompat_CompoundButton_CheckBox
style Base_Widget_AppCompat_CompoundButton_RadioButton
style Base_Widget_AppCompat_CompoundButton_Switch
style Base_Widget_AppCompat_DrawerArrowToggle
style Base_Widget_AppCompat_DrawerArrowToggle_Common
style Base_Widget_AppCompat_DropDownItem_Spinner
style Base_Widget_AppCompat_EditText
style Base_Widget_AppCompat_ImageButton
style Base_Widget_AppCompat_Light_ActionBar
style Base_Widget_AppCompat_Light_ActionBar_Solid
style Base_Widget_AppCompat_Light_ActionBar_TabBar
style Base_Widget_AppCompat_Light_ActionBar_TabText
style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
style Base_Widget_AppCompat_Light_ActionBar_TabView
style Base_Widget_AppCompat_Light_PopupMenu
style Base_Widget_AppCompat_Light_PopupMenu_Overflow
style Base_Widget_AppCompat_ListMenuView
style Base_Widget_AppCompat_ListPopupWindow
style Base_Widget_AppCompat_ListView
style Base_Widget_AppCompat_ListView_DropDown
style Base_Widget_AppCompat_ListView_Menu
style Base_Widget_AppCompat_PopupMenu
style Base_Widget_AppCompat_PopupMenu_Overflow
style Base_Widget_AppCompat_PopupWindow
style Base_Widget_AppCompat_ProgressBar
style Base_Widget_AppCompat_ProgressBar_Horizontal
style Base_Widget_AppCompat_RatingBar
style Base_Widget_AppCompat_RatingBar_Indicator
style Base_Widget_AppCompat_RatingBar_Small
style Base_Widget_AppCompat_SearchView
style Base_Widget_AppCompat_SearchView_ActionBar
style Base_Widget_AppCompat_SeekBar
style Base_Widget_AppCompat_SeekBar_Discrete
style Base_Widget_AppCompat_Spinner
style Base_Widget_AppCompat_Spinner_Underlined
style Base_Widget_AppCompat_TextView
style Base_Widget_AppCompat_TextView_SpinnerItem
style Base_Widget_AppCompat_Toolbar
style Base_Widget_AppCompat_Toolbar_Button_Navigation
style Base_Widget_Design_TabLayout
style Base_Widget_MaterialComponents_AutoCompleteTextView
style Base_Widget_MaterialComponents_CheckedTextView
style Base_Widget_MaterialComponents_Chip
style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton
style Base_Widget_MaterialComponents_PopupMenu
style Base_Widget_MaterialComponents_PopupMenu_ContextMenu
style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow
style Base_Widget_MaterialComponents_PopupMenu_Overflow
style Base_Widget_MaterialComponents_Slider
style Base_Widget_MaterialComponents_Snackbar
style Base_Widget_MaterialComponents_TextInputEditText
style Base_Widget_MaterialComponents_TextInputLayout
style Base_Widget_MaterialComponents_TextView
style CardView
style CardView_Dark
style CardView_Light
style CustomDialog
style EmptyTheme
style MaterialAlertDialog_MaterialComponents
style MaterialAlertDialog_MaterialComponents_Body_Text
style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar
style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner
style MaterialAlertDialog_MaterialComponents_Title_Icon
style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked
style MaterialAlertDialog_MaterialComponents_Title_Panel
style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked
style MaterialAlertDialog_MaterialComponents_Title_Text
style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked
style MyContextMenuText
style Platform_AppCompat
style Platform_AppCompat_Light
style Platform_MaterialComponents
style Platform_MaterialComponents_Dialog
style Platform_MaterialComponents_Light
style Platform_MaterialComponents_Light_Dialog
style Platform_ThemeOverlay_AppCompat
style Platform_ThemeOverlay_AppCompat_Dark
style Platform_ThemeOverlay_AppCompat_Light
style Platform_V21_AppCompat
style Platform_V21_AppCompat_Light
style Platform_V25_AppCompat
style Platform_V25_AppCompat_Light
style Platform_Widget_AppCompat_Spinner
style RtlOverlay_DialogWindowTitle_AppCompat
style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
style RtlOverlay_Widget_AppCompat_PopupMenuItem
style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
style RtlOverlay_Widget_AppCompat_Search_DropDown
style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
style RtlUnderlay_Widget_AppCompat_ActionButton
style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
style ShapeAppearanceOverlay
style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize
style ShapeAppearanceOverlay_BottomRightCut
style ShapeAppearanceOverlay_Cut
style ShapeAppearanceOverlay_DifferentCornerSize
style ShapeAppearanceOverlay_MaterialComponents_BottomSheet
style ShapeAppearanceOverlay_MaterialComponents_Chip
style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton
style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton
style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day
style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen
style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year
style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox
style ShapeAppearanceOverlay_TopLeftCut
style ShapeAppearanceOverlay_TopRightDifferentCornerSize
style ShapeAppearance_MaterialComponents
style ShapeAppearance_MaterialComponents_LargeComponent
style ShapeAppearance_MaterialComponents_MediumComponent
style ShapeAppearance_MaterialComponents_SmallComponent
style ShapeAppearance_MaterialComponents_Test
style ShapeAppearance_MaterialComponents_Tooltip
style TestStyleWithLineHeight
style TestStyleWithLineHeightAppearance
style TestStyleWithThemeLineHeightAttribute
style TestStyleWithoutLineHeight
style TestThemeWithLineHeight
style TestThemeWithLineHeightDisabled
style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day
style Test_Theme_MaterialComponents_MaterialCalendar
style Test_Widget_MaterialComponents_MaterialCalendar
style Test_Widget_MaterialComponents_MaterialCalendar_Day
style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected
style TextAppearance_AppCompat
style TextAppearance_AppCompat_Body1
style TextAppearance_AppCompat_Body2
style TextAppearance_AppCompat_Button
style TextAppearance_AppCompat_Caption
style TextAppearance_AppCompat_Display1
style TextAppearance_AppCompat_Display2
style TextAppearance_AppCompat_Display3
style TextAppearance_AppCompat_Display4
style TextAppearance_AppCompat_Headline
style TextAppearance_AppCompat_Inverse
style TextAppearance_AppCompat_Large
style TextAppearance_AppCompat_Large_Inverse
style TextAppearance_AppCompat_Light_SearchResult_Subtitle
style TextAppearance_AppCompat_Light_SearchResult_Title
style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
style TextAppearance_AppCompat_Medium
style TextAppearance_AppCompat_Medium_Inverse
style TextAppearance_AppCompat_Menu
style TextAppearance_AppCompat_SearchResult_Subtitle
style TextAppearance_AppCompat_SearchResult_Title
style TextAppearance_AppCompat_Small
style TextAppearance_AppCompat_Small_Inverse
style TextAppearance_AppCompat_Subhead
style TextAppearance_AppCompat_Subhead_Inverse
style TextAppearance_AppCompat_Title
style TextAppearance_AppCompat_Title_Inverse
style TextAppearance_AppCompat_Tooltip
style TextAppearance_AppCompat_Widget_ActionBar_Menu
style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
style TextAppearance_AppCompat_Widget_ActionBar_Title
style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
style TextAppearance_AppCompat_Widget_ActionMode_Title
style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
style TextAppearance_AppCompat_Widget_Button
style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
style TextAppearance_AppCompat_Widget_Button_Colored
style TextAppearance_AppCompat_Widget_Button_Inverse
style TextAppearance_AppCompat_Widget_DropDownItem
style TextAppearance_AppCompat_Widget_PopupMenu_Header
style TextAppearance_AppCompat_Widget_PopupMenu_Large
style TextAppearance_AppCompat_Widget_PopupMenu_Small
style TextAppearance_AppCompat_Widget_Switch
style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Info_Media
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Line2_Media
style TextAppearance_Compat_Notification_Media
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Time_Media
style TextAppearance_Compat_Notification_Title
style TextAppearance_Compat_Notification_Title_Media
style TextAppearance_Design_CollapsingToolbar_Expanded
style TextAppearance_Design_Counter
style TextAppearance_Design_Counter_Overflow
style TextAppearance_Design_Error
style TextAppearance_Design_HelperText
style TextAppearance_Design_Hint
style TextAppearance_Design_Placeholder
style TextAppearance_Design_Prefix
style TextAppearance_Design_Snackbar_Message
style TextAppearance_Design_Suffix
style TextAppearance_Design_Tab
style TextAppearance_MaterialComponents_Badge
style TextAppearance_MaterialComponents_Body1
style TextAppearance_MaterialComponents_Body2
style TextAppearance_MaterialComponents_Button
style TextAppearance_MaterialComponents_Caption
style TextAppearance_MaterialComponents_Chip
style TextAppearance_MaterialComponents_Headline1
style TextAppearance_MaterialComponents_Headline2
style TextAppearance_MaterialComponents_Headline3
style TextAppearance_MaterialComponents_Headline4
style TextAppearance_MaterialComponents_Headline5
style TextAppearance_MaterialComponents_Headline6
style TextAppearance_MaterialComponents_Overline
style TextAppearance_MaterialComponents_Subtitle1
style TextAppearance_MaterialComponents_Subtitle2
style TextAppearance_MaterialComponents_TimePicker_Title
style TextAppearance_MaterialComponents_Tooltip
style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
style TextAppearance_Widget_AppCompat_Toolbar_Title
style ThemeOverlayColorAccentRed
style ThemeOverlay_AppCompat
style ThemeOverlay_AppCompat_ActionBar
style ThemeOverlay_AppCompat_Dark
style ThemeOverlay_AppCompat_Dark_ActionBar
style ThemeOverlay_AppCompat_DayNight
style ThemeOverlay_AppCompat_DayNight_ActionBar
style ThemeOverlay_AppCompat_Dialog
style ThemeOverlay_AppCompat_Dialog_Alert
style ThemeOverlay_AppCompat_Light
style ThemeOverlay_Design_TextInputEditText
style ThemeOverlay_MaterialComponents
style ThemeOverlay_MaterialComponents_ActionBar
style ThemeOverlay_MaterialComponents_ActionBar_Primary
style ThemeOverlay_MaterialComponents_ActionBar_Surface
style ThemeOverlay_MaterialComponents_AutoCompleteTextView
style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox
style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense
style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox
style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense
style ThemeOverlay_MaterialComponents_BottomAppBar_Primary
style ThemeOverlay_MaterialComponents_BottomAppBar_Surface
style ThemeOverlay_MaterialComponents_BottomSheetDialog
style ThemeOverlay_MaterialComponents_Dark
style ThemeOverlay_MaterialComponents_Dark_ActionBar
style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog
style ThemeOverlay_MaterialComponents_Dialog
style ThemeOverlay_MaterialComponents_Dialog_Alert
style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework
style ThemeOverlay_MaterialComponents_Light
style ThemeOverlay_MaterialComponents_Light_BottomSheetDialog
style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework
style ThemeOverlay_MaterialComponents_MaterialAlertDialog
style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered
style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date
style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar
style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text
style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day
style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner
style ThemeOverlay_MaterialComponents_MaterialCalendar
style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen
style ThemeOverlay_MaterialComponents_TextInputEditText
style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox
style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense
style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox
style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense
style ThemeOverlay_MaterialComponents_TimePicker
style ThemeOverlay_MaterialComponents_TimePicker_Display
style ThemeOverlay_MaterialComponents_Toolbar_Primary
style ThemeOverlay_MaterialComponents_Toolbar_Surface
style Theme_AppCompat
style Theme_AppCompat_CompactMenu
style Theme_AppCompat_DayNight
style Theme_AppCompat_DayNight_DarkActionBar
style Theme_AppCompat_DayNight_Dialog
style Theme_AppCompat_DayNight_DialogWhenLarge
style Theme_AppCompat_DayNight_Dialog_Alert
style Theme_AppCompat_DayNight_Dialog_MinWidth
style Theme_AppCompat_DayNight_NoActionBar
style Theme_AppCompat_Dialog
style Theme_AppCompat_DialogWhenLarge
style Theme_AppCompat_Dialog_Alert
style Theme_AppCompat_Dialog_MinWidth
style Theme_AppCompat_Empty
style Theme_AppCompat_Light
style Theme_AppCompat_Light_DarkActionBar
style Theme_AppCompat_Light_Dialog
style Theme_AppCompat_Light_DialogWhenLarge
style Theme_AppCompat_Light_Dialog_Alert
style Theme_AppCompat_Light_Dialog_MinWidth
style Theme_AppCompat_Light_NoActionBar
style Theme_AppCompat_NoActionBar
style Theme_Design
style Theme_Design_BottomSheetDialog
style Theme_Design_Light
style Theme_Design_Light_BottomSheetDialog
style Theme_Design_Light_NoActionBar
style Theme_Design_NoActionBar
style Theme_MaterialComponents
style Theme_MaterialComponents_BottomSheetDialog
style Theme_MaterialComponents_Bridge
style Theme_MaterialComponents_CompactMenu
style Theme_MaterialComponents_DayNight
style Theme_MaterialComponents_DayNight_BottomSheetDialog
style Theme_MaterialComponents_DayNight_Bridge
style Theme_MaterialComponents_DayNight_DarkActionBar
style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge
style Theme_MaterialComponents_DayNight_Dialog
style Theme_MaterialComponents_DayNight_DialogWhenLarge
style Theme_MaterialComponents_DayNight_Dialog_Alert
style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge
style Theme_MaterialComponents_DayNight_Dialog_Bridge
style Theme_MaterialComponents_DayNight_Dialog_FixedSize
style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge
style Theme_MaterialComponents_DayNight_Dialog_MinWidth
style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge
style Theme_MaterialComponents_DayNight_NoActionBar
style Theme_MaterialComponents_DayNight_NoActionBar_Bridge
style Theme_MaterialComponents_Dialog
style Theme_MaterialComponents_DialogWhenLarge
style Theme_MaterialComponents_Dialog_Alert
style Theme_MaterialComponents_Dialog_Alert_Bridge
style Theme_MaterialComponents_Dialog_Bridge
style Theme_MaterialComponents_Dialog_FixedSize
style Theme_MaterialComponents_Dialog_FixedSize_Bridge
style Theme_MaterialComponents_Dialog_MinWidth
style Theme_MaterialComponents_Dialog_MinWidth_Bridge
style Theme_MaterialComponents_Light
style Theme_MaterialComponents_Light_BarSize
style Theme_MaterialComponents_Light_BottomSheetDialog
style Theme_MaterialComponents_Light_Bridge
style Theme_MaterialComponents_Light_DarkActionBar
style Theme_MaterialComponents_Light_DarkActionBar_Bridge
style Theme_MaterialComponents_Light_Dialog
style Theme_MaterialComponents_Light_DialogWhenLarge
style Theme_MaterialComponents_Light_Dialog_Alert
style Theme_MaterialComponents_Light_Dialog_Alert_Bridge
style Theme_MaterialComponents_Light_Dialog_Bridge
style Theme_MaterialComponents_Light_Dialog_FixedSize
style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge
style Theme_MaterialComponents_Light_Dialog_MinWidth
style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge
style Theme_MaterialComponents_Light_LargeTouch
style Theme_MaterialComponents_Light_NoActionBar
style Theme_MaterialComponents_Light_NoActionBar_Bridge
style Theme_MaterialComponents_NoActionBar
style Theme_MaterialComponents_NoActionBar_Bridge
style Widget_AppCompat_ActionBar
style Widget_AppCompat_ActionBar_Solid
style Widget_AppCompat_ActionBar_TabBar
style Widget_AppCompat_ActionBar_TabText
style Widget_AppCompat_ActionBar_TabView
style Widget_AppCompat_ActionButton
style Widget_AppCompat_ActionButton_CloseMode
style Widget_AppCompat_ActionButton_Overflow
style Widget_AppCompat_ActionMode
style Widget_AppCompat_ActivityChooserView
style Widget_AppCompat_AutoCompleteTextView
style Widget_AppCompat_Button
style Widget_AppCompat_ButtonBar
style Widget_AppCompat_ButtonBar_AlertDialog
style Widget_AppCompat_Button_Borderless
style Widget_AppCompat_Button_Borderless_Colored
style Widget_AppCompat_Button_ButtonBar_AlertDialog
style Widget_AppCompat_Button_Colored
style Widget_AppCompat_Button_Small
style Widget_AppCompat_CompoundButton_CheckBox
style Widget_AppCompat_CompoundButton_RadioButton
style Widget_AppCompat_CompoundButton_Switch
style Widget_AppCompat_DrawerArrowToggle
style Widget_AppCompat_DropDownItem_Spinner
style Widget_AppCompat_EditText
style Widget_AppCompat_ImageButton
style Widget_AppCompat_Light_ActionBar
style Widget_AppCompat_Light_ActionBar_Solid
style Widget_AppCompat_Light_ActionBar_Solid_Inverse
style Widget_AppCompat_Light_ActionBar_TabBar
style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
style Widget_AppCompat_Light_ActionBar_TabText
style Widget_AppCompat_Light_ActionBar_TabText_Inverse
style Widget_AppCompat_Light_ActionBar_TabView
style Widget_AppCompat_Light_ActionBar_TabView_Inverse
style Widget_AppCompat_Light_ActionButton
style Widget_AppCompat_Light_ActionButton_CloseMode
style Widget_AppCompat_Light_ActionButton_Overflow
style Widget_AppCompat_Light_ActionMode_Inverse
style Widget_AppCompat_Light_ActivityChooserView
style Widget_AppCompat_Light_AutoCompleteTextView
style Widget_AppCompat_Light_DropDownItem_Spinner
style Widget_AppCompat_Light_ListPopupWindow
style Widget_AppCompat_Light_ListView_DropDown
style Widget_AppCompat_Light_PopupMenu
style Widget_AppCompat_Light_PopupMenu_Overflow
style Widget_AppCompat_Light_SearchView
style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
style Widget_AppCompat_ListMenuView
style Widget_AppCompat_ListPopupWindow
style Widget_AppCompat_ListView
style Widget_AppCompat_ListView_DropDown
style Widget_AppCompat_ListView_Menu
style Widget_AppCompat_PopupMenu
style Widget_AppCompat_PopupMenu_Overflow
style Widget_AppCompat_PopupWindow
style Widget_AppCompat_ProgressBar
style Widget_AppCompat_ProgressBar_Horizontal
style Widget_AppCompat_RatingBar
style Widget_AppCompat_RatingBar_Indicator
style Widget_AppCompat_RatingBar_Small
style Widget_AppCompat_SearchView
style Widget_AppCompat_SearchView_ActionBar
style Widget_AppCompat_SeekBar
style Widget_AppCompat_SeekBar_Discrete
style Widget_AppCompat_Spinner
style Widget_AppCompat_Spinner_DropDown
style Widget_AppCompat_Spinner_DropDown_ActionBar
style Widget_AppCompat_Spinner_Underlined
style Widget_AppCompat_TextView
style Widget_AppCompat_TextView_SpinnerItem
style Widget_AppCompat_Toolbar
style Widget_AppCompat_Toolbar_Button_Navigation
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
style Widget_Design_AppBarLayout
style Widget_Design_BottomNavigationView
style Widget_Design_BottomSheet_Modal
style Widget_Design_CollapsingToolbar
style Widget_Design_FloatingActionButton
style Widget_Design_NavigationView
style Widget_Design_ScrimInsetsFrameLayout
style Widget_Design_Snackbar
style Widget_Design_TabLayout
style Widget_Design_TextInputEditText
style Widget_Design_TextInputLayout
style Widget_MaterialComponents_ActionBar_Primary
style Widget_MaterialComponents_ActionBar_PrimarySurface
style Widget_MaterialComponents_ActionBar_Solid
style Widget_MaterialComponents_ActionBar_Surface
style Widget_MaterialComponents_AppBarLayout_Primary
style Widget_MaterialComponents_AppBarLayout_PrimarySurface
style Widget_MaterialComponents_AppBarLayout_Surface
style Widget_MaterialComponents_AutoCompleteTextView_FilledBox
style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense
style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox
style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense
style Widget_MaterialComponents_Badge
style Widget_MaterialComponents_BottomAppBar
style Widget_MaterialComponents_BottomAppBar_Colored
style Widget_MaterialComponents_BottomAppBar_PrimarySurface
style Widget_MaterialComponents_BottomNavigationView
style Widget_MaterialComponents_BottomNavigationView_Colored
style Widget_MaterialComponents_BottomNavigationView_PrimarySurface
style Widget_MaterialComponents_BottomSheet
style Widget_MaterialComponents_BottomSheet_Modal
style Widget_MaterialComponents_Button
style Widget_MaterialComponents_Button_Icon
style Widget_MaterialComponents_Button_OutlinedButton
style Widget_MaterialComponents_Button_OutlinedButton_Icon
style Widget_MaterialComponents_Button_TextButton
style Widget_MaterialComponents_Button_TextButton_Dialog
style Widget_MaterialComponents_Button_TextButton_Dialog_Flush
style Widget_MaterialComponents_Button_TextButton_Dialog_Icon
style Widget_MaterialComponents_Button_TextButton_Icon
style Widget_MaterialComponents_Button_TextButton_Snackbar
style Widget_MaterialComponents_Button_UnelevatedButton
style Widget_MaterialComponents_Button_UnelevatedButton_Icon
style Widget_MaterialComponents_CardView
style Widget_MaterialComponents_CheckedTextView
style Widget_MaterialComponents_ChipGroup
style Widget_MaterialComponents_Chip_Action
style Widget_MaterialComponents_Chip_Choice
style Widget_MaterialComponents_Chip_Entry
style Widget_MaterialComponents_Chip_Filter
style Widget_MaterialComponents_CircularProgressIndicator
style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall
style Widget_MaterialComponents_CircularProgressIndicator_Medium
style Widget_MaterialComponents_CircularProgressIndicator_Small
style Widget_MaterialComponents_CollapsingToolbar
style Widget_MaterialComponents_CompoundButton_CheckBox
style Widget_MaterialComponents_CompoundButton_RadioButton
style Widget_MaterialComponents_CompoundButton_Switch
style Widget_MaterialComponents_ExtendedFloatingActionButton
style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon
style Widget_MaterialComponents_FloatingActionButton
style Widget_MaterialComponents_Light_ActionBar_Solid
style Widget_MaterialComponents_LinearProgressIndicator
style Widget_MaterialComponents_MaterialButtonToggleGroup
style Widget_MaterialComponents_MaterialCalendar
style Widget_MaterialComponents_MaterialCalendar_Day
style Widget_MaterialComponents_MaterialCalendar_DayTextView
style Widget_MaterialComponents_MaterialCalendar_Day_Invalid
style Widget_MaterialComponents_MaterialCalendar_Day_Selected
style Widget_MaterialComponents_MaterialCalendar_Day_Today
style Widget_MaterialComponents_MaterialCalendar_Fullscreen
style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton
style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton
style Widget_MaterialComponents_MaterialCalendar_HeaderDivider
style Widget_MaterialComponents_MaterialCalendar_HeaderLayout
style Widget_MaterialComponents_MaterialCalendar_HeaderSelection
style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen
style Widget_MaterialComponents_MaterialCalendar_HeaderTitle
style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton
style Widget_MaterialComponents_MaterialCalendar_Item
style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton
style Widget_MaterialComponents_MaterialCalendar_MonthTextView
style Widget_MaterialComponents_MaterialCalendar_Year
style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton
style Widget_MaterialComponents_MaterialCalendar_Year_Selected
style Widget_MaterialComponents_MaterialCalendar_Year_Today
style Widget_MaterialComponents_NavigationView
style Widget_MaterialComponents_PopupMenu
style Widget_MaterialComponents_PopupMenu_ContextMenu
style Widget_MaterialComponents_PopupMenu_ListPopupWindow
style Widget_MaterialComponents_PopupMenu_Overflow
style Widget_MaterialComponents_ProgressIndicator
style Widget_MaterialComponents_ShapeableImageView
style Widget_MaterialComponents_Slider
style Widget_MaterialComponents_Snackbar
style Widget_MaterialComponents_Snackbar_FullWidth
style Widget_MaterialComponents_Snackbar_TextView
style Widget_MaterialComponents_TabLayout
style Widget_MaterialComponents_TabLayout_Colored
style Widget_MaterialComponents_TabLayout_PrimarySurface
style Widget_MaterialComponents_TextInputEditText_FilledBox
style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense
style Widget_MaterialComponents_TextInputEditText_OutlinedBox
style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense
style Widget_MaterialComponents_TextInputLayout_FilledBox
style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense
style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu
style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu
style Widget_MaterialComponents_TextInputLayout_OutlinedBox
style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense
style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu
style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu
style Widget_MaterialComponents_TextView
style Widget_MaterialComponents_TimePicker
style Widget_MaterialComponents_TimePicker_Button
style Widget_MaterialComponents_TimePicker_Clock
style Widget_MaterialComponents_TimePicker_Display
style Widget_MaterialComponents_TimePicker_Display_TextInputEditText
style Widget_MaterialComponents_TimePicker_ImageButton
style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance
style Widget_MaterialComponents_Toolbar
style Widget_MaterialComponents_Toolbar_Primary
style Widget_MaterialComponents_Toolbar_PrimarySurface
style Widget_MaterialComponents_Toolbar_Surface
style Widget_MaterialComponents_Tooltip
style Widget_Support_CoordinatorLayout
style actionActivity
style animAlpha
style animScaleY
style listView
styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
styleable ActionBarLayout android_layout_gravity
styleable ActionMenuItemView android_minWidth
styleable ActionMenuView
styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
styleable AnimatedStateListDrawableItem android_drawable android_id
styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
styleable AppBarLayout android_background android_keyboardNavigationCluster android_touchscreenBlocksFocus elevation expanded liftOnScroll liftOnScrollTargetViewId statusBarForeground
styleable AppBarLayoutStates state_collapsed state_collapsible state_liftable state_lifted
styleable AppBarLayout_Layout layout_scrollFlags layout_scrollInterpolator
styleable AppCompatEmojiHelper
styleable AppCompatImageView android_src srcCompat tint tintMode
styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType drawableBottomCompat drawableEndCompat drawableLeftCompat drawableRightCompat drawableStartCompat drawableTint drawableTintMode drawableTopCompat emojiCompatEnabled firstBaselineToTopHeight fontFamily fontVariationSettings lastBaselineToBottomHeight lineHeight textAllCaps textLocale
styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseContentDescription actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeTheme actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listChoiceIndicatorMultipleAnimated listChoiceIndicatorSingleAnimated listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingEnd listPreferredItemPaddingLeft listPreferredItemPaddingRight listPreferredItemPaddingStart panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
styleable Badge backgroundColor badgeGravity badgeTextColor horizontalOffset maxCharacterCount number verticalOffset
styleable BaseLib_TitleBar baselib_titlebar_color baselib_titlebar_title baselib_titlebar_top_distance
styleable BaseLib_WifiItemView show_mode ssid_single_line
styleable BaseLib_WordItem baselib_bg_color baselib_tv_color
styleable BaseProgressIndicator android_indeterminate hideAnimationBehavior indicatorColor minHideDelay showAnimationBehavior showDelay trackColor trackCornerRadius trackThickness
styleable BottomAppBar backgroundTint elevation fabAlignmentMode fabAnimationMode fabCradleMargin fabCradleRoundedCornerRadius fabCradleVerticalOffset hideOnScroll paddingBottomSystemWindowInsets paddingLeftSystemWindowInsets paddingRightSystemWindowInsets
styleable BottomNavigationView backgroundTint elevation itemBackground itemHorizontalTranslationEnabled itemIconSize itemIconTint itemRippleColor itemTextAppearanceActive itemTextAppearanceInactive itemTextColor labelVisibilityMode menu
styleable BottomSheetBehavior_Layout android_elevation backgroundTint behavior_draggable behavior_expandedOffset behavior_fitToContents behavior_halfExpandedRatio behavior_hideable behavior_peekHeight behavior_saveFlags behavior_skipCollapsed gestureInsetBottomIgnored shapeAppearance shapeAppearanceOverlay
styleable ButtonBarLayout allowStacking
styleable Capability queryPatterns shortcutMatchRequired
styleable CardView android_minHeight android_minWidth cardBackgroundColor cardCornerRadius cardElevation cardMaxElevation cardPreventCornerOverlap cardUseCompatPadding contentPadding contentPaddingBottom contentPaddingLeft contentPaddingRight contentPaddingTop
styleable Carousel carousel_backwardTransition carousel_emptyViewsBehavior carousel_firstView carousel_forwardTransition carousel_infinite carousel_nextState carousel_previousState carousel_touchUpMode carousel_touchUp_dampeningFactor carousel_touchUp_velocityThreshold
styleable CheckedTextView android_checkMark checkMarkCompat checkMarkTint checkMarkTintMode
styleable Chip android_checkable android_ellipsize android_maxWidth android_text android_textAppearance android_textColor android_textSize checkedIcon checkedIconEnabled checkedIconTint checkedIconVisible chipBackgroundColor chipCornerRadius chipEndPadding chipIcon chipIconEnabled chipIconSize chipIconTint chipIconVisible chipMinHeight chipMinTouchTargetSize chipStartPadding chipStrokeColor chipStrokeWidth chipSurfaceColor closeIcon closeIconEnabled closeIconEndPadding closeIconSize closeIconStartPadding closeIconTint closeIconVisible ensureMinTouchTargetSize hideMotionSpec iconEndPadding iconStartPadding rippleColor shapeAppearance shapeAppearanceOverlay showMotionSpec textEndPadding textStartPadding
styleable ChipGroup checkedChip chipSpacing chipSpacingHorizontal chipSpacingVertical selectionRequired singleLine singleSelection
styleable CircleView breathEffect circleColor
styleable CircularProgressIndicator indicatorDirectionCircular indicatorInset indicatorSize
styleable ClockFaceView clockFaceBackgroundColor clockNumberTextColor
styleable ClockHandView clockHandColor materialCircleRadius selectorSize
styleable CollapsingToolbarLayout collapsedTitleGravity collapsedTitleTextAppearance contentScrim expandedTitleGravity expandedTitleMargin expandedTitleMarginBottom expandedTitleMarginEnd expandedTitleMarginStart expandedTitleMarginTop expandedTitleTextAppearance maxLines scrimAnimationDuration scrimVisibleHeightTrigger statusBarScrim title titleEnabled toolbarId
styleable CollapsingToolbarLayout_Layout layout_collapseMode layout_collapseParallaxMultiplier
styleable ColorStateListItem alpha android_alpha android_color android_lStar lStar
styleable CommonButton baselib_theme
styleable CompoundButton android_button buttonCompat buttonTint buttonTintMode
styleable Constraint android_alpha android_elevation android_id android_layout_height android_layout_marginBottom android_layout_marginEnd android_layout_marginLeft android_layout_marginRight android_layout_marginStart android_layout_marginTop android_layout_width android_maxHeight android_maxWidth android_minHeight android_minWidth android_orientation android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_transformPivotX android_transformPivotY android_translationX android_translationY android_translationZ android_visibility animateCircleAngleTo animateRelativeTo animate_relativeTo barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraint_referenced_ids constraint_referenced_tags drawPath flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTag layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent motionProgress motionStagger pathMotionArc pivotAnchor polarRelativeTo quantizeMotionInterpolator quantizeMotionPhase quantizeMotionSteps transformPivotTarget transitionEasing transitionPathRotate visibilityMode
styleable ConstraintLayout_Layout android_elevation android_layout_height android_layout_margin android_layout_marginBottom android_layout_marginEnd android_layout_marginHorizontal android_layout_marginLeft android_layout_marginRight android_layout_marginStart android_layout_marginTop android_layout_marginVertical android_layout_width android_maxHeight android_maxWidth android_minHeight android_minWidth android_orientation android_padding android_paddingBottom android_paddingEnd android_paddingLeft android_paddingRight android_paddingStart android_paddingTop android_visibility barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl circularflow_angles circularflow_defaultAngle circularflow_defaultRadius circularflow_radiusInDP circularflow_viewCenter constraintSet constraint_referenced_ids constraint_referenced_tags flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layoutDescription layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTag layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_optimizationLevel layout_wrapBehaviorInParent
styleable ConstraintLayout_ReactiveGuide reactiveGuide_animateChange reactiveGuide_applyToAllConstraintSets reactiveGuide_applyToConstraintSet reactiveGuide_valueId
styleable ConstraintLayout_placeholder content placeholder_emptyVisibility
styleable ConstraintOverride android_alpha android_elevation android_id android_layout_height android_layout_marginBottom android_layout_marginEnd android_layout_marginLeft android_layout_marginRight android_layout_marginStart android_layout_marginTop android_layout_width android_maxHeight android_maxWidth android_minHeight android_minWidth android_orientation android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_transformPivotX android_transformPivotY android_translationX android_translationY android_translationZ android_visibility animateCircleAngleTo animateRelativeTo barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraint_referenced_ids drawPath flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBottom_creator layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintRight_creator layout_constraintTag layout_constraintTop_creator layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent motionProgress motionStagger motionTarget pathMotionArc pivotAnchor polarRelativeTo quantizeMotionInterpolator quantizeMotionPhase quantizeMotionSteps transformPivotTarget transitionEasing transitionPathRotate visibilityMode
styleable ConstraintSet android_alpha android_elevation android_id android_layout_height android_layout_marginBottom android_layout_marginEnd android_layout_marginLeft android_layout_marginRight android_layout_marginStart android_layout_marginTop android_layout_width android_maxHeight android_maxWidth android_minHeight android_minWidth android_orientation android_pivotX android_pivotY android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_transformPivotX android_transformPivotY android_translationX android_translationY android_translationZ android_visibility animateCircleAngleTo animateRelativeTo animate_relativeTo barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraintRotate constraint_referenced_ids constraint_referenced_tags deriveConstraintsFrom drawPath flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTag layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent motionProgress motionStagger pathMotionArc pivotAnchor polarRelativeTo quantizeMotionSteps stateLabels transitionEasing transitionPathRotate
styleable CoordinatorLayout keylines statusBarBackground
styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
styleable CustomAttribute attributeName customBoolean customColorDrawableValue customColorValue customDimension customFloatValue customIntegerValue customPixelDimension customReference customStringValue methodName
styleable CustomProgressBar bg_color max progress progress_color
styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
styleable EditTextView editSolidColor hint showTextColor
styleable ExtendedFloatingActionButton collapsedSize elevation extendMotionSpec hideMotionSpec showMotionSpec shrinkMotionSpec
styleable ExtendedFloatingActionButton_Behavior_Layout behavior_autoHide behavior_autoShrink
styleable FlexboxLayout alignContent alignItems dividerDrawable dividerDrawableHorizontal dividerDrawableVertical flexDirection flexWrap justifyContent maxLine showDivider showDividerHorizontal showDividerVertical
styleable FlexboxLayout_Layout layout_alignSelf layout_flexBasisPercent layout_flexGrow layout_flexShrink layout_maxHeight layout_maxWidth layout_minHeight layout_minWidth layout_order layout_wrapBefore
styleable FloatTips float_tips float_tips_theme
styleable FloatingActionButton android_enabled backgroundTint backgroundTintMode borderWidth elevation ensureMinTouchTargetSize fabCustomSize fabSize hideMotionSpec hoveredFocusedTranslationZ maxImageSize pressedTranslationZ rippleColor shapeAppearance shapeAppearanceOverlay showMotionSpec useCompatPadding
styleable FloatingActionButton_Behavior_Layout behavior_autoHide
styleable FlowLayout itemSpacing lineSpacing
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFallbackQuery fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
styleable ForegroundLinearLayout android_foreground android_foregroundGravity foregroundInsidePadding
styleable Fragment android_id android_name android_tag
styleable FragmentContainerView android_name android_tag
styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
styleable GradientColorItem android_color android_offset
styleable Grid grid_columnWeights grid_columns grid_horizontalGaps grid_orientation grid_rowWeights grid_rows grid_skips grid_spans grid_useRtl grid_validateInputs grid_verticalGaps
styleable ImageFilterView altSrc blendSrc brightness contrast crossfade imagePanX imagePanY imageRotate imageZoom overlay round roundPercent saturation warmth
styleable Insets paddingBottomSystemWindowInsets paddingLeftSystemWindowInsets paddingRightSystemWindowInsets
styleable KeyAttribute android_alpha android_elevation android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_transformPivotX android_transformPivotY android_translationX android_translationY android_translationZ curveFit framePosition motionProgress motionTarget transformPivotTarget transitionEasing transitionPathRotate
styleable KeyCycle android_alpha android_elevation android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_translationX android_translationY android_translationZ curveFit framePosition motionProgress motionTarget transitionEasing transitionPathRotate waveOffset wavePeriod wavePhase waveShape waveVariesBy
styleable KeyFrame
styleable KeyFramesAcceleration
styleable KeyFramesVelocity
styleable KeyPosition curveFit drawPath framePosition keyPositionType motionTarget pathMotionArc percentHeight percentWidth percentX percentY sizePercent transitionEasing
styleable KeyTimeCycle android_alpha android_elevation android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_translationX android_translationY android_translationZ curveFit framePosition motionProgress motionTarget transitionEasing transitionPathRotate waveDecay waveOffset wavePeriod wavePhase waveShape
styleable KeyTrigger framePosition motionTarget motion_postLayoutCollision motion_triggerOnCollision onCross onNegativeCross onPositiveCross triggerId triggerReceiver triggerSlack viewTransitionOnCross viewTransitionOnNegativeCross viewTransitionOnPositiveCross
styleable Layout android_layout_height android_layout_marginBottom android_layout_marginEnd android_layout_marginLeft android_layout_marginRight android_layout_marginStart android_layout_marginTop android_layout_width android_orientation barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraint_referenced_ids constraint_referenced_tags guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent maxHeight maxWidth minHeight minWidth
styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
styleable LinearProgressIndicator indeterminateAnimationType indicatorDirectionLinear
styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
styleable MaterialAlertDialog backgroundInsetBottom backgroundInsetEnd backgroundInsetStart backgroundInsetTop
styleable MaterialAlertDialogTheme materialAlertDialogBodyTextStyle materialAlertDialogTheme materialAlertDialogTitleIconStyle materialAlertDialogTitlePanelStyle materialAlertDialogTitleTextStyle
styleable MaterialAutoCompleteTextView android_inputType
styleable MaterialButton android_background android_checkable android_insetBottom android_insetLeft android_insetRight android_insetTop backgroundTint backgroundTintMode cornerRadius elevation icon iconGravity iconPadding iconSize iconTint iconTintMode rippleColor shapeAppearance shapeAppearanceOverlay strokeColor strokeWidth
styleable MaterialButtonToggleGroup checkedButton selectionRequired singleSelection
styleable MaterialCalendar android_windowFullscreen dayInvalidStyle daySelectedStyle dayStyle dayTodayStyle nestedScrollable rangeFillColor yearSelectedStyle yearStyle yearTodayStyle
styleable MaterialCalendarItem android_insetBottom android_insetLeft android_insetRight android_insetTop itemFillColor itemShapeAppearance itemShapeAppearanceOverlay itemStrokeColor itemStrokeWidth itemTextColor
styleable MaterialCardView android_checkable cardForegroundColor checkedIcon checkedIconMargin checkedIconSize checkedIconTint rippleColor shapeAppearance shapeAppearanceOverlay state_dragged strokeColor strokeWidth
styleable MaterialCheckBox buttonTint useMaterialThemeColors
styleable MaterialRadioButton buttonTint useMaterialThemeColors
styleable MaterialShape shapeAppearance shapeAppearanceOverlay
styleable MaterialTextAppearance android_letterSpacing android_lineHeight lineHeight
styleable MaterialTextView android_lineHeight android_textAppearance lineHeight
styleable MaterialTimePicker clockIcon keyboardIcon
styleable MaterialToolbar navigationIconTint
styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
styleable MockView mock_diagonalsColor mock_label mock_labelBackgroundColor mock_labelColor mock_showDiagonals mock_showLabel
styleable Motion animateCircleAngleTo animateRelativeTo animate_relativeTo drawPath motionPathRotate motionStagger pathMotionArc quantizeMotionInterpolator quantizeMotionPhase quantizeMotionSteps transitionEasing
styleable MotionEffect motionEffect_alpha motionEffect_end motionEffect_move motionEffect_start motionEffect_strict motionEffect_translationX motionEffect_translationY motionEffect_viewTransition
styleable MotionHelper onHide onShow
styleable MotionLabel android_autoSizeTextType android_fontFamily android_gravity android_shadowRadius android_text android_textColor android_textSize android_textStyle android_typeface borderRound borderRoundPercent scaleFromTextSize textBackground textBackgroundPanX textBackgroundPanY textBackgroundRotate textBackgroundZoom textOutlineColor textOutlineThickness textPanX textPanY textureBlurFactor textureEffect textureHeight textureWidth
styleable MotionLayout applyMotionScene currentState layoutDescription motionDebug motionProgress showPaths
styleable MotionScene defaultDuration layoutDuringTransition
styleable MotionTelltales telltales_tailColor telltales_tailScale telltales_velocityMode
styleable NavigationView android_background android_fitsSystemWindows android_maxWidth elevation headerLayout itemBackground itemHorizontalPadding itemIconPadding itemIconSize itemIconTint itemMaxLines itemShapeAppearance itemShapeAppearanceOverlay itemShapeFillColor itemShapeInsetBottom itemShapeInsetEnd itemShapeInsetStart itemShapeInsetTop itemTextAppearance itemTextColor menu shapeAppearance shapeAppearanceOverlay
styleable OnClick clickAction targetId
styleable OnSwipe autoCompleteMode dragDirection dragScale dragThreshold limitBoundsTo maxAcceleration maxVelocity moveWhenScrollAtTop nestedScrollFlags onTouchUp rotationCenterId springBoundary springDamping springMass springStiffness springStopThreshold touchAnchorId touchAnchorSide touchRegionId
styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
styleable PopupWindowBackgroundState state_above_anchor
styleable PropertySet android_alpha android_visibility layout_constraintTag motionProgress visibilityMode
styleable RadialViewGroup materialCircleRadius
styleable RangeSlider minSeparation values
styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
styleable RecyclerView android_clipToPadding android_descendantFocusability android_orientation fastScrollEnabled fastScrollHorizontalThumbDrawable fastScrollHorizontalTrackDrawable fastScrollVerticalThumbDrawable fastScrollVerticalTrackDrawable layoutManager reverseLayout spanCount stackFromEnd
styleable ScrimInsetsFrameLayout insetForeground
styleable ScrollingViewBehavior_Layout behavior_overlapTop
styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
styleable SeekBarTouch targetSeekBar
styleable ShapeAppearance cornerFamily cornerFamilyBottomLeft cornerFamilyBottomRight cornerFamilyTopLeft cornerFamilyTopRight cornerSize cornerSizeBottomLeft cornerSizeBottomRight cornerSizeTopLeft cornerSizeTopRight
styleable ShapeableImageView contentPadding contentPaddingBottom contentPaddingEnd contentPaddingLeft contentPaddingRight contentPaddingStart contentPaddingTop shapeAppearance shapeAppearanceOverlay strokeColor strokeWidth
styleable Slider android_enabled android_stepSize android_value android_valueFrom android_valueTo haloColor haloRadius labelBehavior labelStyle thumbColor thumbElevation thumbRadius thumbStrokeColor thumbStrokeWidth tickColor tickColorActive tickColorInactive tickVisible trackColor trackColorActive trackColorInactive trackHeight
styleable Snackbar snackbarButtonStyle snackbarStyle snackbarTextViewStyle
styleable SnackbarLayout actionTextColorAlpha android_maxWidth animationMode backgroundOverlayColorAlpha backgroundTint backgroundTintMode elevation maxActionInlineWidth
styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
styleable State android_id constraints
styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
styleable StateListDrawableItem android_drawable
styleable StateSet defaultState
styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
styleable SwitchIcon offBgColor onBgColor
styleable SwitchMaterial useMaterialThemeColors
styleable TabItem android_icon android_layout android_text
styleable TabLayout tabBackground tabContentStart tabGravity tabIconTint tabIconTintMode tabIndicator tabIndicatorAnimationDuration tabIndicatorAnimationMode tabIndicatorColor tabIndicatorFullWidth tabIndicatorGravity tabIndicatorHeight tabInlineLabel tabMaxWidth tabMinWidth tabMode tabPadding tabPaddingBottom tabPaddingEnd tabPaddingStart tabPaddingTop tabRippleColor tabSelectedTextColor tabTextAppearance tabTextColor tabUnboundedRipple
styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textFontWeight android_textSize android_textStyle android_typeface fontFamily fontVariationSettings textAllCaps textLocale
styleable TextEffects android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_text android_textSize android_textStyle android_typeface borderRound borderRoundPercent textFillColor textOutlineColor textOutlineThickness
styleable TextInputEditText textInputLayoutFocusedRectEnabled
styleable TextInputLayout android_enabled android_hint android_textColorHint boxBackgroundColor boxBackgroundMode boxCollapsedPaddingTop boxCornerRadiusBottomEnd boxCornerRadiusBottomStart boxCornerRadiusTopEnd boxCornerRadiusTopStart boxStrokeColor boxStrokeErrorColor boxStrokeWidth boxStrokeWidthFocused counterEnabled counterMaxLength counterOverflowTextAppearance counterOverflowTextColor counterTextAppearance counterTextColor endIconCheckable endIconContentDescription endIconDrawable endIconMode endIconTint endIconTintMode errorContentDescription errorEnabled errorIconDrawable errorIconTint errorIconTintMode errorTextAppearance errorTextColor expandedHintEnabled helperText helperTextEnabled helperTextTextAppearance helperTextTextColor hintAnimationEnabled hintEnabled hintTextAppearance hintTextColor passwordToggleContentDescription passwordToggleDrawable passwordToggleEnabled passwordToggleTint passwordToggleTintMode placeholderText placeholderTextAppearance placeholderTextColor prefixText prefixTextAppearance prefixTextColor shapeAppearance shapeAppearanceOverlay startIconCheckable startIconContentDescription startIconDrawable startIconTint startIconTintMode suffixText suffixTextAppearance suffixTextColor
styleable ThemeEnforcement android_textAppearance enforceMaterialTheme enforceTextAppearance
styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight menu navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
styleable Tooltip android_layout_margin android_minHeight android_minWidth android_padding android_text android_textAppearance backgroundTint
styleable Transform android_elevation android_rotation android_rotationX android_rotationY android_scaleX android_scaleY android_transformPivotX android_transformPivotY android_translationX android_translationY android_translationZ transformPivotTarget
styleable Transition android_id autoTransition constraintSetEnd constraintSetStart duration layoutDuringTransition motionInterpolator pathMotionArc staggered transitionDisable transitionFlags
styleable Variant constraints region_heightLessThan region_heightMoreThan region_widthLessThan region_widthMoreThan
styleable View android_focusable android_theme paddingEnd paddingStart theme
styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
styleable ViewPager2 android_orientation
styleable ViewStubCompat android_id android_inflatedId android_layout
styleable ViewTransition SharedValue SharedValueId android_id clearsTag duration ifTagNotSet ifTagSet motionInterpolator motionTarget onStateTransition pathMotionArc setsTag transitionDisable upDuration viewTransitionMode
styleable background bl_checkable_gradient_angle bl_checkable_gradient_centerColor bl_checkable_gradient_centerX bl_checkable_gradient_centerY bl_checkable_gradient_endColor bl_checkable_gradient_gradientRadius bl_checkable_gradient_startColor bl_checkable_gradient_type bl_checkable_gradient_useLevel bl_checkable_solid_color bl_checkable_stroke_color bl_checked_gradient_angle bl_checked_gradient_centerColor bl_checked_gradient_centerX bl_checked_gradient_centerY bl_checked_gradient_endColor bl_checked_gradient_gradientRadius bl_checked_gradient_startColor bl_checked_gradient_type bl_checked_gradient_useLevel bl_checked_solid_color bl_checked_stroke_color bl_corners_bottomLeftRadius bl_corners_bottomRadius bl_corners_bottomRightRadius bl_corners_leftRadius bl_corners_radius bl_corners_rightRadius bl_corners_topLeftRadius bl_corners_topRadius bl_corners_topRightRadius bl_enabled_gradient_angle bl_enabled_gradient_centerColor bl_enabled_gradient_centerX bl_enabled_gradient_centerY bl_enabled_gradient_endColor bl_enabled_gradient_gradientRadius bl_enabled_gradient_startColor bl_enabled_gradient_type bl_enabled_gradient_useLevel bl_enabled_solid_color bl_enabled_stroke_color bl_focused_gradient_angle bl_focused_gradient_centerColor bl_focused_gradient_centerX bl_focused_gradient_centerY bl_focused_gradient_endColor bl_focused_gradient_gradientRadius bl_focused_gradient_startColor bl_focused_gradient_type bl_focused_gradient_useLevel bl_focused_solid_color bl_focused_stroke_color bl_gradient_angle bl_gradient_centerColor bl_gradient_centerX bl_gradient_centerY bl_gradient_endColor bl_gradient_gradientRadius bl_gradient_startColor bl_gradient_type bl_gradient_useLevel bl_padding_bottom bl_padding_left bl_padding_right bl_padding_top bl_pressed_gradient_angle bl_pressed_gradient_centerColor bl_pressed_gradient_centerX bl_pressed_gradient_centerY bl_pressed_gradient_endColor bl_pressed_gradient_gradientRadius bl_pressed_gradient_startColor bl_pressed_gradient_type bl_pressed_gradient_useLevel bl_pressed_solid_color bl_pressed_stroke_color bl_ripple_color bl_ripple_enable bl_selected_gradient_angle bl_selected_gradient_centerColor bl_selected_gradient_centerX bl_selected_gradient_centerY bl_selected_gradient_endColor bl_selected_gradient_gradientRadius bl_selected_gradient_startColor bl_selected_gradient_type bl_selected_gradient_useLevel bl_selected_solid_color bl_selected_stroke_color bl_shape bl_shape_alpha bl_size_height bl_size_width bl_solid_color bl_stroke_color bl_stroke_dashGap bl_stroke_dashWidth bl_stroke_position bl_stroke_width bl_unCheckable_gradient_angle bl_unCheckable_gradient_centerColor bl_unCheckable_gradient_centerX bl_unCheckable_gradient_centerY bl_unCheckable_gradient_endColor bl_unCheckable_gradient_gradientRadius bl_unCheckable_gradient_startColor bl_unCheckable_gradient_type bl_unCheckable_gradient_useLevel bl_unCheckable_solid_color bl_unCheckable_stroke_color bl_unChecked_gradient_angle bl_unChecked_gradient_centerColor bl_unChecked_gradient_centerX bl_unChecked_gradient_centerY bl_unChecked_gradient_endColor bl_unChecked_gradient_gradientRadius bl_unChecked_gradient_startColor bl_unChecked_gradient_type bl_unChecked_gradient_useLevel bl_unChecked_solid_color bl_unChecked_stroke_color bl_unEnabled_gradient_angle bl_unEnabled_gradient_centerColor bl_unEnabled_gradient_centerX bl_unEnabled_gradient_centerY bl_unEnabled_gradient_endColor bl_unEnabled_gradient_gradientRadius bl_unEnabled_gradient_startColor bl_unEnabled_gradient_type bl_unEnabled_gradient_useLevel bl_unEnabled_solid_color bl_unEnabled_stroke_color bl_unFocused_gradient_angle bl_unFocused_gradient_centerColor bl_unFocused_gradient_centerX bl_unFocused_gradient_centerY bl_unFocused_gradient_endColor bl_unFocused_gradient_gradientRadius bl_unFocused_gradient_startColor bl_unFocused_gradient_type bl_unFocused_gradient_useLevel bl_unFocused_solid_color bl_unFocused_stroke_color bl_unPressed_gradient_angle bl_unPressed_gradient_centerColor bl_unPressed_gradient_centerX bl_unPressed_gradient_centerY bl_unPressed_gradient_endColor bl_unPressed_gradient_gradientRadius bl_unPressed_gradient_startColor bl_unPressed_gradient_type bl_unPressed_gradient_useLevel bl_unPressed_solid_color bl_unPressed_stroke_color bl_unSelected_gradient_angle bl_unSelected_gradient_centerColor bl_unSelected_gradient_centerX bl_unSelected_gradient_centerY bl_unSelected_gradient_endColor bl_unSelected_gradient_gradientRadius bl_unSelected_gradient_startColor bl_unSelected_gradient_type bl_unSelected_gradient_useLevel bl_unSelected_solid_color bl_unSelected_stroke_color
styleable background_button_drawable bl_checked_button_drawable bl_unChecked_button_drawable
styleable background_multi_selector bl_multi_selector1 bl_multi_selector2 bl_multi_selector3 bl_multi_selector4 bl_multi_selector5 bl_multi_selector6
styleable background_multi_selector_text bl_multi_text_selector1 bl_multi_text_selector2 bl_multi_text_selector3 bl_multi_text_selector4 bl_multi_text_selector5 bl_multi_text_selector6
styleable background_press bl_pressed_color bl_unpressed_color
styleable background_selector bl_checkable_drawable bl_checked_drawable bl_enabled_drawable bl_focused_activated bl_focused_drawable bl_focused_hovered bl_pressed_drawable bl_selected_drawable bl_unCheckable_drawable bl_unChecked_drawable bl_unEnabled_drawable bl_unFocused_activated bl_unFocused_drawable bl_unFocused_hovered bl_unPressed_drawable bl_unSelected_drawable
styleable background_selector_pre_21 bl_checkable_solid_color bl_checkable_stroke_color bl_checked_solid_color bl_checked_stroke_color bl_enabled_solid_color bl_enabled_stroke_color bl_focused_solid_color bl_focused_stroke_color bl_pressed_solid_color bl_pressed_stroke_color bl_selected_solid_color bl_selected_stroke_color bl_unCheckable_solid_color bl_unCheckable_stroke_color bl_unChecked_solid_color bl_unChecked_stroke_color bl_unEnabled_solid_color bl_unEnabled_stroke_color bl_unFocused_solid_color bl_unFocused_stroke_color bl_unPressed_solid_color bl_unPressed_stroke_color bl_unSelected_solid_color bl_unSelected_stroke_color
styleable bl_anim bl_anim_auto_start bl_duration bl_duration_item0 bl_duration_item1 bl_duration_item10 bl_duration_item11 bl_duration_item12 bl_duration_item13 bl_duration_item14 bl_duration_item2 bl_duration_item3 bl_duration_item4 bl_duration_item5 bl_duration_item6 bl_duration_item7 bl_duration_item8 bl_duration_item9 bl_frame_drawable_item0 bl_frame_drawable_item1 bl_frame_drawable_item10 bl_frame_drawable_item11 bl_frame_drawable_item12 bl_frame_drawable_item13 bl_frame_drawable_item14 bl_frame_drawable_item2 bl_frame_drawable_item3 bl_frame_drawable_item4 bl_frame_drawable_item5 bl_frame_drawable_item6 bl_frame_drawable_item7 bl_frame_drawable_item8 bl_frame_drawable_item9 bl_oneshot
styleable bl_other bl_function bl_position
styleable bl_text bl_text_gradient_endColor bl_text_gradient_orientation bl_text_gradient_startColor
styleable include constraintSet
styleable text_selector bl_activated_textColor bl_active_textColor bl_checkable_textColor bl_checked_textColor bl_enabled_textColor bl_expanded_textColor bl_focused_textColor bl_pressed_textColor bl_selected_textColor bl_unActivated_textColor bl_unActive_textColor bl_unCheckable_textColor bl_unChecked_textColor bl_unEnabled_textColor bl_unExpanded_textColor bl_unFocused_textColor bl_unPressed_textColor bl_unSelected_textColor
xml standalone_badge
xml standalone_badge_gravity_bottom_end
xml standalone_badge_gravity_bottom_start
xml standalone_badge_gravity_top_start
xml standalone_badge_offset
xml web_files_public
