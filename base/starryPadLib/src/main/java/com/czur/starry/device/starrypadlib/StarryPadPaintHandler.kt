package com.czur.starry.device.starrypadlib

import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.handler.SPContentHandler

/**
 * Created by 陈丰尧 on 2024/6/3
 */
object StarryPadPaintHandler : SPContentHandler() {

    private const val KEY_NEW_PAINT_FILE_NAME = "new_paint_file_name"  // 新的画板文件名

    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
            // 新的画板文件名
            KEY_NEW_PAINT_FILE_NAME to LiveTrans(newPaintFileNameLive as MutableLiveData) {
                it
            }
        )
    }
    override val authority: String
        get() = "com.czur.starry.device.starrypad.provider.StarryPadPaintProvider"


    // 新的画板文件名
    val newPaintFileNameLive = createMutableLive { newPaintFileName }
    var newPaintFileName: String
        get() = getValue(KEY_NEW_PAINT_FILE_NAME, "")
        set(value) = setValue(KEY_NEW_PAINT_FILE_NAME, value)
}