plugins {
    alias(libs.plugins.library)
    alias(libs.plugins.kotlinAndroid)
}

private val pkgName = "com.czur.starry.device.starrypadlib"
android.buildFeatures.buildConfig = true

android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdkVersion.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")

        setFlavorDimensions(listOf("constantEnv"))
    }

    buildTypes {
        create("unsigned") {
            isMinifyEnabled = false
        }
    }

    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {// 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

    implementation(project(":baselib"))
}