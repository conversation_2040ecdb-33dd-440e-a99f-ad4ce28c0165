package com.czur.starry.device.bluetoothlib.gatt

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import java.util.*

/**
 * Created by 陈丰尧 on 2022/11/2
 */

/**
 * Gatt的定义
 */
abstract class GattDefine(
    val uuid: UUID,
    val name: String
) {
    override fun toString(): String {
        return "name:${name} - uuid:${uuid}"
    }
}


abstract class GattCharacteristic(uuid: String, name: String, val belongService: GattService<*>) :
    GattDefine(UUID.fromString(uuid), name) {
    override fun toString(): String {
        return "Characteristic(${super.toString()})"
    }
}

abstract class GattService<C : GattCharacteristic>(uuid: String, name: String) :
    GattDefine(UUID.fromString(uuid), name) {
    override fun toString(): String {
        return "GattService(${super.toString()})"
    }
}


/**
 * HID Service
 */
object GattHidService : GattService<GattHidService.HidCharacteristic>(
    "00001812-0000-1000-8000-00805f9b34fb",
    "HID Service"
) {
    sealed class HidCharacteristic(uuid: String, name: String) :
        GattCharacteristic(uuid, name, this)

    object ReportCharacteristic : HidCharacteristic(
        "00002a4d-0000-1000-8000-00805f9b34fb",
        "Report Characteristic"
    )
}

/**
 * 电池信息
 */
object GattBatteryService : GattService<GattBatteryService.BatteryCharacteristic>(
    "0000180F-0000-1000-8000-00805f9b34fb",
    "Battery Service"
) {
    sealed class BatteryCharacteristic(uuid: String, name: String) :
        GattCharacteristic(uuid, name, this)

    object BatteryLevelCharacteristic : BatteryCharacteristic(
        "00002a19-0000-1000-8000-00805f9b34fb",
        "Battery Level Characteristic"
    )
}

object GattSecureDFUService : GattService<GattSecureDFUService.DFUCharacteristic>(
    "0000fe59-0000-1000-8000-00805f9b34fb",
    "Secure DFU Service"
) {
    sealed class DFUCharacteristic(uuid: String, name: String) :
        GattCharacteristic(uuid, name, this)

    object ButtonlessDFUCharacteristic : DFUCharacteristic(
        "8ec90003-f315-4F60-9FB8-838830daea50",
        "Buttonless DFU Characteristic"
    )
}

/**
 * 设备信息
 */
object GattDeviceInfoService : GattService<GattDeviceInfoService.DeviceInfoCharacteristic>(
    "0000180a-0000-1000-8000-00805f9b34fb",
    "Device Information Service"
) {
    sealed class DeviceInfoCharacteristic(uuid: String, name: String) :
        GattCharacteristic(uuid, name, this)

    object FirmwareRevisionStrCharacteristic : DeviceInfoCharacteristic(
        "00002a26-0000-1000-8000-00805f9b34fb",
        "Firmware Revision String"
    )

    object PnPIdStrStrCharacteristic : DeviceInfoCharacteristic(
        "00002a50-0000-1000-8000-00805f9b34fb",
        "PnP ID"
    ) {
        private const val TAG = "PnPIdStrStrCharacteristic"

        data class PnPInfo(
            val pid: Int,
            val vid: Int,
        )

        /**
         * 解析Pnp信息
         * 根据蓝牙官网的文档进行解析
         * https://www.bluetooth.com/specifications/assigned-numbers/
         * https://www.bluetooth.org/docman/handlers/DownloadDoc.ashx?doc_id=555004
         */
        fun parse(value: ByteArray): PnPInfo? {
            logTagD(TAG, "解析PnP ID")
            if (value.size != 7) {
                logTagW(TAG, "数据长度不符合PnP ID定义, 无法解析")
                return null
            }
            val pid = (value[4].toUByte().toUInt() shl 8) or value[3].toUInt()
            val vid = (value[2].toUByte().toUInt() shl 8) or value[1].toUInt()
            return PnPInfo(pid.toInt(), vid.toInt())
        }
    }
}



