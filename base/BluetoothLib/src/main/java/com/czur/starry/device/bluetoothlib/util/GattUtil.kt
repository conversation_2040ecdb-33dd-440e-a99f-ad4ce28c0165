package com.czur.starry.device.bluetoothlib.util

import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService

import com.czur.starry.device.bluetoothlib.gatt.GattBatteryService
import com.czur.starry.device.bluetoothlib.gatt.GattCharacteristic
import com.czur.starry.device.bluetoothlib.gatt.GattService

/**
 * Created by 陈丰尧 on 2022/10/19
 */
private const val TAG = "BTGattUtil"

/**
 * 获取GattService
 */
fun BluetoothGatt.getService(gattService: GattService<*>): BluetoothGattService? =
    getService(gattService.uuid)

fun BluetoothGattService.getCharacteristic(gattCharacteristic: GattCharacteristic): BluetoothGattCharacteristic? =
    getCharacteristic(gattCharacteristic.uuid)


/**
 * 读取属性值
 */
fun <S : GattService<C>, C : GattCharacteristic> BluetoothGatt.readCharacteristic(
    characteristic: C
): Boolean {
    val service: S = characteristic.belongService as S
    return readCharacteristic(service, characteristic)
}

fun <S : GattService<C>, C : GattCharacteristic> BluetoothGatt.readCharacteristic(
    service: S,
    characteristic: C
): Boolean {
    logTagV(TAG, "读取ble值:${service.name} - ${characteristic.name}")
    val gattService = getService(service)
    if (gattService == null) {
        logTagW(TAG, "没有Service:$service")
        return false
    }
    val gattCharacteristic = gattService.getCharacteristic(characteristic)
    if (gattCharacteristic == null) {
        logTagW(TAG, "没有找到Characteristic:$characteristic")
        return false
    }
    return readCharacteristic(gattCharacteristic)
}

/**
 * 写入Characteristic
 */
fun <S : GattService<C>, C : GattCharacteristic> BluetoothGatt.writeCharacteristic(
    characteristic: C,
    data: ByteArray,
    beforeWriteBlock: ((BluetoothGattCharacteristic) -> Unit)? = null
): Boolean {
    val service: S = characteristic.belongService as S
    return writeCharacteristic(service, characteristic, data, beforeWriteBlock)
}

fun <S : GattService<C>, C : GattCharacteristic> BluetoothGatt.writeCharacteristic(
    service: S,
    characteristic: C,
    data: ByteArray,
    beforeWriteBlock: ((BluetoothGattCharacteristic) -> Unit)? = null
): Boolean {
    logTagV(TAG, "写入ble值:${service.name} - ${characteristic.name}")
    val gattService = getService(service)
    if (gattService == null) {
        logTagW(TAG, "没有Service:$service")
        return false
    }
    val gattCharacteristic = gattService.getCharacteristic(characteristic)
    if (gattCharacteristic == null) {
        logTagW(TAG, "没有找到Characteristic:$characteristic")
        return false
    }
    beforeWriteBlock?.invoke(gattCharacteristic)
    val setRes = gattCharacteristic.setValue(data)
    if (!setRes) {
        logTagW(TAG, "setGattValue失败:${characteristic}")
    }
    return writeCharacteristic(gattCharacteristic)
}

/**
 * 获取电量的Characteristic
 */
fun BluetoothGatt.readBatteryLevel(): Boolean =
    readCharacteristic(GattBatteryService.BatteryLevelCharacteristic)

fun <S : GattService<C>, C : GattCharacteristic> BluetoothGatt.enableNotification(
    characteristic: C
): Boolean {
    val service: S = characteristic.belongService as S
    return enableNotification(service, characteristic)
}

/**
 * 开启通知
 */
fun <S : GattService<C>, C : GattCharacteristic> BluetoothGatt.enableNotification(
    service: S,
    characteristic: C
): Boolean {
    logTagV(TAG, "开启通知:${service.name} - ${characteristic.name}")
    val gattService = getService(service)
    if (gattService == null) {
        logTagW(TAG, "service:${service} 为空")
        return false
    }

    // 这里有多个重名的UUID, 必须要统一处理
    val characteristicList = gattService.characteristics.filter {
        characteristic.uuid == it.uuid
    }
    if (characteristicList.isEmpty()) {
        logTagW(TAG, "characteristic:${characteristic} 为空")
        return false
    }

    characteristicList.forEach { btCharacteristic ->
        val success = setCharacteristicNotification(btCharacteristic, true)
        if (success) {
            btCharacteristic.descriptors.filterNotNull().forEach { dp ->
                if ((btCharacteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                    dp.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                } else if ((btCharacteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                    dp.value = BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                }
                writeDescriptor(dp)
            }
        }
    }
    return true
}