package com.czur.starry.device.bluetoothlib.util

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothHearingAid

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/07
 */


class BluetoothHearingAidProxy(private val device: BluetoothHearingAid) {
    companion object {
        const val ACTION_ACTIVE_DEVICE_CHANGED =
            "android.bluetooth.hearingaid.profile.action.ACTIVE_DEVICE_CHANGED"
    }

    private val bluetoothHearingAid = device


    private val bluetoothHearingAidClass by lazy {
        Class.forName("android.bluetooth.BluetoothHearingAid")
    }

    private val getActiveDevices by lazy {
        bluetoothHearingAidClass.getDeclaredMethod("getActiveDevices").apply { isAccessible = true }
    }
    private val getConnectionPolicy by lazy {
        bluetoothHearingAidClass.getDeclaredMethod(
            "getConnectionPolicy",
            BluetoothDevice::class.java
        ).apply { isAccessible = true }
    }
    private val setConnectionPolicy by lazy {
        bluetoothHearingAidClass.getDeclaredMethod(
            "setConnectionPolicy",
            BluetoothDevice::class.java,
            Int::class.java
        ).apply { isAccessible = true }
    }
    private val setVolume by lazy {
        bluetoothHearingAidClass.getDeclaredMethod("setVolume", Int::class.java)
            .apply { isAccessible = true }
    }

    private val getHiSyncId by lazy {
        bluetoothHearingAidClass.getDeclaredMethod("getHiSyncId", BluetoothDevice::class.java)
            .apply { isAccessible = true }
    }
    private val getDevicesMatchingConnectionStates by lazy {
        bluetoothHearingAidClass.getDeclaredMethod(
            "getDevicesMatchingConnectionStates",
            IntArray::class.java
        ).apply {
            isAccessible = true
        }
    }
    private val getConnectionState by lazy {
        bluetoothHearingAidClass.getDeclaredMethod(
            "getConnectionState",
            BluetoothDevice::class.java
        ).apply { isAccessible = true }
    }


    fun getActiveDevices(): List<BluetoothDevice> {
        return getActiveDevices.invoke(bluetoothHearingAid) as List<BluetoothDevice>
    }

    fun getConnectionPolicy(device: BluetoothDevice): Int {
        return getConnectionPolicy.invoke(bluetoothHearingAid, device) as Int
    }

    fun setConnectionPolicy(device: BluetoothDevice, key: Int): Boolean {
        return setConnectionPolicy.invoke(bluetoothHearingAid, device, key) as Boolean
    }

    fun setVolume(volume: Int) {
        setVolume.invoke(bluetoothHearingAid, volume)
    }

    fun getHiSyncId(device: BluetoothDevice): Long {
        return getHiSyncId.invoke(bluetoothHearingAid, device) as Long
    }

    fun getDevicesMatchingConnectionStates(states: IntArray): List<BluetoothDevice> {
        return getDevicesMatchingConnectionStates.invoke(
            bluetoothHearingAid,
            states
        ) as List<BluetoothDevice>
    }

    fun getConnectionState(device: BluetoothDevice): Int {
        return getConnectionState.invoke(bluetoothHearingAid, device) as Int
    }
}