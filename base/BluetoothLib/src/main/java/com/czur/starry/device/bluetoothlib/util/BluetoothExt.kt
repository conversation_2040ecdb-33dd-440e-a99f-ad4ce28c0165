package com.czur.starry.device.bluetoothlib.util

import android.annotation.SuppressLint
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.bluetoothlib.bluetooth.CachedBluetoothDevice
import kotlin.reflect.jvm.internal.impl.metadata.jvm.deserialization.JvmMemberSignature.Method

/**
 * Created by 陈丰尧 on 2022/10/19
 */
private const val TAG = "BluetoothExt"

// 键盘
val CachedBluetoothDevice.isKeyBoard: Boolean
    get() = btClass.deviceClass == BluetoothClass.Device.PERIPHERAL_KEYBOARD

// 鼠标
val CachedBluetoothDevice.isMouse: Boolean
    get() = btClass.deviceClass == BluetoothClass.Device.PERIPHERAL_POINTING

// 麦克风
val CachedBluetoothDevice.ismicroPhone: Boolean
    get() = btClass.deviceClass == BluetoothClass.Device.AUDIO_VIDEO_MICROPHONE ||
            btClass.deviceClass == BluetoothClass.Device.AUDIO_VIDEO_HEADPHONES

// 扬声器
val CachedBluetoothDevice.isSpeaker: Boolean
    get() = btClass.deviceClass == BluetoothClass.Device.AUDIO_VIDEO_LOUDSPEAKER ||
            btClass.deviceClass == BluetoothClass.Device.AUDIO_VIDEO_HIFI_AUDIO ||
            btClass.deviceClass == BluetoothClass.Device.AUDIO_VIDEO_HANDSFREE

// 耳机
val CachedBluetoothDevice.isHeadPhones: Boolean
    get() = btClass.deviceClass == BluetoothClass.Device.AUDIO_VIDEO_WEARABLE_HEADSET

val CachedBluetoothDevice.isWritePad: Boolean
    @SuppressLint("MissingPermission")
    get() = device.name.orEmpty().contains("writepad", true)

private val cancelBondProcessMethod by lazy {
    BluetoothDevice::class.java.getDeclaredMethod("cancelBondProcess").apply {
        isAccessible = true
    }
}
private val removeBondMethod by lazy {
    BluetoothDevice::class.java.getDeclaredMethod("removeBond").apply {
        isAccessible = true
    }
}

/**
 * 判断蓝牙设备是否连接
 * 通过反射
 */
val BluetoothDevice.isConnect: Boolean
    get() {
        val isConnectedMethod = BluetoothDevice::class.java.getDeclaredMethod("isConnected")
        isConnectedMethod.isAccessible = true
        return isConnectedMethod.invoke(this) as Boolean
    }

@SuppressLint("MissingPermission")
fun BluetoothDevice.unpair() {
    val devName = name
    logTagI(TAG, "unpair:$devName")
    val state = bondState
    if (state == BluetoothDevice.BOND_BONDING) {
        cancelBondProcessMethod.invoke(this)
    }
    if (state != BluetoothDevice.BOND_NONE) {
        val successful = removeBondMethod.invoke(this) as Boolean
        logTagD(TAG, "unpair - ${devName}:$successful")
    }
}