package com.czur.starry.device.noticecenter

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.has
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.noticecenter.entity.NoticeDetailEntity
import com.czur.starry.device.noticecenter.net.NoticeManager
import com.czur.starry.device.noticelib.NOTICE_STATUS_PROCESS
import com.czur.starry.device.noticelib.NOTICE_STATUS_READ
import com.czur.starry.device.noticelib.NOTICE_STATUS_UNREAD
import com.czur.starry.device.noticelib.NOTICE_TYPE_INVITED_COMPANY
import com.czur.starry.device.noticelib.NotifyMsgHandler
import kotlin.math.ceil
import kotlin.math.max

/**
 * Created by 陈丰尧 on 4/8/21
 */
private const val TAG = "NoticePadModel"

class NoticePadModel : ViewModel() {

    private val pageNum = DifferentLiveData(1)
    private val pageTotal = DifferentLiveData(1)

    val pageInfo = MediatorLiveData<Pair<Int, Int>>()
    private var firstLoad = true

    // 上一页是否可用
    val preBtnEnable = pageNum.map {
        it != 1
    }

    // 下一页是否可用
    val nextBtnEnable = pageInfo.map {
        it.first != it.second
    }

    // 当前页的通知数据
    val noticeData = MutableLiveData<List<NoticeDetailEntity>>()

    // 当前是否没有消息
    val noNotice = noticeData.map {
        it.isNullOrEmpty()
    }

    val readAllEnableLive = DifferentLiveData(false)
    private var readAllLive by LiveDataDelegate(readAllEnableLive)

    init {
        pageNum.observeForever {
            loadNoticeData()
        }

        pageInfo.addSource(pageNum) {
            makePageInfo()
        }
        pageInfo.addSource(pageTotal) {
            makePageInfo()
        }
    }


    private fun makePageInfo() {
        val num = pageNum.value ?: 1
        val total = pageTotal.value ?: 1
        pageInfo.value = num to total
    }

    /**
     * 加载通知数据
     * @param reset 是否需要重新加载
     */
    fun loadNoticeData(reset: Boolean = false) {
        launch {
            if (reset) {
                // 重新加载第一页
                logTagD(TAG, "回到第一页重新加载")
                pageNum.value = 1
                firstLoad = true
            }
            val entity = NoticeManager.getNotices(pageNum.value!!)
            val total = ceil(entity.total / 6.toDouble()).toInt()
            pageTotal.postValue(max(1, total))
            noticeData.postValue(entity.notices)
            if (firstLoad) {
                updateReadAllStatus()
            }
            firstLoad = false
        }
    }

    /**
     * 加载前一页
     */
    fun loadPrePage() {
        val current = pageNum.value ?: 1
        if (current > 1) {
            pageNum.value = current - 1
        }
    }

    /**
     * 加载后一页
     */
    fun loadNextPage() {
        val current = pageNum.value ?: 1
        val total = pageTotal.value ?: 1
        if (current < total) {
            pageNum.value = current + 1
        }
    }

    /**
     * 将所有消息标记为已读
     */
    fun readAll() {
        launch {
            val updateStatus = NoticeManager.updateNoticesStatus(NOTICE_STATUS_READ)
            if (updateStatus) {
                val data = (noticeData.value ?: listOf()).map {
                    if (it.status == NOTICE_STATUS_UNREAD) {
                        it.copy(status = NOTICE_STATUS_READ)
                    } else {
                        it
                    }
                }

                noticeData.value = data
            }
            updateReadAllStatus()
        }
    }

    /**
     * 阅读单条消息
     */
    fun readOne(entity: NoticeDetailEntity, updateStatus: Int = NOTICE_STATUS_READ) {
        launch {
            if (entity.status >= updateStatus) {
                logTagD(TAG, "该条消息不需要改变状态, 不需要发送请求")
                return@launch
            }

            val result = NoticeManager.updateNoticesStatus(updateStatus, entity.id)
            if (result) {
                if (entity.status != updateStatus) {
                    logTagD(TAG, "更新列表UI")
                    val nowList = noticeData.value ?: emptyList()
                    val newList = nowList.map {
                        if (it.id == entity.id) {
                            it.copy(status = updateStatus)
                        } else {
                            it
                        }
                    }
                    noticeData.value = newList
                }
            }
            updateReadAllStatus()
        }
    }

    /**
     * 处理通知
     */
    fun handleAction(notice: NoticeDetailEntity, actionType: Int) {
        logTagV(TAG, "处理消息")
        launch {
            NotifyMsgHandler.processNotifyMsg(
                CZURAtyManager.currentActivity(),
                notice.id,
                notice.type,
                actionType,
                notice.data
            ).then {
                logTagV(TAG, "操作成功")
                // 操作成功,标记为已操作
                readOne(entity = notice, NOTICE_STATUS_PROCESS)
                // 如果是企业,则将所有对该企业的邀请信息, 标记为已操作
                when (notice.type) {
                    NOTICE_TYPE_INVITED_COMPANY -> {
                        val enterpriseId = notice.dataObj["enterpriseId"].asString
                        finishAllInvitedMsg(enterpriseId)
                    }
                }
            }.orNot {
                logTagD(TAG, "操作失败")
            }
        }
    }

    private fun finishAllInvitedMsg(enterpriseId: String) {
        logTagV(TAG, "将企业:${enterpriseId}的所有消息标记为[已处理]")
        val newMap = (noticeData.value ?: emptyList()).map {
            if ((it.hasAction() && it.type == NOTICE_TYPE_INVITED_COMPANY)) {
                val itEnterpriseId =
                    if (it.dataObj.has("enterpriseId")) it.dataObj["enterpriseId"].asString else ""
                if (itEnterpriseId == enterpriseId) {
                    // 将消息更新
                    it.copy(status = NOTICE_STATUS_PROCESS)
                } else {
                    it
                }
            } else {
                it
            }
        }
        // 更新消息
        noticeData.value = newMap
    }

    /**
     * 删除指定消息
     */
    fun delNotice(noticeId: Long) {
        launch {
            val result = NoticeManager.delNotice(noticeId)
            if (result) {
                logTagD(TAG, "删除成功")
                if (noticeData.value!!.size == 1 && pageNum.value!! != 1) {
                    loadPrePage()
                } else {
                    loadNoticeData()
                }
            }
            updateReadAllStatus()
        }
    }

    /**
     * 删除全部
     */
    fun delAll() {
        launch {
            val result = NoticeManager.delNotice()
            if (result) {
                logTagD(TAG, "全部删除成功")
                noticeData.value = listOf()
                pageTotal.value = 1
                pageNum.value = 1
            }
            updateReadAllStatus()
        }
    }

    /**
     * 更新全部已读按钮状态
     */
    private fun updateReadAllStatus() {
        launch {
            readAllLive = hasUnReadMsg()
        }
    }

    /**
     * 是否还有未读消息
     */
    private suspend fun hasUnReadMsg(): Boolean {
        // 检查当前页
        val currentPageRead = noticeData.value?.has {
            !it.read
        } ?: false
        if (currentPageRead) {
            // 当前页就有未读消息
            return true
        }
        return NoticeManager.hasUnReadNotices()
    }

    private fun Boolean.then(block: () -> Unit): Boolean {
        if (this) {
            block()
        }
        return this
    }

    private fun Boolean.orNot(block: () -> Unit): Boolean {
        if (!this) {
            block()
        }
        return this
    }
}