package com.czur.starry.device.noticecenter.notice.interceptor

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.ONE_SECOND

import com.czur.starry.device.noticecenter.provider.CheckUnReadMsg
import com.czur.starry.device.noticecenter.provider.NotifyMsgService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * Created by 陈丰尧 on 2021/8/9
 */
class UserInfoInterceptor(val context: Context) : NoticeInterceptor {
    companion object {
        private const val TAG = "UserInfoInterceptor"
    }

    // 需要关闭后台
    private val czPackages = listOf(
        "com.czur.starry.device.contacts",
        "com.czur.starry.device.file",
        "com.czur.starry.device.hdmiin",
        "com.czur.starry.device.meeting",
        "com.czur.starry.device.personalcenter",
        "com.czur.starry.device.settings"
    )

    private val am by lazy {
        context.getSystemService(ActivityManager::class.java)
    }
    private val pm by lazy {
        context.packageManager
    }

    private val bgPkgWhiteList = listOf(
        "com.czur.keystone"
    )

    override suspend fun intercept(msgType: MsgType, msg: NoticeMsg, scope: CoroutineScope): Boolean {
        return when {
            msgType.match(MsgType.USER_INFO, MsgType.USER_INFO_OTHER_DEVICE_LOGIN) -> {
                if (!UserHandler.isLogin) {
                    logTagD(TAG, "当前已经是非登录状态, 拦截消息:${msgType}")
                    return true
                }
                logTagD(TAG, "用户被顶掉")
                logTagV(TAG, "将UserHandler标记为未登录")
                runBlocking {
                    UserHandler.logout(bySelf = false)
                    delay(ONE_SECOND)   // 等待各个进程自己执行退出逻辑
                }
                startHome()
                cleanBgProcess()

                // 用户被顶掉后
                // 需要杀死其他进程
//                logTagV(TAG, ">>> killBackgroundProcesses <<<")
//                czPackages.forEach { packageName ->
//                    am.forceStopPackage(packageName)
//                }

                false
            }
            msgType.match(MsgType.USER_INFO, MsgType.USER_INFO_OTHER_DEVICE_LOGIN) -> {
                NotifyMsgService.addTask(context, CheckUnReadMsg)
                true
            }
            else -> false
        }
    }

    private fun startHome() {
        logTagV(TAG, ">>> 回到Home <<<")
        val intent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_HOME)
        }
        context.startActivity(intent)
    }

    private fun cleanBgProcess() {
        logTagV(TAG, ">>> cleanBgProcess <<<")
        val infoList = am.runningAppProcesses

        infoList.forEach {
            if (it.importance > ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                val pkgList = it.pkgList
                pkgList.forEach { pkgName ->
                    if (pkgName !in bgPkgWhiteList && !pkgName.contains("ecloud", true)) {
                        logTagV(TAG, "killBackgroundProcesses: $pkgName")
                        am.killBackgroundProcesses(pkgName)
                    } else {
                        logTagD(TAG, "$pkgName 在白名单中, 不销毁")
                    }
                }
            }
        }

    }
}