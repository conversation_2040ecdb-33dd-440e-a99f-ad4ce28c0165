package com.czur.starry.device.noticecenter.net

import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.core.util.checkAndThrowException

import com.czur.starry.device.noticecenter.netty.PORT
import com.czur.starry.device.noticecenter.netty.SERVER_ADDRESS
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/5/20
 */
object NettyNodeManager {
    private const val TAG = "NettyNodeManager"
    private val nettyNodeService: INettyService by lazy {
        HttpManager.getService()
    }

    private val DEF_NODE_INFO = SERVER_ADDRESS to PORT

    var currentNodeInfo = DEF_NODE_INFO

    /**
     * 获取Netty节点信息
     */
    suspend fun getNettyNodeInfo(): Pair<String, Int> {
        return withContext(Dispatchers.IO) {
            val entity = nettyNodeService.getNotices()
            checkAndThrowException(entity)
            if (entity.body == null) {
                logTagW(TAG, "没有获取到节点信息,使用默认信息")
                currentNodeInfo = DEF_NODE_INFO
                DEF_NODE_INFO
            } else {
                val nodeInfo = entity.body.host to entity.body.port
                logTagV(TAG,"获取到节点信息:host:${nodeInfo.first},port:${nodeInfo.second}")
                currentNodeInfo = nodeInfo
                nodeInfo
            }
        }
    }
}