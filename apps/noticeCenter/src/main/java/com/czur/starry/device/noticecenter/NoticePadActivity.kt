package com.czur.starry.device.noticecenter

import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.NoNavActivity
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.MsgType.Companion.SYNC
import com.czur.starry.device.baselib.notice.MsgType.Companion.SYNC_NOTICE
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.noticecenter.app.NoticeApp.Companion.context
import com.czur.starry.device.noticecenter.dialog.DetailDialog
import com.czur.starry.device.noticecenter.entity.NoticeDetailEntity
import com.czur.starry.device.noticecenter.entity.NoticeSyncData
import com.czur.starry.device.noticecenter.provider.CheckUnReadMsg
import com.czur.starry.device.noticecenter.provider.NotifyMsgService
import com.czur.starry.device.noticelib.ACTION_TYPE_POSITIVE
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers

private const val TAG = "NoticePadActivity"

class NoticePadActivity : NoNavActivity() {
    override fun getLayout() = R.layout.activity_main

    private val model: NoticePadModel by viewModels()
    private val adapter = NoticeAdapter()

    private val gson = Gson()

    private val noticeRv by findView<RecyclerView>(R.id.noticeRv)
    private val preIv by findView<ImageView>(R.id.preIv)
    private val nextIv by findView<ImageView>(R.id.nextIv)
    private val readAllBtn by findView<CommonButton>(R.id.readAllBtn)
    private val clearBtn by findView<CommonButton>(R.id.clearBtn)
    private val pageInfoTv by findView<TextView>(R.id.pageInfoTv)
    private val noNoticeGroup by findView<Group>(R.id.noNoticeGroup)
    private val hasNoticeGroup by findView<Group>(R.id.hasNoticeGroup)

    override fun initViews() {
        super.initViews()

        noticeRv.layoutManager = LinearLayoutManager(this)
        noticeRv.adapter = adapter
        noticeRv.closeDefChangeAnimations()

        preIv.setOnClickListener {
            model.loadPrePage()
        }
        nextIv.setOnClickListener {
            model.loadNextPage()
        }
        // 标记所有为已读
        readAllBtn.setOnClickListener {
            model.readAll()
        }

        // 删除全部消息
        clearBtn.setOnClickListener {
            model.delAll()
        }

        noticeRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            logTagD(TAG, "点击Item: $pos")
            val entity = adapter.getData(pos)
            model.readOne(entity)
            when (view.id) {
                R.id.noticeActionBtn -> model.handleAction(entity, ACTION_TYPE_POSITIVE)
                else -> DetailDialog(entity, ::noticeDel, ::noticeAction).show()
            }
            true

        }

        model.pageInfo.observe(this) {
            pageInfoTv.text = getString(R.string.text_page_info, it.first, it.second)
        }
        // 上一页和下一页按钮是否可用,如果不可用将按钮置为隐藏
        model.preBtnEnable.observe(this) {
            preIv.isEnabled = it
            if (it) preIv.show() else preIv.invisible()
        }
        model.nextBtnEnable.observe(this) {
            nextIv.isEnabled = it
            if (it) nextIv.show() else nextIv.invisible()
        }

        model.noticeData.observe(this) {
            adapter.setData(it)
        }

        // 根据是否有消息,切换显示
        model.noNotice.observe(this) {
            if (it) {
                noNoticeGroup.show()
                hasNoticeGroup.gone()
            } else {
                noNoticeGroup.gone()
                hasNoticeGroup.show()
            }
        }

        model.readAllEnableLive.observe(this) {
            readAllBtn.isEnabled = it
        }

        NoticeHandler.register(MsgType(SYNC, SYNC_NOTICE), this) {
            logTagD(TAG, "有新的通知")
            val dataJson = it.getStr()
            if (dataJson.isEmpty()) {
                logTagD(TAG, "有新消息, 回到第一页")
                model.loadNoticeData(true)
            } else {
                val data = gson.fromJson(dataJson, NoticeSyncData::class.java)
                if (data.fromApi == "updateNoticesStatus") {
                    if ("WEB".equals(data.fromPlatform, true)) {
                        logTagD(TAG, "WEB端阅读了消息, 刷新一次")
                        model.loadNoticeData(false)
                    } else {
                        logTagD(TAG, "阅读消息, 不翻页")
                    }
                } else {
                    logTagD(TAG, "有新消息, 回到第一页")
                    model.loadNoticeData(true)
                }
            }

        }

    }

    /**
     * 对话框底部按钮的点击事件
     */
    private fun noticeAction(btnType: Int, noticeDetail: NoticeDetailEntity, dialog: DetailDialog) {
        model.handleAction(noticeDetail, btnType)
        dialog.dismiss()
    }

    /**
     * Dialog 点击删除按钮
     */
    private fun noticeDel(noticeDetail: NoticeDetailEntity, dialog: DetailDialog) {
        dialog.dismiss()
        model.delNotice(noticeDetail.id)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        model.loadNoticeData()

        launch(Dispatchers.IO) {
            doWithoutCatch {
                WatchService.startWatchingService(context)
            }

            doWithoutCatch {
                logTagD(TAG, "检查一次是否有未读消息")
                NotifyMsgService.addTask(this@NoticePadActivity, CheckUnReadMsg)
            }
        }
    }

}