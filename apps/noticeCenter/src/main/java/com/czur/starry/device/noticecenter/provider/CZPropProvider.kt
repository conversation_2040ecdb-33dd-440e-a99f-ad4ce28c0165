package com.czur.starry.device.noticecenter.provider

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.base.CZURContentProvider
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.handler.CZPropHandler
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.stringToCursor
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking

/**
 * Created by 陈丰尧 on 2024/5/23
 * 用来管理系统的属性, 因为Android12中不能方便的使用systemProp, 所以使用ContentProvider来实现
 */


class CZPropProvider : CZURContentProvider() {

    private val Context.propTempDataStore: DataStore<Preferences> by preferencesDataStore(
        name = CZPropHandler.PropScope.TEMP.scopeName,
        corruptionHandler = createDefCorruptionHandler(CZPropHandler.PropScope.TEMP.scopeName)
    )
    private val Context.propPersistDataStore: DataStore<Preferences> by preferencesDataStore(
        name = CZPropHandler.PropScope.PERSIST.scopeName,
        corruptionHandler = createDefCorruptionHandler(CZPropHandler.PropScope.PERSIST.scopeName)
    )

    private val keyMap = hashMapOf<String, Preferences.Key<String>>()


    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        val datastore = uri.toDataStore() ?: return null
        val key = uri.getQueryParameter(CZPropHandler.PARAM_KEY_NAME) ?: return null

        val dataStoreKey = getSaveKey(key)
        val value = runBlocking {
            datastore.data.first()[dataStoreKey]
        } ?: return null

        return stringToCursor(value)
    }

    private fun getSaveKey(spKey: String): Preferences.Key<String> {
        return keyMap.getOrPut(spKey) {
            stringPreferencesKey(spKey)
        }
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        val dataStore = uri.toDataStore() ?: return 0
        if (selection == CZPropHandler.DEL_SELECTION_CLEAR_ALL){
            val dataSize = runBlocking {
                dataStore.data.toList().size
            }
            runBlocking {
                dataStore.edit {
                    it.clear()
                }
            }
            return dataSize
        }
        // TODO 删除指定ITEM
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        if (values == null || values.size() == 0) {
            return 0
        }
        val dataStore = uri.toDataStore() ?: return 0
        var updateSize = 0
        runBlocking {
            dataStore.edit { preferences ->
                values.keySet().forEach {
                    val key = getSaveKey(it)
                    val value: String? = values.getAsString(it)
                    value?.let { str ->
                        updateSize++
                        preferences[key] = str
                    }
                }
            }
        }
        return updateSize
    }

    private fun Uri.toDataStore(): DataStore<Preferences>? {
        return when (CZPropHandler.uriMatcher.match(this)) {
            CZPropHandler.PropScope.PERSIST.pathWrapper.code -> {
                globalAppCtx.propPersistDataStore
            }

            CZPropHandler.PropScope.TEMP.pathWrapper.code -> {
                globalAppCtx.propTempDataStore
            }

            else -> null
        }
    }
}