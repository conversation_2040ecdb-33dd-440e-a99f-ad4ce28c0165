package com.czur.starry.device.noticecenter.net

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.noticecenter.entity.NettyNodeInfo

/**
 * Created by 陈丰尧 on 2021/5/20
 */
interface INettyService {
    @MiaoHttpGet("/api/starry/netty/node")
    fun getNotices(
        clazz: Class<NettyNodeInfo> = NettyNodeInfo::class.java
    ): MiaoHttpEntity<NettyNodeInfo>

    @MiaoHttpGet("/api/starry/netty/country")
    fun getServerCountry(
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}