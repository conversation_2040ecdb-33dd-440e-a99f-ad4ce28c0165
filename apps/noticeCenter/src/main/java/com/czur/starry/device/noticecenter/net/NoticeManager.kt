package com.czur.starry.device.noticecenter.net

import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.noticecenter.app.NoticeApp
import com.czur.starry.device.noticecenter.entity.NoticeEntity
import com.czur.starry.device.noticecenter.provider.CheckUnReadMsg
import com.czur.starry.device.noticecenter.provider.NotifyMsgService
import com.czur.starry.device.noticelib.NOTICE_TYPE_INVITED_COMPANY
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 4/8/21
 */
object NoticeManager {
    private const val TAG = "NoticeManager"
    private val noticeService: INoticeService by lazy {
        HttpManager.getService()
    }

    /**
     * 获取通知消息
     * @param pageNum 第几页
     * @param pageSize 每页多少条记录(默认是6条)
     */
    suspend fun getNotices(pageNum: Int, pageSize: Int = 6): NoticeEntity =
        withContext(Dispatchers.IO) {
            val notices = noticeService.getNotices(pageNum, pageSize)
            try {
                notices.withCheck().body
            } catch (exp: Exception) {
                logTagW(TAG, "获取通知消息出错", tr = exp)
                NoticeEntity(0, emptyList())
            }
        }


    /**
     * 更新消息状态
     * @param status:要更新的状态: 0 未读; 1已读;2已处理
     * @param id: 消息的ID, 如果传入0, 表示所有信息
     */
    suspend fun updateNoticesStatus(status: Int, id: Long = 0L): Boolean =
        withContext(Dispatchers.IO) {
            val result = noticeService.updateNoticesStatus(id, status)
            val updateResult = if (result.isSuccess) result.body else false
            if (updateResult) {
                // 发送检查未读任务
                NotifyMsgService.addTask(NoticeApp.context, CheckUnReadMsg)
            }
            updateResult
        }


    /**
     * 删除通知
     * @param id: 通知的ID, 0表示所有信息
     */
    suspend fun delNotice(id: Long = 0L): Boolean = withContext(Dispatchers.IO) {
        val result = noticeService.deleteNotice(id)
        val delResult = try {
            result.withCheck().body
        } catch (exp: Exception) {
            logTagW(TAG, "删除通知失败", tr = exp)
            false
        }
        if (delResult) {
            NotifyMsgService.addTask(NoticeApp.context, CheckUnReadMsg)
        }
        delResult
    }

    /**
     * 是否有未读消息
     */
    suspend fun hasUnReadNotices(): Boolean = withContext(Dispatchers.IO) {
        if (!UserHandler.isLogin) {
            // 没有登录, 则一定不显示
            return@withContext false
        }
        val result = noticeService.hasUnReadNotices()
        if (result.isSuccess) {
            result.body
        } else {
            false
        }
    }

    /**
     * 根据企业id将该企业所有邀请信息设置为已读
     */
    suspend fun readByEnterpriseId(enterpriseId: String): Boolean = withContext(Dispatchers.IO) {
        val result = noticeService.readByEnterpriseId(enterpriseId, NOTICE_TYPE_INVITED_COMPANY)
        if (result.isSuccess) result.body else false
    }
}