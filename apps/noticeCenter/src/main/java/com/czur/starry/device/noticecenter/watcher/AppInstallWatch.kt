package com.czur.starry.device.noticecenter.watcher

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.SystemClock
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.APP_STORE_BASE_URL
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.core.util.createMiaoHttpException
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.SettingUtil.AppStoreSetting
import com.czur.starry.device.baselib.utils.basic.no
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.noticecenter.net.IAppStoreService
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2025/4/14
 */
private const val TAG = "AppInstallWatch"

class AppInstallWatch : CoroutineScope by MainScope() {
    private val receiver: AppInstallReceiver by lazy { AppInstallReceiver() }
    private val appStoreService: IAppStoreService by lazy {
        HttpManager.getService<IAppStoreService>(BASE_URL = APP_STORE_BASE_URL)
    }
    private val gson: Gson by lazy { Gson() }

    // 25.4.14 的默认列表
    private var whiteListPkgList = listOf(
        "com.google.android.apps.meetings",
        "com.microsoft.teams",
        "us.zoom.videomeetings",
        "com.android.chrome",
        "com.wetransfer.app.live",
        "com.amazon.avod.thirdpartyclient",
        "com.google.android.apps.docs.editors.docs",
        "com.microsoft.office.excel",
        "com.microsoft.office.officehub",
        "com.microsoft.office.word",
        "com.microsoft.office.powerpoint",
        "com.google.android.apps.docs.editors.sheets",
        "com.google.android.apps.docs.editors.slides",
        "com.google.android.apps.docs",
        "com.zhiliaoapp.musically",
        "tv.twitch.android.app",
        "com.spotify.music"
    )
    private val googlePkg = listOf(
        "com.google.android.gms",
        "com.android.vending",
        "com.google.android.contactkeys"
    )
    private var lastRefreshTime = -1L


    fun register(context: Context) {
        launch {
            if (AppStoreSetting.isRemindInstallApp()) {
                // 设置了提醒才注册广播, 因为目前的UE中, 只要设置了不再提醒,就没有途径恢复了,所以不需要考虑中途恢复提醒的情况
                val filter = IntentFilter()
                filter.addAction(Intent.ACTION_PACKAGE_ADDED)
                filter.addDataScheme("package")
                context.registerReceiver(receiver, filter)
            }
        }
    }

    fun unregister(context: Context) {
        doWithoutCatch {
            context.unregisterReceiver(receiver)
        }
    }

    private val cacheFile by lazy {
        File(globalAppCtx.cacheDir, "whiteList.json")
    }

    init {
        launch {
            withContext(Dispatchers.IO) {
                if (cacheFile.exists()) {
                    val json = cacheFile.readText()
                    whiteListPkgList =
                        gson.fromJson(json, object : TypeToken<List<String>>() {}.type)
                }
            }
            refreshFromCloud()
        }
    }

    fun isInstallWhiteList(pkg: String): Boolean {
        return whiteListPkgList.contains(pkg) || googlePkg.contains(pkg) || pkg.startsWith("com.czur.starry.device")
    }

    /**
     * 从云端刷新
     */
    suspend fun refreshFromCloud() {
        // 6小时内不刷新
        if (SystemClock.elapsedRealtime() - lastRefreshTime < 6 * ONE_HOUR && lastRefreshTime > 0) {
            logTagD(TAG, "白名单刷新间隔未到, 不刷新")
            return
        }

        if (!AppStoreSetting.isRemindInstallApp()) {
            logTagI(TAG, "用户关闭了提醒, 不再请求该接口")
        }

        val result = getInstallWhiteListFromServer()
        result.getOrNull()?.let {
            logTagV(TAG, "刷新白名单成功:${it.joinToString(",\n")}")
            lastRefreshTime = SystemClock.elapsedRealtime() // 记录成功刷新的时间
            whiteListPkgList = it
            saveToCache(it)
        }
        result.exceptionOrNull()?.let {
            logTagD(TAG, "获取白名单失败: ${it.message}")
        }
    }

    private suspend fun saveToCache(list: List<String>) {
        withContext(Dispatchers.IO) {
            cacheFile.writeText(gson.toJson(list))
        }
    }


    /**
     * 获取应用安装的白名单
     */
    suspend fun getInstallWhiteListFromServer(): Result<List<String>> {
        return withContext(Dispatchers.IO) {
            val region = if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland) 1 else 0
            val result = appStoreService.getInstallWhiteList(region)
            if (result.isSuccess) {
                Result.success(result.bodyList)
            } else {
                Result.failure(createMiaoHttpException(result))
            }
        }
    }


    inner class AppInstallReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val packageName = intent.data?.schemeSpecificPart ?: return
            isInstallWhiteList(packageName).no {
                logTagD(TAG, "安装的应用不在白名单中:${packageName}")
                val remind = runBlocking { AppStoreSetting.isRemindInstallApp() }
                if (!remind) {
                    logTagI(TAG, "用户关闭了提醒, 不提示了")
                    return
                }
                val intent = Intent().apply {
                    setPackage("com.czur.starry.device.appstore")
                    setComponent(
                        ComponentName(
                            "com.czur.starry.device.appstore",
                            "com.czur.starry.device.appstore.InstallAppRemindDialogActivity"
                        )
                    )
                    putExtra("pkgName", packageName)
                }.newTask()
                doWithoutCatch {
                    context.startActivity(intent)
                }
            }.otherwise {
                logTagV(TAG, "安装的应用在白名单中:${packageName}")
            }
        }
    }
}