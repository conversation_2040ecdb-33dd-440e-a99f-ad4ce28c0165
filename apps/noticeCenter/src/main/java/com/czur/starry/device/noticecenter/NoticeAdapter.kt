package com.czur.starry.device.noticecenter

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.noticecenter.entity.NoticeDetailEntity
import com.czur.starry.device.noticelib.NOTICE_STATUS_PROCESS

/**
 * Created by 陈丰尧 on 2021/10/28
 */
class NoticeAdapter : BaseDifferAdapter<NoticeDetailEntity>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: NoticeDetailEntity) {
        holder.visible(!itemData.read, R.id.readPointView)
        holder.setText(itemData.title, R.id.noticeTitleTv)
        holder.setText(itemData.getTimeStr(), R.id.noticeDateTv)
        holder.visible(
            itemData.hasAction() && itemData.status != NOTICE_STATUS_PROCESS,
            R.id.noticeActionBtn
        )
            .setText(itemData.getActionText())
    }

    override fun areItemsTheSame(
        oldItem: NoticeDetailEntity,
        newItem: NoticeDetailEntity
    ): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
        oldItem: NoticeDetailEntity,
        newItem: NoticeDetailEntity
    ): Boolean {
        return oldItem == newItem
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_notice, parent)
    }
}