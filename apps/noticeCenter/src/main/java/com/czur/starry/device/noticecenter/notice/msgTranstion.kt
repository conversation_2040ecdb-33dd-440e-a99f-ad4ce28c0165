package com.czur.starry.device.noticecenter.notice

import android.content.ContentValues
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeConstant
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.notice.byte2NoticeMsg

/**
 * Created by 陈丰尧 on 2021/8/9
 */


/**
 * 从ContentValue中 获取NoticeMsg
 */
internal fun ContentValues.getNoticeMsg(): NoticeMsg {
    val srcBytes = getAsByteArray(NoticeConstant.KEY_BLOB)
    return byte2NoticeMsg(srcBytes)
}

/**
 * 从ContentValue中 获取MsgType
 */
internal fun ContentValues.getMsgType(): MsgType {
    return MsgType.getByKey(getMsgKey())
}


/**
 * 从ContentValue中 获取MsgTypeKey
 */
internal fun ContentValues.getMsgKey(): Long {
    return getAsLong(NoticeConstant.KEY_MSG_TYPE)
}

