package com.czur.starry.device.noticecenter.netty.msg

import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagE

import com.google.gson.JsonArray
import com.google.gson.JsonElement
import com.google.gson.JsonNull
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import org.json.JSONArray

/**
 * Created by 陈丰尧 on 4/15/21
 * 业务信息的层级比较复杂, 所以单独放到一个文件中
 */

/**
 * 业务信息的Msg, 多了一个body属性
 */
open class CZBizMsg : CZMInfoMsg(CZMsgType.BIZ) {
    // 业务信息的Body属性
    @SerializedName("body")
    private var jsonObj: JsonObject? = null

    @delegate:Transient
    val body: BizBody by lazy {
        if (jsonObj == null) {
            jsonObj = JsonObject()
        }
        BizBody(jsonObj!!)
    }

    override fun toString(): String {
        return "BizMsg(body=$body)"
    }
}

/**
 * 业务Msg的Body属性, 因为里面不确定有什么, 所以使用Map
 */
class BizBody(jsonObj: JsonObject) : JsonObjWrapper(jsonObj) {
    companion object {
        // CMD 命令可能出现在 data和replay属性中
        // 发给Server的 放到data里
        // server返回的, 放在reply里
        const val METHOD_CMD = "cmd"
        const val METHOD_USER_LIST = "roomUserList"
        const val METHOD_USER_UPDATE = "userUpdate"

    }

    // Action属性
    var action: CZBizAction
        set(value) {
            set("action", value.name)
        }
        get() {
            // 如果没有action属性, 默认是 REPLAY,表示是服务端的回执信息
            val actionStr = get("action") ?: CZBizAction.REPLAY.name
            return CZBizAction.valueOf(actionStr)
        }

    var module: String? // 有的消息有这个字段
        set(value) {
            set("module", value.toString())
        }
        get() = get("module")

    // data 属性
    val data: BizBodyData by lazy {
        if (!jsonObj.has("data")) {
            logTagV("BizBodyData", "data属性不存在")
            val dataObj = JsonObject()
            jsonObj.add("data", dataObj)
        }
        val jsonElement = jsonObj.get("data")
        BizBodyData(jsonElement)
    }


    // replay 属性
    val reply: JsonObject?
        get() = get("reply")

    // method属性
    val method: String?
        get() = get("method")

}


/**
 * BizBody 里的Data属性
 */
class BizBodyData(jsonElement: JsonElement) : JsonObjWrapper(jsonElement)

/**
 * Gson的JsonObject包装类，
 * 封装了 get、set操作
 */
open class JsonObjWrapper(val jsonElement: JsonElement) {
    val isJsonObj: Boolean
        get() = jsonElement.isJsonObject

    val isJsonArr: Boolean
        get() = jsonElement.isJsonArray

    fun remove(property: String) {
        if (!isJsonObj) {
            logTagW("JsonObjWrapper", "不是JsonObject, 不能使用get方法")
            return
        }
        val jsonObj = jsonElement.asJsonObject
        if (!jsonObj.has(property)) {
            return
        }
        jsonObj.remove(property)
    }

    /**
     * 是否是空的JsonArr
     */
    fun isEmptyArr(): Boolean {
        return if (isJsonArr) {
            jsonElement.asJsonArray.size() == 0
        } else {
            logTagW("JsonObjWrapper", "不是JsonArr, 默认返回false-${jsonElement.toString()}")
            true
        }
    }

    fun toJsonArrOrNull(): JsonArray? {
        return if (isJsonArr) jsonElement.asJsonArray else null
    }

    inline operator fun <reified T> get(key: String): T? {
        if (!isJsonObj) {
            logTagW("JsonObjWrapper", "不是JsonObject, 不能使用get方法")
            return null
        }
        val jsonObj = jsonElement.asJsonObject
        if (!jsonObj.has(key)) {
            return null
        }
        val result: Any? = when (T::class) {
            String::class -> jsonObj.getOrNull(key)?.asString
            Byte::class -> jsonObj.getOrNull(key)?.asByte
            Short::class -> jsonObj.getOrNull(key)?.asShort
            Int::class -> jsonObj.getOrNull(key)?.asInt
            Long::class -> jsonObj.getOrNull(key)?.asLong
            Float::class -> jsonObj.getOrNull(key)?.asFloat
            Number::class -> jsonObj.getOrNull(key)?.asNumber
            Boolean::class -> jsonObj.getOrNull(key)?.asBoolean
            JsonObject::class -> jsonObj?.getOrNull(key)?.asJsonObject
            JsonArray::class -> jsonObj?.getOrNull(key)?.asJsonArray
            else -> null
        }
        return result as T?
    }

    fun JsonObject.getOrNull(key: String): JsonElement? {
        if (!has(key)) {
            return null
        }
        val res = get(key)
        if (res is JsonNull) {
            return null
        }
        return res
    }

    inline fun <reified T> getOrDef(key: String, def: T): T {
        return get<T>(key) ?: def
    }

    operator fun set(key: String, value: Any) {
        if (!isJsonObj) {
            logTagW("JsonObjWrapper", "不是JsonObject, 不能使用get方法")
            return
        }
        val jsonObj = jsonElement.asJsonObject
        when (value) {
            is String -> jsonObj.addProperty(key, value)
            is Number -> jsonObj.addProperty(key, value)
            is Boolean -> jsonObj.addProperty(key, value)
            is Char -> jsonObj.addProperty(key, value)
            is JsonElement -> jsonObj.add(key, value)
            else -> logTagE("JsonObjWrapper", "只能添加:String,Number,Bool,Char类型的数据")
        }
    }

    override fun toString(): String {
        return try {
            return jsonElement.asString
        } catch (e: Exception) {
            super.toString()
        }
    }
}


