package com.czur.starry.device.noticecenter.netty

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import androidx.core.app.NotificationCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpManager
import com.czur.starry.device.baselib.network.core.exception.MiaoNetException
import com.czur.starry.device.baselib.network.core.exception.MiaoNoLoginExp
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.MsgType.Companion.MEET
import com.czur.starry.device.baselib.notice.MsgType.Companion.MEET_CMD
import com.czur.starry.device.baselib.notice.MsgType.Companion.SYNC
import com.czur.starry.device.baselib.notice.MsgType.Companion.SYNC_NOTICE
import com.czur.starry.device.baselib.notice.MsgType.Companion.SYNC_PARTY_TRANS_CODE
import com.czur.starry.device.baselib.notice.MsgType.Companion.USER_INFO
import com.czur.starry.device.baselib.notice.MsgType.Companion.USER_INFO_OTHER_DEVICE_LOGIN
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.notice.NoticeMsg
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.createNotificationChannel
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.noticecenter.R
import com.czur.starry.device.noticecenter.netty.msg.CZBizAction
import com.czur.starry.device.noticecenter.netty.msg.CZBizMsg
import com.czur.starry.device.noticecenter.netty.msg.CZMessage
import com.czur.starry.device.noticecenter.netty.msg.makeMeetCmdMsg
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

private const val TAG = "NettyService"
private const val NOTICE_CHANNEL_NAME = "通知"

class NettyService : LifecycleService() {
    // 协程
    private val job = Job()
    private val scope = CoroutineScope(job)

    private val nettyClient = NettyClient(::onReceiveNettyMsg, ::processNettyExp, scope)
    private val noticeReceiver = ::onNoticeMsgReceive
    private val noticeMsgType = MsgType(MEET, MEET_CMD)

    private lateinit var handler: Handler

    private var onCreateTime = 0L

    private val partyTransCodeFinishFlow by lazy {
        MutableStateFlow(-1L)
    }

    // 会议功能是否开启
    private val meetingEnable = false


    companion object {
        private const val MSG_WHAT_START = 0
        private const val RESTART_DELAY = 2 * ONE_SECOND
        private const val DEF_REPLY_TIMEOUT = 2 * ONE_SECOND

        var isRunning: Boolean
            get() = getBooleanSystemProp("system.netty.running", false)
            set(value) = setBooleanSystemProp("system.netty.running", value)


        fun start(context: Context) {
            val nettyIntent = Intent(context, NettyService::class.java)
            nettyIntent.`package` = context.packageName
            context.startService(nettyIntent)
        }

        fun stop(context: Context) {
            val nettyIntent = Intent(context, NettyService::class.java)
            nettyIntent.`package` = context.packageName
            context.stopService(nettyIntent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "Service 启动")
        onCreateTime = System.currentTimeMillis()
        scope.launch {
            isRunning = true
        }

        initEveryThing()

        // 启动Netty
        nettyClient.start()

        // 注册消息回调
        NoticeHandler.register(noticeMsgType, this, noticeReceiver)

        if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
            logTagD(TAG, "党建版本, 关注转码消息")
            launch {
                partyTransCodeFinishFlow.filter { it > 0 }.debounce(ONE_SECOND).collect {
                    logTagD(TAG, "发送刷新UI")
                    NoticeHandler.sendMessage(MsgType(SYNC, SYNC_PARTY_TRANS_CODE))
                }
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (System.currentTimeMillis() - onCreateTime > 3 * ONE_SECOND) {
            // 刚启动不用去检查
            launch {
                nettyClient.checkStatus()
                nettyClient.keepRunning()
            }
        } else {
            logTagV(TAG, "NettyService 刚启动, 不需要检查状态")
        }

        return super.onStartCommand(intent, flags, startId)
    }

    private fun initEveryThing() {
        // 添加Notification
        showNotification()

        // 初始化MiaoHttpManager
        MiaoHttpManager.getInstance().init()

        // 初始化handler
        handler = Handler(
            Looper.getMainLooper()
        ) {
            when (it.what) {
                MSG_WHAT_START -> {
                    if (lifecycle.currentState != Lifecycle.State.DESTROYED) {
                        logTagV(TAG, "NettyServer 没有被销毁, 执行重启操作")
                        scope.launch {
                            nettyClient.reboot()
                        }
                    }
                }
            }
            true
        }

    }

    /**
     * 处理Netty启动的异常
     */
    private fun processNettyExp(exp: Exception) {
        logTagW(TAG, "处理NettyClient 异常")
        when (exp) {
            is MiaoNetException -> {
                logTagV(TAG, "网络错误")
                // 网络错误
                if (exp.isNoConnect) {
                    // 停止服务
                    logTagV(TAG, "没有网络连接")
                    stopSelf()
                } else {
                    // 连接无网络,则需要反复尝试了
                    sendRestartMsg(RESTART_DELAY)
                }
            }

            is MiaoNoLoginExp -> {
                // 如果没有登录, 则也停止NettService
                logTagV(TAG, "账号没有登录")
                stopSelf()
            }

            is NettyCloseExp -> {
                logTagV(TAG, "netty被关闭:${exp.message}")
                sendRestartMsg()
            }

            else -> {
                // 其他错误
                logTagD(TAG, "Netty其他错误:", tr = exp)
                sendRestartMsg(RESTART_DELAY)
            }
        }
    }

    private fun sendRestartMsg(delay: Long = 0L) {
        if (lifecycle.currentState == Lifecycle.State.DESTROYED) {
            // Service 已经销毁了, 不再向Handler中发送请求
            return
        }
        // 先取消所有的Msg
        handler.removeMessages(MSG_WHAT_START)
        logTagV(TAG, "发送重启NettyClient的命令,延迟:${delay}")
        if (delay > 0) {
            handler.sendEmptyMessageDelayed(MSG_WHAT_START, delay)
        } else {
            handler.sendEmptyMessage(MSG_WHAT_START)
        }

    }


    private fun showNotification() {
        createNotificationChannel(NOTICE_CHANNEL_NAME, NOTICE_CHANNEL_NAME)
        val notification = NotificationCompat.Builder(this, NOTICE_CHANNEL_NAME)
            .setContentTitle("NettyService")
            .setContentText("NettyService")
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()
        startForeground(1, notification)
    }

    /**
     * 收到服务器的事物消息
     */
    private fun onReceiveNettyMsg(msg: CZMessage) {
        logTagD(TAG, "onReceiveNettyMsg")
        when (msg) {
            is CZBizMsg -> {
                handleBizMsg(msg)
            }

            else -> {
                logTagD(TAG, "其他消息类型")
            }
        }
    }

    /**
     * 处理事务消息
     */
    private fun handleBizMsg(msg: CZBizMsg) {
        logTagD(TAG, "处理业务消息")
        when (msg.body.action) {
            CZBizAction.NEW_NOTICE -> {
                when (msg.body.module?.lowercase()) {
                    "other_device_login" -> {
                        logTagD(TAG, "有人顶掉了当前用户")
                        NoticeHandler.sendMessage(
                            MsgType(
                                USER_INFO,
                                USER_INFO_OTHER_DEVICE_LOGIN
                            )
                        )
                    }

                    "account" -> {
                        logTagD(TAG, "需要更新用户信息")
                        scope.launch {
                            UserHandler.updateUserInfo()
                        }
                        NoticeHandler.sendMessage(MsgType(SYNC, SYNC_NOTICE))
                    }

                    "close_meeting", "contacts" -> {
                        logTagV(TAG, "会议功能未开启, 不处理会议结束")
                    }


                    "party_transcode" -> {
                        logTagD(TAG, "党建会议转码完成")
                        onPartyTransCodeFinish()
                    }

                    else -> {
                        logTagD(TAG, "通知有新的通知消息")
                        val dataJson = msg.body.data.toString()
                        NoticeHandler.sendMessage(MsgType(SYNC, SYNC_NOTICE)) {
                            if (dataJson.length > 4) { // 如果是空数据就会是{}, 所以这里用长度判断
                                logTagV(TAG, "阅读消息导致的更新")
                                put(dataJson)
                            }
                        }
                    }
                }
            }

            CZBizAction.CALL_VIDEO, CZBizAction.MEETING, CZBizAction.CHECK_MEETING_LIST -> {
                logTagV(TAG, "视频会议消息,不处理")
            }

            CZBizAction.REPLAY -> {
                logTagV(TAG, "Server回执:${msg.body}")
            }


            else -> {
                logTagW(TAG, "其他action:${msg.body.action}, 还未定义, 不做任何处理!")
            }
        }
    }

    private fun onPartyTransCodeFinish() {
        partyTransCodeFinishFlow.value = System.currentTimeMillis()
    }

    /**
     * 接收消息中心的消息
     */
    private fun onNoticeMsgReceive(msg: NoticeMsg) {
        logTagD(TAG, "接收到消息:${msg}")
        val czMsg = makeMeetCmdMsg(msg)
        nettyClient.sendMsg(czMsg)
    }


    override fun onDestroy() {
        scope.launch {
            // 清除启动消息
            handler.removeMessages(MSG_WHAT_START)
            // 服务停止时 关闭netty
            nettyClient.shutDown()
            isRunning = false
            job.cancel()
        }
        onCreateTime = 0L
        super.onDestroy()
    }
}