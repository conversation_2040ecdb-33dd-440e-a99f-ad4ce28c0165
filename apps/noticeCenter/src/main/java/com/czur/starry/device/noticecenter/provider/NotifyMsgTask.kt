package com.czur.starry.device.noticecenter.provider

import com.czur.czurutils.log.logTagW

/**
 * Created by 陈丰尧 on 2021/7/5
 */
sealed class NotifyMsgTaskType(val taskName: String)

// 检查是否有未读消息
object CheckUnReadMsg : NotifyMsgTaskType("CheckUnReadMsg")

// 同步最新消息
object SyncNotifyMsg : NotifyMsgTaskType("SyncNotifyMsg")

// 标记消息为已读
data class ReadMsg(val data: Map<String, String>) : NotifyMsgTaskType("ReadMsg")

fun getNotifyMsgTaskType(taskName: String, data: Map<String, String>?): NotifyMsgTaskType {
    return when (taskName) {
        "CheckUnReadMsg" -> CheckUnReadMsg
        "SyncNotifyMsg" -> SyncNotifyMsg
        "ReadMsg" -> ReadMsg(data ?: mapOf())
        else -> {
            logTagW("NotifyMsgTaskType", "taskName:${taskName} 没有找到匹配")
            // 默认是检查未读消息
            CheckUnReadMsg
        }
    }
}
