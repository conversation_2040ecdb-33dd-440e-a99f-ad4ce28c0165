package com.czur.starry.device.noticecenter.netty

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV

import com.czur.starry.device.noticecenter.netty.msg.CZMInfoMsg
import com.czur.starry.device.noticecenter.netty.msg.CZMessage
import com.czur.starry.device.noticecenter.netty.msg.CZMsgType
import com.czur.starry.device.noticecenter.netty.msg.MessageParseHandler
import io.netty.buffer.Unpooled
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelInitializer
import io.netty.channel.SimpleChannelInboundHandler
import io.netty.channel.socket.SocketChannel
import io.netty.handler.codec.DelimiterBasedFrameDecoder
import io.netty.handler.codec.string.StringDecoder
import io.netty.handler.codec.string.StringEncoder
import java.nio.charset.StandardCharsets

/**
 * Created by 陈丰尧 on 4/6/21
 * 初始化NettyChannel信息
 */
private const val TAG = "CZURClientInitializer"

class CZURClientInitializer(
    val receiveListener: (msg: CZMessage) -> Unit, // 接收到消息的回调
    val closeListener: () -> Unit, // 关闭回调
    val onOnline: () -> Unit    // 上线完成后的回调
) : ChannelInitializer<SocketChannel>() {

    override fun initChannel(ch: SocketChannel) {
        logTagD(TAG, "初始化Channel")

        // 获取流水线
        val pipeline = ch.pipeline()

        // 分隔符,必须放在第一位
        val delimiter = Unpooled.copiedBuffer(MESSAGE_DELIMITER.toByteArray())
        pipeline.addLast("framer", DelimiterBasedFrameDecoder(MAX_MESSAGE_SIZE, delimiter))
        // 解码
        pipeline.addLast("decoderStr", StringDecoder(StandardCharsets.UTF_8))
        // 编码
        pipeline.addLast("encoderStr", StringEncoder(StandardCharsets.UTF_8))

        // 将字符串处理成 java类
        pipeline.addLast("parse", MessageParseHandler())
        pipeline.addLast("heartbeat", HeartbeatHandler(HEART_INTERVAL_SECOND))


        pipeline.addLast("handler", object : SimpleChannelInboundHandler<CZMessage>() {
            /**
             * 发上线信息
             */
            fun sendOnLineMsg(ctx: ChannelHandlerContext) {
                val userMsg = CZMInfoMsg(CZMsgType.ONLINE)
                ctx.writeAndFlush(userMsg)
            }

            override fun channelRead0(ctx: ChannelHandlerContext, msg: CZMessage) {
                logTagD(TAG,"channelRead0:接到了长连接消息")
                when (msg.type) {
                    CZMsgType.CONNECTED -> {
                        logTagD(TAG, "连接成功发上线信息")
                        sendOnLineMsg(ctx)
                        onOnline()
                    }
                    CZMsgType.HEARTBEAT -> {
                        logTagD(TAG, "Server 发送心跳包")
                    }
                    CZMsgType.DONE -> {
                        logTagV(TAG, "Server 回复握手信息")
                    }
                    else -> receiveListener(msg)
                }
            }

            /**
             * 连接断开
             */
            override fun channelInactive(ctx: ChannelHandlerContext?) {
                super.channelInactive(ctx)
                logTagD(TAG, "连接被框架断开")
                closeListener()
            }

        })

    }
}