package com.czur.starry.device.noticecenter.entity

import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.gson.CallRecordGMT8DateDeserializer
import com.czur.starry.device.noticecenter.R
import com.czur.starry.device.noticelib.*
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.annotations.JsonAdapter
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by 陈丰尧 on 4/8/21
 */
data class NoticeEntity(
    val total: Long,      // 总共有多少条信息
    val notices: List<NoticeDetailEntity>
)

private val sdf = SimpleDateFormat("yyyy.MM.dd", Locale.ROOT)

data class NoticeDetailEntity(
    val id: Long,            // 消息ID
    val title: String,      // 消息标题
    val content: String?,    // 内容, 不知道用在哪
    val status: Int,        // 消息状态
    val type: Int,          // 消息类型
    @JsonAdapter(CallRecordGMT8DateDeserializer::class)
    val createTime: Date,
    @JsonAdapter(CallRecordGMT8DateDeserializer::class)
    val updateTime: Date,
    val meetingNo: String,    // accountNo
    val data: String?       // Json数据类型
) {
    fun getTimeStr(): String {
        return sdf.format(createTime)
    }

    private var jsonOBj: JsonObject? = null
    val dataObj: JsonObject
        get() {
            if (jsonOBj == null) {
                jsonOBj = parseJsonData()
            }
            return jsonOBj!!
        }

    private fun parseJsonData(): JsonObject {
        return if (data.isNullOrEmpty()) {
            JsonObject()
        } else {
            Gson().fromJson(data, JsonObject::class.java)
        }
    }


    // 是否已读
    val read: Boolean
        get() = status != NOTICE_STATUS_UNREAD


    fun hasAction(): Boolean {
        return type == NOTICE_TYPE_UPDATE || type == NOTICE_TYPE_INVITED_COMPANY || type == NOTICE_TYPE_EXPIRE
    }


    fun getActionText(): String = when (type) {
        NOTICE_TYPE_UPDATE -> getString(R.string.notice_action_version)
        NOTICE_TYPE_INVITED_COMPANY -> getString(R.string.notice_action_agree)
        NOTICE_TYPE_EXPIRE -> getString(R.string.notice_action_view)
        else -> ""
    }

    fun getNegativeText(): String = when (type) {
        NOTICE_TYPE_UPDATE -> getString(R.string.notice_action_version_negative)
        NOTICE_TYPE_INVITED_COMPANY -> getString(R.string.notice_action_agree_negative)
        NOTICE_TYPE_EXPIRE -> getString(R.string.notice_action_view_negative)
        else -> ""
    }


}

/**
 * 加入企业时使用
 */
data class CompanyData(
    val enterpriseId: String
)

/**
 * 长连接节点信息
 */
data class NettyNodeInfo(
    val id: Int,
    val balance: Int,
    val host: String,
    val port: Int,
    val internalHost: String?
)

data class CloudConfigEntity(
    private val apps: List<Map<String, Any>>
) {
    val appEntities
        get() = apps.map { AppCloudConfigEntity(it) }
}

@JvmInline
value class AppCloudConfigEntity(val configMap: Map<String, Any> = mutableMapOf()) {
    val name: String
        get() = (configMap["name"] ?: "").toString()
    val pkgName: String
        get() = (configMap["pkgName"] ?: "").toString()

    inline fun <reified T> getConfigValue(key: String): T {
        return (configMap[key]!! as T)
    }

    inline fun onEachConfig(block: (key: String, value: Any) -> Unit) {
        configMap
            .filterKeys {
                it != "name" && it != "pkgName"
            }
            .forEach { (key, value) ->
                block(key, value)
            }
    }
}

/**
 * 弹窗消息
 */
data class PopupMsg(
    val id: Long,
    val cancelButtonAction: String,     // 取消按钮的动作
    val cancelButtonText: String,       // 取消按钮的文案
    val primaryButtonAction: String,    // 主按钮的动作
    val primaryButtonText: String,      // 主按钮的文案
    val title: String,                  // 标题
    val content: String,                // 内容

    val iconUrl: String,                // 图标地址
    val noticeTitle:String,             // 通知标题

    val imageUrl: String,               // 图片地址
    val startTimeStamp:Long,            // 开始时间
    val endTimeStamp: Long,             // 结束时间
)
