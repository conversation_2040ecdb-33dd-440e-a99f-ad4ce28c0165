<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Message</string>
    <string name="text_page_info">%1$d / %2$d</string>
    <string name="notice_action_version">Update</string>
    <string name="notice_action_version_negative">Ignore</string>
    <string name="notice_action_agree">Agree</string>
    <string name="notice_action_agree_negative">Deny</string>
    <string name="notice_action_view">Check</string>
    <string name="notice_action_view_negative">Ignore</string>
    <string name="notice_read_all">All read</string>
    <string name="notice_clear">Empty</string>
    <string name="dialog_title_ask">Invitation</string>
    <string name="dialog_title_version">Update Notification</string>
    <string name="dialog_title_system">Message</string>
    <string name="str_no_notice">Empty</string>
    <string name="str_hw_connected">Connected</string>
    <string name="str_hw_name_touch_board">TouchBoard</string>
    <string name="str_hw_name_click_drop">ClickDrop</string>
    <string name="toast_byom_conflict">Camera or microphone is occupied. Cannot set to Peripheral Mode.</string>
    <string name="str_hw_paired_success">Successfully Paired</string>
    <string name="str_hw_paired_not_active">Wireless screen sharing function is not activated. Please connect to the Internet then try again!</string>
    <string name="str_hw_name_keyboard">Bluetooth keyboard</string>
    <string name="str_hw_paired_not_active2">Pairing failed! \n Please connect to the Internet then try again.</string>
    <string name="str_popup_msg_no_more_reminders">Don’t remind again.</string>
    <string name="toast_byom_refuse_by_usb">Cannot enable Wireless Mode when Wired Mode is in use.</string>
</resources>
