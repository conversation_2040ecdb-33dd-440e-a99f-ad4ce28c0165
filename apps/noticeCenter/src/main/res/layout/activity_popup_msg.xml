<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage,RtlHardcoded">

    <ImageView
        android:id="@+id/bgImgIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="720px"
        android:minHeight="720px"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_temp"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        android:src="@drawable/ic_popup_msg_close"
        app:layout_constraintRight_toRightOf="@id/bgImgIv"
        app:layout_constraintTop_toTopOf="@id/bgImgIv"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#5879FC"
        android:textSize="24px"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/bgImgIv"
        app:layout_constraintLeft_toLeftOf="@id/bgImgIv"
        app:layout_constraintRight_toRightOf="@id/bgImgIv"
        app:layout_constraintTop_toTopOf="@id/bgImgIv"
        tools:text="发现全新版本"
        tools:visibility="visible" />

    <ScrollView
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginVertical="5px"
        app:layout_constraintBottom_toTopOf="@id/negativeButton"
        app:layout_constraintLeft_toLeftOf="@id/negativeButton"
        app:layout_constraintRight_toRightOf="@id/positiveButton"
        app:layout_constraintTop_toBottomOf="@id/titleTv">

        <TextView
            android:id="@+id/contentTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:visibility="invisible"
            android:textSize="18px"
            android:lineSpacingExtra="8px"
            android:lineSpacingMultiplier="1"
            android:textStyle="normal"
            tools:visibility="visible" />

    </ScrollView>


    <TextView
        android:id="@+id/negativeButton"
        android:layout_width="240px"
        android:layout_height="60px"
        android:layout_marginBottom="35px"
        android:background="@drawable/bg_popup_msg_negative"
        android:gravity="center"
        android:textColor="#5879FC"
        android:textSize="20px"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/noMoreRemindersCb"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/bgImgIv"
        app:layout_constraintRight_toLeftOf="@id/positiveButton"
        tools:visibility="visible" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/positiveButton"
        android:layout_width="240px"
        android:layout_height="60px"
        android:layout_marginLeft="25px"
        android:textSize="20px"
        app:colorStyle="blueWhite"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/negativeButton"
        app:layout_constraintLeft_toRightOf="@id/negativeButton"
        app:layout_constraintRight_toRightOf="@id/bgImgIv"
        app:layout_constraintTop_toTopOf="@id/negativeButton"
        tools:visibility="visible" />

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/noMoreRemindersCb"
        android:layout_width="24px"
        android:layout_height="24px"
        android:layout_marginBottom="24px"
        android:visibility="invisible"
        app:cusRadius="5"
        app:layout_constraintBottom_toBottomOf="@id/bgImgIv"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/bgImgIv"
        app:layout_constraintRight_toLeftOf="@id/noMoreRemindersTv"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/noMoreRemindersTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8px"
        android:text="@string/str_popup_msg_no_more_reminders"
        android:visibility="invisible"
        android:textColor="#393939"
        android:textSize="18px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@id/noMoreRemindersCb"
        app:layout_constraintLeft_toRightOf="@id/noMoreRemindersCb"
        app:layout_constraintRight_toRightOf="@id/bgImgIv"
        app:layout_constraintTop_toTopOf="@id/noMoreRemindersCb"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>