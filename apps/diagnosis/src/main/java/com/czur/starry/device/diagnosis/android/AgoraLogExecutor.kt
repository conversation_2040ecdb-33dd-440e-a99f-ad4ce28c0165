package com.czur.starry.device.diagnosis.android

import android.os.Environment
import com.czur.starry.device.diagnosis.LogCollectExecutor
import java.io.File

/**
 * Created by 陈丰尧 on 2022/7/5
 * 抓取声网log
 */
class AgoraLogExecutor : LogCollectExecutor() {
    override val outputFileName: String
        get() = "AgoraLogs"
    override val outIsDir: Boolean = true

    override suspend fun doExec(file: File) {
        // 1. 声网分享流的log
        val agoraShareLogFile = File(Environment.getExternalStorageDirectory(), "ss_svr.log")
        if (agoraShareLogFile.exists()) {
            val svrLogFile = File(file, "ss_svr.log")
            agoraShareLogFile.copyTo(svrLogFile, true)
        }
        // 2. 声网log
        val agoraLogDir = File(
            Environment.getExternalStorageDirectory(),
            "Android/data/com.czur.starry.device.meeting/files"
        )
        if (!agoraLogDir.exists()) {
            // 没有声网的log
            return
        }
        val destDir = File(file, "meetingAgora").apply {
            mkdirs()
        }
        (agoraLogDir.listFiles() ?: emptyArray<File>())
            .filter {
                it.isFile
            }
            .forEach {
                val logFile = File(destDir, it.name)
                it.copyTo(logFile, true)
            }
    }
}