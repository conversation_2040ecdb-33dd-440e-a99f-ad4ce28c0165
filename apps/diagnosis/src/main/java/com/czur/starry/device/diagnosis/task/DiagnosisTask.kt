package com.czur.starry.device.diagnosis.task

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.diagnosis.NetService
import com.czur.starry.device.diagnosis.ZipUtil
import com.czur.starry.device.diagnosis.clearLogs
import com.czur.starry.device.diagnosis.commandExecutors
import com.czur.starry.device.diagnosis.db.DiagnosisDatabase
import com.czur.starry.device.diagnosis.db.entity.*
import com.czur.starry.device.temposs.TempOSSUtil
import com.czur.starry.device.temposs.TmpOSSType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2021/9/16
 */
/**
 * 分析程序的任务
 * @param Param:    初始参数的泛型
 * @param Result: 任务结束的返回值
 */
sealed class DiagnosisTask<Param, Result>(private val param: Param) {

    suspend fun runTask(): Result {
        return withContext(Dispatchers.IO) {
            // 所有的任务都会在IO线程中执行
            run(param)
        }
    }

    protected abstract suspend fun run(param: Param): Result
}

data class UserParam(
    val taskID: String,
    val feedback: String?,
    val deviceFeedbackType: String?,
    val contact: String?,
    val logFile: File?,
    val ossKey: String?
)

class UserTask(userParam: UserParam) : DiagnosisTask<UserParam, Boolean>(userParam) {
    private var logFile: File? = null
    val taskID = userParam.taskID

    companion object {
        private const val TAG = "UserTask"
    }

    override suspend fun run(param: UserParam): Boolean {
        logTagD(TAG, "开始执行UserTask")
        val feedbackDao = DiagnosisDatabase.instance.feedbackDao()
        var feedbackEntity = param.toFeedbackEntity()
        feedbackDao.insertOrUpdateFeedbackTask(feedbackEntity)   // 向数据库中插入数据

        // 收集日志
        if (param.logFile?.exists() != true) {
            logTagD(TAG, "收集日志")
            feedbackEntity = feedbackEntity.copy(
                taskStatus = FEED_BACK_STATUS_COLLECTION    // 更新任务状态为收集中
            ).also {
                feedbackDao.insertOrUpdateFeedbackTask(it)
            }
            val zipFile = CollectionTask().runTask()
            logFile = zipFile
        } else {
            logTagV(TAG, "已有LogFile:${param.logFile}")
            logFile = param.logFile

        }

        // 上传
        logTagD(TAG, "收集完成,开始上传")
        feedbackEntity = feedbackEntity.copy(
            localLogFilePath = logFile!!.absolutePath,
            taskStatus = FEED_BACK_STATUS_UPLOADING    // 更新任务状态为上传中
        ).also {
            feedbackDao.insertOrUpdateFeedbackTask(it)
        }
        var ossKey = param.ossKey
        if (ossKey.isNullOrEmpty()) {
            val uploadTask = UploadToOSSTask(logFile!!)
            ossKey = uploadTask.runTask()
            if (ossKey.isNotEmpty()) {
                logTagD(TAG, "任务上传成功")
            } else {
                logTagW(TAG, "任务上传失败")
                feedbackEntity = feedbackEntity.copy(
                    taskStatus = FEED_BACK_STATUS_FAILED    // 更新任务状态为失败
                ).also {
                    feedbackDao.insertOrUpdateFeedbackTask(it)
                }
                return false
            }
        }
        logTagV(TAG, "ossKey:${ossKey}")
        // 发送给后台
        feedbackEntity = feedbackEntity.copy(
            ossKey = ossKey,
            taskStatus = FEED_BACK_STATUS_SENDING    // 更新任务状态为成功
        ).also {
            feedbackDao.insertOrUpdateFeedbackTask(it)
        }
        val uploadParams = UploadParams(
            param.deviceFeedbackType,
            param.contact,
            logFile!!,
            param.feedback,
            ossKey
        )
        val sendToServerTask = SendToServerTask(uploadParams)
        val result = sendToServerTask.runTask()
        if (result) {
            logTagD(TAG, "任务发送后台成功")
            feedbackEntity = feedbackEntity.copy(
                ossKey = ossKey,
                taskStatus = FEED_BACK_STATUS_FINISH    // 更新任务状态为成功
            ).also {
                feedbackDao.insertOrUpdateFeedbackTask(it)
            }
        } else {
            logTagW(TAG, "任务发送后台失败")
            feedbackEntity = feedbackEntity.copy(
                ossKey = ossKey,
                taskStatus = FEED_BACK_STATUS_FAILED    // 更新任务状态为成功
            ).also {
                feedbackDao.insertOrUpdateFeedbackTask(it)
            }
        }


        return result
    }

    fun clearLogFile() {
        logTagV("UserTask", "清理LogFile:${logFile?.absolutePath}")
        logFile?.delete()
        logFile = null
    }

}

/**
 * 收集日志Task
 */
class CollectionTask : DiagnosisTask<Unit, File>(Unit) {
    companion object {
        private const val TAG = "CollectionTask"
    }

    override suspend fun run(param: Unit): File {
        // 每次收集日志之前, 清理之前的日志文件, 保证本地只有一个日志文件
        clearLogs()
        logTagD(TAG, "开始收集日志")
        commandExecutors.forEach {
            doWithoutCatch {
                it.exec()
            }
        }
        logTagD(TAG, "日志收集完成,开始压缩")
        val zipFile = ZipUtil.startZip()
        logTagD(TAG, "压缩完成")
        return zipFile
    }

}

private val netService: NetService = HttpManager.getService()

data class UploadParams(
    val deviceFeedbackType: String?,
    val contact: String?,
    val logFile: File? = null,
    val feedback: String? = null,
    val ossKey: String? = null
) {
    val uploadFeedbackType:String
        get() = deviceFeedbackType ?: "others"
}

class UploadToOSSTask(logFile: File) : DiagnosisTask<File, String>(logFile) {
    companion object {
        private const val TAG = "UploadToOSSTask"
    }

    override suspend fun run(param: File): String {
        if (!param.exists()) return ""
        logTagD(TAG, "上传Log文件到OSS")
        val ossUtil = TempOSSUtil(TmpOSSType.LOG)
        val res = ossUtil.uploadFile(param, Constants.SERIAL)
        if (!res.success) {
            logTagE(TAG, "上传文件失败")
            return ""
        }
        return res.ossKey
    }
}

class SendToServerTask(uploadParams: UploadParams) :
    DiagnosisTask<UploadParams, Boolean>(uploadParams) {
    companion object {
        private const val TAG = "SendToServerTask"
    }

    override suspend fun run(param: UploadParams): Boolean {
        logTagD(TAG, "通知后台:${param}")
        val res =
            netService.logRecord(
                sn = Constants.SERIAL,
                deviceFeedbackType = param.uploadFeedbackType,
                contactWay = param.contact,
                ossKey = param.ossKey,
                feedback = param.feedback
            )
        // 如果上传失败, 就会报错, 下次会继续上窜
        if (res.isSuccess) {
            logTagV(TAG, "推送成功, 清理Log文件")
            clearLogs()
        }
        return res.isSuccess
    }

}

/**
 * 检查上传失败的日志
 */
class CheckTask(param: List<String>) : DiagnosisTask<List<String>, List<FeedbackEntity>>(param) {
    companion object {
        private const val TAG = "CheckTask"
    }

    override suspend fun run(param: List<String>): List<FeedbackEntity> {
        logTagV(TAG, "runningTaskID:${param}")
        val feedbackDao = DiagnosisDatabase.instance.feedbackDao()

        return feedbackDao.queryFailTask(param)
    }

}

class UploadTask(uploadParams: UploadParams) :
    DiagnosisTask<UploadParams, Boolean>(uploadParams) {
    companion object {
        private const val TAG = "UploadTask"
    }

    override suspend fun run(param: UploadParams): Boolean {
        val uploadRes = param.logFile?.let {
            logTagD(TAG, "上传Log文件")
            val ossUtil = TempOSSUtil(TmpOSSType.LOG)
            val res = ossUtil.uploadFile(it, Constants.SERIAL)
            if (!res.success) {
                logTagE(TAG, "上传文件失败")
                return false
            }
            res.ossKey
        }
        logTagD(TAG, "通知后台")
        val res =
            netService.logRecord(
                sn = Constants.SERIAL,
                deviceFeedbackType = param.uploadFeedbackType,
                contactWay = param.contact,
                ossKey = uploadRes,
                feedback = param.feedback
            )
        // 如果上传失败, 就会报错, 下次会继续上窜
        if (res.isSuccess) {
            logTagV(TAG, "推送成功, 清理Log文件")
            clearLogs()
        }
        return res.isSuccess
    }

}
