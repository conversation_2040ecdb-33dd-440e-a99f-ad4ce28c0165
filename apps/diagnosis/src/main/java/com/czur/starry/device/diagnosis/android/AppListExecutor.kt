package com.czur.starry.device.diagnosis.android

import android.os.Build
import com.czur.starry.device.diagnosis.LogCollectExecutor
import java.io.File

/**
 * 收集当前系统用户安装的apk列表
 */
class AppListExecutor : LogCollectExecutor() {
    override val outputFileName: String
        get() = "AppList.txt"

    override suspend fun doExec(file: File) {
        val pmManager = context.packageManager
        val packages = pmManager.getInstalledPackages(0)


        val pw = file.printWriter()

        pw.use {
            it.println("NAME\t包名\t版本名称\t版本号")
            packages.forEach { info ->
                val appName = info.applicationInfo?.loadLabel(pmManager).toString()
                val packageName = info.packageName
                val versionName = info.versionName
                val versionCode = info.versionCode
                it.println("${appName}\t\t${packageName}\t\t${versionName}\t\t${versionCode}")

            }
        }

    }


}