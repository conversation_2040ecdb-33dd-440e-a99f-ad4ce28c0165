package com.czur.starry.device.diagnosis.send

import com.czur.czurutils.log.logTagD
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File

/**
 * Created by 陈丰尧 on 2022/8/2
 */
class UploadUtil {
    companion object {
        private const val TAG = "UploadUtil"
        private const val KEY_FILE = "files"
    }

    private val client = OkHttpClient()


    /**
     * 发送log文件
     */
    suspend fun transLogFile(logFile: File, serverInfo: LocalServerInfoEntity): Boolean =
        withContext(Dispatchers.IO) {
            try {
                val url = "http://${serverInfo.ip}:${serverInfo.port}/upload/logs"
                logTagD(TAG,"url:${url}")
                val builder = Request.Builder()

                val req = builder.url(url)
                    .post(mkRequestBody(logFile))
                    .build()
                logTagD(TAG,"开始上传")
                val response = client.newCall(req).execute()
                logTagD(TAG,"上传完成:${response.code}")
                response.code == 200
            } catch (exp: Exception) {
                logTagD(TAG, "发送log文件失败", tr = exp)
                false
            }

        }

    private fun mkRequestBody(logFile: File): RequestBody {
        logTagD(TAG,"logFileSize:${logFile.length()}")
        val builder = MultipartBody.Builder()

        builder.addFormDataPart(
            KEY_FILE,
            logFile.name,
            logFile.asRequestBody("multipart/form-data".toMediaTypeOrNull())
        )
        return builder.build()
    }
}