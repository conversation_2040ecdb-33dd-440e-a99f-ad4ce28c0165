package com.czur.starry.device.diagnosis.task

import android.content.Intent
import android.os.IBinder
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.TaskService
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.inMainThread
import com.czur.starry.device.baselib.utils.randomStr
import com.czur.starry.device.diagnosis.db.DiagnosisDatabase
import com.czur.starry.device.diagnosis.db.entity.FEED_BACK_STATUS_FAILED
import com.czur.starry.device.diagnosislib.IDiagnosis
import kotlinx.coroutines.runBlocking
import java.io.File

/**
 * Created by 陈丰尧 on 2021/9/16
 */
class DiagnosisService : TaskService<DiagnosisTask<*, *>>(needLogin = false, concurrent = false) {
    companion object {
        private const val TAG = "DiagnosisService"

        private const val TASK_TYPE = "taskType"
        private const val TASK_TYPE_CHECK = "checkFailTask"
    }

    override val idleTimeInMs: Long = 10 * ONE_MIN   // 空闲时间变为10分钟

    /**
     * 用来记录意见反馈进度信息的
     */
    private val feedbackDao by lazy {
        DiagnosisDatabase.instance.feedbackDao()
    }

    private val runningTaskID = mutableSetOf<String>()


    private val binder = object : IDiagnosis.Stub() {
        /**
         * 手机Log并上传 是耗时操作, 请在子线程中调用
         * @params feedback 意见反馈, 可以为空
         * @params deviceFeedbackType 意见反馈类型
         * @params contact 是否通过手机联系用户
         * @params collectLog 是否收集Log
         */
        override fun collectionAndUpload(
            feedback: String?,
            deviceFeedbackType: String?,
            contact: String?,
            collectLog: Boolean
        ): Boolean {
            if (inMainThread()) {
                throw RuntimeException("收集Log方法不能再主线程调用")
            }
            logTagD(TAG, "collectionAndUpload")
            return runBlocking {
                if (collectLog) {
                    // 需要收集Log
                    logTagD(TAG, "用户选择上传日志, 在后台上传")
                    // 生成任务ID
                    val taskID = randomStr()
                    addTask(
                        UserTask(
                            UserParam(
                                taskID,
                                feedback,
                                deviceFeedbackType,
                                contact,
                                null,
                                null
                            )
                        )
                    )
                    return@runBlocking true
                }
                // 不需要收集Log
                logTagD(TAG, "用户选择不上传日志, 在前台上传")
                val uploadParams = UploadParams(
                    deviceFeedbackType,
                    contact,
                    null,
                    feedback
                )
                val uploadTask = UploadTask(uploadParams)

                // 上传
                uploadTask.runTask()
            }
        }


    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        return binder
    }

    override fun mkTaskFromIntent(intent: Intent): DiagnosisTask<*, *>? {
        logTagV(TAG, "mkTaskFromIntent")
        if (intent.getStringExtra(TASK_TYPE) == TASK_TYPE_CHECK) {
            logTagV(TAG, "检查未完成任务")
            return CheckTask(runningTaskID.toList())
        }
        return null
    }

    override suspend fun handleTask(task: DiagnosisTask<*, *>) {
        logTagD(TAG, "handleTask:${Thread.currentThread().name}")
        if (task is UserTask) {
            try {
                runningTaskID.add(task.taskID)
                val result = task.runTask()
                logTagD(TAG, "UserTask-result:${result}")
                if (result) {
                    task.clearLogFile() // 成功之后删除log文件
                }
            } catch (exp: Exception) {
                logTagE(TAG, "收集失败", tr = exp)
                feedbackDao.queryByTaskId(task.taskID)?.let {
                    val entity = it.copy(
                        taskStatus = FEED_BACK_STATUS_FAILED
                    )
                    // 更新失败信息
                    feedbackDao.insertOrUpdateFeedbackTask(entity)
                }
            } finally {
                runningTaskID.remove(task.taskID)
            }
        } else if (task is CheckTask) {
            logTagV(TAG, "开始检查未完成的任务")
            val failedTask = task.runTask()
            logTagV(TAG, "共计找到:${failedTask.size} 个任务")
            failedTask.map {
                val logFile = it.localLogFilePath?.let { path ->
                    File(path).run {
                        if (exists()) this else null
                    }
                }
                val userParam = UserParam(
                    it.id,
                    it.feedback,
                    it.feedbackType,
                    it.contact,
                    logFile,
                    it.ossKey
                )
                logTagW(TAG, "添加失败任务")
                addTask(UserTask(userParam))
            }
        }
    }


}