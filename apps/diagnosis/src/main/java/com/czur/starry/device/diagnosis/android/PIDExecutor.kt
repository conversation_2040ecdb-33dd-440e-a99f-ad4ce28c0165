package com.czur.starry.device.diagnosis.android

import android.app.ActivityManager
import com.czur.starry.device.diagnosis.LogCollectExecutor
import java.io.File

/**
 * Created by 陈丰尧 on 2021/9/14
 */

/**
 * 收集当前运行的所有进程的PID
 * 因为shell命令 ps 无法获取全部的信息,所以使用ActivityManager进行PID的收集
 * 与Logcat一起分析使用
 */
class PIDExecutor : LogCollectExecutor() {
    override val outputFileName: String
        get() = "PidList.txt"

    override suspend fun doExec(file: File) {
        val pw = file.printWriter()
        val activityManager = context.getSystemService(ActivityManager::class.java)

        pw.use {
            pw.println("PID\t\tNAME")
            activityManager.runningAppProcesses.forEach {
                val pid = it.pid
                val name = it.processName
                pw.println("${pid}\t\t${name}")
            }
        }

    }


}