package com.czur.starry.device.diagnosis.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.czur.starry.device.diagnosis.db.entity.CrashInfoEntity

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/09/18
 */


@Dao
abstract class CrashInfoDao {

    // 查询所有信息
    @Query("select * from tab_crash_info where currentDate != :currentDate")
    abstract fun getAllCrashInfoList(currentDate: String): List<CrashInfoEntity>?

    // 插入报错信息
    @Insert
    abstract fun insert(entity: CrashInfoEntity): Long

    //删除
    @Query("delete from tab_crash_info where id = :id")
    abstract fun delete(id: Long): Int

    //清空
    @Query("delete from tab_crash_info where currentDate != :currentDate")
    abstract fun clearAll(currentDate: String)

}