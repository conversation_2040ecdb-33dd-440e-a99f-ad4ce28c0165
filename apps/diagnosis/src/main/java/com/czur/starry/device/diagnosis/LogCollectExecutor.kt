package com.czur.starry.device.diagnosis

import android.content.Context
import com.czur.starry.device.diagnosis.android.AgoraLogExecutor
import com.czur.starry.device.diagnosis.android.AppListExecutor
import com.czur.starry.device.diagnosis.android.CZURLogExecutor
import com.czur.starry.device.diagnosis.android.EShareLogExecutor
import com.czur.starry.device.diagnosis.android.HardwareInfoExecutor
import com.czur.starry.device.diagnosis.android.PIDExecutor
import com.czur.starry.device.diagnosis.command.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File


/**
 * Created by 陈丰尧 on 2021/9/14
 */

val commandExecutors = listOf(
    LogcatCommandExecutor(),
    DropBoxCommandExecutor(),
    BugreportCommandExecutor(),
    DumpCommandExecutor(),
    DMESGCommandExecutor(),
    PIDExecutor(),
    CZURLogExecutor(),
    AgoraLogExecutor(),
    EShareLogExecutor(),    // 宜享Log收集
    AppListExecutor(),
    HardwareInfoExecutor(),
)

abstract class LogCollectExecutor {
    val context: Context = DiagnosisApp.instance

    companion object {
        val logDirFile: File
            get() = File(DiagnosisApp.instance.filesDir, "logs")
    }


    /**
     * 输出文件名字
     */
    protected abstract val outputFileName: String
    protected open val outIsDir: Boolean = false

    suspend fun exec(): File {
        return withContext(Dispatchers.IO) {
            val file = makeFile()
            doExec(file)
            file
        }
    }

    /**
     * 创建保存Log的文件
     */
    private fun makeFile(): File {
        val file = File(logDirFile, outputFileName)
        if (file.exists()) {
            file.delete()
        }

        if (!logDirFile.exists()) {
            logDirFile.mkdirs()
        }
        if (outIsDir) {
            file.mkdirs()
        } else {
            file.createNewFile()
        }
        return file
    }

    protected abstract suspend fun doExec(file: File)
}


