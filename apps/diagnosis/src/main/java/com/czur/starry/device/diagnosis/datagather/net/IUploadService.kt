package com.czur.starry.device.diagnosis.datagather.net

import com.czur.starry.device.baselib.network.core.MiaoHttpBody
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpHeader
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.diagnosis.db.entity.AppDurationEntity
import com.czur.starry.device.diagnosis.db.entity.CrashInfoEntity
import com.czur.starry.device.diagnosis.db.entity.DeviceDurationEntity

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/09/21
 */


interface IUploadService {
    @MiaoHttpPost("/api/ota/tracking/duration")
    @MiaoHttpHeader("Content-Type: application/json")
    fun uploadDeviceDuration(
        @MiaoHttpBody
        deviceDurationEntityList: List<DeviceDurationEntity>,
        clazz: Class<Int> = Int::class.java
    ): MiaoHttpEntity<Int>

    @MiaoHttpPost("/api/ota/tracking/appDuration")
    @MiaoHttpHeader("Content-Type: application/json")
    fun uploadAppDuration(
        @MiaoHttpBody
        appDurationEntityList: List<AppDurationEntity>,
        clazz: Class<Int> = Int::class.java
    ): MiaoHttpEntity<Int>

    @MiaoHttpPost("/api/ota/tracking/crash")
    @MiaoHttpHeader("Content-Type: application/json")
    fun uploadCrashInfo(
        @MiaoHttpBody
        crashInfoEntityList: List<CrashInfoEntity>,
        clazz: Class<Int> = Int::class.java
    ): MiaoHttpEntity<Int>

    @MiaoHttpGet("/api/starry/share/dayActive")
    fun deviceDayActive(
        @MiaoHttpParam("sn") sn: String,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}