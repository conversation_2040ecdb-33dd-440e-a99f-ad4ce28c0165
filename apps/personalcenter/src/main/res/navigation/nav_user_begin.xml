<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_user_begin"
    app:startDestination="@id/userBeginFragment">

    <fragment
        android:id="@+id/userBeginFragment"
        android:name="com.czur.starry.device.personalcenter.begin.UserBeginFragment"
        android:label="UserBeginFragment"
        tools:layout="@layout/fragment_user_begin">
        <action
            android:id="@+id/action_userBeginFragment_to_login"
            app:destination="@id/loginFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
        <action
            android:id="@+id/action_userBeginFragment_to_register"
            app:destination="@id/registerAccountFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.czur.starry.device.personalcenter.begin.login.LoginFragment"
        android:label="LoginFragment"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_loginForgetCaptchaFragment"
            app:destination="@id/loginForgetCaptchaFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/registerAccountFragment"
        android:name="com.czur.starry.device.personalcenter.begin.register.RegisterAccountFragment"
        android:label="RegisterAccountFragment"
        tools:layout="@layout/fragment_register">
        <action
            android:id="@+id/action_registerAccountFragment_to_bind_info"
            app:destination="@id/registerBindPhoneFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/loginForgetCaptchaFragment"
        android:name="com.czur.starry.device.personalcenter.begin.login.LoginForgetCaptchaFragment"
        android:label="LoginForgetCaptchaFragment"
        tools:layout="@layout/layout_phone_captcha">
        <action
            android:id="@+id/action_loginForgetCaptchaFragment_to_deviceInfoFragment"
            app:destination="@id/deviceInfoFragment"
            app:popUpTo="@id/loginFragment"
            app:popUpToInclusive="false"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/deviceInfoFragment"
        android:name="com.czur.starry.device.personalcenter.begin.login.DeviceInfoFragment"
        android:label="DeviceInfoFragment"
        tools:layout="@layout/fragment_device_info">
        <action
            android:id="@+id/action_deviceInfoFragment_to_resetPwdFragment"
            app:destination="@id/resetPwdFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim">
            <argument
                android:name="phoneNo"
                app:argType="string"
                app:nullable="false" />
            <argument
                android:name="token"
                app:argType="string"
                app:nullable="false" />
            <argument
                android:name="account"
                app:argType="string"
                app:nullable="false" />
        </action>
    </fragment>
    <fragment
        android:id="@+id/resetPwdFragment"
        android:name="com.czur.starry.device.personalcenter.begin.login.ResetPwdFragment"
        android:label="ResetPwdFragment"
        tools:layout="@layout/fragment_reset_pwd">
        <argument
            android:name="phoneNo"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="token"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="account"
            app:argType="string"
            app:nullable="false" />
        <action
            android:id="@+id/action_resetPwdFragment_to_resetPwdSuccessFragment"
            app:destination="@id/resetPwdSuccessFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim">
            <argument
                android:name="account"
                app:argType="string"
                app:nullable="false" />
            <argument
                android:name="password"
                app:argType="string"
                app:nullable="false" />
        </action>
    </fragment>
    <fragment
        android:id="@+id/resetPwdSuccessFragment"
        android:name="com.czur.starry.device.personalcenter.begin.login.ResetPwdSuccessFragment"
        android:label="ResetPwdSuccessFragment"
        tools:layout="@layout/fragment_reset_pwd_success">
        <argument
            android:name="account"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="password"
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/registerBindPhoneFragment"
        android:name="com.czur.starry.device.personalcenter.begin.register.RegisterBindPhoneFragment"
        android:label="RegisterBindPhoneFragment"
        tools:layout="@layout/layout_phone_captcha">
        <action
            android:id="@+id/action_registerBindPhoneFragment_to_userBeginFragment"
            app:destination="@id/userBeginFragment"
            app:popUpTo="@id/loginFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
        <action
            android:id="@+id/action_registerBindPhoneFragment_to_registerSuccessFragment"
            app:destination="@id/registerSuccessFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:exitAnim="@anim/nav_default_exit_anim"/>
    </fragment>
    <fragment
        android:id="@+id/registerSuccessFragment"
        android:name="com.czur.starry.device.personalcenter.begin.register.RegisterSuccessFragment"
        android:label="RegisterSuccessFragment"
        tools:layout="@layout/fragment_register_success" />
</navigation>