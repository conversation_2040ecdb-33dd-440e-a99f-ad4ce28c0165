<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="98px"
        android:text="@string/login"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.noober.background.view.BLEditText
        android:id="@+id/loginAccountEt"
        style="@style/login_input_et"
        android:layout_marginTop="265px"
        android:hint="@string/login_account"
        android:imeOptions="actionNext"
        android:inputType="number"
        android:nextFocusDown="@id/loginPwdEt"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#********"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.noober.background.view.BLEditText
        android:id="@+id/loginPwdEt"
        style="@style/login_input_et"
        android:layout_marginTop="30px"
        android:hint="@string/login_pwd"
        android:imeOptions="actionDone"
        android:inputType="textPassword"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#********"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginAccountEt" />

    <com.czur.starry.device.baselib.widget.EyeView
        android:id="@+id/pwdEyeView"
        android:layout_width="40px"
        android:layout_height="24px"
        android:layout_marginLeft="40px"
        app:layout_constraintBottom_toBottomOf="@id/loginPwdEt"
        app:layout_constraintLeft_toRightOf="@id/loginPwdEt"
        app:layout_constraintTop_toTopOf="@id/loginPwdEt" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/loginBtn"
        style="@style/login_btn"
        android:text="@string/login"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginPwdEt" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/loginForgetBtn"
        style="@style/login_btn"
        android:text="@string/login_forget_account_pwd"
        app:colorStyle="NegativeInBlue"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginBtn" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/loginBackBtn"
        style="@style/login_btn"
        android:text="@string/login_back"
        app:colorStyle="NegativeInBlue"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginForgetBtn" />

</androidx.constraintlayout.widget.ConstraintLayout>