<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="RtlHardcoded">

    <TextView
        android:id="@+id/captchaTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="98px"
        android:text="@string/title_country_sel"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/selCountryRv"
        android:layout_width="480px"
        android:layout_height="630px"
        android:paddingVertical="10px"
        android:scrollbars="vertical"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#0D000000"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/countrySelPreBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/captchaTitleTv"
        tools:background="#0D000000"
        tools:listitem="@layout/item_country_sel" />


    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/countrySelPreBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/step_pre"
        android:textSize="30px"
        app:baselib_theme="dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/countrySelNextBtn" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/countrySelNextBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:text="@string/step_next"
        android:textSize="30px"
        app:baselib_theme="white2"
        app:layout_constraintLeft_toRightOf="@id/countrySelPreBtn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/countrySelPreBtn" />

</androidx.constraintlayout.widget.ConstraintLayout>