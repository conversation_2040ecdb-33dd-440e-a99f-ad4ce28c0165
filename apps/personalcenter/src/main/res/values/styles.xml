<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="register_line">
        <item name="android:background">@color/white</item>
        <item name="android:alpha">0.3</item>
        <item name="android:layout_width">800px</item>
        <item name="android:layout_height">2px</item>
    </style>

    <style name="startup_et">
        <item name="android:cursorVisible">true</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="register_input_et" parent="startup_et">
        <item name="android:layout_width">480px</item>
        <item name="android:layout_height">80px</item>
        <item name="android:cursorVisible">true</item>
    </style>

    <style name="register_input_title_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginRight">31px</item>
    </style>

    <style name="register_success_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
    </style>

    <style name="login_input_et">
        <item name="android:layout_width">480px</item>
        <item name="android:layout_height">80px</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="login_btn">
        <item name="android:layout_width">480px</item>
        <item name="android:layout_height">80px</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginTop">30px</item>
    </style>

    <style name="reset_pwd_et" parent="startup_et">
        <item name="android:layout_width">630px</item>
        <item name="android:layout_height">80px</item>
        <item name="android:maxLength">20</item>
        <item name="android:cursorVisible">true</item>
        <item name="android:inputType">textPassword</item>
    </style>

    <style name="begin_textview">
        <item name="android:layout_width">500px</item>
        <item name="android:layout_height">80px</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="title_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_title_color</item>
        <item name="android:layout_marginStart">30px</item>
    </style>

    <style name="content_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginStart">20px</item>
    </style>

    <style name="action_button">
        <item name="android:layout_width">150px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:textSize">24px</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>