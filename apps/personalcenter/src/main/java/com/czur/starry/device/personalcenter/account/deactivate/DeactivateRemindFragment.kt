package com.czur.starry.device.personalcenter.account.deactivate

import androidx.navigation.fragment.findNavController
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.personalcenter.databinding.LayoutDeactivateRemindBinding

/**
 * Created by 陈丰尧 on 2025/4/24
 */
class DeactivateRemindFragment: CZViewBindingFragment<LayoutDeactivateRemindBinding>() {
    override fun LayoutDeactivateRemindBinding.initBindingViews() {
        nextBtn.setOnDebounceClickListener {
            val action = DeactivateRemindFragmentDirections.actionRemindFragmentToDeactivateFragment()
            findNavController().navigate(action)
        }
    }
}