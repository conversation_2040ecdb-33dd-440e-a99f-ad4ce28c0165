package com.czur.starry.device.personalcenter.begin.register

import android.os.Bundle
import android.view.KeyEvent
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.begin.AbsBeginFragment
import com.czur.starry.device.personalcenter.databinding.FragmentRegisterSuccessBinding

/**
 * Created by 陈丰尧 on 3/2/21
 */
class RegisterSuccessFragment : AbsBeginFragment<FragmentRegisterSuccessBinding>(),
    KeyDownListener {
    override fun FragmentRegisterSuccessBinding.initBindingViews() {
        startBtn.setOnClickListener {
            beginAty.moveToNextStep()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        launch {
            val accountNo = UserHandler.accountNo

            binding.accountNoTv.text = getString(R.string.register_success_id, accountNo)
            when (Constants.starryHWInfo.salesLocale) {
                StarryDevLocale.Mainland -> {
                    val phone = UserHandler.mobile
                    binding.bindTargetTv.text = getString(R.string.register_success_phone, phone)
                }
                StarryDevLocale.Overseas -> {
                    val email = UserHandler.email
                    binding.bindTargetTv.text = getString(R.string.register_success_email, email)
                }
            }

        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        beginAty.moveToNextStep()
        return true
    }
}