package com.czur.starry.device.personalcenter.begin

import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment

/**
 * Created by 陈丰尧 on 2023/3/9
 */
abstract class AbsBeginFragment<VB : ViewBinding> : CZViewBindingFragment<VB>() {
    protected val beginAty: UserBeginNavActivity
        get() = requireActivity() as UserBeginNavActivity

    fun skipThisStep() {
        beginAty.skipThisStep()
    }

    fun NavDirections.nav() {
        findNavController().navigate(this)
    }
}