package com.czur.starry.device.personalcenter.account.ai.recharge

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.czur.starry.device.personalcenter.R
import androidx.navigation.fragment.navArgs
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.personalcenter.databinding.LayoutAiTransRechargeQrCodeBinding

/**
 * Created by 陈丰尧 on 2025/4/22
 */
private const val TAG = "RechargeQRCodeFragment"

class RechargeQRCodeFragment : CZViewBindingFragment<LayoutAiTransRechargeQrCodeBinding>() {
    private val rechargeViewModel: AITransRechargeViewModel by activityViewModels()
    private val args: RechargeQRCodeFragmentArgs by navArgs()

    override fun LayoutAiTransRechargeQrCodeBinding.initBindingViews() {
        val priceStr = formatPrice(args.price)
        val productName = args.productName
        val infoStr = getString(R.string.str_ai_trans_qr_info, productName, priceStr)
        qrInfoTv.text = infoStr

        if (Constants.starryHWInfo.hasTouchScreen) {
            titleBar.gone()
        } else {
            titleBar.onBackClick = {
                findNavController().popBackStack()
            }
        }

        // 添加刷新二维码的点击事件
        loadingErrorBgView.setOnDebounceClickListener {
            refreshQrCode()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        refreshQrCode()

        repeatCollectOnResume(rechargeViewModel.orderFinishFlow) {
            if (it) {
                logTagD(TAG, "支付成功, 自动跳转到支付成功页面")
                val orderNumber = rechargeViewModel.businessOrderNo
                val action = RechargeQRCodeFragmentDirections.actionQrCodeToSuccess(
                    args.price, args.productName, orderNumber
                )
                findNavController().navigate(action, null)
            }
        }
    }

    private fun refreshQrCode() {
        launch {
            logTagV(TAG, "refreshQrCode")
            binding.progressBar.show()
            binding.loadingErrorGroup.gone()
            binding.qrCodeIv.invisible()

            binding.qrCodeIv.setImageBitmap(null)
            val qrCodeBmpRes =
                rechargeViewModel.getQrCodeImg(args.productId, args.price, args.productName)
            val qrCodeBmp = qrCodeBmpRes.getOrNull()

            binding.progressBar.gone()

            if (qrCodeBmp != null) {
                logTagV(TAG, "获取二维码成功")
                binding.qrCodeIv.show()
                binding.qrCodeIv.setImageBitmap(qrCodeBmp)
            } else {
                logTagW(TAG, "获取二维码失败", tr = qrCodeBmpRes.exceptionOrNull())
                binding.loadingErrorGroup.show()
            }
        }
    }

    /**
     * 格式化价格
     * 如果有小数点后两位，则保留两位小数，否则保留整数
     */
    private fun formatPrice(price: Float): String {
        return if (price % 1 == 0f) {
            price.toInt().toString()
        } else {
            String.format(null, "%.2f", price)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        rechargeViewModel.stopWatchOrder()
    }
}