package com.czur.starry.device.personalcenter.account.phone

import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.databinding.LayoutChangePhoneBinding

/**
 * Created by 陈丰尧 on 2025/4/21
 */
private const val TAG = "ChangePhoneNewFragment"

class ChangePhoneNewFragment : AbsChangePhoneFragment<LayoutChangePhoneBinding>() {
    override val viewType: ChangePhoneViewModel.ViewType
        get() = ChangePhoneViewModel.ViewType.NEW

    override fun LayoutChangePhoneBinding.initSelfViews() {
        captchaTitleTv.setText(R.string.title_change_bind_phone_new)
        captchaDescTv.setText(R.string.str_change_bind_phone_new_hint)
    }

    override fun doOnNextStep() {
        val phoneNumber = viewModel.userInputPhoneNum
        val verificationCode = viewModel.userInputCaptcha
        logTagI(TAG, "更新手机号: $phoneNumber, $verificationCode")
        launch {
            withLoading {
                val result = viewModel.updateNewPhone(phoneNumber, verificationCode)
                if (result.isSuccess) {
                    // 更新成功，跳转到绑定成功页面
                    val action = ChangePhoneNewFragmentDirections.actionNewToSuccess()
                    findNavController().navigate(action)
                } else {
                    toastError(result.exceptionOrNull())
                }
            }
        }
    }


}