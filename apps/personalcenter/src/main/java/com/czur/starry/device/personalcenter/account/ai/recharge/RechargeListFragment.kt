package com.czur.starry.device.personalcenter.account.ai.recharge

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.personalcenter.databinding.LayoutAiTransRechargeListBinding
import com.czur.uilib.extension.rv.addCornerRadius

/**
 * Created by 陈丰尧 on 2025/4/22
 */
private const val TAG = "RechargeListFragment"

class RechargeListFragment : CZViewBindingFragment<LayoutAiTransRechargeListBinding>() {
    private val rechargeViewModel: AITransRechargeViewModel by activityViewModels()
    private val monthlyCardAdapter = AITransRechargeAdapter()
    private val timeBasedAdapter = AITransRechargeAdapter()


    override fun LayoutAiTransRechargeListBinding.initBindingViews() {
        monthlyCardRv.apply {
            closeDefChangeAnimations()
            addCornerRadius(10F, 0x1AFFFFFF)
            adapter = monthlyCardAdapter
        }
        timeBasedRv.apply {
            closeDefChangeAnimations()
            addCornerRadius(10F, 0x1AFFFFFF)
            adapter = timeBasedAdapter
        }

        monthlyCardAdapter.onUserSelectChange = {
            timeBasedAdapter.clearSelection()
            val selectedItem = monthlyCardAdapter.getData(it)
            rechargeViewModel.updateUserSelect(selectedItem)
        }

        timeBasedAdapter.onUserSelectChange = {
            monthlyCardAdapter.clearSelection()
            val selectedItem = timeBasedAdapter.getData(it)
            rechargeViewModel.updateUserSelect(selectedItem)
        }

        rechargeBtn.setOnDebounceClickListener {
            logTagD(TAG, "用户点击支付按钮")
            val selectedProduct =
                rechargeViewModel.userSelectItem ?: return@setOnDebounceClickListener
            val priceID = selectedProduct.id
            val productName = selectedProduct.product
            val price = selectedProduct.price

            val action = RechargeListFragmentDirections.actionListToQrCode(
                priceID, price, productName
            )
            logTagD(TAG, "用户点击支付按钮，价格ID：$priceID，产品名称：$productName，价格：$price")
            findNavController().navigate(action)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            val savedList = rechargeViewModel.savedProductList
            if (savedList.isNotEmpty()) {
                return@launch
            }

            binding.progressBar.show()
            val result = rechargeViewModel.getProductList()
            binding.progressBar.gone()
            if (result.isSuccess) {
                rechargeViewModel.saveProductList(result.getOrNull() ?: emptyList())
            } else {
                logTagW(TAG, "获取产品列表失败", tr = result.exceptionOrNull())
                binding.noNetworkGroup.show()
            }
        }

        // 月卡
        repeatCollectOnResume(rechargeViewModel.monthlyCardListFlow) {
            monthlyCardAdapter.setData(it)
        }

        // 计时
        repeatCollectOnResume(rechargeViewModel.timeBasedListFlow) {
            timeBasedAdapter.setData(it)
        }

        repeatCollectOnResume(rechargeViewModel.userSelectFlow) {
            // 支付按钮
            binding.rechargeBtn.isEnabled = it != null
        }
    }
}