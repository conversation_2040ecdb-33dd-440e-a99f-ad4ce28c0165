package com.czur.starry.device.personalcenter.begin.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.personalcenter.bean.DeviceInfo

/**
 * Created by 陈丰尧 on 2023/3/9
 */
class DeviceInfoViewModel(application: Application) : AndroidViewModel(application) {
    var deviceInfo: DeviceInfo? = null
    var phoneNoOrEmail: String = ""

    fun clearDeviceInfo() {
        deviceInfo = null
        phoneNoOrEmail = ""
    }
}