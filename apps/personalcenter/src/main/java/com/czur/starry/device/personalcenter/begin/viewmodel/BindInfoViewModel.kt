package com.czur.starry.device.personalcenter.begin.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MediatorLiveData
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.addMultipleSouse
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.isEmail
import com.czur.starry.device.baselib.utils.isPhoneNumber
import com.czur.starry.device.baselib.utils.isVerificationCode
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.personalcenter.bean.BindInfo
import com.czur.starry.device.personalcenter.net.AccountManager

/**
 * Created by 陈丰尧 on 2022/7/18
 */
private const val TAG = "BindInfoViewModel"
abstract class BindInfoViewModel(application: Application) : AndroidViewModel(application) {
    abstract val sendWay: AccountManager.VerificationCodeSendWay

    // 要发送验证码的目标: 手机号/电子邮箱
    private val sendTargetLive = DifferentLiveData("")
    var sendTarget: String by LiveDataDelegate(sendTargetLive)

    // 用户输入的信息是否合法
    val isTargetLegalLive = DifferentLiveData(false)
    private var isTargetLegal: Boolean by LiveDataDelegate(isTargetLegalLive)


    // 用户输入的验证码
    private val verificationCodeLive = DifferentLiveData("")
    var verificationCode: String by LiveDataDelegate(verificationCodeLive)

    // 注册按钮是否可用
    val registerEnableLive = MediatorLiveData<Boolean>()
    private var registerEnabled by LiveDataDelegate(registerEnableLive)

    init {
        sendTargetLive.observeForever {
            isTargetLegal = when (sendWay) {
                AccountManager.VerificationCodeSendWay.PHONE -> it.isPhoneNumber()
                AccountManager.VerificationCodeSendWay.EMAIL -> it.isEmail()
            }
        }

        launch {
            registerEnableLive.addMultipleSouse(sendTargetLive, verificationCodeLive) {
                // 更新注册按钮是否可用 输入验证码, 并且目标地址是合法的
                registerEnabled =
                    verificationCode.isVerificationCode() && isTargetLegal
            }
        }
    }

    /**
     * 清空发送信息
     */
    fun clearInfo() {
        logTagV(TAG, "clearInfo")
        sendTarget = ""
        verificationCode = ""
    }

    /**
     * 获取绑定信息
     */
    fun getBindInfo(): BindInfo {
        return BindInfo(sendTarget, verificationCode, sendWay)
    }

}

class RegisterBindEmailViewModel(application: Application) : BindInfoViewModel(application) {
    override val sendWay
        get() = AccountManager.VerificationCodeSendWay.EMAIL
}

class RegisterBindPhoneViewModel(application: Application) : BindInfoViewModel(application) {
    override val sendWay
        get() = AccountManager.VerificationCodeSendWay.PHONE
}