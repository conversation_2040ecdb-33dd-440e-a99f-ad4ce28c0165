package com.czur.starry.device.personalcenter.account.deactivate

import androidx.navigation.fragment.NavHostFragment
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.databinding.ActivityAiTransCommonBinding

/**
 * Created by 陈丰尧 on 2025/4/24
 */
class DeactivateActivity : CZViewBindingAty<ActivityAiTransCommonBinding>() {
    override fun ActivityAiTransCommonBinding.initBindingViews() {
        val fragment = navHost.getFragment<NavHostFragment>()
        val navId = R.navigation.nav_deactivate
        fragment.navController.setGraph(navId)

        titleBar.show()
    }
}