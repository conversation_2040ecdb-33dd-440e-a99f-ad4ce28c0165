package com.czur.starry.device.personalcenter.begin.register

import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnActionDoneListener
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.personalcenter.begin.AbsBeginFragment
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterBindEmailViewModel
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.CAPTCHA_ERROR
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.NETWORK_REGISTER
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.NETWORK_SEND_CODE
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.SUCCESS
import com.czur.starry.device.personalcenter.begin.viewmodel.RegisterViewModel.Companion.TIMEOUT_ERROR
import com.czur.starry.device.personalcenter.databinding.LayoutEmailCaptchaBinding
import com.czur.starry.device.personalcenter.net.AccountManager
import com.czur.starry.device.personalcenter.startup.getNetworkErrorDialog

/**
 * Created by 陈丰尧 on 8/07/22
 */
class RegisterBindEmailFragment : AbsBeginFragment<LayoutEmailCaptchaBinding>(), KeyDownListener {
    private val model: RegisterViewModel by viewModels({ requireActivity() })
    private val bindEmailViewModel: RegisterBindEmailViewModel by viewModels()
    private val loadingDialog by lazy {
        LoadingDialog()
    }

    override fun LayoutEmailCaptchaBinding.initBindingViews() {

        // 上一步
        captchaPreBtn.setOnClickListener {
            findNavController().navigateUp()
            bindEmailViewModel.clearInfo()
        }

        // 获取验证码
        getCaptchaTv.setOnClickListener {
            // 发送验证码
            sendVerifyCode()
        }

        captchaEmailEt.doAfterTextChanged {
            val text = it?.toString() ?: ""
            bindEmailViewModel.sendTarget = text
        }
        captchaEmailEt.requestFocus()
        captchaEmailEt.keyboardShow()

        // 验证码输入框
        captchaEt.doAfterTextChanged {
            it?.let {
                val verificationCode = it.toString()
                bindEmailViewModel.verificationCode = verificationCode
            }
        }
        captchaEt.setOnActionDoneListener {
            if (captchaNextBtn.isEnabled) {
                captchaNextBtn.performClick()
            } else {
                captchaEt.keyboardHide()
            }
            true
        }

        // 下一步
        captchaNextBtn.setOnClickListener {
            register()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        bindEmailViewModel.isTargetLegalLive.observe(viewLifecycleOwner) {
            // 输入正确电子邮箱地址 才能发验证码
            binding.getCaptchaTv.isEnabled = it
        }
        AccountManager.captchaTime.observe(viewLifecycleOwner) {
            if (it < 0) {
                // 显示获取
                binding.countDownTv.visibility = View.GONE
                binding.getCaptchaTv.visibility = View.VISIBLE
            } else {
                binding.countDownTv.visibility = View.VISIBLE
                binding.getCaptchaTv.visibility = View.GONE
                binding.countDownTv.text = "${it}s"
            }
        }
        bindEmailViewModel.registerEnableLive.observe(viewLifecycleOwner) {
            binding.captchaNextBtn.isEnabled = it
        }
        model.networkError.observe(viewLifecycleOwner) {
            when (it) {
                NETWORK_SEND_CODE -> getNetworkErrorDialog(requireActivity()) {
                    sendVerifyCode()
                }.show()
                NETWORK_REGISTER -> getNetworkErrorDialog(requireActivity()) {
                    register()
                }.show()
            }
        }

        model.loading.observe(viewLifecycleOwner) {
            if (it) {
                loadingDialog.show()
            } else {
                loadingDialog.dismiss()
            }
        }
    }

    private fun register() {
        launch {
            when (model.register(bindEmailViewModel.getBindInfo())) {
                SUCCESS -> {
                    // 注册成功, 下一步
                    RegisterBindEmailFragmentDirections
                        .actionRegisterBindEmailFragmentToRegisterSuccessFragment()
                        .nav()
                }
                CAPTCHA_ERROR -> {
                    // 验证码错误时, 情况验证码输入框
                    binding.captchaEt.setText("")
                }
                TIMEOUT_ERROR -> {
                    RegisterBindEmailFragmentDirections
                        .actionRegisterBindEmailFragmentToUserBeginFragment()
                        .nav()
                }
            }
        }
    }

    /**
     * 请求验证码
     */
    private fun sendVerifyCode() {
        launch {
            val email = bindEmailViewModel.sendTarget
            model.sendVerificationCode(email, AccountManager.VerificationCodeSendWay.EMAIL)
        }
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            binding.captchaPreBtn.performClick()
            true
        } else {
            false
        }
    }


}