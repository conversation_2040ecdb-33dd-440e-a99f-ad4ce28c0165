package com.czur.starry.device.personalcenter.bean

/**
 * Created by 陈丰尧 on 2025/4/23
 */
data class OrderInfo(
    val total: Int,
    val orders: List<OrderDetails>
)

data class OrderDetails(
    val id: Int,
    val accountId: String,
    val productId: Int,
    val productAlias: String,
    val num: Int,
    val invoiceStatus: Int,
    val invoiceId: String? = null,
    val orderNo: String,
    val outOrderNo: String,
    val payWay: String,
    val payStatus: String,
    val currency: String,
    val orderPrice: Double,
    val effectiveDuration: Int,
    val createTime: String,
    val updateTime: String,
    val appKey: String? = null,
    val startTime: String? = null,
    val expiredTime: String? = null
)

data class OrderQrCode(
    val binary: String
)