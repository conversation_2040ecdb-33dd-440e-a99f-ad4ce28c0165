package com.czur.starry.device.personalcenter.net

import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.personalcenter.bean.OrderUrlEntity

/**
 * Created by 陈丰尧 on 2025/4/22
 */
interface ICheckOutServer {
    /**
     * 生成订单付款的二维码
     */
    @MiaoHttpPost("/api/checkout/pay/generateCodeForm")
    fun getOrderUrl(
        @MiaoHttpParam("businessOrderNo")
        businessOrderNo: String,
        @MiaoHttpParam("productId")
        productId: Int,
        @MiaoHttpParam("orderPrice")
        orderPrice: Float,
        @MiaoHttpParam("productName")
        productName: String,
        @MiaoHttpParam("productCount")
        productCount: Int = 1,
        @MiaoHttpParam("account")
        account: String = UserHandler.accountNo,
        @MiaoHttpParam("appName")
        appName: String = "Starry Hub",
        clazz: Class<OrderUrlEntity> = OrderUrlEntity::class.java
    ): MiaoHttpEntity<OrderUrlEntity>
}