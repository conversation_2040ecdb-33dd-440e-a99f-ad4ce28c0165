package com.czur.starry.device.personalcenter.account.ai.recharge

import android.view.KeyEvent
import androidx.navigation.fragment.navArgs
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.personalcenter.databinding.LayoutAiTransRechargeSuccessBinding
import kotlin.getValue
import com.czur.starry.device.personalcenter.R


/**
 * Created by 陈丰尧 on 2025/4/23
 */
class RechargeSuccessFragment : CZViewBindingFragment<LayoutAiTransRechargeSuccessBinding>(),
    KeyDownListener {
    private val args: RechargeSuccessFragmentArgs by navArgs()
    override fun LayoutAiTransRechargeSuccessBinding.initBindingViews() {
        val price = formatPrice(args.price)
        priceTv.text = price

        val productName = args.productName
        nameTv.text = productName

        val orderNumber = args.orderNumber
        val time = formatTime(orderNumber)
        timeTv.text = time
        numberTv.text = orderNumber

        backBtn.setOnDebounceClickListener {
            requireActivity().finish()
        }
    }


    private fun formatTime(orderNumber: String): String {
        // 取出前14位 按照 yyyy-MM-dd HH:mm:ss 格式化
        val year = orderNumber.substring(0, 4)
        val month = orderNumber.substring(4, 6)
        val day = orderNumber.substring(6, 8)
        val hour = orderNumber.substring(8, 10)
        val minute = orderNumber.substring(10, 12)
        val second = orderNumber.substring(12, 14)
        return "$year-$month-$day $hour:$minute:$second"
    }



    private fun formatPrice(price: Float): String {
        // 固定保留两位小数
        val formatPrice = String.format(null, "%.2f", price)
        return getString(R.string.str_ai_trans_recharge_success_price, formatPrice)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK){
            requireActivity().finish()
            return true
        }
        return false
    }

}