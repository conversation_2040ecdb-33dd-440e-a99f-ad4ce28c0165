package com.czur.starry.device.personalcenter.account.phone

import androidx.navigation.fragment.NavHostFragment
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.databinding.ActivityChangeBindingPhoneNavBinding

/**
 * Created by 陈丰尧 on 2025/4/21
 */
class ChangeBindPhoneAty : CZViewBindingAty<ActivityChangeBindingPhoneNavBinding>() {
    override fun ActivityChangeBindingPhoneNavBinding.initBindingViews() {
        val fragment = navHost.getFragment<NavHostFragment>()
        val navId = R.navigation.nav_change_bind_phone
        fragment.navController.setGraph(navId)
    }
}