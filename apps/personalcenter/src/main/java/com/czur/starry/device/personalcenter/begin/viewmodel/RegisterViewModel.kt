package com.czur.starry.device.personalcenter.begin.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.extension.lang.hasDigit
import com.czur.czurutils.extension.lang.hasLetter
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.network.core.exception.*
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.bean.AccountNo
import com.czur.starry.device.personalcenter.bean.BindInfo
import com.czur.starry.device.personalcenter.net.AccountManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2/26/21
 */
class RegisterViewModel : ViewModel() {
    // 注册时需要选择国家
    var selCountryCode: String = "CHN"// 国内版就直接默认CHN了
    val accountNos: MutableLiveData<List<AccountNo>> = MutableLiveData()

    // 当前选中的Number
    val selectAccountNoFlow = MutableStateFlow("")

    // 密码
    private val pwdStateFlow = MutableStateFlow("")


    // 昵称
    private val nickName: MutableLiveData<String> = MutableLiveData()

    // 网络错误
    val networkError: MutableLiveData<Int> = MutableLiveData(NETWORK_NONE)


    val loading: DifferentLiveData<Boolean> = DifferentLiveData()

    val showAllAccountLive = DifferentLiveData(false)
    var showAllAccount: Boolean by LiveDataDelegate(showAllAccountLive)

    // 用户输入密码的状态
    val pwdEditStateFlow = pwdStateFlow.mapLatest {
        withContext(Dispatchers.Default) {
            when {
                it.isEmpty() -> PWDEditState.NONE
                it.length < 8 -> PWDEditState.PWD_LENGTH_ERROR
                !it.hasDigit() && !it.hasLetter() -> PWDEditState.PWD_MISSING_NUMBER_LETTER
                !it.hasDigit() -> PWDEditState.PWD_MISSING_NUMBER
                !it.hasLetter() -> PWDEditState.PWD_MISSING_LETTER
                else -> PWDEditState.PWD_OK
            }
        }
    }.distinctUntilChanged()
    val accountNextEnabledFlow = combine(selectAccountNoFlow, pwdEditStateFlow) { selNo, pwdState ->
        selNo.isNotEmpty() && pwdState == PWDEditState.PWD_OK
    }

    // 网络错误
    // 于洋:2021.2.26:
    // 初期设定的flow中,网络错误不会引导用户重新配置网络,而是点击取消后,直接进入Launcher
    var lastNetwork = NETWORK_NONE

    companion object {
        const val TAG = "RegisterViewModel"

        const val NETWORK_NONE = -1
        const val NETWORK_GET_ACCOUNT = 0
        const val NETWORK_SEND_CODE = 1
        const val NETWORK_REGISTER = 2

        const val SUCCESS = 0
        const val CAPTCHA_ERROR = -1
        const val TIMEOUT_ERROR = -2
        const val OTHER_FAIL = -10

    }

    init {
        accountNos.observeForever {
            if (it.isNotEmpty()) {
                selectAccountNoFlow.value = it[0].no.toString()
            } else {
                // 没有账号,就不展示所有账号
                showAllAccount = false
                selectAccountNoFlow.value = ""
            }
        }
    }

    /**
     * 重置网络错误
     */
    fun resetNetworkError() {
        logTagV(TAG, "重置网络错误")
        networkError.value = NETWORK_NONE
    }

    /**
     * 展示所有待选号码
     */
    fun showAllAccount() {
        if (!accountNos.value.isNullOrEmpty()) {
            showAllAccount = true
        }
    }

    fun setNickName(nickName: String) {
        val value = this.nickName.value
        if (nickName != value) {
            this.nickName.value = nickName
        }
    }


    fun setPwd(pwd: String) {
        pwdStateFlow.value = pwd
    }

    fun setSelectAccountNoIndex(sel: Int) {
        selectAccountNoFlow.value = accountNos.value?.get(sel)?.no?.toString() ?: ""
    }


    fun clearAccountNos() {
        logTagD(TAG, "清除缓存的账号")
        accountNos.value = emptyList()
        pwdStateFlow.value = ""
    }

    /**
     * 获取会议号
     */
    suspend fun getAccountIds() {
        networkError.value = NETWORK_NONE
        if (accountNos.value.isNullOrEmpty()) {
            lastNetwork = NETWORK_GET_ACCOUNT
            try {
                logTagD(TAG, "向后台获取账号")
                // 目前需要后台提供 账号的生效实现, 所以暂时搁置
                val accounts = AccountManager.obtainAccountId()
                accountNos.value = accounts
            } catch (e: MiaoHttpException) {
                processNetworkError(e)
            }
        } else {
            logTagI(TAG, "已经获取过账号了")
        }
    }

    /**
     * 发送验证码
     */
    suspend fun sendVerificationCode(
        target: String,
        sendWay: AccountManager.VerificationCodeSendWay
    ) {
        lastNetwork = NETWORK_SEND_CODE
        try {
            AccountManager.obtainCaptchaCode(target, sendWay)
        } catch (e: MiaoHttpException) {
            logTagE(TAG, "发送验证码网络错误", tr = e)
            processNetworkError(e)
        }

    }

    fun checkPwd(): Boolean {
        val password = pwdStateFlow.value
        return password.isPwdOK()
    }

    /**
     * 注册
     */
    suspend fun register(bindInfo: BindInfo): Int {
        lastNetwork = NETWORK_REGISTER
        return try {
            loading.value = true
            val userInfo = AccountManager.register(
                accountNo = selectAccountNoFlow.value,
                password = runBlocking { pwdStateFlow.value.md5() }.uppercase(),
                nickName.value ?: "",
                countryCode = selCountryCode,
                bindInfo
            )
            logTagD(TAG, "userInfo:${userInfo}")

            SUCCESS
        } catch (e: MiaoHttpException) {
            processNetworkError(e)
            when (e) {
                is MiaoVerificationCodeException -> CAPTCHA_ERROR
                is MiaoRegisterTimeOutExp -> TIMEOUT_ERROR
                else -> OTHER_FAIL
            }
        } finally {
            loading.value = false
        }
    }

    /**
     * 处理网络请求的错误
     */
    private fun processNetworkError(e: MiaoHttpException) {
        when (e) {
            // 网络错误
            is MiaoNetException -> networkError.value = lastNetwork
            // 验证码错误
            is MiaoVerificationCodeException ->
                updateToast(getString(R.string.toast_error_verification_code))

            is MiaoSMSRequestToMuch ->
                updateToast(getString(R.string.toast_error_sms_request_to_much))

            is MiaoSendSMSExp ->
                updateToast(getString(R.string.toast_error_send_sms_error))

            is MiaoSendSMSRepeat ->
                updateToast(getString(R.string.toast_error_verification_phone_repeat))

            is MiaoRegisterOverLimitExp ->
                updateToast(getString(R.string.toast_error_register_over_limit))

            is MiaoRegisterTimeOutExp ->
                updateToast(getString(R.string.toast_error_register_timeout))
            // 其他错误
            else -> {
                logTagE(TAG, "其他注册失败!!", tr = e)
                updateToast(getString(R.string.toast_register_fail))
            }

        }
    }

    private fun updateToast(toastMsg: String) {
        launch(Dispatchers.Main) {
            CZURAtyManager.currentActivity().toast(toastMsg)
        }
    }


    enum class PWDEditState {
        NONE,                       // 没有输入
        PWD_OK,                     // 密码符合要求
        PWD_LENGTH_ERROR,           // 长度不够
        PWD_MISSING_NUMBER,         // 缺少数字
        PWD_MISSING_LETTER,         // 缺少字母
        PWD_MISSING_NUMBER_LETTER,  // 缺少数字和字母
    }

}