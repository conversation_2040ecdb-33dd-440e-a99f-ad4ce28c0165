package com.czur.starry.device.personalcenter.widget

import android.content.Context
import android.view.View
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.junit.Assert.*

@RunWith(MockitoJUnitRunner::class)
class BulletPointTextViewTest {

    @Mock
    private lateinit var mockContext: Context

    private lateinit var bulletPointTextView: BulletPointTextView

    @Before
    fun setup() {
        bulletPointTextView = BulletPointTextView(mockContext)
    }

    @Test
    fun testSetText() {
        // Test that setText method doesn't crash
        bulletPointTextView.setText("Test text")
        assertEquals("Test text", bulletPointTextView.getText())
    }

    @Test
    fun testSetBulletChar() {
        // Test that setBulletChar method doesn't crash
        bulletPointTextView.setBulletChar("*")
    }

    @Test
    fun testMultilineText() {
        // Test with multiline text
        val multilineText = "Line 1\nLine 2\nLine 3"
        bulletPointTextView.setText(multilineText)
        assertEquals(multilineText, bulletPointTextView.getText())
    }
}
