<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions">
    <!-- 文件读写 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.CHAN<PERSON>_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.czur.starry.contentProvider.rw.sp" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_USB" />

    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="true" />
    <application
        android:name=".UpdateApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:persistent="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme.NoDisplay">
        <!--android:theme="@style/Theme.AppCompat">-->
        <meta-data
            android:name="design_width_in_dp"
            android:value="1920" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="1080" />
        <!--  <activity android:name=".MainActivity"
              android:launchMode="singleTask"
              android:theme="@style/AppTheme">
              <intent-filter>
                  <action android:name="android.intent.action.MAIN" />

                  <category android:name="android.intent.category.LAUNCHER" />
                  <category android:name="android.intent.category.DEFAULT" />
              </intent-filter>
          </activity>-->

        <service
            android:name=".UpdateService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.starry.update.service" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        <receiver
            android:name=".UpdateReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
            <!-- <intent-filter>
                 <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
             </intent-filter>-->
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_STATE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_MOUNTED" />
                <data android:scheme="file" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_UNMOUNTED" />
                <data android:scheme="file" />
            </intent-filter>
        </receiver>
        <provider
            android:name=".provider.OTAProvider"
            android:authorities="com.czur.starry.device.update.otaprovider"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name=".ui.DataRecoveryAlertWindow"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.update.DATA_RECOVERY_MARK" />
            </intent-filter>
        </service>
    </application>

</manifest>