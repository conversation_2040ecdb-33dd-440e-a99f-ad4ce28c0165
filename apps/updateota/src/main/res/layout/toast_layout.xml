<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	android:background="#87000000"
	android:layout_width="match_parent"
	android:layout_height="match_parent">
	<!--android:background="@drawable/shape_corner"-->
	<LinearLayout
		android:id="@+id/layout_with_buttons"
		android:background="@drawable/shape_corner"
		tools:background="#5879FC"
		android:layout_width="400px"
		android:layout_height="300px"
		android:orientation="vertical"
		android:gravity="center"
		android:paddingBottom="10px"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<ImageView
			android:id="@+id/bg_imge"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:background="@drawable/update_success"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/tv_msg"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="30px"
			android:text="升级成功"
			android:textColor="@color/white"
			android:textSize="24px"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/bg_imge" />

	</LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>