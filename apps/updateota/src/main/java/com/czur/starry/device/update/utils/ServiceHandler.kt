package com.czur.starry.device.update.utils

import android.app.Application
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import com.czur.starry.device.update.UpdateService

object ServiceHandler {
   /* private var updateServiceBinder: UpdateService.UpdateServiceBinder? = null
    private var app: Application? = null


    fun init(app: Application) {
        this.app = app


        app.bindService(Intent(app, UpdateService::class.java), object : ServiceConnection {
            override fun onServiceDisconnected(name: ComponentName?) {
            }

            override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                updateServiceBinder = service as UpdateService.UpdateServiceBinder
            }
        }, Context.BIND_AUTO_CREATE)
    }


    fun startOTATask() {
        updateServiceBinder?.service?.startOTATask()
    }
    fun sendMessage(){
        val intent = Intent("starry.device.ready")
        intent.putExtra("starry.ota.download",true)
        app?.sendBroadcast(intent)
    }*/
}