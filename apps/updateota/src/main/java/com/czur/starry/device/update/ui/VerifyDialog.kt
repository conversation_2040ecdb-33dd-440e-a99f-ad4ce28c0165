package com.czur.starry.device.update.ui

import android.app.Dialog
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.WindowManager
import android.widget.ImageView
import android.widget.Toast
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.otalib.OTAHandler.newVersionStatus
import com.czur.starry.device.update.R
import com.czur.starry.device.update.utils.RKRecoverySystem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.io.File

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/07/11
 */

private const val TAG = "VerifyDialog"

class VerifyDialog(context: Context, path: String) : Dialog(context, R.style.UpdateDialog) {
    private var path = path

    init {
        setContentView(R.layout.view_verify_dialog)
        setCancelable(false)

        val window = window!!
        window.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )

        val attributes = window.attributes
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes.height = WindowManager.LayoutParams.MATCH_PARENT
        attributes.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_NOSENSOR
        window.attributes = attributes
        window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        val iv = findViewById<ImageView>(R.id.ivIcon)
        try {
            Glide.with(context)
                .asGif()
                .load(R.drawable.icon_verify)
                .into(iv)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        }

        doVerify(File(path))
    }

    private fun doVerify(file: File) {
        logTagD(TAG, "=====file.path${file.path}=")
        MainScope().launch {
            if (!RKRecoverySystem.doesOtaPackageMatchProduct(file.path)) {
                MainScope().launch(Dispatchers.IO) {
                    RKRecoverySystem.deletePackage("@update.zip")
                }
                SPHandler.isreadyForUpdate = false
                SPHandler.firmwareUpdateVersion = "1"
                Toast.makeText(context, getString(R.string.update_verify_failed), Toast.LENGTH_LONG)
                    .show()
            } else {
                newVersionStatus = false
                RKRecoverySystem.installPackage(context, file)
            }
            dismiss()
        }

    }

    override fun onBackPressed() {
        return
    }
}