package com.czur.starry.device.settings.utils

import com.czur.czurutils.log.logTagE
import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

object FileUtils {
    fun isFileExists(filePath: String): Boolean {
        return isFileExists(getFileByPath(filePath))
    }

    fun getFileByPath(filePath: String): File? {
        return if (isSpace(filePath)) null else File(filePath)
    }

    fun isFileExists(file: File?): Boolean {
        return file != null && file.exists()
    }

    fun isFile(filePath: String): Boolean {
        return isFile(getFileByPath(filePath))
    }

    fun isFile(file: File?): Boolean {
        return file != null && file.exists() && file.isFile
    }

    fun deleteFile(srcFilePath: String): <PERSON>olean {
        return deleteFile(getFileByPath(srcFilePath))
    }

    fun deleteFile(file: File?): Boolean {
        return file != null && (!file.exists() || file.isFile && file.delete())
    }

    private fun isSpace(s: String?): Boolean {
        if (s == null) return true
        var i = 0
        val len = s.length
        while (i < len) {
            if (!Character.isWhitespace(s[i])) {
                return false
            }
            ++i
        }
        return true
    }


     fun deleteOtaFiles(f: File) {
        val fi = f.listFiles()
        for (file in fi) {
            if (file.name.substring(file.name.lastIndexOf(".") + 1) == "CZUROTA") {
                logTagE("OTA","成功删除" + file.name)
                file.delete()
            }
        }
    }

    @Throws(IOException::class)
    fun copyBigDataToSD(context: Context, fileAssetPath: String, strOutFileName: String) {
        val myInput: InputStream
        val myOutput = FileOutputStream(strOutFileName)
        myInput = context.assets.open(fileAssetPath)
        val buffer = ByteArray(1024)
        var length = myInput.read(buffer)
        while (length > 0) {
            myOutput.write(buffer, 0, length)
            length = myInput.read(buffer)
        }
        myOutput.flush()
        myInput.close()
        myOutput.close()
    }
}