package com.czur.starry.device.update.utils

import android.os.Build
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.task.CameraOTATask
import com.czur.starry.device.update.`interface`.InnerServices
import com.czur.starry.device.update.utils.HandleUtils.getAvailableExternalMemorySize
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.util.Timer
import java.util.concurrent.TimeUnit

/**
 *  author : WangHao
 *  time   :2023/11/04
 */


object CameraOTAUtil {
    private const val TAG = "CameraOTAUtil"
    var cameraOtaTask: CameraOTATask? = null
    var cameraTimerTask: Timer? = null

    /**
     * 启动camera固件Task
     */
    @Synchronized
    fun startCameraTask() {
        if (CameraOTATask.isCameraOTARunning.get()) {
            return
        }
        cameraTimerTask?.cancel()
        cameraTimerTask = null
        cameraOtaTask?.cancel()
        cameraOtaTask = null

        cameraOtaTask = CameraOTATask()
        cameraTimerTask = Timer()
        cameraTimerTask!!.schedule(cameraOtaTask, 15000, 2 * ONE_HOUR)
    }

    suspend fun checkVersionRequest() = withContext(Dispatchers.IO) {
        logTagD(TAG, "开始CameraOTA检查")
        try {

            val version =  OTAHandler.currentCameraVersion
            val sn = Constants.SERIAL

            val httpEntity = HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                .checkCameraFWVersion(sn, version, FWVersionModel::class.java)
            if (httpEntity != null && httpEntity.code == 1000) {
                logTagD(TAG, "httpEntity.body==" + httpEntity.body.toString())
                val versionEntity = httpEntity.body
                versionEntity
            } else {
                logTagD(TAG, "OTA检查请求失败")
                CameraOTATask.isCameraOTARunning.set(false)
                null
            }
        } catch (e: Exception) {
            logTagD(CameraOTATask.TAG, "OTA检查网络异常")
            e.printStackTrace()
            CameraOTATask.isCameraOTARunning.set(false)
            null
        }
    }


    suspend fun downloadOTAFile(localPath: String, url: String, total: Long = 0L) =
        withContext(Dispatchers.IO) {
            if (getAvailableExternalMemorySize() - total < 1024 * 1024 * 10) {
                return@withContext false
            } else {
                try {
                    var response: Response? = null
                    val file = File(localPath)
                    val httpClient = OkHttpClient().newBuilder()
                        .connectTimeout(10, TimeUnit.SECONDS)
                        .readTimeout(20, TimeUnit.SECONDS)
                        .build()

                    if (file.exists()) {
                        file.delete()
                    }
                    val request = Request.Builder()
                        .url(url)
                        .build()
                    response = httpClient.newCall(request).execute()
                    val body = response.body ?: return@withContext false
                    body?.byteStream()?.apply {
                        file.outputStream().use { output ->
                            this.copyTo(output)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    return@withContext false
                }
                return@withContext true

            }

        }
}