package com.czur.starry.device.update.`interface`

import android.os.Build
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.settings.model.FWVersionModel

interface InnerServices {
    /**
     * 验证版本
     */
    @MiaoHttpGet("/api/ota/starry/versionCheck")
    fun checkFWVersion(
        @MiaoHttpParam("sn") currentSN: String?,
        @MiaoHttpParam("version") currentVersion: String?,
        clazz: Class<FWVersionModel>
    ): MiaoHttpEntity<FWVersionModel>

    /**
     * 验证camera版本
     */
    @MiaoHttpGet("/api/ota/starry/camera/versionCheck")
    fun checkCameraFWVersion(
        @MiaoHttpParam("sn") currentSN: String?,
        @MiaoHttpParam("version") currentVersion: String?,
        clazz: Class<FWVersionModel>
    ): MiaoHttpEntity<FWVersionModel>

    /**
     * 验证clickDrop版本
     */
    @MiaoHttpGet("/api/ota/starry/clickDrop/versionCheck")
    fun checkClickDropFWVersion(
        @MiaoHttpParam("sn") currentSN: String?,
        @MiaoHttpParam("version") currentVersion: String?,
        @MiaoHttpParam("frameworkVersion") frameworkVersion: String = Constants.FIRMWARE_NAME,
        clazz: Class<FWVersionModel> = FWVersionModel::class.java
    ): MiaoHttpEntity<FWVersionModel>


    @MiaoHttpGet("/api/ota/starry/token")
    fun requestToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}


