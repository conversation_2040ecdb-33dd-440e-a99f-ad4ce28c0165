package com.czur.starry.device.update.ui

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.transferservice.TransferServiceManager
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.update.R
import com.czur.uilib.CZTitleBar

/**
 * Created by 陈丰尧 on 2024/7/22
 */
private const val TAG = "DataRecoveryAlertWindow"
private const val ACTION_START_RECOVERY = "com.czur.starry.device.update.ACTION_START_RECOVERY"

class DataRecoveryAlertWindow : AlertWindowService() {
    override val layoutId: Int
        get() = R.layout.alert_window_data_recovery
    override val windowWidthParam: Int
        get() = getScreenWidth()
    override val windowHeightParam: Int
        get() = getScreenHeight()

    override val keepScreenOn: Boolean
        get() = true

    override val careKeyEvent: Boolean
        get() = true

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    private val transferServiceManager: TransferServiceManager by lazy {
        TransferServiceManager(this)
    }

    private val titleBar: CZTitleBar by ViewFinder(R.id.titleBar)
    private val recoveryTitleTv: TextView by ViewFinder(R.id.recoveryTitleTv)
    private val hintTv: TextView by ViewFinder(R.id.hintTv)

    private val receiver = StartRecoveryReceiver()

    override fun View.initViews() {
        // 打开ADB
        launch {
            logTagV(TAG, "打开ADB")
            systemManager.enableADB()
        }

        titleBar.setOnClickListener {
            launch {
                logTagV(TAG, "点击返回键")
                systemManager.enableADB(false)
                stopSelf()
            }
        }
        titleBar.onBackClick = {
            launch {
                logTagV(TAG, "点击返回键")
                systemManager.enableADB(false)
                stopSelf()
            }
        }
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun initData() {
        super.initData()
        registerReceiver(
            receiver,
            IntentFilter(ACTION_START_RECOVERY)
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(receiver)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return true
    }

    private inner class StartRecoveryReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            recoveryTitleTv.show()
            titleBar.gone()
            hintTv.setText(R.string.str_data_recovery_hint)
            logTagV(TAG, "startTransferService - START")
            transferServiceManager.start()
            logTagV(TAG, "startTransferService - END")

        }

    }
}