/*
 *  UVCCamera
 *  library and sample to access to UVC web camera on non-rooted Android device

 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 *
 *  All files in the folder are under this Apache License, Version 2.0.
 *  Files in the libjpeg-turbo, libusb, libuvc, rapidjson folder
 *  may have a different license, see the respective files.
 */
package com.serenegiant.usb;

import android.graphics.SurfaceTexture;
import android.hardware.usb.UsbDevice;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;

import com.serenegiant.usb.USBMonitor.UsbControlBlock;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class UVCCamera {
	private static final boolean DEBUG = true;	// TODO set false when releasing
	private static final String TAG = UVCCamera.class.getSimpleName();
	private static final String DEFAULT_USBFS = "/dev/bus/usb";

	public static final int DEFAULT_PREVIEW_WIDTH = 640;
	public static final int DEFAULT_PREVIEW_HEIGHT = 480;
	public static final int DEFAULT_PREVIEW_MODE = 0;
	public static final int DEFAULT_PREVIEW_MIN_FPS = 1;
	public static final int DEFAULT_PREVIEW_MAX_FPS = 30;
	public static final float DEFAULT_BANDWIDTH = 1.0f;

	public static final int FRAME_FORMAT_YUYV = 0;
	public static final int FRAME_FORMAT_MJPEG = 1;

	public static final int PIXEL_FORMAT_RAW = 0;
	public static final int PIXEL_FORMAT_YUV = 1;
	public static final int PIXEL_FORMAT_RGB565 = 2;
	public static final int PIXEL_FORMAT_RGBX = 3;
	public static final int PIXEL_FORMAT_YUV420SP = 4;
	public static final int PIXEL_FORMAT_NV21 = 5;		// = YVU420SemiPlanar

	//--------------------------------------------------------------------------------
	public static final int	CTRL_SCANNING		= 0x00000001;	// D0:  Scanning Mode
	public static final int CTRL_AE				= 0x00000002;	// D1:  Auto-Exposure Mode
	public static final int CTRL_AE_PRIORITY	= 0x00000004;	// D2:  Auto-Exposure Priority
	public static final int CTRL_AE_ABS			= 0x00000008;	// D3:  Exposure Time (Absolute)
	public static final int CTRL_AR_REL			= 0x00000010;	// D4:  Exposure Time (Relative)
	public static final int CTRL_FOCUS_ABS		= 0x00000020;	// D5:  Focus (Absolute)
	public static final int CTRL_FOCUS_REL		= 0x00000040;	// D6:  Focus (Relative)
	public static final int CTRL_IRIS_ABS		= 0x00000080;	// D7:  Iris (Absolute)
	public static final int CTRL_IRIS_REL		= 0x00000100;	// D8:  Iris (Relative)
	public static final int CTRL_ZOOM_ABS		= 0x00000200;	// D9:  Zoom (Absolute)
	public static final int CTRL_ZOOM_REL		= 0x00000400;	// D10: Zoom (Relative)
	public static final int CTRL_PANTILT_ABS	= 0x00000800;	// D11: PanTilt (Absolute)
	public static final int CTRL_PANTILT_REL	= 0x00001000;	// D12: PanTilt (Relative)
	public static final int CTRL_ROLL_ABS		= 0x00002000;	// D13: Roll (Absolute)
	public static final int CTRL_ROLL_REL		= 0x00004000;	// D14: Roll (Relative)
	public static final int CTRL_FOCUS_AUTO		= 0x00020000;	// D17: Focus, Auto
	public static final int CTRL_PRIVACY		= 0x00040000;	// D18: Privacy
	public static final int CTRL_FOCUS_SIMPLE	= 0x00080000;	// D19: Focus, Simple
	public static final int CTRL_WINDOW			= 0x00100000;	// D20: Window

	public static final int PU_BRIGHTNESS		= 0x80000001;	// D0: Brightness
	public static final int PU_CONTRAST			= 0x80000002;	// D1: Contrast
	public static final int PU_HUE				= 0x80000004;	// D2: Hue
	public static final int PU_SATURATION		= 0x80000008;	// D3: Saturation
	public static final int PU_SHARPNESS		= 0x80000010;	// D4: Sharpness
	public static final int PU_GAMMA			= 0x80000020;	// D5: Gamma
	public static final int PU_WB_TEMP			= 0x80000040;	// D6: White Balance Temperature
	public static final int PU_WB_COMPO			= 0x80000080;	// D7: White Balance Component
	public static final int PU_BACKLIGHT		= 0x80000100;	// D8: Backlight Compensation
	public static final int PU_GAIN				= 0x80000200;	// D9: Gain
	public static final int PU_POWER_LF			= 0x80000400;	// D10: Power Line Frequency
	public static final int PU_HUE_AUTO			= 0x80000800;	// D11: Hue, Auto
	public static final int PU_WB_TEMP_AUTO		= 0x80001000;	// D12: White Balance Temperature, Auto
	public static final int PU_WB_COMPO_AUTO	= 0x80002000;	// D13: White Balance Component, Auto
	public static final int PU_DIGITAL_MULT		= 0x80004000;	// D14: Digital Multiplier
	public static final int PU_DIGITAL_LIMIT	= 0x80008000;	// D15: Digital Multiplier Limit
	public static final int PU_AVIDEO_STD		= 0x80010000;	// D16: Analog Video Standard
	public static final int PU_AVIDEO_LOCK		= 0x80020000;	// D17: Analog Video Lock Status
	public static final int PU_CONTRAST_AUTO	= 0x80040000;	// D18: Contrast, Auto

	// uvc_status_class from libuvc.h
	public static final int STATUS_CLASS_CONTROL = 0x10;
	public static final int STATUS_CLASS_CONTROL_CAMERA = 0x11;
	public static final int STATUS_CLASS_CONTROL_PROCESSING = 0x12;

	// uvc_status_attribute from libuvc.h
	public static final int STATUS_ATTRIBUTE_VALUE_CHANGE = 0x00;
	public static final int STATUS_ATTRIBUTE_INFO_CHANGE = 0x01;
	public static final int STATUS_ATTRIBUTE_FAILURE_CHANGE = 0x02;
	public static final int STATUS_ATTRIBUTE_UNKNOWN = 0xff;

	private static boolean isLoaded;
	static {
		if (!isLoaded) {
			System.loadLibrary("UVCjpeg");
			System.loadLibrary("UVCCamera");
			isLoaded = true;
		}
	}

	private UsbControlBlock mCtrlBlock;
	private int mUpdateStatus = 0;
	protected long mControlSupports;			// カメラコントロールでサポートしている機能フラグ
	protected long mProcSupports;				// プロセッシングユニットでサポートしている機能フラグ
	protected int mCurrentFrameFormat = FRAME_FORMAT_MJPEG;
	protected int mCurrentWidth = DEFAULT_PREVIEW_WIDTH, mCurrentHeight = DEFAULT_PREVIEW_HEIGHT;
	protected float mCurrentBandwidthFactor = DEFAULT_BANDWIDTH;
	protected String mSupportedSize;
	protected List<Size> mCurrentSizeList;
	// these fields from here are accessed from native code and do not change name and remove
	protected long mNativePtr;
	protected int mExposureModeMin, mExposureModeMax, mExposureModeDef;
	protected int mExposureMin, mExposureMax, mExposureDef;
	protected int mFocusMin, mFocusMax, mFocusDef;
	protected int mWhiteBlanceMin, mWhiteBlanceMax, mWhiteBlanceDef;
	protected int mBacklightCompMin, mBacklightCompMax, mBacklightCompDef;
	protected int mBrightnessMin, mBrightnessMax, mBrightnessDef;
	protected int mContrastMin, mContrastMax, mContrastDef;
	protected int mSharpnessMin, mSharpnessMax, mSharpnessDef;
	protected int mGainMin, mGainMax, mGainDef;
	protected int mGammaMin, mGammaMax, mGammaDef;
	protected int mSaturationMin, mSaturationMax, mSaturationDef;
	protected int mHueMin, mHueMax, mHueDef;
	protected int mZoomMin, mZoomMax, mZoomDef;
	// until here

	static {
		if (!isLoaded) {
			System.loadLibrary("UVCjpeg");
			System.loadLibrary("UVCCamera");
			isLoaded = true;
		}
	}

	/**
	 * the sonctructor of this class should be call within the thread that has a looper
	 * (UI thread or a thread that called Looper.prepare)
	 */
	public UVCCamera() {
		mNativePtr = nativeCreate();
		mSupportedSize = null;
	}

	/**
	 * connect to a UVC camera
	 * USB permission is necessary before this method is called
	 * @param ctrlBlock
	 */
	public synchronized void open(final UsbControlBlock ctrlBlock) {
		int result;
		try {
			mCtrlBlock = ctrlBlock.clone();
			result = nativeConnect(mNativePtr,
					mCtrlBlock.getVenderId(), mCtrlBlock.getProductId(),
					mCtrlBlock.getFileDescriptor(),
					mCtrlBlock.getBusNum(),
					mCtrlBlock.getDevNum(),
					getUSBFSName(mCtrlBlock));

		} catch (final Exception e) {
			Log.w(TAG, e);
			result = -1;
		}
		if (result != 0) {
			throw new UnsupportedOperationException("open failed:result=" + result);
		}
		if (mNativePtr != 0 && TextUtils.isEmpty(mSupportedSize)) {
			mSupportedSize = nativeGetSupportedSize(mNativePtr);
		}
		nativeSetPreviewSize(mNativePtr, DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT,
				DEFAULT_PREVIEW_MIN_FPS, DEFAULT_PREVIEW_MAX_FPS, DEFAULT_PREVIEW_MODE, DEFAULT_BANDWIDTH);
	}

	/**
	 * close and release UVC camera
	 */
	public synchronized void close() {
		stopPreview();
		if (mNativePtr != 0) {
			nativeRelease(mNativePtr);
//    		mNativePtr = 0;	// nativeDestroyを呼ぶのでここでクリアしちゃダメ
		}
		if (mCtrlBlock != null) {
			mCtrlBlock.close();
			mCtrlBlock = null;
		}
		mControlSupports = mProcSupports = 0;
		mCurrentFrameFormat = -1;
		mCurrentBandwidthFactor = 0;
		mSupportedSize = null;
		mCurrentSizeList = null;
		if (DEBUG) Log.v(TAG, "close:finished");
	}

	public UsbDevice getDevice() {
		return mCtrlBlock != null ? mCtrlBlock.getDevice() : null;
	}

	public String getDeviceName(){
		return mCtrlBlock != null ? mCtrlBlock.getDeviceName() : null;
	}

	public UsbControlBlock getUsbControlBlock() {
		return mCtrlBlock;
	}

	public synchronized String getSupportedSize() {
		return !TextUtils.isEmpty(mSupportedSize) ? mSupportedSize : (mSupportedSize = nativeGetSupportedSize(mNativePtr));
	}

	public Size getPreviewSize() {
		Size result = null;
		final List<Size> list = getSupportedSizeList();
		for (final Size sz: list) {
			if ((sz.width == mCurrentWidth)
					|| (sz.height == mCurrentHeight)) {
				result =sz;
				break;
			}
		}
		return result;
	}

	/**
	 * Set preview size and preview mode
	 * @param width
	 @param height
	 */
	public void setPreviewSize(final int width, final int height) {
		setPreviewSize(width, height, DEFAULT_PREVIEW_MIN_FPS, DEFAULT_PREVIEW_MAX_FPS, mCurrentFrameFormat, mCurrentBandwidthFactor);
	}

	/**
	 * Set preview size and preview mode
	 * @param width
	 * @param height
	 * @param frameFormat either FRAME_FORMAT_YUYV(0) or FRAME_FORMAT_MJPEG(1)
	 */
	public void setPreviewSize(final int width, final int height, final int frameFormat) {
		setPreviewSize(width, height, DEFAULT_PREVIEW_MIN_FPS, DEFAULT_PREVIEW_MAX_FPS, frameFormat, mCurrentBandwidthFactor);
	}

	/**
	 * Set preview size and preview mode
	 * @param width
	 @param height
	 @param frameFormat either FRAME_FORMAT_YUYV(0) or FRAME_FORMAT_MJPEG(1)
	 @param bandwidth [0.0f,1.0f]
	 */
	public void setPreviewSize(final int width, final int height, final int frameFormat, final float bandwidth) {
		setPreviewSize(width, height, DEFAULT_PREVIEW_MIN_FPS, DEFAULT_PREVIEW_MAX_FPS, frameFormat, bandwidth);
	}

	/**
	 * Set preview size and preview mode
	 * @param width
	 * @param height
	 * @param min_fps
	 * @param max_fps
	 * @param frameFormat either FRAME_FORMAT_YUYV(0) or FRAME_FORMAT_MJPEG(1)
	 * @param bandwidthFactor
	 */
	public void setPreviewSize(final int width, final int height, final int min_fps, final int max_fps, final int frameFormat, final float bandwidthFactor) {
		if ((width == 0) || (height == 0))
			throw new IllegalArgumentException("invalid preview size");
		if (mNativePtr != 0) {
			final int result = nativeSetPreviewSize(mNativePtr, width, height, min_fps, max_fps, frameFormat, bandwidthFactor);
			if (result != 0)
				throw new IllegalArgumentException("Failed to set preview size");
			mCurrentFrameFormat = frameFormat;
			mCurrentWidth = width;
			mCurrentHeight = height;
			mCurrentBandwidthFactor = bandwidthFactor;
		}
	}

	public List<Size> getSupportedSizeList() {
		final int type = (mCurrentFrameFormat > 0) ? 6 : 4;
		return getSupportedSize(type, mSupportedSize);
	}

	public static List<Size> getSupportedSize(final int type, final String supportedSize) {
		final List<Size> result = new ArrayList<Size>();
		if (!TextUtils.isEmpty(supportedSize))
			try {
				final JSONObject json = new JSONObject(supportedSize);
				final JSONArray formats = json.getJSONArray("formats");
				final int format_nums = formats.length();
				for (int i = 0; i < format_nums; i++) {
					final JSONObject format = formats.getJSONObject(i);
					if(format.has("type") && format.has("size")) {
						final int format_type = format.getInt("type");
						if ((format_type == type) || (type == -1)) {
							addSize(format, format_type, 0, result);
						}
					}
				}
			} catch (final JSONException e) {
				e.printStackTrace();
			}
		return result;
	}

	private static final void addSize(final JSONObject format, final int formatType, final int frameType, final List<Size> size_list) throws JSONException {
		final JSONArray size = format.getJSONArray("size");
		final int size_nums = size.length();
		for (int j = 0; j < size_nums; j++) {
			final String[] sz = size.getString(j).split("x");
			try {
				size_list.add(new Size(formatType, frameType, j, Integer.parseInt(sz[0]), Integer.parseInt(sz[1])));
			} catch (final Exception e) {
				break;
			}
		}
	}

	/**
	 * set preview surface with SurfaceHolder</br>
	 * you can use SurfaceHolder came from SurfaceView/GLSurfaceView
	 * @param holder
	 */
	public synchronized void setPreviewDisplay(final SurfaceHolder holder) {
		nativeSetPreviewDisplay(mNativePtr, holder.getSurface());
	}

	/**
	 * set preview surface with SurfaceTexture.
	 * this method require API >= 14
	 * @param texture
	 */
	public synchronized void setPreviewTexture(final SurfaceTexture texture) {	// API >= 11
		final Surface surface = new Surface(texture);	// XXX API >= 14
		nativeSetPreviewDisplay(mNativePtr, surface);
	}

	/**
	 * set preview surface with Surface
	 * @param surface
	 */
	public synchronized void setPreviewDisplay(final Surface surface) {
		nativeSetPreviewDisplay(mNativePtr, surface);
	}

	/**
	 * set frame callback
	 * @param callback
	 * @param pixelFormat
	 */
	public void setFrameCallback(final IFrameCallback callback, final int pixelFormat) {
		if (mNativePtr != 0) {
			nativeSetFrameCallback(mNativePtr, callback, pixelFormat);
		}
	}

	/**
	 * start preview
	 */
	public synchronized void startPreview() {
		if (mCtrlBlock != null) {
			nativeStartPreview(mNativePtr);
		}
	}

	/**
	 * stop preview
	 */
	public synchronized void stopPreview() {
		setFrameCallback(null, 0);
		if (mCtrlBlock != null) {
			nativeStopPreview(mNativePtr);
		}
	}

	public synchronized void openLed() {
		Log.d("yanjl", "aar openLed");
		if (mNativePtr != 0) {
			nativeOpenLed(mNativePtr);
		}
	}

	public synchronized void closeLed() {
		if (mNativePtr != 0) {
			nativeCloseLed(mNativePtr);
		}
	}

	public synchronized void getSerial() {
		if (mNativePtr != 0) {
			String serial = nativeGetSerial(mNativePtr);
			Log.d("yanjl", "serial :"+serial);
		}
	}
	public synchronized String getCamVersion() {
		if (mNativePtr != 0) {
			String version = nativeGetCamVersion(mNativePtr);
			Log.d("duiguang", "version :"+version);
			return version;
		}
		return null;
	}

	public synchronized int updateCamera(String file) {
		if (mNativePtr != 0) {
			mUpdateStatus = 0;

			int ret = nativeUpdateCamera(mNativePtr, file);//判断底层数据时候传输完成   0 成功   -1失败      快捷键Ctrl+N  全局搜索
			//新增以下
			if (ret == 0){
				mUpdateStatus = 1;
			}
			//新增以上

			//mUpdateStatus = 1;
			Log.d("duiguang", "nativeUpdateCamera ret:" + ret);
		}
		return mUpdateStatus;
	}
	public synchronized int getUpdateStatus() {
		if (mNativePtr != 0) {
			return mUpdateStatus;
		}
		return 0;
	}

	/**
	 * destroy UVCCamera object
	 */
	public synchronized void destroy() {
		close();
		if (mNativePtr != 0) {
			//nativeDestroy(mNativePtr);
			mNativePtr = 0;
		}
	}

	// wrong result may return when you call this just after camera open.
	// it is better to wait several hundreads millseconds.
	public boolean checkSupportFlag(final long flag) {
		updateCameraParams();
		if ((flag & 0x80000000) == 0x80000000)
			return ((mProcSupports & flag) == (flag & 0x7ffffffF));
		else
			return (mControlSupports & flag) == flag;
	}

	//================================================================================
	public synchronized void setAutoFocus(final boolean autoFocus) {
		if (mNativePtr != 0) {
			nativeSetAutoFocus(mNativePtr, autoFocus);
		}
	}

	public synchronized boolean getAutoFocus() {
		boolean result = true;
		if (mNativePtr != 0) {
			result = nativeGetAutoFocus(mNativePtr) > 0;
		}
		return result;
	}
//================================================================================
	/**
	 * @param focus [%]
	 */
	public synchronized void setFocus(final int focus) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mFocusMax - mFocusMin);
			if (range > 0)
				nativeSetFocus(mNativePtr, (int)(focus / 100.f * range) + mFocusMin);
		}
	}

	/**
	 * @param focus_abs
	 * @return focus[%]
	 */
	public synchronized int getFocus(final int focus_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateFocusLimit(mNativePtr);
			final float range = Math.abs(mFocusMax - mFocusMin);
			if (range > 0) {
				result = (int)((focus_abs - mFocusMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return focus[%]
	 */
	public synchronized int getFocus() {
		return getFocus(nativeGetFocus(mNativePtr));
	}

	public synchronized void resetFocus() {
		if (mNativePtr != 0) {
			nativeSetFocus(mNativePtr, mFocusDef);
		}
	}

	//================================================================================
	public synchronized void setAutoWhiteBlance(final boolean autoWhiteBlance) {
		if (mNativePtr != 0) {
			nativeSetAutoWhiteBlance(mNativePtr, autoWhiteBlance);
		}
	}

	public synchronized boolean getAutoWhiteBlance() {
		boolean result = true;
		if (mNativePtr != 0) {
			result = nativeGetAutoWhiteBlance(mNativePtr) > 0;
		}
		return result;
	}

//================================================================================
	/**
	 * @param whiteBlance [%]
	 */
	public synchronized void setWhiteBlance(final int whiteBlance) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mWhiteBlanceMax - mWhiteBlanceMin);
			if (range > 0)
				nativeSetWhiteBlance(mNativePtr, (int)(whiteBlance / 100.f * range) + mWhiteBlanceMin);
		}
	}

	/**
	 * @param whiteBlance_abs
	 * @return whiteBlance[%]
	 */
	public synchronized int getWhiteBlance(final int whiteBlance_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateWhiteBlanceLimit(mNativePtr);
			final float range = Math.abs(mWhiteBlanceMax - mWhiteBlanceMin);
			if (range > 0) {
				result = (int)((whiteBlance_abs - mWhiteBlanceMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return white blance[%]
	 */
	public synchronized int getWhiteBlance() {
		return getFocus(nativeGetWhiteBlance(mNativePtr));
	}

	public synchronized void resetWhiteBlance() {
		if (mNativePtr != 0) {
			nativeSetWhiteBlance(mNativePtr, mWhiteBlanceDef);
		}
	}
//================================================================================
	/**
	 * @param brightness [%]
	 */
	public synchronized void setBrightness(final int brightness) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mBrightnessMax - mBrightnessMin);
			if (range > 0)
				nativeSetBrightness(mNativePtr, (int)(brightness / 100.f * range) + mBrightnessMin);
		}
	}

	/**
	 * @param brightness_abs
	 * @return brightness[%]
	 */
	public synchronized int getBrightness(final int brightness_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateBrightnessLimit(mNativePtr);
			final float range = Math.abs(mBrightnessMax - mBrightnessMin);
			if (range > 0) {
				result = (int)((brightness_abs - mBrightnessMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return brightness[%]
	 */
	public synchronized int getBrightness() {
		return getBrightness(nativeGetBrightness(mNativePtr));
	}

	public synchronized void resetBrightness() {
		if (mNativePtr != 0) {
			nativeSetBrightness(mNativePtr, mBrightnessDef);
		}
	}

//================================================================================
	/**
	 * @param contrast [%]
	 */
	public synchronized void setContrast(final int contrast) {
		if (mNativePtr != 0) {
			nativeUpdateContrastLimit(mNativePtr);
			final float range = Math.abs(mContrastMax - mContrastMin);
			if (range > 0)
				nativeSetContrast(mNativePtr, (int)(contrast / 100.f * range) + mContrastMin);
		}
	}

	/**
	 * @param contrast_abs
	 * @return contrast[%]
	 */
	public synchronized int getContrast(final int contrast_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			final float range = Math.abs(mContrastMax - mContrastMin);
			if (range > 0) {
				result = (int)((contrast_abs - mContrastMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return contrast[%]
	 */
	public synchronized int getContrast() {
		return getContrast(nativeGetContrast(mNativePtr));
	}

	public synchronized void resetContrast() {
		if (mNativePtr != 0) {
			nativeSetContrast(mNativePtr, mContrastDef);
		}
	}

//================================================================================
	/**
	 * @param sharpness [%]
	 */
	public synchronized void setSharpness(final int sharpness) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mSharpnessMax - mSharpnessMin);
			if (range > 0)
				nativeSetSharpness(mNativePtr, (int)(sharpness / 100.f * range) + mSharpnessMin);
		}
	}

	/**
	 * @param sharpness_abs
	 * @return sharpness[%]
	 */
	public synchronized int getSharpness(final int sharpness_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateSharpnessLimit(mNativePtr);
			final float range = Math.abs(mSharpnessMax - mSharpnessMin);
			if (range > 0) {
				result = (int)(Math.abs(sharpness_abs - mSharpnessMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return sharpness[%]
	 */
	public synchronized int getSharpness() {
		return getSharpness(nativeGetSharpness(mNativePtr));
	}

	public synchronized void resetSharpness() {
		if (mNativePtr != 0) {
			nativeSetSharpness(mNativePtr, mSharpnessDef);
		}
	}
//================================================================================
	/**
	 * @param gain [%]
	 */
	public synchronized void setGain(final int gain) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mGainMax - mGainMin);
			if (range > 0)
				nativeSetGain(mNativePtr, (int)(gain / 100.f * range) + mGainMin);
		}
	}

	/**
	 * @param gain_abs
	 * @return gain[%]
	 */
	public synchronized int getGain(final int gain_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateGainLimit(mNativePtr);
			final float range = Math.abs(mGainMax - mGainMin);
			if (range > 0) {
				result = (int)((gain_abs - mGainMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return gain[%]
	 */
	public synchronized int getGain() {
		return getGain(nativeGetGain(mNativePtr));
	}

	public synchronized void resetGain() {
		if (mNativePtr != 0) {
			nativeSetGain(mNativePtr, mGainDef);
		}
	}

//================================================================================
	/**
	 * @param gamma [%]
	 */
	public synchronized void setGamma(final int gamma) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mGammaMax - mGammaMin);
			if (range > 0)
				nativeSetGamma(mNativePtr, (int)(gamma / 100.f * range) + mGammaMin);
		}
	}

	/**
	 * @param gamma_abs
	 * @return gamma[%]
	 */
	public synchronized int getGamma(final int gamma_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateGammaLimit(mNativePtr);
			final float range = Math.abs(mGammaMax - mGammaMin);
			if (range > 0) {
				result = (int)((gamma_abs - mGammaMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return gamma[%]
	 */
	public synchronized int getGamma() {
		return getGamma(nativeGetGamma(mNativePtr));
	}

	public synchronized void resetGamma() {
		if (mNativePtr != 0) {
			nativeSetGamma(mNativePtr, mGammaDef);
		}
	}

//================================================================================
	/**
	 * @param saturation [%]
	 */
	public synchronized void setSaturation(final int saturation) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mSaturationMax - mSaturationMin);
			if (range > 0)
				nativeSetSaturation(mNativePtr, (int)(saturation / 100.f * range) + mSaturationMin);
		}
	}

	/**
	 * @param saturation_abs
	 * @return saturation[%]
	 */
	public synchronized int getSaturation(final int saturation_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateSaturationLimit(mNativePtr);
			final float range = Math.abs(mSaturationMax - mSaturationMin);
			if (range > 0) {
				result = (int)((saturation_abs - mSaturationMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return saturation[%]
	 */
	public synchronized int getSaturation() {
		return getSaturation(nativeGetSaturation(mNativePtr));
	}

	public synchronized void resetSaturation() {
		if (mNativePtr != 0) {
			nativeSetSaturation(mNativePtr, mSaturationDef);
		}
	}
//================================================================================
	/**
	 * @param hue [%]
	 */
	public synchronized void setHue(final int hue) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mHueMax - mHueMin);
			if (range > 0)
				nativeSetHue(mNativePtr, (int)(hue / 100.f * range) + mHueMin);
		}
	}

	/**
	 * @param hue_abs
	 * @return hue[%]
	 */
	public synchronized int getHue(final int hue_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateHueLimit(mNativePtr);
			final float range = Math.abs(mHueMax - mHueMin);
			if (range > 0) {
				result = (int)((hue_abs - mHueMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return hue[%]
	 */
	public synchronized int getHue() {
		return getHue(nativeGetHue(mNativePtr));
	}

	public synchronized void resetHue() {
		if (mNativePtr != 0) {
			nativeSetHue(mNativePtr, mSaturationDef);
		}
	}

	//================================================================================
	public void setPowerlineFrequency(final int frequency) {
		if (mNativePtr != 0)
			nativeSetPowerlineFrequency(mNativePtr, frequency);
	}

	public int getPowerlineFrequency() {
		return nativeGetPowerlineFrequency(mNativePtr);
	}

//================================================================================
	/**
	 * this may not work well with some combination of camera and device
	 * @param zoom [%]
	 */
	public synchronized void setZoom(final int zoom) {
		if (mNativePtr != 0) {
			final float range = Math.abs(mZoomMax - mZoomMin);
			if (range > 0) {
				final int z = (int)(zoom / 100.f * range) + mZoomMin;
// 			   Log.d(TAG, "setZoom:zoom=" + zoom + " ,value=" + z);
				nativeSetZoom(mNativePtr, z);
			}
		}
	}

	/**
	 * @param zoom_abs
	 * @return zoom[%]
	 */
	public synchronized int getZoom(final int zoom_abs) {
		int result = 0;
		if (mNativePtr != 0) {
			nativeUpdateZoomLimit(mNativePtr);
			final float range = Math.abs(mZoomMax - mZoomMin);
			if (range > 0) {
				result = (int)((zoom_abs - mZoomMin) * 100.f / range);
			}
		}
		return result;
	}

	/**
	 * @return zoom[%]
	 */
	public synchronized int getZoom() {
		return getZoom(nativeGetZoom(mNativePtr));
	}

	public synchronized void resetZoom() {
		if (mNativePtr != 0) {
			nativeSetZoom(mNativePtr, mZoomDef);
		}
	}
	//=============================背光补偿=======================================

	public synchronized int getPuBacklight(final int backlight){
		int result = 0;
		if(mNativePtr != 0){
			nativeUpdateBacklightCompLimit(mNativePtr);
			final float range = Math.abs(mBacklightCompMax - mBacklightCompMin);
			if(range > 0){
				result = (int) ((backlight - mBacklightCompMin) * 100.f / range);
			}
		}
		return result;
	}

	public synchronized int getPuBacklight(){
		return getPuBacklight(nativeGetBacklightComp(mNativePtr));
	}

	public synchronized void setBacklight(final int backlight){
		if (mNativePtr != 0) {
			final float range = Math.abs(mBacklightCompMax - mBacklightCompMin);
			if (range > 0) {
				final int z = (int)(backlight / 100.f * range) + mZoomMin;
				// 			   Log.d(TAG, "setZoom:zoom=" + zoom + " ,value=" + z);
				nativeSetBacklightComp(mNativePtr, z);
			}
		}
	}

	public synchronized void resetBacklight(){
		if(mNativePtr !=0){
			nativeSetBacklightComp(mNativePtr,mBacklightCompDef);
		}
	}


	public synchronized int getExposureMode() {
		return getZoom(nativeGetExposureMode(mNativePtr));
	}

	//================================================================================
	public synchronized void updateCameraParams() {
		if (mNativePtr != 0) {
			if ((mControlSupports == 0) || (mProcSupports == 0)) {
				// サポートしている機能フラグを取得
				if (mControlSupports == 0)
					mControlSupports = nativeGetCtrlSupports(mNativePtr);
				if (mProcSupports == 0)
					mProcSupports = nativeGetProcSupports(mNativePtr);
				// 設定値を取得
				if ((mControlSupports != 0) && (mProcSupports != 0)) {
					nativeUpdateBrightnessLimit(mNativePtr);
					nativeUpdateContrastLimit(mNativePtr);
					nativeUpdateSharpnessLimit(mNativePtr);
					nativeUpdateGainLimit(mNativePtr);
					nativeUpdateGammaLimit(mNativePtr);
					nativeUpdateSaturationLimit(mNativePtr);
					nativeUpdateHueLimit(mNativePtr);
					nativeUpdateZoomLimit(mNativePtr);
					nativeUpdateWhiteBlanceLimit(mNativePtr);
					nativeUpdateFocusLimit(mNativePtr);
					nativeUpdateBacklightCompLimit(mNativePtr);
					nativeUpdateExposureRelLimit(mNativePtr);
					nativeUpdateExposureModeLimit(mNativePtr);
				}
				if (DEBUG) {
					dumpControls(mControlSupports);
					dumpProc(mProcSupports);
					Log.v(TAG, String.format("Brightness:min=%d,max=%d,def=%d", mBrightnessMin, mBrightnessMax, mBrightnessDef));
					Log.v(TAG, String.format("Contrast:min=%d,max=%d,def=%d", mContrastMin, mContrastMax, mContrastDef));
					Log.v(TAG, String.format("Sharpness:min=%d,max=%d,def=%d", mSharpnessMin, mSharpnessMax, mSharpnessDef));
					Log.v(TAG, String.format("Gain:min=%d,max=%d,def=%d", mGainMin, mGainMax, mGainDef));
					Log.v(TAG, String.format("Gamma:min=%d,max=%d,def=%d", mGammaMin, mGammaMax, mGammaDef));
					Log.v(TAG, String.format("Saturation:min=%d,max=%d,def=%d", mSaturationMin, mSaturationMax, mSaturationDef));
					Log.v(TAG, String.format("Hue:min=%d,max=%d,def=%d", mHueMin, mHueMax, mHueDef));
					Log.v(TAG, String.format("Zoom:min=%d,max=%d,def=%d", mZoomMin, mZoomMax, mZoomDef));
					Log.v(TAG, String.format("WhiteBlance:min=%d,max=%d,def=%d", mWhiteBlanceMin, mWhiteBlanceMax, mWhiteBlanceDef));
					Log.v(TAG, String.format("Focus:min=%d,max=%d,def=%d", mFocusMin, mFocusMax, mFocusDef));
					Log.v(TAG, String.format("Backlight:min=%d,max=%d,def=%d", mBacklightCompMin, mBacklightCompMax, mBacklightCompDef));
					Log.v(TAG, String.format("exposure:min=%d,max=%d,def=%d", mExposureMin, mExposureMax, mExposureDef));
					Log.v(TAG, String.format("exposureMode:min=%d,max=%d,def=%d", mExposureModeMin, mExposureModeMax, mExposureModeDef));

				}
			}
		} else {
			mControlSupports = mProcSupports = 0;
		}
	}

	public byte[] getLastFrame(){
		if(mNativePtr != 0){
			return getLastFrame(mNativePtr);
		}
		return null;
	}

	private static final String[] SUPPORTS_CTRL = {
			"D0:  Scanning Mode",
			"D1:  Auto-Exposure Mode",
			"D2:  Auto-Exposure Priority",
			"D3:  Exposure Time (Absolute)",
			"D4:  Exposure Time (Relative)",
			"D5:  Focus (Absolute)",
			"D6:  Focus (Relative)",
			"D7:  Iris (Absolute)",
			"D8:  Iris (Relative)",
			"D9:  Zoom (Absolute)",
			"D10: Zoom (Relative)",
			"D11: PanTilt (Absolute)",
			"D12: PanTilt (Relative)",
			"D13: Roll (Absolute)",
			"D14: Roll (Relative)",
			"D15: Reserved",
			"D16: Reserved",
			"D17: Focus, Auto",
			"D18: Privacy",
			"D19: Focus, Simple",
			"D20: Window",
			"D21: Region of Interest",
			"D22: Reserved, set to zero",
			"D23: Reserved, set to zero",
	};

	private static final String[] SUPPORTS_PROC = {
			"D0: Brightness",
			"D1: Contrast",
			"D2: Hue",
			"D3: Saturation",
			"D4: Sharpness",
			"D5: Gamma",
			"D6: White Balance Temperature",
			"D7: White Balance Component",
			"D8: Backlight Compensation",
			"D9: Gain",
			"D10: Power Line Frequency",
			"D11: Hue, Auto",
			"D12: White Balance Temperature, Auto",
			"D13: White Balance Component, Auto",
			"D14: Digital Multiplier",
			"D15: Digital Multiplier Limit",
			"D16: Analog Video Standard",
			"D17: Analog Video Lock Status",
			"D18: Contrast, Auto",
			"D19: Reserved. Set to zero",
			"D20: Reserved. Set to zero",
			"D21: Reserved. Set to zero",
			"D22: Reserved. Set to zero",
			"D23: Reserved. Set to zero",
	};

	private static final void dumpControls(final long controlSupports) {
		Log.i(TAG, String.format("controlSupports=%x", controlSupports));
		for (int i = 0; i < SUPPORTS_CTRL.length; i++) {
			Log.i(TAG, SUPPORTS_CTRL[i] + ((controlSupports & (0x1 << i)) != 0 ? "=enabled" : "=disabled"));
		}
	}

	private static final void dumpProc(final long procSupports) {
		Log.i(TAG, String.format("procSupports=%x", procSupports));
		for (int i = 0; i < SUPPORTS_PROC.length; i++) {
			Log.i(TAG, SUPPORTS_PROC[i] + ((procSupports & (0x1 << i)) != 0 ? "=enabled" : "=disabled"));
		}
	}

	private final String getUSBFSName(final UsbControlBlock ctrlBlock) {
		String result = null;
		final String name = ctrlBlock.getDeviceName();
		final String[] v = !TextUtils.isEmpty(name) ? name.split("/") : null;
		if ((v != null) && (v.length > 2)) {
			final StringBuilder sb = new StringBuilder(v[0]);
			for (int i = 1; i < v.length - 2; i++)
				sb.append("/").append(v[i]);
			result = sb.toString();
		}
		if (TextUtils.isEmpty(result)) {
			Log.w(TAG, "failed to get USBFS path, try to use default path:" + name);
			result = DEFAULT_USBFS;
		}
		return result;
	}

	// #nativeCreate and #nativeDestroy are not static methods.
	public native static long nativeCreate();
	public native static void nativeDestroy(final long id_camera);

	public native static int nativeConnect(long id_camera, int venderId, int productId, int fileDescriptor, int busNum, int devAddr, String usbfs);
	public native static int nativeRelease(final long id_camera);

	public native static int nativeSetPreviewSize(final long id_camera, final int width, final int height, final int min_fps, final int max_fps, final int mode, final float bandwidth);
	public native static String nativeGetSupportedSize(final long id_camera);

	public native static int nativeStartPreview(final long id_camera);
	public native static int nativeStopPreview(final long id_camera);
	public native static int nativeSetPreviewDisplay(final long id_camera, final Surface surface);
	public native static int nativeSetFrameCallback(final long mNativePtr, final IFrameCallback callback, final int pixelFormat);

//**********************************************************************
	/**
	 * start movie capturing(this should call while previewing)
	 * @param surface
	 */
	public void startCapture(final Surface surface) {
		if (mCtrlBlock != null && surface != null) {
			nativeSetCaptureDisplay(mNativePtr, surface);
		} else
			throw new NullPointerException("startCapture");
	}

	/**
	 * stop movie capturing
	 */
	public void stopCapture() {
		if (mCtrlBlock != null) {
			nativeSetCaptureDisplay(mNativePtr, null);
		}
	}
	public native static int nativeSetCaptureDisplay(final long id_camera, final Surface surface);

	public native static long nativeGetCtrlSupports(final long id_camera);
	public native static long nativeGetProcSupports(final long id_camera);

	//扫描模式
	public native static int nativeUpdateScanningModeLimit(final long id_camera);
	public native static int nativeSetScanningMode(final long id_camera, final int scanning_mode);
	public native static int nativeGetScanningMode(final long id_camera);



	//曝光模式
	//曝光模式
	public native static int nativeUpdateExposureModeLimit(final long id_camera);
	public native static int nativeSetExposureMode(final long id_camera, final int exposureMode);
	public native static int nativeGetExposureMode(final long id_camera);
	//曝光优先设置
	public native static int nativeUpdateExposurePriorityLimit(final long id_camera);
	public native static int nativeSetExposurePriority(final long id_camera, final int priority);
	public native static int nativeGetExposurePriority(final long id_camera);
	//曝光（绝对值）设置
	public native static int nativeUpdateExposureLimit(final long id_camera);
	public native static int nativeSetExposure(final long id_camera, final int exposure);
	public native static int nativeGetExposure(final long id_camera);
	//曝光（相对值）设置
	public native static int nativeUpdateExposureRelLimit(final long id_camera);
	public native static int nativeSetExposureRel(final long id_camera, final int exposure_rel);
	public native static int nativeGetExposureRel(final long id_camera);
	//自动聚焦
	public native static int nativeUpdateAutoFocusLimit(final long id_camera);
	public native static int nativeSetAutoFocus(final long id_camera, final boolean autofocus);
	public native static int nativeGetAutoFocus(final long id_camera);
	//对焦（绝对值）调整
	public native static int nativeUpdateFocusLimit(final long id_camera);
	public native static int nativeSetFocus(final long id_camera, final int focus);
	public native static int nativeGetFocus(final long id_camera);
	//对焦（相对值）调整
	public native static int nativeUpdateFocusRelLimit(final long id_camera);
	public native static int nativeSetFocusRel(final long id_camera, final int focus_rel);
	public native static int nativeGetFocusRel(final long id_camera);
	//光圈（绝对值）调整
	public native static int nativeUpdateIrisLimit(final long id_camera);
	public native static int nativeSetIris(final long id_camera, final int iris);
	public native static int nativeGetIris(final long id_camera);
	//光圈（相对值）调整
	public native static int nativeUpdateIrisRelLimit(final long id_camera);
	public native static int nativeSetIrisRel(final long id_camera, final int iris_rel);
	public native static int nativeGetIrisRel(final long id_camera);

	public native static int nativeUpdatePanLimit(final long id_camera);
	public native static int nativeSetPan(final long id_camera, final int pan);
	public native static int nativeGetPan(final long id_camera);

	public native static int nativeUpdatePanRelLimit(final long id_camera);
	public native static int nativeSetPanRel(final long id_camera, final int pan_rel);
	public native static int nativeGetPanRel(final long id_camera);
	//镜头倾斜
	public native static int nativeUpdateTiltLimit(final long id_camera);
	public native static int nativeSetTilt(final long id_camera, final int tilt);
	public native static int nativeGetTilt(final long id_camera);

	public native static int nativeUpdateTiltRelLimit(final long id_camera);
	public native static int nativeSetTiltRel(final long id_camera, final int tilt_rel);
	public native static int nativeGetTiltRel(final long id_camera);
	//左右调整
	public native static int nativeUpdateRollLimit(final long id_camera);
	public native static int nativeSetRoll(final long id_camera, final int roll);
	public native static int nativeGetRoll(final long id_camera);

	public native static int nativeUpdateRollRelLimit(final long id_camera);
	public native static int nativeSetRollRel(final long id_camera, final int roll_rel);
	public native static int nativeGetRollRel(final long id_camera);
	//自动白平衡
	public native static int nativeUpdateAutoWhiteBlanceLimit(final long id_camera);
	public native static int nativeSetAutoWhiteBlance(final long id_camera, final boolean autoWhiteBlance);
	public native static int nativeGetAutoWhiteBlance(final long id_camera);

	public native static int nativeUpdateAutoWhiteBlanceCompoLimit(final long id_camera);
	public native static int nativeSetAutoWhiteBlanceCompo(final long id_camera, final boolean autoWhiteBlanceCompo);
	public native static int nativeGetAutoWhiteBlanceCompo(final long id_camera);
	//白平衡色温度调整
	public native static int nativeUpdateWhiteBlanceLimit(final long id_camera);
	public native static int nativeSetWhiteBlance(final long id_camera, final int whiteBlance);
	public native static int nativeGetWhiteBlance(final long id_camera);

	public native static int nativeUpdateWhiteBlanceCompoLimit(final long id_camera);
	public native static int nativeSetWhiteBlanceCompo(final long id_camera, final int whiteBlance_compo);
	public native static int nativeGetWhiteBlanceCompo(final long id_camera);
	//背光补偿
	public native static int nativeUpdateBacklightCompLimit(final long id_camera);
	public native static int nativeSetBacklightComp(final long id_camera, final int backlight_comp);
	public native static int nativeGetBacklightComp(final long id_camera);
	//亮度
	public native static int nativeUpdateBrightnessLimit(final long id_camera);
	public native static int nativeSetBrightness(final long id_camera, final int brightness);
	public native static int nativeGetBrightness(final long id_camera);
	//对比度
	public native static int nativeUpdateContrastLimit(final long id_camera);
	public native static int nativeSetContrast(final long id_camera, final int contrast);
	public native static int nativeGetContrast(final long id_camera);
	//自动对比度
	public native static int nativeUpdateAutoContrastLimit(final long id_camera);
	public native static int nativeSetAutoContrast(final long id_camera, final boolean autocontrast);
	public native static int nativeGetAutoContrast(final long id_camera);
	//锐度
	public native static int nativeUpdateSharpnessLimit(final long id_camera);
	public native static int nativeSetSharpness(final long id_camera, final int sharpness);
	public native static int nativeGetSharpness(final long id_camera);
	//增益
	public native static int nativeUpdateGainLimit(final long id_camera);
	public native static int nativeSetGain(final long id_camera, final int gain);
	public native static int nativeGetGain(final long id_camera);
	//GAMMA
	public native static int nativeUpdateGammaLimit(final long id_camera);
	public native static int nativeSetGamma(final long id_camera, final int gamma);
	public native static int nativeGetGamma(final long id_camera);
	//彩度（色度）调整
	public native static int nativeUpdateSaturationLimit(final long id_camera);
	public native static int nativeSetSaturation(final long id_camera, final int saturation);
	public native static int nativeGetSaturation(final long id_camera);
	//色相调整
	public native static int nativeUpdateHueLimit(final long id_camera);
	public native static int nativeSetHue(final long id_camera, final int hue);
	public native static int nativeGetHue(final long id_camera);

	public native static int nativeUpdateAutoHueLimit(final long id_camera);
	public native static int nativeSetAutoHue(final long id_camera, final boolean autohue);
	public native static int nativeGetAutoHue(final long id_camera);
	//根据电源频率的模糊修正
	public native static int nativeUpdatePowerlineFrequencyLimit(final long id_camera);
	public native static int nativeSetPowerlineFrequency(final long id_camera, final int frequency);
	public native static int nativeGetPowerlineFrequency(final long id_camera);
	//变焦（ABS）调整
	public native static int nativeUpdateZoomLimit(final long id_camera);
	public native static int nativeSetZoom(final long id_camera, final int zoom);
	public native static int nativeGetZoom(final long id_camera);

	public native static int nativeUpdateZoomRelLimit(final long id_camera);
	public native static int nativeSetZoomRel(final long id_camera, final int zoom_rel);
	public native static int nativeGetZoomRel(final long id_camera);

	public native static int nativeUpdateDigitalMultiplierLimit(final long id_camera);
	public native static int nativeSetDigitalMultiplier(final long id_camera, final int multiplier);
	public native static int nativeGetDigitalMultiplier(final long id_camera);

	public native static int nativeUpdateDigitalMultiplierLimitLimit(final long id_camera);
	public native static int nativeSetDigitalMultiplierLimit(final long id_camera, final int multiplier_limit);
	public native static int nativeGetDigitalMultiplierLimit(final long id_camera);
	//类比标准？
	public native static int nativeUpdateAnalogVideoStandardLimit(final long id_camera);
	public native static int nativeSetAnalogVideoStandard(final long id_camera, final int standard);
	public native static int nativeGetAnalogVideoStandard(final long id_camera);

	public native static int nativeUpdateAnalogVideoLockStateLimit(final long id_camera);
	public native static int nativeSetAnalogVideoLoackState(final long id_camera, final int state);
	public native static int nativeGetAnalogVideoLoackState(final long id_camera);
	//隐私模式
	public native static int nativeUpdatePrivacyLimit(final long id_camera);
	public native static int nativeSetPrivacy(final long id_camera, final boolean privacy);
	public native static int nativeGetPrivacy(final long id_camera);

	// 开关红外Led灯
	public native static int nativeOpenLed(final long id_camera);
	public native static int nativeCloseLed(final long id_camera);

	// 获取序列号
	public native static String nativeGetSerial(final long id_camera);

	public native static String nativeGetCamVersion(final long id_camera);
	public native static int nativeUpdateCamera(final long id_camera, final String file);

	//最后一帧
	public native static byte[] getLastFrame(final long id_camera);
}
