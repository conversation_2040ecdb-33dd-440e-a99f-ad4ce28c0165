package com.czur.starry.device.sharescreen

import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.nfc.Tag
import android.os.Bundle
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.sharescreen.esharelib.util.checkAndActiveEShare
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2022/1/11
 */
private const val TAG = "BootActivity"
class BootActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        super.onCreate(savedInstanceState)
        MainScope().launch {
            val activeRes = checkAndActiveEShare(this@BootActivity)
            if (activeRes) {
                startMain()
            } else {
                toastErrorMsg()
            }
        }
    }

    private fun toastErrorMsg() {
        toast(R.string.toast_eShare_no_active)
        finish()
    }

    private fun startMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }
}