package com.czur.starry.device.sharescreen

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.wifi.WifiManager
import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.commit
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.NoNavActivity
import com.czur.starry.device.baselib.common.ACTION_BROADCAST_SOURCE_CHANGE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_SOURCE_CHANGE
import com.czur.starry.device.baselib.common.VALUE_SOURCE_CHANGE_E_SHARE
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.sharescreen.esharelib.ShareViewModel
import com.czur.starry.device.sharescreen.esharelib.ui.EShareFragment
import com.czur.uilib.CZTitleBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class MainActivity : NoNavActivity() {
    override fun getLayout(): Int = R.layout.activity_main

    private val shareVM: ShareViewModel by viewModels()
    private val titleBar: CZTitleBar by lazy { findViewById(R.id.titleBar) }

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun initViews() {
        super.initViews()

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            titleBar.setTitle(R.string.app_name_army_share_screen)
        } else {
            titleBar.setTitle(R.string.app_name)
        }

        supportFragmentManager.commit {
            add(R.id.content, EShareFragment())
        }

        val filter = IntentFilter().apply {
            addAction(ESHARE_EXIT_ACTION)
            addAction(HDMI_EXIT_ACTION)
            addAction(ESHARE_SETTING_ACTION)
        }
        this.registerReceiver(sourceChannelReceiver, filter)

        val wifiFilter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
        }
        this.registerReceiver(wifiReceiver, wifiFilter)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                super.onResume(owner)
                launch {
                    sendChangSourceBroadcast()
                }
            }

            override fun onDestroy(owner: LifecycleOwner) {
                unregisterReceiver(sourceChannelReceiver)
                unregisterReceiver(wifiReceiver)
                super.onDestroy(owner)
            }
        })
    }


    private suspend fun sendChangSourceBroadcast() = withContext(Dispatchers.IO) {
        logTagV(TAG, "源切换广播：HDMI")
        val intent = Intent(ACTION_BROADCAST_SOURCE_CHANGE).apply {
            putExtra(KEY_SOURCE_CHANGE, VALUE_SOURCE_CHANGE_E_SHARE)
        }
        sendBroadcast(intent)
    }


    //source键切换StarryOS广播
    private val sourceChannelReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == ESHARE_SETTING_ACTION) {
                launch {
                    shareVM.loadShareInfo()
                }
            } else {
                finish()
            }
        }
    }

    private val wifiReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action ?: ""
            logTagD(TAG, "receiver#action: $action")

            shareVM.refreshMiracastEnable()
        }

    }
}