package com.czur.starry.device.sharescreen.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.TypedValue
import androidx.annotation.StringRes
import androidx.camera.core.processing.SurfaceProcessorNode.In
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.sharescreen.R
import com.czur.starry.device.sharescreen.databinding.WidgetStepViewBinding

/**
 * Created by 陈丰尧 on 2024/5/20
 */
class StepView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding by lazy(LazyThreadSafetyMode.NONE) {
        WidgetStepViewBinding.bind(this)
    }

    init {
        inflate(context, R.layout.widget_step_view, this)
        val ta = context.obtainStyledAttributes(attrs, R.styleable.StepView)
        ta.getDrawable(R.styleable.StepView_stepIcon)?.let {
            binding.stepIconIv.setImageDrawable(it)
        }

        val stepText = ta.getText(R.styleable.StepView_stepText)
        binding.stepFirstLineTv.text = stepText
        binding.stepTv.text = stepText

        val stepColor = ta.getColor(R.styleable.StepView_stepTextColor, Color.BLACK)
        binding.stepTv.setTextColor(stepColor)

        val stepTextSize = ta.getDimension(R.styleable.StepView_stepTextSize, 16f)
        binding.stepTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, stepTextSize)
        binding.stepFirstLineTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, stepTextSize)

        val spaceWidth = ta.getDimensionPixelSize(R.styleable.StepView_stepIconSpace, 0)
        val lp = binding.stepSpace.layoutParams as MarginLayoutParams
        lp.width = spaceWidth
        binding.stepSpace.layoutParams = lp

        ta.recycle()
    }

    fun setStepText(@StringRes resId: Int) {
        binding.stepTv.setText(resId)
        binding.stepFirstLineTv.setText(resId)
    }
}