<resources>
    <string name="app_name">无线投屏</string>
    <string name="app_name_army">投屏</string>
    <string name="toast_eShare_no_active">首次使用需要连接网络</string>
    <string name="str_alert_win_device_name">设备名: %s</string>

    <string name="str_quick_boot_title_byom">专业外设模式</string>
    <string name="str_byom_content">会议星作为外部设备，其麦克风 、扬声器、摄像头和大屏可以被电脑端软件使用</string>

    <string name="str_byom_wireless_title">无线外设模式（BYOM）</string>
    <string name="byom_wireless_content1">将无线外设模块，插入电脑端口（USB/Type-C），等待指示灯从闪烁到长亮；</string>
    <string name="byom_wireless_content2">电脑打开会议软件（腾讯会议、飞书、钉钉、Zoom等），发起/加入会议；</string>
    <string name="byom_wireless_content3">在电脑的会议软件上，将麦克风、扬声器、摄像头三项都选择为StarryHub；</string>
    <string name="byom_wireless_content4">点击无线外设模块，进行投屏。</string>

    <string name="str_byom_USB_title">USB 外设模式</string>
    <string name="byom_USB_content1">将专用USB线，连接会议星（2.0端口）和电脑；</string>
    <string name="byom_USB_content1_type_c">将专用数据线，连接会议星（Type-C端口）和电脑；</string>
    <string name="byom_USB_content2">在电脑上打开会议软件（腾讯会议、飞书、钉钉、Zoom等），发起/加入会议；</string>
    <string name="byom_USB_content3">在电脑的会议软件上，将麦克风、扬声器、摄像头三项都选择为StarryHub；</string>
    <string name="byom_USB_content4">同时可按无线投屏的方法，将电脑的屏幕投屏给会议星。</string>

</resources>