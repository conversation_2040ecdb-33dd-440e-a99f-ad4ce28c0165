<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Беспроводная трансляция экрана</string>
    <string name="toast_eShare_no_active">При первом использовании требуется подключение к Интернету.</string>
    <string name="str_alert_win_device_name">Имя устройства: %s</string>
    <string name="str_quick_boot_title_byom">Режим периферии</string>
    <string name="app_name_army">Общий экран</string>
    <string name="str_byom_content">Как периферийное устройство, микрофон / Громкоговорители / камеры / проекционные экраны StarryHub могут использоваться приложениями на компьютере.</string>
    <string name="str_byom_wireless_title">Режим беспроводной установки</string>
    <string name="byom_wireless_content1">Вставьте ClickDrop (USB/Type-C) в StarryHub для спаривания.</string>
    <string name="byom_wireless_content2">Вставьте ClickDrop (уже в паре со StarryHub) в компьютер для спаривания.</string>
    <string name="byom_wireless_content3">После спаривания камера / микрофон StarryHub Камера может использоваться в качестве периферийного устройства через компьютерное приложение.</string>
    <string name="byom_wireless_content4">Нажмите кнопку ClickDrop, чтобы разделить экран компьютера со StarryHub.</string>
    <string name="str_byom_USB_title">Режим внешней проводки</string>
    <string name="byom_USB_content1">Включить внешний проводной режим.</string>
    <string name="byom_USB_content2">Через интерфейс USB 2.0 компьютер подключается к StarryHub с помощью линии передачи данных USB.</string>
    <string name="byom_USB_content3">Выберите микрофон StarryHub Камера / динамик используется в качестве периферийного устройства в компьютерных приложениях.</string>
    <string name="byom_USB_content4">Вы можете использовать беспроводное зеркалирование экрана для трансляции экрана компьютера на устройство.</string>
</resources>
