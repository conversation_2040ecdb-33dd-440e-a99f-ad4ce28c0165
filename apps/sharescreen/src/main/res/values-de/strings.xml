<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Wi-Fi-Screencasting</string>
    <string name="toast_eShare_no_active">Bei der ersten Nutzung ist eine Internetverbindung erforderlich.</string>
    <string name="str_alert_win_device_name">Name der Firma: %s</string>
    <string name="str_quick_boot_title_byom">Peripheriebetrieb</string>
    <string name="app_name_army">Bildschirm teilen</string>
    <string name="str_byom_content">Als Peripherie, das Mikrofon./ Der Lautsprecher/Kamera/Projektionsbildschirm von StarryHub kann von der App auf dem Computer verwendet werden.</string>
    <string name="str_byom_wireless_title">Ka<PERSON>oser Peripherie<PERSON>dus</string>
    <string name="byom_wireless_content1">Schließen Sie den ClickDrop (USB/Type-C) an den StarryHub an, um ihn zu koppeln.</string>
    <string name="byom_wireless_content2">Schließen Sie den ClickDrop (bereits mit StarryHub gekoppelt) zum Koppeln an den Computer an.</string>
    <string name="byom_wireless_content3">Einmal gekoppelt, StarryHubs Kamera/Mikrofon./ Die Kamera kann als Peripherie über die Computer-App angewendet werden.</string>
    <string name="byom_wireless_content4">Drücken Sie die Schaltfläche ClickDrop, um den Computerbildschirm mit StarryHub zu teilen.</string>
    <string name="str_byom_USB_title">Kabelgebundener Peripheriemodus</string>
    <string name="byom_USB_content1">Kabelgebundener Peripheriemodus aktivieren.</string>
    <string name="byom_USB_content2">Verbinden Sie den Computer über das USB-Datenkabel über die USB-2.0-Schnittstelle mit StarryHub.</string>
    <string name="byom_USB_content3">StarryHub mic auswählen./ Kamera/Lautsprecher als Peripherie in der Computer-App.</string>
    <string name="byom_USB_content4">Sie können das Drahtlose Bildschirm-Spiegeln verwenden, \num Ihren Computerbildschirm auf einem Gerät anzuzeigen.</string>
</resources>
