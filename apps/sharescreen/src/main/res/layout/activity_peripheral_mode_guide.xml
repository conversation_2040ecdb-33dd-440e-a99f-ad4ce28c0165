<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_byom_guide"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title=""
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/peripheralModeTitle"
        style="@style/byom_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/str_quick_boot_title_byom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/peripheralModeSub"
        style="@style/byom_content_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5px"
        android:text="@string/str_byom_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/peripheralModeTitle" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/centerVerticalGuideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/eshareByomGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="wirelessModeTitleTv,wirelessModeIv,wirelessStep1,wirelessStep2,wirelessStep3" />

    <TextView
        android:id="@+id/wirelessModeTitleTv"
        style="@style/byom_subContent_title30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="190px"
        android:text="@string/str_byom_wireless_title"
        app:layout_constraintLeft_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/usbModeTitleTv"
        style="@style/byom_subContent_title30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="190px"
        android:text="@string/str_byom_USB_title"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/usbModeSwitch"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/usbModeSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginLeft="30px"
        app:czSwitchBgOffColor="#D34770"
        app:czSwitchBgOnColor="@color/white"
        app:czSwitchBorderColor="@color/white"
        app:czSwitchTextOffColor="@color/white"
        app:czSwitchTextOnColor="#D34770"
        app:czSwitchThumbOffColor="@color/white"
        app:czSwitchThumbOnColor="#D34770"
        app:layout_constraintBottom_toBottomOf="@id/usbModeTitleTv"
        app:layout_constraintLeft_toRightOf="@id/usbModeTitleTv"
        app:layout_constraintRight_toRightOf="@id/centerVerticalGuideline"
        app:layout_constraintTop_toTopOf="@id/usbModeTitleTv" />

    <ImageView
        android:id="@+id/wirelessModeIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="270px"
        android:src="@drawable/img_byom_wireless"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/usbModeIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="270px"
        android:src="@drawable/img_byom_usb"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintTop_toTopOf="parent" />


    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/wirelessStep1"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="60px"
        app:layout_constraintLeft_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wirelessModeIv"
        app:stepIcon="@drawable/icon_number1"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_wireless_content1"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/wirelessStep2"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wirelessStep1"
        app:stepIcon="@drawable/icon_number2"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_wireless_content2"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/wirelessStep3"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wirelessStep2"
        app:stepIcon="@drawable/icon_number3"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_wireless_content3"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/wirelessStep4"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="@id/centerVerticalGuideline"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wirelessStep3"
        app:stepIcon="@drawable/icon_number4"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_wireless_content4"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />


    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep1"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="60px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/centerVerticalGuideline"
        app:layout_constraintTop_toBottomOf="@id/usbModeIv"
        app:stepIcon="@drawable/icon_number1"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content1"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep2"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/centerVerticalGuideline"
        app:layout_constraintTop_toBottomOf="@id/usbStep1"
        app:stepIcon="@drawable/icon_number2"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content2"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep3"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/centerVerticalGuideline"
        app:layout_constraintTop_toBottomOf="@id/usbStep2"
        app:stepIcon="@drawable/icon_number3"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content3"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

    <com.czur.starry.device.sharescreen.widget.StepView
        android:id="@+id/usbStep4"
        android:layout_width="740px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/centerVerticalGuideline"
        app:layout_constraintTop_toBottomOf="@id/usbStep3"
        app:stepIcon="@drawable/icon_number4"
        app:stepIconSpace="15px"
        app:stepText="@string/byom_USB_content4"
        app:stepTextColor="@color/white"
        app:stepTextSize="@dimen/wirelessStepSize" />

</androidx.constraintlayout.widget.ConstraintLayout>