<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/background_cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <com.czur.starry.device.file.view.ViewPagerSlideEventGroup
        android:id="@+id/vp_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />

    </com.czur.starry.device.file.view.ViewPagerSlideEventGroup>


    <com.czur.starry.device.file.widget.PAVSwitchIv
        android:id="@+id/photoPreIv"
        android:layout_width="120px"
        android:layout_height="120px"
        android:layout_marginLeft="90px"
        android:src="@drawable/ic_photo_pre"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.file.widget.PAVSwitchIv
        android:id="@+id/photoNextIv"
        android:layout_width="120px"
        android:layout_height="120px"
        android:layout_marginRight="90px"
        android:src="@drawable/ic_photo_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/photoUIGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="photoNextIv,photoPreIv" />

</androidx.constraintlayout.widget.ConstraintLayout>