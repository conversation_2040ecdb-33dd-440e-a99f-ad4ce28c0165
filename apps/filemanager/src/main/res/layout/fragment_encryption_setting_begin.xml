<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_main"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:constraint_referenced_ids="titleTv,changePwdBtn,changeEmailBtn"
        app:flow_verticalGap="55px"
        app:flow_verticalStyle="packed"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="40px"
        android:text="@string/title_encryption_setting_begin"
        android:textColor="@color/text_content_title"
        android:textSize="44px"
        android:textStyle="bold"
        app:flow_verticalGap="55px" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/changePwdBtn"
        android:layout_width="500px"
        android:layout_height="80px"
        android:text="@string/btn_encryption_setting_begin_change_pwd"
        android:textSize="30px"
        android:textStyle="bold" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/changeEmailBtn"
        android:layout_width="500px"
        android:layout_height="80px"
        android:text="@string/btn_encryption_setting_begin_change_email"
        android:textSize="30px"
        android:textStyle="bold" />

</androidx.constraintlayout.widget.ConstraintLayout>