<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_pop_sort"
    android:orientation="vertical"
    android:showDividers="middle"
    android:elevation="10px">

    <LinearLayout
        android:id="@+id/sortItemTimeAsc"
        style="@style/style_sort_pop_layout">

        <TextView
            style="@style/style_sort_pop_text"
            android:text="@string/sort_type_time_asc" />

        <ImageView style="@style/style_sort_pop_img" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/sortItemTimeDesc"
        style="@style/style_sort_pop_layout">

        <TextView
            style="@style/style_sort_pop_text"
            android:text="@string/sort_type_time_desc" />

        <ImageView style="@style/style_sort_pop_img" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sortItemNameAsc"
        style="@style/style_sort_pop_layout">

        <TextView
            style="@style/style_sort_pop_text"
            android:text="@string/sort_type_name_asc" />

        <ImageView style="@style/style_sort_pop_img" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sortItemNameDesc"
        style="@style/style_sort_pop_layout">

        <TextView
            style="@style/style_sort_pop_text"
            android:text="@string/sort_type_name_desc" />

        <ImageView style="@style/style_sort_pop_img" />
    </LinearLayout>
</LinearLayout>