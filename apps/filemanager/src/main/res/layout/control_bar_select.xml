<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/control_bar_height"
    xmlns:tools="http://schemas.android.com/tools"
    >


    <com.czur.uilib.choose.CZMultiStateCheckBox
        android:id="@+id/selAllCb"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginStart="50px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/selAllTv"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="30px"
        android:gravity="center_vertical"
        android:text="@string/str_select_all"
        android:textColor="@color/text_common"
        android:textSize="30px"
        app:layout_constraintLeft_toRightOf="@id/selAllCb" />

    <TextView
        android:id="@+id/selCountTv"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="30px"
        android:gravity="center_vertical"
        android:textColor="@color/text_common"
        android:textSize="30px"
        app:layout_constraintLeft_toRightOf="@id/selAllTv"
        tools:text="已选1项" />




    <LinearLayout
        android:id="@+id/actionIvLl"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="60px"
        android:divider="@drawable/divider_control_bar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:showDividers="middle"
        android:visibility="visible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/aiTransDownloadIv"
            style="@style/style_control_bar_iv"
            android:visibility="gone"
            android:src="@drawable/selector_download_ai_trans_on"
            app:float_tips="@string/float_tip_download_ai_trans" />

        <ImageView
            android:id="@+id/moveIv"
            style="@style/style_control_bar_iv"
            android:src="@drawable/file_icon_move_on"
            app:float_tips="@string/float_tip_move"/>

        <ImageView
            android:id="@+id/copyIv"
            style="@style/style_control_bar_iv"
            android:src="@drawable/file_icon_copy_on"
            app:float_tips="@string/float_tip_copy"/>

        <ImageView
            android:id="@+id/openMethodIv"
            style="@style/style_control_bar_iv"
            android:src="@drawable/icon_open_method_on"
            app:float_tips="@string/float_tip_open_method"/>

        <ImageView
            android:id="@+id/renameIv"
            style="@style/style_control_bar_iv"
            android:src="@drawable/icon_rename_on"
            app:float_tips="@string/float_tip_rename"/>

        <ImageView
            android:id="@+id/delSelIv"
            style="@style/style_control_bar_iv"
            android:src="@drawable/file_icon_delete_on"
            app:float_tips="@string/float_tip_del_file" />

        <ImageView
            android:id="@+id/selCancelIv"
            android:layout_width="60px"
            android:layout_height="60px"
            android:padding="21px"
            android:scaleType="fitCenter"
            android:layout_gravity="center"
            android:src="@drawable/file_icon_cancel"
            app:float_tips="@string/float_tip_cancel_choose" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>