<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <LinearLayout
        android:layout_width="600px"
        android:layout_height="600px"
        android:layout_gravity="center"
        android:background="@drawable/bg_dialog_input"
        android:orientation="vertical">


        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/encode_image"
            android:layout_width="320px"
            android:layout_height="320px"
            android:layout_gravity="center"
            android:layout_marginTop="100px"
            app:round="10px" />

        <TextView
            android:id="@+id/copyDialogBackHint"
            style="@style/tv_record_qrcode_guide" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="7px"
            android:text="@string/share_scan_timeout"
            android:textColor="@color/white"
            android:textSize="18px" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/bt_share_ok"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_gravity="center"
            android:layout_marginTop="10px"
            android:text="@string/share_ok"
            android:textAllCaps="false"
            android:textSize="18px"
            app:baselib_theme="dark" />


    </LinearLayout>
</FrameLayout>