<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:background="@color/color_local_pdfbar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/im_zoom_in"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="35px"
            android:src="@drawable/page_zoom_in_enable"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/im_zoom_out"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="35px"
            android:src="@drawable/page_zoom_out_enable"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/im_zoom_in"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/pageCurrentTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="24px"
            app:layout_constraintBaseline_toBaselineOf="@id/pageSeparateTv"
            app:layout_constraintRight_toLeftOf="@id/pageSeparateTv" />

        <TextView
            android:id="@+id/pageSeparateTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="80px"
            android:text="@string/str_page_separate"
            android:textColor="@color/white"
            android:textSize="24px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/im_page_up"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.helper.widget.Layer
            android:id="@+id/pageClickLayer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:constraint_referenced_ids="pageCurrentTv,pageSeparateTv,pageSumTv" />

        <TextView
            android:id="@+id/pageSumTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="24px"
            app:layout_constraintBaseline_toBaselineOf="@id/pageSeparateTv"
            app:layout_constraintLeft_toRightOf="@id/pageSeparateTv" />

        <ImageView
            android:id="@+id/im_page_down"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="35px"
            android:src="@drawable/page_down_enable"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/im_page_up"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="35px"
            android:src="@drawable/page_up_enable"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/im_page_down"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.czur.starry.device.baselib.widget.PDFView
        android:id="@+id/pdf_view"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:background="#F3F3F3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_bar"
        app:layout_constraintVertical_bias="0.0" />
</androidx.constraintlayout.widget.ConstraintLayout>