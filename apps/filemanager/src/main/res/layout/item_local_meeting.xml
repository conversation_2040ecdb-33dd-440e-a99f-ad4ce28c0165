<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="38px"
    android:paddingHorizontal="30px"
    android:paddingVertical="25px">

    <ImageView
        android:id="@+id/localMeetingItemSelIv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:src="@drawable/file_select"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <ImageView
        android:id="@+id/local_meeting_iv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:src="@drawable/ic_local_meeting"
        app:layout_constraintBottom_toBottomOf="@id/localMeetingItemSelIv"
        app:layout_constraintLeft_toLeftOf="@id/localMeetingItemSelIv"
        app:layout_constraintRight_toRightOf="@id/localMeetingItemSelIv"
        app:layout_constraintTop_toTopOf="@id/localMeetingItemSelIv" />

    <TextView
        android:id="@+id/localMeetingFileNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="30px"
        android:ellipsize="middle"
        android:lines="1"
        android:text="新会议录像6"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        android:maxWidth="400px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/localMeetingItemSelIv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/localMeetingTimeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="30px"
        android:lines="1"
        android:text="2019.10.29 22:10:00"
        android:textColor="@color/text_common_light"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/localMeetingFileNameTv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/localMeetingDurationTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="15px"
        android:ellipsize="end"
        android:lines="1"
        android:text="时长: 02:10:00"
        android:textColor="@color/text_common_light"
        android:textSize="20px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/localMeetingTimeTv"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/localMeetCheckIv"
        style="@style/style_select_mode_checkbox"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />




</androidx.constraintlayout.widget.ConstraintLayout>