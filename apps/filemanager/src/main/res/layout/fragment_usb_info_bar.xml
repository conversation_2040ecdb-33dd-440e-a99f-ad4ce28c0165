<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/usbInfoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/text_common_light"
        android:textSize="20px"
        app:layout_constraintBottom_toBottomOf="@id/placeHolderView"
        app:layout_constraintRight_toRightOf="@id/placeHolderView"
        app:layout_constraintTop_toTopOf="@id/placeHolderView"
        tools:text="已用3G, 共30G" />

    <View
        android:id="@+id/placeHolderView"
        style="@style/style_info_bar_view"
        android:visibility="invisible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>