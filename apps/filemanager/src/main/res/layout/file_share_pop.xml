<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_pop_sort"
    android:elevation="10px"
    android:orientation="vertical"
    android:paddingVertical="10px"
    android:showDividers="middle">

    <RelativeLayout
        android:id="@+id/firstItem"
        style="@style/style_share_pop_layout">

        <TextView
            style="@style/style_share_pop_text"
            android:text="@string/str_delete_time_item1" />

        <ImageView style="@style/style_share_pop_img" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/secondItem"
        style="@style/style_share_pop_layout">

        <TextView
            style="@style/style_share_pop_text"
            android:text="@string/str_delete_time_item2" />

        <ImageView style="@style/style_share_pop_img" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/thirdItem"
        style="@style/style_share_pop_layout">

        <TextView
            style="@style/style_share_pop_text"
            android:text="@string/str_delete_time_item3" />

        <ImageView style="@style/style_share_pop_img" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/fourthItem"
        style="@style/style_share_pop_layout"
        android:visibility="gone">

        <TextView
            style="@style/style_share_pop_text"
            android:text="@string/str_delete_time_item4" />

        <ImageView style="@style/style_share_pop_img" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/fiveItem"
        style="@style/style_share_pop_layout">

        <TextView
            style="@style/style_share_pop_text"
            android:text="@string/str_delete_time_item5" />

        <ImageView style="@style/style_share_pop_img" />
    </RelativeLayout>
</LinearLayout>