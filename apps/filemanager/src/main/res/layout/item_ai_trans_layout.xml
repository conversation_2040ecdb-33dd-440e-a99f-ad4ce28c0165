<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="38px"
    android:paddingHorizontal="30px"
    android:paddingVertical="25px">

    <ImageView
        android:id="@+id/aiTransItemSelIv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:src="@drawable/file_select"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/aiTransIv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:src="@drawable/ic_local_meeting"
        app:layout_constraintBottom_toBottomOf="@id/aiTransItemSelIv"
        app:layout_constraintLeft_toLeftOf="@id/aiTransItemSelIv"
        app:layout_constraintRight_toRightOf="@id/aiTransItemSelIv"
        app:layout_constraintTop_toTopOf="@id/aiTransItemSelIv" />

    <TextView
        android:id="@+id/aiTransFileNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:ellipsize="middle"
        android:gravity="center_vertical"
        android:lines="1"
        android:maxWidth="400px"
        android:text="新会议录像6"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/aiTransItemSelIv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/aiTransTimeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:gravity="center_vertical"
        android:lines="1"
        android:text="2019.10.29 22:10:00"
        android:textColor="@color/text_common_light"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/aiTransFileNameTv"
        app:layout_constraintTop_toTopOf="parent" />
    
    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/AITransCheckCb"
        style="@style/style_select_mode_checkbox"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/share_ai_trans_record_iv"
        style="@style/style_trans_btn_iv"
        android:layout_marginRight="120px"
        android:src="@drawable/ic_trans_share"
        app:float_tips="@string/float_tip_share_ai_trans"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/open_ai_trans_record_iv"
        style="@style/style_trans_btn_iv"
        android:layout_marginRight="60px"
        android:src="@drawable/ic_trans_preview"
        app:float_tips="@string/float_tip_open_ai_trans"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/isTranslatingTv"
        android:text="@string/ai_is_generating"
        android:textSize="24px"
        android:textColor="#3939394D"
        android:layout_marginRight="60px"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>