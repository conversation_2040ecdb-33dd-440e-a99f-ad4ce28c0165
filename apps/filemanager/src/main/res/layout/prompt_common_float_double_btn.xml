<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500px"
    android:layout_height="360px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    tools:background="#5879FC">

    <TextView
        android:id="@+id/promptDoubleBtnFloatTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:text="@string/dialog_normal_title_tips"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/promptContentLayout"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:layout_marginRight="40px"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/promptCheckBoxFloat"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/promptDoubleBtnFloatTitleTv"
        app:layout_constraintVertical_bias="0.5">

        <TextView
            android:id="@+id/promptDoubleBtnFloatContentTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="bold"
            android:lineSpacingExtra="10px"
            android:lineSpacingMultiplier="1"
            tools:text="您的账号已在其他设备登录, 请确认账号安全。" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/promptCheckBoxFloat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10px"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/promptDoubleBtnFloatBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">


        <com.czur.uilib.choose.CZImageCheckBox
            android:id="@+id/no_prompt_checkBox"
            android:layout_width="30px"
            android:layout_height="30px"
            android:layout_marginBottom="20px"
            android:layout_marginLeft="30px"
            app:checked="false"
            app:checkedImg="@drawable/file_icon_share_checked"
            app:unCheckedImg="@drawable/file_icon_share_unchecked"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/no_prompt_tips_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:ellipsize="end"
            android:lines="1"
            android:text="时长: 02:10:00"
            android:textColor="@color/white"
            android:textSize="24px"
            app:layout_constraintLeft_toRightOf="@id/no_prompt_checkBox"
            app:layout_constraintTop_toTopOf="@id/no_prompt_checkBox"
            app:layout_constraintBottom_toBottomOf="@id/no_prompt_checkBox"
            />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/promptDoubleBtnFloatBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30px"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/promptDoubleBtnFloatCancelBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            app:baselib_theme="dark"
            android:text="@string/dialog_normal_cancel"
            android:textSize="20px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/promptDoubleBtnFloatConfirmBtn" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/promptDoubleBtnFloatConfirmBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginLeft="30px"
            android:text="@string/dialog_normal_confirm"
            android:textSize="20px"
            android:textStyle="bold"
            app:baselib_theme="white2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/promptDoubleBtnFloatCancelBtn"
            app:layout_constraintRight_toRightOf="parent" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>