package com.czur.starry.device.file.manager

import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.FileApp
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.MusicEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.utils.getStrRes
import com.czur.starry.device.file.utils.toArrayList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 *
 */
object RecordFileAccess : FileAccess {
    private const val TAG = "RecordFileAccess"
    private val rootDir: File = File(FileApp.getApp().filesDir, "cache")
    override val isDeleting: Boolean
        get() = false


    override fun getRootEntity() = FileEntity(
        rootDir.absolutePath,
        FileType.ROOT, R.string.file_record.getStrRes(),
        0, "", AccessType.RECORD, 0L
    )

    /**
     * 获取指定路径下的文件
     */
    override suspend fun getItemsByTarget(
        targetDir: FileEntity,
        start: Int,
        count: Int,
        filterMode: FileAccess.FilterMode,
        sortType: FileAccess.SortType
    ): List<FileEntity> = withContext(Dispatchers.IO) {
        // TODO 分页
        val fileEntities = mutableListOf<FileEntity>()

        fileEntities
    }

    /**
     * 删除指定文件
     * @param delFiles 要删除的文件集合
     * @return 如果删除成功, 则返回true, 删除失败返回False
     */
    override suspend fun delFiles(delFiles: List<FileEntity>, reqCloudLock: Boolean) =
        FileAccess.Result.SUCCESS

    /**
     * 会议路径不支持创建文件夹
     */
    override suspend fun createNewFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String? = null

    override suspend fun getNextFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String =
        withContext(Dispatchers.IO) {
            var dirName = folderName
            dirName
        }


    override suspend fun renameFile(
        target: FileEntity,
        newName: String,
        targetDir: FileEntity
    ): FileAccess.Result = withContext(Dispatchers.IO) {

        FileAccess.Result.SUCCESS

    }

    /**
     * 将FileEntity 转换为MusicEntity
     * @param target 要转换的文件
     * @return 因为涉及到Activity之间的传输, 所以使用ArrayList
     */
    override fun getMusicList(target: List<FileEntity>): ArrayList<MusicEntity> {
        val sdf = SimpleDateFormat("yyyy.MM.dd HH:mm:ss", Locale.ROOT)
        val musicEntity = target.map {
            val musicEntity = MusicEntity(it.name)
            musicEntity.musicFilePath = it.absPath
            val lastModify = it.lastModifyTime
            musicEntity.time = sdf.format(lastModify)
            musicEntity
        }
        return musicEntity.toArrayList()
    }

}