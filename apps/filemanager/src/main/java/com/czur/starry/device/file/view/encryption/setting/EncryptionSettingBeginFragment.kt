package com.czur.starry.device.file.view.encryption.setting

import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.file.databinding.FragmentEncryptionSettingBeginBinding

/**
 * Created by 陈丰尧 on 2024/12/12
 */
private const val TAG = "EncryptionSettingBeginFragment"

class EncryptionSettingBeginFragment :
    AbsPwdSettingFragment<FragmentEncryptionSettingBeginBinding>() {
    override fun FragmentEncryptionSettingBeginBinding.initBindingViews() {
        changePwdBtn.setOnDebounceClickListener {
            logTagV(TAG, "修改密码")
            EncryptionSettingBeginFragmentDirections.actionSettingBeginFragmentToVerifyEmailFragment().nav()
        }

        changeEmailBtn.setOnDebounceClickListener {
            logTagV(TAG, "修改邮箱")
            EncryptionSettingBeginFragmentDirections.actionSettingBeginFragmentToResetEmailFragment().nav()
        }
    }
}