package com.czur.starry.device.file.manager.transfer.inter

import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.TAG_KEY_ROOT_ABS_PATH
import com.czur.starry.device.file.filelib.AccessType
import java.io.File

/**
 * Created by 陈丰尧 on 3/25/21
 */
private const val TAG = "CheckSpace"

interface ICheckSpace {
    /**
     * 检测存储空间是否够用,耗时操作
     */
    suspend fun hasEnoughSpace(
        src: FileEntity,
        srcSize: Long,
        destDir: FileEntity,
        isMove: Boolean
    ): Boolean
}

/**
 * 检查本地空间是否够用
 */
class LocalCheckSpace : ICheckSpace {
    override suspend fun hasEnoughSpace(
        src: FileEntity,
        srcSize: Long,
        destDir: FileEntity,
        isMove: Boolean
    ): Boolean {
        if (isMove) {
            if (src.belongTo == AccessType.LOCAL && destDir.belongTo == AccessType.LOCAL) {
                logTagV(TAG, "本地到本地的移动, 不需要检查容量")
                return true
            }

            if (src.belongTo == AccessType.USB && destDir.belongTo == AccessType.USB) {
                if (src.getTag<String>(TAG_KEY_ROOT_ABS_PATH) == destDir.getTag<String>(
                        TAG_KEY_ROOT_ABS_PATH
                    )
                ) {
                    logTagV(TAG, "同一个U盘, 不需要检查容量")
                    return true
                }
            }
        }

        // 需要检查容量
        val file = File(destDir.absPath)
        val freeSize = file.freeSpace // 获取容量
        logTagV(TAG, "剩余容量:${freeSize.toSizeStr()},需要容量:${srcSize.toSizeStr()}")
        return freeSize > srcSize
    }

}