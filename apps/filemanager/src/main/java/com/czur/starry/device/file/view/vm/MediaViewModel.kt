package com.czur.starry.device.file.view.vm

import android.app.Application
import android.media.AudioManager
import android.os.SystemClock
import androidx.annotation.OptIn
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.AudioUtil.Companion.VOLUME_NORMAL
import com.czur.starry.device.baselib.utils.AudioUtil.Companion.VOLUME_QUITE
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.db.FileDataBase
import com.czur.starry.device.file.utils.CZURLoadControl
import com.czur.starry.device.file.utils.isAudio
import com.czur.starry.device.file.utils.isVideo
import com.czur.starry.device.file.utils.toDuration
import com.github.promeg.pinyinhelper.Pinyin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.max
import kotlin.math.min

/**
 * Created by 陈丰尧 on 2021/9/22
 * 使用Google的ExoPlayer代替MediaPlayer
 * MediaPlayer进度条不准确的问题
 *
 * 之后需要优化的点:
 * 1. ExoPlayer可以一次性添加多个媒体文件, 现在没有使用
 * 2. ExoPlayer可以解析音乐文件的元数据
 */
private const val TAG = "MediaViewModel"
private const val UPDATE_POS_INTERVAL = 150L // 每150ms 更新一次进度信息
private const val CORRECT_OFFSET = UPDATE_POS_INTERVAL / 10 // 进度回退时的修正偏移量

class MediaViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val UI_HIDE_DELAY = 3 * ONE_SECOND    // 3s 没有操作隐藏

        private val DEF_ROTATION_MODE = MediaRotationMode.ROTATION_0
        private const val DEF_REPEAT_MODE = Player.REPEAT_MODE_ALL    // 默认循环模式为全部循环
    }

    /**
     * 媒体旋转模式
     */
    enum class MediaRotationMode(val value: Float) {
        ROTATION_0(0F),
        ROTATION_90(90F),
        ROTATION_180(180F),
        ROTATION_270(270F)
    }

    private val mediaPlayer: ExoPlayer by lazy { initMediaPlayer() }


    // 媒体文件的状态
    val mediaStatusLive: LiveData<MediaStatus> = DifferentLiveData(MediaStatus.PLAY)
    private var mediaStatus: MediaStatus by LiveDataDelegate(
        mediaStatusLive as MutableLiveData<MediaStatus>,
        MediaStatus.PAUSE
    )

    // 文件名
    private val _fileNameFlow = MutableStateFlow("")
    val mediaTitleFlow = _fileNameFlow.asStateFlow()

    // 艺术家
    private val _artistFlow = MutableStateFlow("")
    val artistFlow = _artistFlow.asStateFlow()

    private val _fileTypeFlow = MutableStateFlow<FileType?>(null)
    val fileTypeFlow = _fileTypeFlow.asStateFlow()
    val fileType: FileType?
        get() = fileTypeFlow.value


    // 持续时间
    val durationLive: LiveData<Long> = DifferentLiveData(0L)
    var duration: Long by LiveDataDelegate(durationLive as MutableLiveData)
        private set

    val isLoadingLive: LiveData<Boolean> = DifferentLiveData(true)
    var isLoading: Boolean by LiveDataDelegate(isLoadingLive)
        private set

    /**
     * 当前播放的进度位置
     */
    private val _mediaPosFlow = MutableStateFlow(0L)
    val mediaPosFlow = _mediaPosFlow.asStateFlow()
    private var mediaPos: Long
        get() = mediaPosFlow.value
        set(value) {
            _mediaPosFlow.value = value
        }

    // 进度条的显示
    val mediaPosStrFlow = mediaPosFlow.map { it.toDuration() }.flowOn(Dispatchers.Default)

    val forceCloseLive: LiveData<Boolean> = DifferentLiveData(false)
    private var forceClose: Boolean by LiveDataDelegate(forceCloseLive)

    // 旋转角度
    val rotationModeLive = DifferentLiveData(DEF_ROTATION_MODE)
    private var rotateMode: MediaRotationMode by LiveDataDelegate(rotationModeLive)

    // 循环模式
    private val _repeatModeFlow = MutableStateFlow(DEF_REPEAT_MODE)
    val repeatModeFlow = _repeatModeFlow.asStateFlow()

    var autoFinish = false

    // 音频焦点改变监听
    private var afChangeListener: AudioManager.OnAudioFocusChangeListener =
        AudioManager.OnAudioFocusChangeListener {
            when (it) {
                AudioManager.AUDIOFOCUS_GAIN -> mediaPlayer.volume = VOLUME_NORMAL
                AudioManager.AUDIOFOCUS_LOSS -> pause() // 永久失去焦点
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT,
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK
                    -> mediaPlayer.volume = VOLUME_QUITE
            }
            logTagV(TAG, "音频焦点改变${audioUtil.getAudioFocusName(it)}")
        }

    private val audioUtil = AudioUtil()

    // 已经释放过了
    private var hasRelease = false

    val noNextLive = MutableLiveData<Long>()
    private val userOperatorTimeFlow = MutableStateFlow(System.currentTimeMillis())
    private val userOperatorFlow = MutableStateFlow(true)

    // 是否显示UI
    val showUIFlow = combine(userOperatorFlow, fileTypeFlow) { userOperator, fileType ->
        (userOperator || fileType == FileType.AUDIO).also {
            logTagV(TAG, "显示UI:$it , userOperator:$userOperator, fileType:$fileType")
        }
    }.flowOn(Dispatchers.Default)

    private val mediaIndexFlow = MutableStateFlow(-1)
    private val mediaCountFlow = MutableStateFlow(0)
    val nextEnableFlow = combine(mediaIndexFlow, mediaCountFlow) { mediaIndex, mediaCount ->
        mediaIndex < mediaCount - 1 && mediaIndex >= 0
    }
    val preEnableFlow = combine(mediaIndexFlow, mediaCountFlow) { mediaIndex, mediaCount ->
        mediaIndex in 1..<mediaCount
    }

    private var needRePrepare = false
    private var userSeekTime = 0L

    init {
        launch {
            while (isActive) {
                delay(UPDATE_POS_INTERVAL)  // 每150ms更新一次进度
                if (mediaPlayer.isPlaying) {
                    // 更新音频进度
                    val amendmentPos =
                        if (SystemClock.elapsedRealtime() - userSeekTime < UPDATE_POS_INTERVAL) {
                            // 用户主动操作了, 不需要修正
                            mediaPlayer.contentPosition
                        } else {
                            // 没有用户操作, 需要修正
                            val lastPos = mediaPos + CORRECT_OFFSET
                            val current = mediaPlayer.contentPosition
                            if (lastPos > current && (lastPos - current) < (UPDATE_POS_INTERVAL * 2)) {
                                // 进度回退了, 修正
                                logTagW(
                                    TAG,
                                    "进度回退了, 修正: $lastPos - ${mediaPlayer.contentPosition}"
                                )
                                lastPos
                            } else {
                                mediaPlayer.contentPosition
                            }
                        }
                    mediaPos = min(amendmentPos.also {
                        logTagV(TAG, "当前位置:$it")
                    }, duration)

                }
            }
        }

        launch {
            audioUtil.requestAudioFocus(focusListener = afChangeListener)
        }

        launch(Dispatchers.Default) {
            userOperatorTimeFlow.onEach {
                userOperatorFlow.value = true
            }.collectLatest {
                delay(UI_HIDE_DELAY)
                userOperatorFlow.value = false
            }
        }
    }

    /**
     * 初始化MediaPlayer
     */
    @OptIn(UnstableApi::class)
    private fun initMediaPlayer(): ExoPlayer {

        val exoPlayer = ExoPlayer.Builder(appContext)
            .setLoadControl(CZURLoadControl())
            .build()

        exoPlayer.repeatMode = DEF_REPEAT_MODE  // 默认循环模式

        exoPlayer.playWhenReady = true

        exoPlayer.addListener(object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                if (isPlaying) {
                    mediaPlayer.volume = VOLUME_NORMAL
                    isLoading = false
                }
            }

            override fun onRepeatModeChanged(repeatMode: Int) {
                super.onRepeatModeChanged(repeatMode)
                logTagV(TAG, "循环模式改变, repeatMode:$repeatMode")
                _repeatModeFlow.value = repeatMode
            }

            /**
             * 媒体切换时调用
             */
            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                super.onMediaItemTransition(mediaItem, reason)
                logTagV(TAG, "媒体切换, reason:$reason")
                userSeekTime = 0L
                mediaPos = 0
                if (mediaItem == null) {
                    mediaIndexFlow.value = -1
                } else {
                    mediaIndexFlow.value = (mediaPlayer.currentMediaItemIndex)
                }

                _fileTypeFlow.value = mediaItem?.localConfiguration?.tag as? FileType
                _artistFlow.value = ""
                duration = mediaPlayer.duration.takeIf { it > 0 } ?: 0L
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                userSeekTime = 0L
                logTagD(TAG, "媒体播放失败,自动下一曲", tr = error)
                getApplication<Application>().toast(R.string.toast_play_fail)
                if (!isCurrentFileExist()) {
                    forceClose = true
                }

                if (mediaPlayer.currentMediaItemIndex < mediaPlayer.mediaItemCount - 1) {
                    logTagV(TAG, "自动下一曲")
                    switchNext()
                    mediaPlayer.prepare()
                } else {
                    logTagD(TAG, "自动暂停")
                    pause()
                    needRePrepare = true
                }
            }

            override fun onMediaMetadataChanged(mediaMetadata: MediaMetadata) {
                super.onMediaMetadataChanged(mediaMetadata)
                rotationModeLive.takeIf { it.value != MediaRotationMode.ROTATION_0 }?.value =
                    MediaRotationMode.ROTATION_0
                logTagV(TAG, "媒体元数据改变")
                val title = mediaMetadata.title?.toString()
                if (!title.isNullOrEmpty()) {
                    _fileNameFlow.value = mediaMetadata.title?.toString() ?: ""
                } else {
                    mediaPlayer.currentMediaItem?.let {
                        val filePath = it.localConfiguration?.uri?.toString() ?: ""
                        _fileNameFlow.value =
                            filePath.substringAfterLast("/").substringBeforeLast(".")
                    }
                }
                val artist = mediaMetadata.artist?.toString()
                if (!artist.isNullOrEmpty()) {
                    _artistFlow.value = artist
                }

            }

            override fun onAvailableCommandsChanged(availableCommands: Player.Commands) {
                super.onAvailableCommandsChanged(availableCommands)
                if (availableCommands.contains(Player.COMMAND_GET_CURRENT_MEDIA_ITEM)) {
                    // 可以获取媒体文件信息, 在第一次加载的时候会回调
                    duration = (mediaPlayer.duration.takeIf { it > 0 } ?: 0L)
                }
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                super.onPlaybackStateChanged(playbackState)
                when (playbackState) {
                    Player.STATE_IDLE -> {
                        logTagV(TAG, "媒体状态改变: STATE_IDLE")
                        isLoading = false
                    }

                    Player.STATE_BUFFERING -> {
                        logTagV(TAG, "媒体状态改变: STATE_BUFFERING")
                        isLoading = true
                    }

                    Player.STATE_READY -> {
                        logTagV(TAG, "媒体状态改变: STATE_READY")
                        duration = (mediaPlayer.duration.takeIf { it > 0 } ?: 0L)
                        isLoading = false
                    }

                    Player.STATE_ENDED -> {
                        logTagV(TAG, "媒体状态改变: STATE_ENDED")
                        isLoading = false
                        onUserOperation()
                        noNextLive.value = System.currentTimeMillis()
                    }
                }
            }
        })

        return exoPlayer
    }

    /**
     * 用户有操作时调用
     */
    fun onUserOperation() {
        userOperatorTimeFlow.value = System.currentTimeMillis()
    }


    suspend fun setDataSource(folder: File, targetName: String?, sortType: FileAccess.SortType) {
        val files = withContext(Dispatchers.IO) {
            val uploadFiles = FileDataBase.instance.uploadDao().getCurrentAllUploadFiles()
            val list = folder.listFiles { file: File ->
                val hasUpload = uploadFiles.any {
                    it.fileNewName == file.name
                }
                (file.isAudio() || file.isVideo()) && (!hasUpload)
            }?.toList() ?: emptyList()
            // 排序
            when (sortType) {
                FileAccess.SortType.TYPE_NAME_ASC -> list.sortedBy {
                    Pinyin.toPinyin(it.name, "").lowercase()
                }

                FileAccess.SortType.TYPE_NAME_DESC -> list.sortedByDescending {
                    Pinyin.toPinyin(
                        it.name,
                        ""
                    ).lowercase()
                }

                FileAccess.SortType.TYPE_TIME_ASC -> list.sortedBy { it.lastModified() }
                FileAccess.SortType.TYPE_TIME_DESC -> list.sortedByDescending { it.lastModified() }
            }
        }
        if (files.isEmpty()) {
            return
        }
        var targetIndex = -1
        val mediaItems = files.mapIndexed { index, it ->
            if (it.name == targetName) {
                targetIndex = index
                _fileTypeFlow.value = if (it.isVideo()) FileType.VIDEO else FileType.AUDIO
            }
            val mediaMetadata = MediaMetadata.Builder().setTitle(it.name).build()
            MediaItem.Builder()
                .setTag(if (it.isVideo()) FileType.VIDEO else FileType.AUDIO)
                .setUri(it.absolutePath)
                .setMediaMetadata(mediaMetadata)
                .build()
        }

        mediaPlayer.setMediaItems(mediaItems)
        mediaCountFlow.value = mediaItems.size
        if (targetIndex > 0) {
            mediaPlayer.seekTo(targetIndex, 0)// 切换到指定MediaItem
        } else {
            _fileTypeFlow.value = mediaItems[0].localConfiguration?.tag as? FileType
        }

    }

    fun setDataSource(uri: String) {
        val mediaItem = MediaItem.Builder()
            .setTag(FileType.VIDEO)
            .setUri(uri)
            .build()
        mediaPlayer.setMediaItem(mediaItem)
    }

    /**
     * 设置数据源,单个本地文件
     * @param file 文件
     */
    fun setDataSource(file: File) {
        val fileType = if (file.isVideo()) FileType.VIDEO else FileType.AUDIO
        val mediaMetadata = MediaMetadata.Builder().setTitle(file.name).build()
        val mediaItem = MediaItem.Builder()
            .setTag(fileType)
            .setUri(file.absolutePath)
            .setMediaMetadata(mediaMetadata)
            .build()
        mediaPlayer.setMediaItem(mediaItem)
        _fileTypeFlow.value = fileType
        mediaPlayer.repeatMode = Player.REPEAT_MODE_ONE // 单曲循环
    }

    /**
     * 改变旋转角度
     */
    fun changeRotationMode() {
        rotateMode = when (rotateMode) {
            MediaRotationMode.ROTATION_0 -> MediaRotationMode.ROTATION_90
            MediaRotationMode.ROTATION_90 -> MediaRotationMode.ROTATION_180
            MediaRotationMode.ROTATION_180 -> MediaRotationMode.ROTATION_270
            MediaRotationMode.ROTATION_270 -> MediaRotationMode.ROTATION_0
        }
    }

    /**
     * 改变循环模式
     */
    fun changeRepeatMode() {
        val mode = when (_repeatModeFlow.value) {
            Player.REPEAT_MODE_ALL -> Player.REPEAT_MODE_ONE
            Player.REPEAT_MODE_ONE -> Player.REPEAT_MODE_ALL
            else -> Player.REPEAT_MODE_OFF
        }
        mediaPlayer.repeatMode = mode
    }

    /**
     * 判断文件是否存在
     * 如果是云盘文件,直接就会判断为有
     * 如果是党建课程的视频, 虽然是网络的, 但是会走到本地文件的判断, 则会永远返回false
     *  所以播放失败就会自动退出
     */
    private fun isCurrentFileExist(): Boolean {
        val filePath =
            mediaPlayer.currentMediaItem?.localConfiguration?.uri?.toString() ?: return false
        if (filePath.startsWith("http")) {
            return true
        }
        return File(filePath).exists()
    }

    fun setSurface(playerView: PlayerView) {
        playerView.player = mediaPlayer
    }


    /**
     * 准备并播放当前音乐
     */
    fun prepareAndPlayCurrent() {
        mediaPlayer.prepare()
    }

    /**
     * 播放
     */
    fun play() {
        mediaStatus = MediaStatus.PLAY
        if (needRePrepare) {
            logTagV(TAG, "需要重新prepare")
            mediaPlayer.prepare()
            needRePrepare = false
        } else {
            mediaPlayer.play()
        }
    }

    /**
     * 暂停
     */
    fun pause() {
        mediaStatus = MediaStatus.PAUSE
        mediaPlayer.pause()
    }

    /**
     * 定位到指定位置
     */
    fun seekTo(pos: Int) {
        userSeekTime = SystemClock.elapsedRealtime()    // 记录用户操作时间
        mediaPlayer.seekTo(pos.toLong())
    }


    fun releaseResource() {
        // 释放mediaPlayer资源
        if (hasRelease) return

        logTagD(TAG, "释放MediaPlayer")
        hasRelease = true
        viewModelScope.cancel() // 取消掉所有协程
        audioUtil.abandonAudioFocus(afChangeListener)
        mediaPlayer.release()
        Runtime.getRuntime().gc()
    }

    /**
     * 切换到下一曲
     */
    fun switchNext() {
        if (mediaPlayer.hasNextMediaItem()) {
            mediaPlayer.seekToNextMediaItem()
        } else {
            mediaPlayer.seekTo(0, 0)
        }
        if (needRePrepare) {
            logTagV(TAG, "需要重新prepare")
            mediaPlayer.prepare()
            needRePrepare = false
        }
    }

    /**
     * 切换到上一曲
     */
    fun switchPre() {
        if (mediaPlayer.hasPreviousMediaItem()) {
            mediaPlayer.seekToPreviousMediaItem()
        } else {
            mediaPlayer.seekTo(mediaPlayer.mediaItemCount - 1, 0)
        }
        if (needRePrepare) {
            logTagV(TAG, "需要重新prepare")
            mediaPlayer.prepare()
            needRePrepare = false
        }
    }

    fun setExternalRepeatMode() {
        _repeatModeFlow.value = Player.REPEAT_MODE_OFF
    }

    enum class MediaStatus {
        PLAY,
        PAUSE
    }
}