package com.czur.starry.device.file.bean

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry

/**
 * Created by 陈丰尧 on 2023/2/16
 * 预制文件
 */
data class PresetFile(
    val fileName: String,
    val showName: String,
    val doCopy: <PERSON>olean,
)

val presetFileList = listOf(
    PresetFile("a.pdf", "产品入门指南.pdf", true),
    PresetFile("a_os.pdf", "StarryHub Quick User Guide.pdf", true),
    PresetFile("b_os.pdf", "StarryHub クイックスタートガイド.pdf", true),
    PresetFile("c_os.pdf", "Краткое руководство пользователя StarryHub.pdf", true),
    PresetFile("d_os.pdf", "Guida rapida di StarryHub.pdf", true),
    PresetFile("e_os.pdf", "StarryHub ‒ Kurzanleitung.pdf", true),
    PresetFile("f_os.pdf", "StarryHub 빠른 사용자 가이드.pdf", true),
    PresetFile("g_os.pdf", "Guide d_introduction du produit.pdf", true),
    PresetFile("h_os.pdf", "Guía de inicio del producto.pdf", true),
    PresetFile(
        "b.jpeg",
        "产品图示01.jpeg",
        Constants.versionIndustry != VersionIndustry.PARTY_BUILDING
    ),
    PresetFile(
        "c.jpeg",
        "产品图示02.jpeg",
        Constants.versionIndustry != VersionIndustry.PARTY_BUILDING
    ),
    PresetFile(
        "d.jpeg",
        "产品图示03.jpeg",
        Constants.versionIndustry != VersionIndustry.PARTY_BUILDING
    ),
    PresetFile(
        "e.jpeg",
        "产品图示04.jpeg",
        Constants.versionIndustry != VersionIndustry.PARTY_BUILDING
    ),
    PresetFile(
        "f.jpeg",
        "产品图示05.jpeg",
        Constants.versionIndustry != VersionIndustry.PARTY_BUILDING
    ),
)