package com.czur.starry.device.file.server.component.servlet

import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse
import io.netty.handler.codec.http.HttpContent

/**
 * Created by 陈丰尧 on 2024/12/16
 */
abstract class HttpServlet {
    abstract suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse)

    open suspend fun onContentReceive(
        httpContent: HttpContent,
        response: CZHttpResponse,
        isLast: Boolean,
    ) {
    }

    open suspend fun onExceptionCaught(e: Throwable) {}
}