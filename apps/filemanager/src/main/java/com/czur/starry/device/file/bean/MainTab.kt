package com.czur.starry.device.file.bean

import androidx.annotation.DrawableRes
import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.file.R
import com.czur.starry.device.file.view.FolderFragment
import com.czur.starry.device.file.view.aitranscription.AITransRecordFragment
import com.czur.starry.device.file.view.encryption.LocalEncryptionFragment
import com.czur.starry.device.file.view.record.MeetingRecordFragment


/**
 * Created by 陈丰尧 on 2023/2/8
 * 主页的标签
 */
data class MainTab(
    val folderName: String,
    @DrawableRes val iconNormal: Int,
    val tabKey: Int,
    val fragment: CZBaseFragment,
) {
    var additionalData: Any? = null    // 扩展数据
}

object MainTabKey {
    const val SHOW_TYPE_LOCAL = 1
    const val SHOW_TYPE_ENCRYPTION = 2  // 加密文件
    const val SHOW_TYPE_USB = 3
    const val SHOW_TYPE_LOCAL_DOWNLOAD = 4
    const val SHOW_TYPE_LOCAL_SCREEN_SHOT = 5 //拍照/截图
    const val SHOW_TYPE_LOCAL_MEETING = 6
    const val SHOW_TYPE_LOCAL_CZUR_SHARE = 7
    const val SHOW_TYPE_NET_TRANS_RECORD = 8 //AI会议纪要
}

private val mainTabNormal
    get() = listOf(
        MainTab(
            getString(R.string.file_folders_local),
            R.drawable.icon_local_on,
            MainTabKey.SHOW_TYPE_LOCAL,
            FolderFragment.getInstance(MainTabKey.SHOW_TYPE_LOCAL)
        ),
        MainTab(
            getString(R.string.file_folders_encrypt),
            R.drawable.icon_local_encryption,
            MainTabKey.SHOW_TYPE_ENCRYPTION,
            LocalEncryptionFragment()
        ),
        MainTab(
            getString(R.string.file_folders_local_download),
            R.drawable.icon_local_download_on,
            MainTabKey.SHOW_TYPE_LOCAL_DOWNLOAD,
            FolderFragment.getInstance(
                MainTabKey.SHOW_TYPE_LOCAL_DOWNLOAD
            )
        ),
        MainTab(
            getString(R.string.file_folders_czur),
            R.drawable.icon_local_share,
            MainTabKey.SHOW_TYPE_LOCAL_CZUR_SHARE,
            FolderFragment.getInstance(MainTabKey.SHOW_TYPE_LOCAL_CZUR_SHARE)
        ),
        MainTab(
            getString(R.string.file_folders_meeting_record),
            R.drawable.icon_local_meeting_on,
            MainTabKey.SHOW_TYPE_LOCAL_MEETING,
            MeetingRecordFragment()
        ),
        MainTab(
            getString(R.string.file_folders_ai_trans_record),
            R.drawable.icon_ai_trans,
            MainTabKey.SHOW_TYPE_NET_TRANS_RECORD,
            AITransRecordFragment()
        ),
        MainTab(
            getString(R.string.file_folders_local_screenshot),
            R.drawable.icon_local_screenshot_on,
            MainTabKey.SHOW_TYPE_LOCAL_SCREEN_SHOT,
            FolderFragment.getInstance(MainTabKey.SHOW_TYPE_LOCAL_SCREEN_SHOT)
        ),
    )

/**
 * 创建主页的标签
 */
fun createMainTabList() = mainTabNormal.toMutableList()
