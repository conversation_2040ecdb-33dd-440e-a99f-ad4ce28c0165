package com.czur.starry.device.file.server.upload

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.ComponentRepository
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.component.servlet.post.FileUploadServlet
import com.czur.starry.device.file.server.msg.ResultMessage
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse
import com.czur.starry.device.file.server.util.ResponseUtil
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelInboundHandlerAdapter
import io.netty.handler.codec.http.HttpContent
import io.netty.handler.codec.http.HttpRequest
import io.netty.handler.codec.http.LastHttpContent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

/**
 *  author : WangHao
 *  time   :2023/10/10
 */

private const val TAG = "ServerHandler"

class UploadHandler(
    private val componentRepository: ComponentRepository
) : ChannelInboundHandlerAdapter() {

    private var runningServlet: HttpServlet? = null
    private val scope = CoroutineScope(Job())


    override fun channelInactive(ctx: ChannelHandlerContext) {
        super.channelInactive(ctx)
        endFileUpload()
    }


    @Throws(Exception::class)
    override fun channelRead(ctx: ChannelHandlerContext, msg: Any) {
        try {

            if (msg is HttpRequest) {
                val czRequest = CZHttpRequest(msg)
                val servlet = componentRepository.createHttpServlet(czRequest.path).also {
                    runningServlet = it // 记录当前运行的Servlet
                }

                if (servlet != null) {
                    val response = CZHttpResponse()
                    runBlocking {
                        servlet.onConnect(czRequest, response)
                    }
                    // 返回消息
                    sendResponse(ctx, response)
                } else {
                    super.channelRead(ctx, msg)   // 继续传递
                }
            } else if (msg is HttpContent) {
                val response = CZHttpResponse()
                runningServlet?.let {
                    runBlocking {
                        it.onContentReceive(msg, response, msg is LastHttpContent)
                    }
                }
                sendResponse(ctx, response)
                msg.release()
            } else {
                super.channelRead(ctx, msg)   // 继续传递
            }

        } catch (ex: Throwable) {
            logTagD(TAG, "Starry Server Error", tr = ex)
            ResponseUtil.response(
                ctx,
                ResultMessage(FileShareConstant.UNKNOWN_ERROR, "Starry Server Error")
            )
        }

    }

    override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable?) {
        if (ctx.channel().isActive) {
            logTagW(TAG, "秒传错误exceptionCaught:", tr = cause)
            scope.launch {
                runningServlet?.onExceptionCaught(cause ?: Throwable("unknown error"))
            }
            ctx.channel().close()
        } else {
            super.exceptionCaught(ctx, cause)
        }
    }

    private fun sendResponse(ctx: ChannelHandlerContext, response: CZHttpResponse) {
        response.content?.let {
            ResponseUtil.response(
                ctx,
                response
            )
        }
    }


    //去除此正在上传标记
    private fun endFileUpload() {
        logTagD(TAG, "===endFileUpload=")
        (runningServlet as? FileUploadServlet)?.let {
            runBlocking {
                it.endFileUploadOrDownload()
            }
        }
    }


}