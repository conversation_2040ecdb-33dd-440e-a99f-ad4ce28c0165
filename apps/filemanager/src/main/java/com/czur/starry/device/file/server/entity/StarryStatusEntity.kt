package com.czur.starry.device.file.server.entity

import com.czur.starry.device.file.server.common.FileShareConstant

/**
 * Created by 陈丰尧 on 2025/1/7
 */
data class StarryStatusEntity(
    val enableShareFile: <PERSON>olean,       // 服务器是否关闭
    val enableTransMeeting: <PERSON><PERSON><PERSON>,    // 是否允许传输会议文件
    val cpuHigh: <PERSON>olean,               // CPU是否过高
    val diskSpaceNotEnough: Boolean,    // 磁盘空间是否不足
    val starryApiVersion: Int = FileShareConstant.API_VERSION, // Starry API版本
)