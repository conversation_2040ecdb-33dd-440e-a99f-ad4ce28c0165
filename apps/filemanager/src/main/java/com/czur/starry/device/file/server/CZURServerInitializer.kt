package com.czur.starry.device.file.server

import com.czur.starry.device.file.server.component.ComponentRepository
import com.czur.starry.device.file.server.download.NotFoundHandler
import com.czur.starry.device.file.server.msg.ServerHandler
import com.czur.starry.device.file.server.upload.UploadHandler
import io.netty.channel.ChannelInitializer
import io.netty.channel.ChannelPipeline
import io.netty.channel.socket.SocketChannel
import io.netty.handler.codec.http.HttpObjectAggregator
import io.netty.handler.codec.http.HttpRequestDecoder
import io.netty.handler.codec.http.HttpResponseEncoder
import io.netty.handler.stream.ChunkedWriteHandler
import io.netty.handler.timeout.ReadTimeoutHandler


/**
 *  author : WangHao
 *  time   :2023/10/08
 */


class CZURServerInitializer(private val componentRepository: ComponentRepository) :
    ChannelInitializer<SocketChannel>() {
    override fun initChannel(ch: SocketChannel) {
        val p: ChannelPipeline = ch.pipeline()
        p.addLast(HttpRequestDecoder())
        p.addLast(HttpResponseEncoder())
        //添加ChunkedWriteHandler以处理作为ChunkedInput传入的数据，支持异步写大型数据
        //流，而又不会导致大量的内存消耗。
        p.addLast(ChunkedWriteHandler())
        p.addLast(ReadTimeoutHandler(90))
        p.addLast(ServerHandler())
        p.addLast(UploadHandler(componentRepository))
        p.addLast(NotFoundHandler())
    }
}