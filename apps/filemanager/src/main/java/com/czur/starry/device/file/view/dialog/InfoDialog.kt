package com.czur.starry.device.file.view.dialog

import android.text.TextUtils
import android.view.View
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.CustomDialog
import com.czur.starry.device.file.base.FileApp
import com.czur.starry.device.file.databinding.DialogInfoBinding

/**
 * Created by 陈丰尧 on 12/30/20
 */
class InfoDialog : CustomDialog<DialogInfoBinding>() {
    var info = ""
    var singleLine:Boolean = false

    override fun getLayout() = R.layout.dialog_info

    override fun createSubViewBinding(container: View): DialogInfoBinding {
        return DialogInfoBinding.bind(container)
    }

    override fun initView(view: View) {
        binding?.dialogInfoTv?.text = info
        if (singleLine){
            binding?.dialogInfoTv?.setLines(1)
            binding?.dialogInfoTv?.ellipsize = TextUtils.TruncateAt.MIDDLE
        }
    }

    class Builder : CustomDialogBuilder<InfoDialog>() {

        override fun setDialog() = InfoDialog()

        open fun setInfo(info: String, singleLine: Boolean = false): Builder {
            dialog.info = info
            dialog.singleLine = singleLine
            return this
        }

        open fun setInfo(infoId: Int): Builder {
            val info = FileApp.getApp().resources.getString(infoId)
            return setInfo(info)
        }

    }
}

fun showHintDialog(info: String) {
    InfoDialog.Builder()
        .setInfo(info)
        .setTitle(R.string.dialog_info_title)
        .hideCancelBtn()
        .buildAndShow()
}