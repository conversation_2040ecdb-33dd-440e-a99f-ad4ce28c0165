package com.czur.starry.device.file.view.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.fragment.app.commit
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.RefreshAble
import com.czur.starry.device.file.bean.MainTab
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_CZUR_SHARE
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_DOWNLOAD
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_MEETING
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL_SCREEN_SHOT
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_NET_TRANS_RECORD
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_USB
import com.czur.starry.device.file.bean.createMainTabList
import com.czur.starry.device.file.databinding.ActivityFileMainPageBinding
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileAITransLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileDownloadLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileLocalLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileMeetingLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileScreenLive
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileShareLive
import com.czur.starry.device.file.manager.FileShowInfoManager
import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.manager.LocalInfoManager
import com.czur.starry.device.file.manager.usb.OnUnmountListener
import com.czur.starry.device.file.manager.usb.UsbHelper
import com.czur.starry.device.file.server.ServerService
import com.czur.starry.device.file.service.FileWatcherService
import com.czur.starry.device.file.service.ScheduleCleanService
import com.czur.starry.device.file.view.FilePadFragment.Companion.index
import com.czur.starry.device.file.view.FolderFragment
import com.czur.starry.device.file.view.czurshare.vm.FileShareViwModel
import com.czur.starry.device.file.view.dialog.InfoDialog
import com.czur.starry.device.file.view.vm.MainViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean

class FileMainPageActivity : CZViewBindingAty<ActivityFileMainPageBinding>() {
    companion object {
        private const val TAG = "FileMainPageActivity"

        val changeSelect: MutableLiveData<Boolean> = MutableLiveData()

        //保存正在读写操作的U盘路径
        var usbOperationPath = mutableListOf<String>()

    }


    private val atomicRunning = AtomicBoolean(false)
    private var isRunning: Boolean
        get() = atomicRunning.get()
        set(value) = atomicRunning.set(value)

    var adapter: FolderAdapter? = null
    private var lastSelect = -1

    private val context = this

    private val myTabList = createMainTabList()

    private val shareVM: FileShareViwModel by viewModels()
    private val mainViewModel: MainViewModel by viewModels()
    private val loadingDialog by lazy { LoadingDialog() }


    override fun AtyParams.initAtyParams() {
        lifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                LocalInfoManager.bindLifecycle(this@FileMainPageActivity)
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                launch {
                    logTagV(TAG, "onStop")
                    LocalEncryptionManager.onEncryptionBackground()
                }
                LocalInfoManager.unbindLifecycle()
            }
        }
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        launch {
            while (adapter == null) {
                delay(100)
            }
            preIntent.getStringExtra(BOOT_KEY_PAGE_MENU_NAME)?.let { tag ->
                val selectedIndex = if (tag == getString(R.string.voice_command_file_udisk) && myTabList.size > 7) {
                    myTabList.size - 1
                } else {
                    // 避免不必要的类型转换
                    myTabList.indexOfFirst { it.folderName.contains(tag) }
                        .takeIf { it != -1 } ?: 0
                }

                adapter?.let { adapter ->
                    with(adapter) {
                        setSelectPosition(selectedIndex)
                        showSelect(selectedIndex)
                        notifyDataSetChanged()
                    }
                }
            }
            delay(1000)
            preIntent?.getStringExtra(BOOT_KEY_PAGE_MENU_NAVIGATE)?.let {
                logTagD(TAG, "navigate:$it")
                mainViewModel.voiceOpenFileName.value =  it
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlePreIntent(intent)
    }



    override fun onDestroy() {
        // 清理所有的Fragment
        supportFragmentManager.commit(true) {
            supportFragmentManager.fragments.forEach {
                remove(it)
            }
        }

        super.onDestroy()
        NoticeHandler.clearAll()
        index = 0
        this.unregisterReceiver(usbReceiver)
        usbOperationPath.clear()
    }


    override fun ActivityFileMainPageBinding.initBindingViews() {
        val lastFragments = supportFragmentManager.fragments
        if (lastFragments.isNotEmpty()) {
            supportFragmentManager.commit(true) {
                logTagD(TAG, "remove LastFragment: ${lastFragments.size}")
                lastFragments.forEach {
                    remove(it)
                }
            }
        }

        launch {
            // 加载全部的排序规则
            FileShowInfoManager.loadAllSortTypes()
        }

        rvFolder.layoutManager = LinearLayoutManager(context)
        adapter = FolderAdapter(myTabList)
        rvFolder.adapter = adapter
        showSelect(0)

        val filterUsb = IntentFilter().apply {
            addAction(Intent.ACTION_MEDIA_UNMOUNTED)
            addAction(Intent.ACTION_MEDIA_MOUNTED)
            addDataScheme("file")
        }
        registerReceiver(usbReceiver, filterUsb)

        launch { checkUsbList() }

        startFileWatchService()

        startScheduleCleanService()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        unReadFileLocalLive.observe(this) {
            if (!it || mainViewModel.currentTabKey != SHOW_TYPE_LOCAL) {
                adapter?.updateUnReadStatus(SHOW_TYPE_LOCAL, it)
            }
        }
        unReadFileDownloadLive.observe(this) {
            if (!it || mainViewModel.currentTabKey != SHOW_TYPE_LOCAL_DOWNLOAD) {
                adapter?.updateUnReadStatus(SHOW_TYPE_LOCAL_DOWNLOAD, it)
            }
        }
        unReadFileShareLive.observe(this) {
            if (!it || mainViewModel.currentTabKey != SHOW_TYPE_LOCAL_CZUR_SHARE) {
                adapter?.updateUnReadStatus(SHOW_TYPE_LOCAL_CZUR_SHARE, it)
            }
        }
        unReadFileScreenLive.observe(this) {
            if (!it || mainViewModel.currentTabKey != SHOW_TYPE_LOCAL_SCREEN_SHOT) {
                adapter?.updateUnReadStatus(SHOW_TYPE_LOCAL_SCREEN_SHOT, it)
            }
        }
        unReadFileMeetingLive.observe(this) {
            if (!it || mainViewModel.currentTabKey != SHOW_TYPE_LOCAL_MEETING) {
                adapter?.updateUnReadStatus(SHOW_TYPE_LOCAL_MEETING, it)
            }
        }
        unReadFileAITransLive.observe(this) {
            if (!it || mainViewModel.currentTabKey != SHOW_TYPE_NET_TRANS_RECORD) {
                adapter?.updateUnReadStatus(SHOW_TYPE_NET_TRANS_RECORD, it)
            }
        }

        repeatCollectOnResume(LocalInfoManager.localSpaceFlow) {
            val infoStr = it.toStingNew(R.string.str_local_space_info_new)
            binding.localInfoContentTv.text = infoStr
            binding.localSpaceProgressBar.progress =
                (it.usage.toDouble() / it.usageLimit.toDouble() * 100).toInt()

        }

        repeatOnResume {
            shareVM.refreshQRCodeInfo() // 刷新二维码
        }

        repeatOnResume {
            withContext(Dispatchers.Default) {
                delay(ONE_SECOND)
                ServerService.start(this@FileMainPageActivity)
            }
        }

        repeatCollectOnResume(mainViewModel.showEncryptionPageFlow) {
            if (!it) {
                // 退出该标签页
                logTagV(TAG, "没有显示加密页面")
                LocalEncryptionManager.onEncryptionBackground()
            }
        }

    }

    private fun getShowTab(select: Int): MainTab {
        //云文档隐藏掉该逻辑也删除
        return myTabList[select]
    }


    /**
     * 重新选择当前页面
     */
    fun reSelCurrentFragment() {
        if (lastSelect < 0) return
        logTagI(TAG, "重新刷新当前页面")
        showSelect(lastSelect, true)
    }

    private fun showSelect(select: Int, force: Boolean = false) {
        if (select == lastSelect && !force) {
            logTagV(TAG, "选择没有改变, 不切换页面")
            return
        }
        val selTab = getShowTab(select)
        val fragment = selTab.fragment
        setCurrentFragmentId(selTab.tabKey)
        supportFragmentManager.commit(true) {
            myTabList.forEach {
                if (it.fragment.isAdded) {
                    hide(it.fragment)
                }
            }
            if (!fragment.isAdded) {
                add(R.id.container, fragment, select.toString())
            }

            show(fragment)
        }
        if (fragment.isAdded && fragment is RefreshAble) {
            fragment.refresh()
        }
        //切换菜单取消选择模式
        if (select != lastSelect) {
            changeSelect.postValue(true)
        }
        lastSelect = select
    }

    /**
     * 当前fragment刷新 删除红点未读
     */
    private fun setCurrentFragmentId(tabKey: Int) {
        mainViewModel.setCurrentFragment(tabKey)
    }


    inner class FolderAdapter(var data: MutableList<MainTab>) :
        RecyclerView.Adapter<BaseVH>() {
        var select = 0

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
            return BaseVH(R.layout.item_file_folder, parent).apply {
                itemView.setOnHoverListener(OnHoverListener(this))
            }
        }

        override fun getItemCount(): Int {
            return myTabList.size
        }

        fun setSelectPosition(position: Int) {
            select = position
        }

        /**
         * 更新菜单的未读状态
         */
        // 展示红点的子项
        private var showRedPointSubMenuKey = mutableSetOf<Int>()
        fun updateUnReadStatus(tabKey: Int, remind: Boolean) {
            if (remind) {
                showRedPointSubMenuKey.add(tabKey)
            } else {
                showRedPointSubMenuKey.remove(tabKey)
            }
            val position = findItemPositionByTabKey(tabKey)
            if (position >= 0) {
                notifyItemChanged(position)
            }
        }

        private fun findItemPositionByTabKey(tabKey: Int): Int {
            val index = data.map {
                it.tabKey
            }.indexOf(tabKey)
            return index
        }

        override fun onBindViewHolder(holder: BaseVH, position: Int) {
            val folderName = data[position].folderName
            val folderKey = data[position].tabKey
            holder.setText(folderName, R.id.tv_folder)
            holder.setTips(folderName, R.id.tv_folder)
            var textSize = holder.context.resources.getDimension(R.dimen.text_size_menu_sel)
            if (getItemViewType(position) == 1) {
                textSize -= 4   // 长文字,缩小TextSize
            }
            holder.setImgResource(data[position].iconNormal, R.id.iv_icon)
            holder.visible(select == position, R.id.viewChecked)
            holder.visible(false, R.id.viewCheckedCursor)
            //更新红点
            holder.visible(folderKey in showRedPointSubMenuKey, R.id.tabPoint)
            //显示分割线
            if (folderKey == SHOW_TYPE_LOCAL_MEETING ||
                (folderKey == SHOW_TYPE_USB && (data.getOrNull(position - 1)?.tabKey
                    ?: SHOW_TYPE_USB) != SHOW_TYPE_USB)
            ) {
                holder.visible(true, R.id.dividerLine)
            } else {
                holder.visible(false, R.id.dividerLine)
            }
            //显示U盘卸载icon
            holder.visible(folderKey == SHOW_TYPE_USB, R.id.im_usb_unmount)
            val unmountView = holder.getView<ImageView>(R.id.im_usb_unmount)

            unmountView.setOnDebounceClickListener {
                CoroutineScope(Dispatchers.Main).launch {
                    try {
                        if (usbOperationPath.contains(data[position].additionalData.toString())) {
                            showTipDialog()
                        } else {
                            loadingDialog.show()
                            UsbHelper.setOnUnmountListener(object : OnUnmountListener {
                                override fun onUnmountSuccessful() {
                                    CoroutineScope(Dispatchers.Main).launch {
                                        toast(R.string.file_usb_unmount)
                                        loadingDialog.dismissImmediate()
                                    }
                                }
                            })
                            UsbHelper.doUnmount(data[position].additionalData.toString())
                        }

                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

            }

            holder.itemView.setOnClickListener {
                select = position
                showSelect(select)
                notifyDataSetChanged()
            }

        }


        inner class OnHoverListener(private val holder: BaseVH) : View.OnHoverListener {
            override fun onHover(v: View?, event: MotionEvent?): Boolean {
                if (event == null || holder.bindingAdapterPosition == select || holder.bindingAdapterPosition < 0) {
                    return false
                }
                when (event.action) {
                    MotionEvent.ACTION_HOVER_MOVE, MotionEvent.ACTION_HOVER_ENTER
                        -> holder.visible(true, R.id.viewCheckedCursor)

                    else -> holder.visible(false, R.id.viewCheckedCursor)
                }
                return true
            }

        }
    }

    //首次启动检测USB
    private suspend fun checkUsbList() = withContext(Dispatchers.IO) {
        UsbHelper.getusb().forEach {
            listenerUsb(it.first)
        }
    }


    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            logTagD("VoiceRecognitionReceiver", "=====${intent?.action}" + intent?.data?.path)
            when (intent?.action) {
                Intent.ACTION_MEDIA_UNMOUNTED -> {
                    launch {
                        intent.data?.path?.let { removeUsb(it) }
                    }

                }

                Intent.ACTION_MEDIA_MOUNTED -> {
                    launch {
                        intent.data?.path?.let {
                            UsbHelper.updateMountTime(it)
                            listenerUsb(it)
                        }
                    }
                }

            }
        }

    }

    override fun onStart() {
        super.onStart()
        if (lastSelect > 0) {
            myTabList[lastSelect].fragment.onHiddenChanged(false)
        }
    }

    override fun onStop() {
        super.onStop()
        if (lastSelect > 0) {
            if (lastSelect < myTabList.size) {
                getShowTab(lastSelect).fragment.onHiddenChanged(true)
            } else {
                logTagW(TAG, "lastSelect:${lastSelect},fragmentSize:${myTabList.size}")
            }
        }
    }

    private fun addResource(usbTab: MainTab) {
        myTabList.add(usbTab)
        supportFragmentManager.commit(true) {
            add(R.id.container, usbTab.fragment, index.toString())
            hide(usbTab.fragment)
        }
    }

    suspend fun listenerUsb(path: String) = withContext(Dispatchers.IO) {
        while (isRunning) {
            delay(2000)
        }
        isRunning = true

        // 检查路径是否已存在，如果存在则直接返回
        if (myTabList.any { it.additionalData == path }) {
            isRunning = false
            return@withContext
        }

        val label = UsbHelper.getLabel(path)
        if (label == "null") {
            isRunning = false
            return@withContext
        }
        //初始化路径
        val extData = bundleOf(UsbHelper.KEY_USB_PATH to path, UsbHelper.KEY_FROM_USB to true)
        val usbFragment = FolderFragment.getInstance(SHOW_TYPE_USB, extData)
        val usbTab = MainTab(
            label,
            R.drawable.icon_usb_on,
            SHOW_TYPE_USB,
            usbFragment
        ).apply {
            additionalData = path
        }
        addResource(usbTab)

        val index = adapter!!.itemCount
        withContext(Dispatchers.Main) {
            adapter?.notifyItemInserted(index)
        }
        isRunning = false

    }

    suspend fun removeUsb(path: String) = withContext(Dispatchers.Main) {
        while (isRunning) {
            delay(2000)
        }
        isRunning = true

        val usbTab = myTabList.find {
            it.additionalData == path
        }

        if (usbTab == null) {
            isRunning = false
            logTagW(TAG, "removeUsb: usbTab is null,path:$path")
            return@withContext
        }

        val usbFragment = usbTab.fragment
        val position = myTabList.map { it.fragment }.indexOf(usbFragment)
        // 如果移除的是当前选中的Fragment, 则需要返回首页
        val needMoveToZero = position == lastSelect

        myTabList.remove(usbTab)
        supportFragmentManager.commit(true) { remove(usbTab.fragment) }

        // 如果移除的页面,在当前选中的页面上面, 则索引需要减一
        if (lastSelect > position) {
            lastSelect--
            adapter?.setSelectPosition(lastSelect)
        }
        adapter?.notifyDataSetChanged()
        if (needMoveToZero) {
            adapter?.let {
                it.setSelectPosition(0)
                showSelect(0)
                it.notifyDataSetChanged()
            }
        }

        isRunning = false
    }

    private fun showTipDialog() {
        InfoDialog.Builder()
            .setInfo(R.string.file_usb_unmount_failed)
            .setTitle(R.string.dialog_info_title)
            .hideCancelBtn()
            .buildAndShow()
    }

    //启动文件监听未读服务
    private fun startFileWatchService() {
        val intent = Intent(this, FileWatcherService::class.java)
        startService(intent)
    }

    private fun startScheduleCleanService() {
        val intent = Intent(this, ScheduleCleanService::class.java)
        intent.putExtra("command", 1)
        startService(intent)
    }


}