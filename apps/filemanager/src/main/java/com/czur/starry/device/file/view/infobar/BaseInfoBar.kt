package com.czur.starry.device.file.view.infobar

import androidx.viewbinding.ViewBinding
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.show

/**
 * Created by 陈丰尧 on 2022/1/14
 */
abstract class BaseInfoBar<VB : ViewBinding> : CZViewBindingFragment<VB>() {
    abstract fun updateInfo(info: String)

    fun changeVisibility(visibility: Boolean) {
        view?.let {
            if (visibility) {
                it.show()
            } else {
                it.invisible()
            }
        }
    }
}