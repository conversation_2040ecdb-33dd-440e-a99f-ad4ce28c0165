package com.czur.starry.device.file.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * Created by 陈丰尧 on 12/29/20
 */
class SpacesFileGrid(var space: Int) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        outRect.bottom = space.px
    }

    private val Int.px: Int
        get() {
            return this
        }
}