package com.czur.starry.device.file.view.dialog

import android.graphics.Bitmap
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.file.R
import com.czur.starry.device.file.adapter.CopyFileDialogAdapter
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.TAG_KEY_LOCAL_TYPE
import com.czur.starry.device.file.databinding.DialogCopyFileBinding
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_ENCRYPTION
import com.czur.starry.device.file.filelib.EXT_TYPE_NORMAL
import com.czur.starry.device.file.manager.EncryptFileAccess
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileManager
import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.manager.USBFileAccess
import com.czur.starry.device.file.view.encryption.LocalEncryptionViewModel
import com.czur.starry.device.file.widget.OnRecyclerItemClickListener
import com.czur.starry.device.file.widget.createSortPopup
import com.wanglu.lib.WPopupDirection
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import java.util.Stack

/**
 * Created by 陈丰尧 on 12/31/20
 */
private const val TAG = "CopyFileDialog"

class CopyFileDialog : CZVBFloatingFragment<DialogCopyFileBinding>() {
    private var path = PathStack<FileEntity>()
    lateinit var adapter: CopyFileDialogAdapter

    lateinit var selectEntity: List<FileEntity> //选中的文件
    private var isMove: Boolean = false  // 是否是移动模式

    private val selPaths: Set<String> by lazy {
        selectEntity.map { it.parentPath }.toSet()
    }

    var listener: ((FileEntity, Boolean) -> Unit?)? = null

    private val sortPopup by lazy {
        createSortPopup(requireContext(), ::onSortItemClick)
    }

    private val sortTypeLive = DifferentLiveData(FileAccess.SortType.TYPE_TIME_DESC)
    private var sortType by LiveDataDelegate(sortTypeLive, FileAccess.SortType.TYPE_TIME_DESC)

    private val encryptionViewModel: LocalEncryptionViewModel by activityViewModels()

    private var bgBitmapCache: Deferred<Bitmap>? = null

    override fun FloatingFragmentParams.initFloatingParams() {
        this.floatingBgMode = FloatingBgMode.Dark
        this.keyBackDismiss = true
        this.outSideDismiss = false
    }

    override fun DialogCopyFileBinding.initBindingViews() {
        onPathChange()

        dialogCopyRv.layoutManager = LinearLayoutManager(requireContext())
        adapter = CopyFileDialogAdapter(selectEntity)
        dialogCopyRv.adapter = adapter


        copyDialogCloseIv.setOnClickListener {
            dismiss()
        }
        dialogConfirmBtn.setOnClickListener {
            listener?.let {
                // 如果当前是在根路径,那么就不回调Listener
                // 直接dismissDialog
                if (path.isNotEmpty()) {
                    it(path.peek(), isMove)
                }
            }
            dismiss()
        }
        copyDialogSortIv.setOnClickListener {
            // 显示排序Popup
            sortPopup.showAtDirectionByViewAlignRight(it, WPopupDirection.BOTTOM, 20)
        }

        dialogCancelBtn.setOnClickListener {
            dismiss()
        }

        sortTypeLive.observe(viewLifecycleOwner) {
            logTagV(TAG, "排序规则改变, 重新加载数据")
            loadData()
        }

        dialogCopyRv.addOnItemTouchListener(object : OnRecyclerItemClickListener(dialogCopyRv) {
            override fun onItemClick(vh: RecyclerView.ViewHolder, childId: Int) {
                val position = vh.bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val entity = adapter.data[position]
                    if (entity.isDir()) {
                        // 如果点击的是文件夹
                        path.push(entity)
                        loadData()
                    }
                }
            }
        })

        copyDialogBackIv.setOnDebounceClickListener(500) {
            back2UpperFolder()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        bgBitmapCache = viewLifecycleScope.async { takeScreenShot()!!.blur(10, 8) }

        loadData()

        // 监控是否需要加密的变化
        repeatCollectOnResume(encryptionViewModel.needPwdFlow) {
            if (it) {
                if (path.isEmpty()) return@repeatCollectOnResume
                val currentFileEntity = path.peek()
                val belongTo = currentFileEntity.belongTo
                // 获取根的类型
                val fileType = path[0].getTagWithDefault(TAG_KEY_LOCAL_TYPE, EXT_TYPE_NORMAL)
                val fileAccess = FileManager.createFileAccess(belongTo, fileType)
                if (fileAccess is EncryptFileAccess) {
                    showPwdFloating()
                }
            }
        }
    }

    /**
     * 显示密码输入框
     */
    private suspend fun showPwdFloating() {
        logTagD(TAG, "showPwdFloating")
        sortPopup.dismiss()
        val bgImg = bgBitmapCache!!.await()
        EncryptionPwdDialog(bgImg).apply {
            setOnDismissListener {
                if (encryptionViewModel.needPwd) {
                    logTagV(TAG, "密码输入框消失, 但是还需要密码")
                    path.clear()
                    loadData()
                }
            }
            show()
        }
    }


    /**
     * 当文件夹发生变化时,更新Dialog的UI
     */
    fun onPathChange() {
        if (path.isEmpty()) {
            binding.copyDialogTitle.text =
                if (!isMove) getString(R.string.str_dialog_copy_title) else
                    getString(R.string.str_dialog_move_title)
            binding.dialogConfirmBtn.text = if (!isMove) getString(R.string.str_dialog_copy) else
                getString(R.string.str_dialog_move)
            binding.copyDialogTitle.gravity = Gravity.LEFT
            binding.dialogCopyBtnBar.visibility = View.GONE
            binding.copyDialogBackIv.visibility = View.GONE
            binding.copyDialogSortIv.gone()

        } else {
            binding.copyDialogTitle.text = path.peek().name
            binding.copyDialogTitle.gravity = Gravity.CENTER_HORIZONTAL
            binding.dialogCopyBtnBar.visibility = View.VISIBLE
            binding.copyDialogBackIv.visibility = View.VISIBLE
            binding.copyDialogSortIv.show()
            checkCopyBtn()
        }

    }

    /**
     * 返回上层文件夹
     */
    private fun back2UpperFolder() {
        path.pop()
        loadData()
    }

    /**
     * 加载当前文件夹数据
     */
    private fun loadData() {
        launch {
            if (path.isEmpty()) {
                // 获取全部根FileEntity
                adapter.data = FileManager.roots
                    .filter {
                        when (it.belongTo) {
                            AccessType.RECORD -> false   // 不能移动到 录音 目录
                            AccessType.LOCAL_MEETING -> false
                            AccessType.LOCAL -> when (it.getTag<Int>(TAG_KEY_LOCAL_TYPE)) {
                                EXT_TYPE_LOCAL_ENCRYPTION -> LocalEncryptionManager.hasEncryptionInfo()
                                else -> true
                            }

                            else -> true
                        }
                    }
            } else {
                val currentFileEntity = path.peek()
                val belongTo = currentFileEntity.belongTo
                // 获取根的类型
                val fileType = path[0].getTagWithDefault(TAG_KEY_LOCAL_TYPE, EXT_TYPE_NORMAL)
                val fileEntity = FileManager.createFileAccess(belongTo, fileType)

                //数据加载完成前不可复制移动
                binding.dialogConfirmBtn.isEnabled = false
                binding.copyDialogBackIv.isClickable = false

                val fileEntities = try {
                    if (belongTo == AccessType.USB) USBFileAccess.isCopyFlag = true
                    fileEntity.getItemsByTarget(currentFileEntity, sortType = sortType)
                } catch (exp: Exception) {
                    logTagE(TAG, "获取文件失败", tr = exp)
                    emptyList()
                }

                if (fileEntity is EncryptFileAccess && encryptionViewModel.needPwd) {
                    showPwdFloating()
                }
                adapter.data = fileEntities

                binding.copyDialogBackIv.isClickable = true
                checkCopyBtn()
            }
        }
    }

    /**
     * 检查复制按钮的状态
     *  如果当前文件夹,就是选中的文件夹,那么复制按钮应该是不可用的
     *      因为这是没有意义的
     */
    private fun checkCopyBtn() {
        binding.dialogConfirmBtn.isEnabled =
            path.isNotEmpty() && path.peek().absPathWithSuffix !in selPaths
    }

    /**
     * 当sortTypeItem被点击
     */
    private fun onSortItemClick(sortType: FileAccess.SortType) {
        this.sortType = sortType
    }

    /**
     * 自定义栈
     * 用来监听栈中数量的改变
     */
    inner class PathStack<E> : Stack<E>() {
        override fun push(item: E): E {
            val e = super.push(item)
            onPathChange()
            return e
        }

        override fun pop(): E {
            val e = super.pop()
            onPathChange()
            return e
        }

        override fun clear() {
            super.clear()
            onPathChange()
        }
    }


    class Builder(selectEntity: List<FileEntity>, isMove: Boolean = false) {
        private val dialog = CopyFileDialog()

        init {
            dialog.selectEntity = selectEntity
            dialog.isMove = isMove
        }

        /**
         * 设置确定按钮的回调
         */
        fun setOnConfirmListener(listener: (path: FileEntity, moveMode: Boolean) -> Unit): Builder {
            dialog.listener = listener
            return this
        }

        fun build(): CopyFileDialog {
            return dialog
        }

        fun buildAndShow(): CopyFileDialog {
            dialog.show()
            return build()
        }

    }

}