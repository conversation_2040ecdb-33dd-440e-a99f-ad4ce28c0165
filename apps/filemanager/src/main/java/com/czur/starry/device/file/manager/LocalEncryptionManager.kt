package com.czur.starry.device.file.manager

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.extension.RandomStrType
import com.czur.czurutils.extension.nextString
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.ONE_DAY
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.isEmail
import com.czur.starry.device.file.db.FileDataBase
import com.czur.starry.device.file.db.entity.EncryptionStrategy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.random.Random
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

/**
 * Created by 陈丰尧 on 2024/12/10
 */
object LocalEncryptionManager {
    private const val TAG = "LocalEncryptionManager"
    const val EMAIL_VERIFY_CODE_LENGTH = 6
    const val PWD_LENGTH = 6
    const val ERROR_COUNT_LIMIT = 3 // 24小时内错误次数限制

    // 于洋 25.1.8 飞书反馈，修改错误锁定时间为12小时
    private val ERROR_LOCK_DURATION = 12.hours // 错误锁定时间
    private val ERROR_LOCK_CHECK_DURATION = 12.hours // 错误锁定检查时间
    private const val ENCRYPT_EMAIL_MASK = "***"

    private var refreshLockTimeJob: Job? = null

    private val scope = CoroutineScope(Dispatchers.Main)

    private val Context.ds: DataStore<Preferences> by preferencesDataStore(
        name = "encryption",
        corruptionHandler = createDefCorruptionHandler("encryption")
    )
    private val encryptionDs by lazy {
        globalAppCtx.ds
    }

    // 数据库
    private val encryptionDao by lazy {
        FileDataBase.instance.localEncryptionDao()
    }

    private val PWD_KEY = stringPreferencesKey("pwd")
    private val STRATEGY_KEY = stringPreferencesKey("strategy")
    private val EMAIL_KEY = stringPreferencesKey("email")

    // 锁定时间(结束时间)
    private val LOCK_TIME_KEY = longPreferencesKey("lockTime")

    // 邮箱
    val encryptionEmailFlow: Flow<String> by lazy {
        encryptionDs.data.map {
            it[EMAIL_KEY] ?: ""
        }
    }

    // 密码
    private val encryptionPwdFlow: Flow<String> by lazy {
        encryptionDs.data.map {
            it[PWD_KEY] ?: ""
        }.onEach {
            logTagV(TAG, "encryptionPwdFlow: $it")
        }
    }

    // 策略
    val encryptionStrategyFlow: Flow<EncryptionStrategy> by lazy {
        encryptionDs.data.map {
            it[STRATEGY_KEY] ?: EncryptionStrategy.EVERY_TIME.name
        }.map {
            EncryptionStrategy.valueOf(it)
        }
    }

    // 锁定时间
    private val encryptionLockTimeFlow: Flow<Long> by lazy {
        encryptionDs.data.map {
            it[LOCK_TIME_KEY] ?: 0
        }
    }

    // 是否可以检查密码
    val lockInputFlow =
        encryptionLockTimeFlow.map {
            it > System.currentTimeMillis()
        }
            .distinctUntilChanged()

    private val _needPwdFlow = MutableStateFlow(true)
    val needPwdFlow = _needPwdFlow.asStateFlow()

    val hasEncryptionInfoFlow
        get() = encryptionPwdFlow.map { it.isNotEmpty() }


    private val screenLockReceiver: CZPowerManager.LockScreenReceiver by lazy(
        LazyThreadSafetyMode.NONE
    ) {
        CZPowerManager.createOneLockScreenReceiver()
    }

    init {
        screenLockReceiver.register(globalAppCtx) { lock ->
            if (lock) {
                onScreenOff()
            }
        }

        scope.launch {
            CZURAtyManager.backgroundFlow.collect {
                onEncryptionBackground()
            }
        }
    }

    /**
     * 屏幕息屏
     */
    private fun onScreenOff() {
        scope.launch {
            logTagV(TAG, "onScreenOff")
            if (encryptionStrategyFlow.first() == EncryptionStrategy.SCREEN) {
                changeNeedPwd(true, "屏幕息屏")
            }
        }
    }

    /**
     * 应用进入后台
     */
    suspend fun onEncryptionBackground() {
        logTagV(TAG, "onEncryptionBackground")
        if (encryptionStrategyFlow.first() == EncryptionStrategy.EVERY_TIME) {
            changeNeedPwd(true, "应用进入后台")
        }
    }

    private fun changeNeedPwd(needPwd: Boolean, reason: String) {
        logTagV(TAG, "changeNeedPwd: $needPwd, reason: $reason")
        _needPwdFlow.value = needPwd
    }

    /**
     * 获取邮箱
     */
    suspend fun getEmail(): String {
        return encryptionEmailFlow.first()
    }

    /**
     * 加密邮箱,首字符 和 尾字符正常显示，中间以代替。
     *  如：<EMAIL> 则显示为： y***<EMAIL>
     * @param email 邮箱
     */
    fun encryptEmail(email: String): String {
        val emailInfoArray = email.split("@")
        if (emailInfoArray.size != 2) {
            return email
        }
        val emailName = emailInfoArray[0]
        val emailDomain = emailInfoArray[1]
        val emailNameFirst = emailName.firstOrNull()?.toString() ?: ""
        val emailNameLast = emailName.lastOrNull()?.toString() ?: ""
        return "${emailNameFirst}${ENCRYPT_EMAIL_MASK}${emailNameLast}@${emailDomain}"
    }

    /**
     * 是否有加密信息
     */
    suspend fun hasEncryptionInfo(): Boolean {
        return hasEncryptionInfoFlow.first()
    }

    /**
     * 更新邮箱
     */
    suspend fun updateEncryptionEmail(email: String) {
        logTagV(TAG, "updateEncryptionEmail: $email")
        encryptionDs.edit {
            it[EMAIL_KEY] = email
        }
        clearAllError() // 清空错误记录
        changeNeedPwd(true, "用户修改邮箱")
    }

    /**
     * 更新加密信息
     */
    suspend fun updateEncryptionStrategy(strategy: EncryptionStrategy) {
        encryptionDs.edit {
            it[STRATEGY_KEY] = strategy.name
        }
    }

    suspend fun checkPwd(pwd: String): Boolean {
        if (pwd.isEmail()) return false
        val checkPwd = encryptionPwdFlow.first() == pwd
        return checkPwd
    }

    /**
     * 记录用户输入结果
     */
    suspend fun saveInputRes(pwdOK: Boolean) {
        if (pwdOK) {
            changeNeedPwd(false, "用户输入正确")
            clearAllError()   // 清空错误记录
        } else {
            changeNeedPwd(true, "用户输入错误")
            encryptionDao.saveError()  // 记录错误
        }
        // 更新错误次数
        val errorCount = getErrorCount()
        if (errorCount >= ERROR_COUNT_LIMIT) {
            // 锁定
            logTagI(TAG, "错误次数超过限制, 锁定")
            val currentTime = System.currentTimeMillis()
            refreshLockTimeJob?.cancel()
            encryptionDs.edit {
                it[LOCK_TIME_KEY] =
                    currentTime + ERROR_LOCK_DURATION.inWholeMilliseconds
            }
            refreshLockTimeJob = scope.launch {
                delay(ERROR_LOCK_DURATION)
                val lockTime = getLockTime()
                if (System.currentTimeMillis() >= lockTime) {
                    logTagD(TAG, "自动计时结束, 清空所有错误记录")
                    clearAllError()
                } else if (lockTime - System.currentTimeMillis() < ONE_MIN) {
                    logTagW(TAG, "自动计时结束, 1分钟后清空所有错误记录")
                    delay(max(1, lockTime - System.currentTimeMillis()))
                    clearAllError()
                }
            }
        }
    }

    /**
     * 清空所有错误记录
     */
    private suspend fun clearAllError() {
        logTagD(TAG, "清除所有错误记录")
        encryptionDao.deleteAllError()
        clearLockTime()
    }

    suspend fun clearLockTime() {
        refreshLockTimeJob?.cancel()
        encryptionDs.edit {
            it[LOCK_TIME_KEY] = 0L // 清空锁定时间
        }
    }

    /**
     * 获取锁定到期时间
     */
    suspend fun getLockTime(): Long {
        return encryptionDs.data.map {
            it[LOCK_TIME_KEY] ?: 0L
        }.first()
    }

    /**
     * 刷新错误次数
     */
    suspend fun getErrorCount(): Int {
        val errorCount =
            encryptionDao.getErrorCountFromTime(System.currentTimeMillis() - ERROR_LOCK_CHECK_DURATION.inWholeMilliseconds)
        return errorCount.also {
            logTagV(TAG, "getErrorCount:${it}")
        }
    }

    /**
     * 更新密码
     */
    suspend fun updatePwd(pwd: String) {
        encryptionDs.edit {
            it[PWD_KEY] = pwd
        }
        clearAllError()
        changeNeedPwd(true, "用户修改密码")
    }

    /**
     * 创建邮箱验证码
     */
    fun createEmailVerificationCode(): String {
        return Random.nextString(EMAIL_VERIFY_CODE_LENGTH, RandomStrType.NUMBER).also {
            logTagV(TAG, "createEmailVerificationCode: $it")
        }
    }

    suspend fun createEncryption(userSetPwd: String, email: String) {
        logTagV(TAG, "createEncryption: $userSetPwd, $email")
        encryptionDs.edit {
            it[PWD_KEY] = userSetPwd
            it[EMAIL_KEY] = email
            it[STRATEGY_KEY] = EncryptionStrategy.EVERY_TIME.name
        }
        changeNeedPwd(false, "用户设置密码")
    }
}