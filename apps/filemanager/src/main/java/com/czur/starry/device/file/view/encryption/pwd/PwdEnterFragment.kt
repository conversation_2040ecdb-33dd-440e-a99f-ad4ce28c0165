package com.czur.starry.device.file.view.encryption.pwd

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.base.v2.fragment.startActivity
import com.czur.starry.device.baselib.utils.basic.no
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.MainTabKey
import com.czur.starry.device.file.databinding.FragmentEncryptionPwdEnterBinding
import com.czur.starry.device.file.db.entity.EncryptionStrategy.EVERY_TIME
import com.czur.starry.device.file.db.entity.EncryptionStrategy.SCREEN
import com.czur.starry.device.file.view.encryption.setting.EncryptionSettingActivity
import com.czur.starry.device.file.view.vm.MainViewModel
import com.czur.starry.device.file.widget.EncryptionPwdGroupView

/**
 * Created by 陈丰尧 on 2024/12/10
 */
private const val TAG = "PwdEnterFragment"

class PwdEnterFragment : CZViewBindingFragment<FragmentEncryptionPwdEnterBinding>() {
    private val enterViewModel: PwdEnterViewModel by viewModels()
    private val mainViewModel: MainViewModel by activityViewModels()

    override fun FragmentEncryptionPwdEnterBinding.initBindingViews() {
        pwdGroupView.onPwdChangeCallback = {
            enterViewModel.setUserSetPwd(it)
        }
        pwdGroupView.bindWithEyeImageView(
            pwdEyeIv, R.drawable.ic_pwd_eye_open, R.drawable.ic_pwd_eye_close
        )
        pwdGroupView.eyeStatus = EncryptionPwdGroupView.EyeStatus.CLOSE // 默认不显示密码

        // 让密码输入完成后下一个焦点落在确认按钮上,但是确认按钮可能是disable无法获取焦点,所以设置一个中间的View
        pwdGroupView.setPwdFinishFocusView(confirmFocusBtn)

        // 密码策略选择
        encryptionStrategyEveryTimeLayer.setOnDebounceClickListener {
            launch {
                if (!encryptionStrategyEveryTimeCb.isChecked()) {
                    enterViewModel.setEncryptionStrategy(EVERY_TIME)
                }
            }
        }

        encryptionStrategyScreenLayer.setOnDebounceClickListener {
            launch {
                if (!encryptionStrategyScreenCb.isChecked()) {
                    enterViewModel.setEncryptionStrategy(SCREEN)
                }
            }
        }

        // 密码设置
        settingLayer.setOnDebounceClickListener {
            logTagV(TAG, "密码设置页面")
            startActivity<EncryptionSettingActivity>()
        }

        confirmFocusBtn.setOnClickListener {
            logTagV(TAG, "confirmFocusBtn click")
            confirmBtn.performClick()
        }
        // 进入按钮
        confirmBtn.setOnDebounceClickListener {
            logTagV(TAG, "进入按钮点击")
            launch {
                logTagV(TAG, "检查密码")
                enterViewModel.checkPwd().also {
                    // 记录输入结果
                    enterViewModel.saveInputRes(it)
                }.no {
                    // 密码错误, 显示Toast
                    val count = enterViewModel.getRemainingInputCount()
                    if (count > 0) {
                        toast(getString(R.string.toast_encryption_pwd_error, count))
                    }
                    binding.pwdGroupView.clearAllPwd()
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        // 进入按钮的enable状态
        repeatCollectOnResume(enterViewModel.enterBtnEnableFlow) {
            logTagV(TAG, "进入按钮enable: $it")
            binding.confirmBtn.isEnabled = it
        }

        repeatCollectOnResume(enterViewModel.encryptionStrategyFlow) {
            // 密码显示策略
            logTagV(TAG, "encryptionStrategyFlow: $it")
            when (it) {
                SCREEN -> {
                    binding.encryptionStrategyScreenCb.setChecked(true)
                    binding.encryptionStrategyEveryTimeCb.setChecked(false)
                }

                EVERY_TIME -> {
                    binding.encryptionStrategyScreenCb.setChecked(false)
                    binding.encryptionStrategyEveryTimeCb.setChecked(true)
                }
            }
        }

        // 锁定输入
        repeatCollectOnResume(enterViewModel.lockInputFlow) { lock ->
            binding.pwdGroupView.isEnabled = !lock
            binding.pwdEyeIv.isEnabled = !lock
            binding.lockInfoTv.gone(!lock)
        }

        // 提示时间
        repeatCollectOnResume(enterViewModel.remainingLockTimeFlow) {
            binding.lockInfoTv.text =
                getString(
                    R.string.str_encryption_lock_input_info,
                    enterViewModel.formatLockTime(it)
                )
        }

        // 页面切换
        repeatCollectOnResume(mainViewModel.currentTabKeyFlow) {
            if (it != MainTabKey.SHOW_TYPE_ENCRYPTION) {
                logTagD(TAG, "页面不可见")
                binding.pwdGroupView.clearAllPwd()
            }
        }
    }

}