package com.czur.starry.device.file.view.czurshare.vm

import android.app.Application
import android.graphics.Bitmap
import android.graphics.Color
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.img.QrCodeUtil
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.db.FileDataBase
import com.czur.starry.device.file.db.entity.ShareConfig
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeStatus
import com.czur.starry.device.sharescreen.esharelib.util.czQRCodeInfo
import com.czur.starry.device.sharescreen.esharelib.util.logoBitmap
import com.czur.starry.device.sharescreen.esharelib.util.reFormatEShareQRCodeInfo
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.withContext
import kotlin.random.Random

/**
 *  author : WangHao
 *  time   :2023/10/18
 */

private const val TAG = "FileShareViwModel"

class FileShareViwModel(application: Application) : AndroidViewModel(application) {
    val deviceNameLive = DifferentLiveData("")
    var deviceName: String by LiveDataDelegate(deviceNameLive)
        private set

    val deviceCodeLive = DifferentLiveData("")
    var deviceCode: String by LiveDataDelegate(deviceCodeLive)
        private set

    val qrCodeImgLive = DifferentLiveData<Bitmap?>(null)
    var qrCodeImg: Bitmap? by LiveDataDelegate(qrCodeImgLive)

    private val qrCodeFlow = MutableStateFlow("")


    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(application)
    }

    init {
        launch {
            qrCodeFlow.debounce(100).collect {
                qrCodeImg = if (it.isNotEmpty()) {
                    // 只生成一张大图的二维码就行了
                    QrCodeUtil.generateQrCodeBmp(it) {
                        this.pointColor = 0xFF5879FC.toInt()
                        this.bgColor = Color.WHITE
                        this.edge = 400
                        this.logoConfig {
                            this.logoBmp = logoBitmap
                            delPadding = true
                        }
                    }
                } else {
                    null
                }
            }
        }
    }

    /**
     * 更新设备名称
     */
    suspend fun updateDeviceName(newName: String) {
        eShareServerSDK.setDeviceName(newName)
        deviceName = newName
    }

    suspend fun getDeviceCode() {
        deviceCode = Random.nextInt(1000, 10000).toString()
        fileShareCodeStatus = deviceCode
    }


    suspend fun loadDeviceName() = withContext(Dispatchers.IO) {
        refreshQRCodeInfo()
        // 设备名称，验证码
        deviceName = eShareServerSDK.deviceName ?: ""
        deviceCode = fileShareCodeStatus
    }


    /**
     * 刷新二维码信息
     */
    fun refreshQRCodeInfo() {
        val qrcodeInfo = eShareServerSDK.czQRCodeInfo
        logTagD(TAG, "qrcode:${qrcodeInfo}")
        if ("0.0.0.0" in qrcodeInfo) {
            logTagW(TAG, "二维码是无网络的")
        }
        qrCodeFlow.value = reFormatEShareQRCodeInfo(qrcodeInfo)
    }
}