package com.czur.starry.device.file.view.encryption.setting

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.FragmentEncryptionSettingResetPwdBinding
import com.czur.starry.device.file.view.encryption.setting.vm.ResetPwdViewModel
import com.czur.starry.device.file.widget.EncryptionPwdGroupView.EyeStatus

/**
 * Created by 陈丰尧 on 2024/12/12
 * 设置新密码页面
 */
private const val TAG = "EncryptionSettingResetPwdFragment"

class EncryptionSettingResetPwdFragment :
    AbsPwdSettingFragment<FragmentEncryptionSettingResetPwdBinding>() {
    private val args: EncryptionSettingResetPwdFragmentArgs by navArgs()

    private val resetPwdViewModel: ResetPwdViewModel by viewModels()

    override fun FragmentEncryptionSettingResetPwdBinding.initBindingViews() {

        pwdGroupView.bindWithEyeImageView(
            pwdEyeIv,
            R.drawable.ic_pwd_eye_open,
            R.drawable.ic_pwd_eye_close
        )
        pwdGroupView.eyeStatus = EyeStatus.OPEN // 默认打开

        pwdGroupView.onPwdChangeCallback = {
            resetPwdViewModel.onUserInputPwdChanged(it)
        }

        pwdGroupView.setPwdFinishFocusView(confirmFocusBtn)
        confirmFocusBtn.setOnClickListener { finishBtn.performClick() }

        finishBtn.setOnClickListener {
            launch {
                resetPwdViewModel.updateEncryptionInfo(
                    email = args.email,
                    pwd = resetPwdViewModel.userInputPwd
                ).onSuccess {
                    logTagV(TAG, "修改密码成功")
                    toast(R.string.toast_set_pwd_success)
                    activity?.finish()
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // 按钮可用性
        repeatCollectOnResume(resetPwdViewModel.finishEnableFlow) {
            binding.finishBtn.isEnabled = it
        }
    }
}