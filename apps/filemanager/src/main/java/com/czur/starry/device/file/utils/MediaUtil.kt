package com.czur.starry.device.file.utils

import android.media.MediaMetadataRetriever
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.getNumberDurationStr
import com.czur.starry.device.file.bean.FileEntity
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.withContext
import okhttp3.internal.toLongOrDefault
import java.util.concurrent.Executors

/**
 * Created by 陈丰尧 on 2021/9/22
 */
const val KEY_DURATION = "duration"
const val KEY_DURATION_STR = "durationStr"
private const val TAG = "MediaUtil"

// 数据解析Dispatcher, 来控制同一之间最多解析多少个数据
private val dispatcher = Executors.newFixedThreadPool(8).asCoroutineDispatcher()

suspend fun parseDuration(file: FileEntity): Long {
    if (file.containsAttr(KEY_DURATION)) {
        return file.getAttribute(KEY_DURATION).toLongOrDefault(0L)
    }
    return withContext(dispatcher) {
        val mmr = MediaMetadataRetriever()
        val duration = try {
            mmr.setDataSource(file.absPath)
            val duration = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            logTagD(TAG, "持续时间:${duration}")
            duration
        } catch (exp: Exception) {
            logTagW(TAG, "解析持续时间出错(${file.absPath}):", tr = exp)
            null
        } finally {
            mmr.release()
        }
        duration ?: 0L
    }
}

fun Int.toDuration(): String {
    return getNumberDurationStr(this.toLong())
}

fun Long.toDuration(): String {
    return getNumberDurationStr(this)
}

