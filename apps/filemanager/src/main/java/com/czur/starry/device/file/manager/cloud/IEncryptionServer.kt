package com.czur.starry.device.file.manager.cloud

import com.czur.starry.device.baselib.network.core.MiaoHttpBody
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpHeader
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.file.bean.EncryptionSendEmailEntity

/**
 * Created by 陈丰尧 on 2024/12/10
 */
interface IEncryptionServer {

    @MiaoHttpPost("/api/common/mail/code")
    @MiaoHttpHeader("Content-Type:application/json")
    fun sendEmailVerificationCode(
        @MiaoHttpBody
        params: EncryptionSendEmailEntity,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}