package com.czur.starry.device.file.view.encryption.setting

import androidx.fragment.app.activityViewModels
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.file.view.encryption.setting.vm.EncryptionSettingViewModel

/**
 * Created by 陈丰尧 on 2024/12/12
 */
abstract class AbsPwdSettingFragment<VB : ViewBinding> : CZViewBindingFragment<VB>() {
    protected val settingsViewModel: EncryptionSettingViewModel by activityViewModels()

    fun NavDirections.nav() {
        findNavController().navigate(this)
    }
}