package com.czur.starry.device.file.manager.localMeetingTransfer

import android.os.Environment
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.file.bean.FileTransferListRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLDecoder

object LocalMeetingTransferAccess {

    private val local_meeting_path: File = File(Environment.getExternalStorageDirectory().path+"/localMeetingVideo/")
    private val transferService: LocalMeetingTransferServer by lazy { HttpManager.getService() }

    suspend fun getTransferRecord(page: Int, size: Int): FileTransferListRecord = withContext(Dispatchers.IO) {
        try {
            val result = transferService.getTransferRecord(page, size)
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                FileTransferListRecord(emptyList(),1,0)
            }
        } catch (e: Exception) {
            FileTransferListRecord(emptyList(),1,0)
        }
    }

    suspend fun postTransferRecord(duration: Int, recordName: String, recordPath: String):String = withContext(Dispatchers.IO) {
        try {
            val result = transferService.postTransferRecord(duration, recordName, recordPath)
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }

    suspend fun startTransfer(id: String): String = withContext(Dispatchers.IO) {
        try {
            val result = transferService.startTransfer(id)
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }

    suspend fun deleteTransferRecord(id: String): String = withContext(Dispatchers.IO) {
        try {
            val result = transferService.deleteTransferRecord(id)
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }

    suspend fun getUsedDuration(): Int = withContext(Dispatchers.IO) {
        try {
            val result = transferService.getUsedDuration()
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                0
            }
        } catch (e: Exception) {
            0
        }
    }

    suspend fun getTransferDownLoadUrl(id: String): String = withContext(Dispatchers.IO) {
        try {
            val result = transferService.getTransferDownLoadUrl(id)
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }

    suspend fun downLoadFile(downLoadUrl: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val fileName: String = URLDecoder.decode(File(downLoadUrl).nameWithoutExtension, "UTF-8")
            val uniqueFileName = getUniqueFileName(local_meeting_path.path, fileName)
            val outputPath = File(local_meeting_path, uniqueFileName)
            val url = URL(downLoadUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.inputStream.use { inputStream ->
                FileOutputStream(outputPath).use { outputStream ->
                    copyStream(inputStream, outputStream)
                }
            }
            // 如果没有抛出异常，表明下载成功。
            return@withContext true
        } catch (e: Exception) {
            // 捕获并打印异常
            e.printStackTrace()
            // 下载过程中遇到了异常，表明下载失败。
            return@withContext false
        }
    }

    suspend fun getIsTranslatingWithRecord(): Boolean? = withContext(Dispatchers.IO) {
        try {
            val result = transferService.getIsTranslatingWithRecord()
            if (result.isSuccess) {
                result.withCheck().body
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun getUniqueFileName(path: String, fileName: String): String {
        val (baseName, index) = parseFileName(fileName)
        var counter = index ?: 0
        while (true) {
            val testName = if (counter > 0) "$baseName($counter)" else baseName
            val file = File(path, "$testName.txt")
            if (!file.exists()) break
            counter++
        }
        return if (counter > 0) "$baseName($counter).txt" else "$baseName.txt"
    }


    private fun parseFileName(fileName: String): Pair<String, Int?> {
        val regex = Regex("""(.*)\((\d+)\)$""")
        return regex.matchEntire(fileName)?.destructured?.let { (name, number) ->
            name to number.toInt()
        } ?: (fileName to null)
    }
    
    // 从输入流读取数据并写入到输出流
    private fun copyStream(inputStream: InputStream, outputStream: OutputStream) {
        val buffer = ByteArray(1024)
        var bytesRead = inputStream.read(buffer)
        while (bytesRead != -1) {
            outputStream.write(buffer, 0, bytesRead)
            bytesRead = inputStream.read(buffer)
        }
    }

}
