package com.czur.starry.device.file.view.encryption

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.file.bean.MainTabKey
import com.czur.starry.device.file.databinding.FragmentLocalEncryptionBinding
import com.czur.starry.device.file.view.FolderFragment
import com.czur.starry.device.file.view.encryption.pwd.EncryptionPwdFragment
import com.czur.starry.device.file.view.vm.MainViewModel
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * Created by 陈丰尧 on 2024/12/10
 * 加密文件夹页面
 */
private const val TAG_FOLDER_FRAGMENT = "FolderFragment"
private const val TAG = "LocalEncryptionFragment"

class LocalEncryptionFragment : CZViewBindingFragment<FragmentLocalEncryptionBinding>() {
    private val localEncryptionViewModel: LocalEncryptionViewModel by activityViewModels()
    private val mainViewModel: MainViewModel by activityViewModels()

    private val showPwdPageFlow by lazy {
        mainViewModel.showEncryptionPageFlow.combine(localEncryptionViewModel.needPwdFlow) { show, needPwd ->
            show && needPwd
        }.distinctUntilChanged()
    }

    override fun FragmentLocalEncryptionBinding.initBindingViews() {
        childFragmentManager.commit {
            val folderFragment = FolderFragment.getInstance(MainTabKey.SHOW_TYPE_ENCRYPTION)
            add(encryptionContainer.id, folderFragment, TAG_FOLDER_FRAGMENT)
            hide(folderFragment)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnCreate(localEncryptionViewModel.needPwdFlow) {
            childFragmentManager.commit(allowStateLoss = true) {
                val folderFragment =
                    childFragmentManager.findFragmentByTag(TAG_FOLDER_FRAGMENT) as? FolderFragment

                if (!it) {
                    folderFragment?.let { fragment ->
                        show(fragment)
                    }
                } else {
                    folderFragment?.let { fragment ->
                        hide(fragment)
                    }
                }
            }
        }


        repeatCollectOnCreate(showPwdPageFlow) {
            // 标签页不在当前时,需要卸载掉加密页面, 否则会出现异常呼出输入法的bug
            logTagV(TAG, "needPwd:${it}")
            if (it) {
                childFragmentManager.commit(allowStateLoss = true) {
                    val encryptionPwdFragment = EncryptionPwdFragment()
                    add(
                        binding.encryptionContainer.id,
                        encryptionPwdFragment,
                        "EncryptionPwdFragment"
                    )
                }
            } else {
                childFragmentManager.commit(allowStateLoss = true) {
                    childFragmentManager.findFragmentByTag("EncryptionPwdFragment")
                        ?.let { fragment ->
                            remove(fragment)
                        }
                }
            }
        }
    }
}