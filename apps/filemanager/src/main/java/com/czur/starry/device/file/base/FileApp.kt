package com.czur.starry.device.file.base

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.starry.device.baselib.base.listener.StarryApp
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 12/27/20
 *
 * Application
 */
class FileApp : StarryApp() {
    companion object {
        private const val DATA_STORE_NAME = "FileAppConfig"

        var instance: FileApp by Delegates.notNull()
        fun getApp(): FileApp {
            return instance
        }
    }

    val dataStore: DataStore<Preferences> by preferencesDataStore(
        name = DATA_STORE_NAME,
        corruptionHandler = createDefCorruptionHandler(DATA_STORE_NAME)
    )

    override fun onCreate() {
        instance = this
        super.onCreate()
    }


}