package com.czur.starry.device.file.utils

import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.LoadControl
import com.czur.starry.device.baselib.utils.ONE_MIN

/**
 * Created by 陈丰尧 on 2023/2/21
 */
private const val MIN_BUFFER_MS = 50000
private const val MAX_BUFFER_MS = 5 * ONE_MIN.toInt()  // 最大缓冲5分钟
private const val BUFFER_FOR_PLAYBACK_MS = 1500
private const val BUFFER_FOR_PLAYBACK_AFTER_RE_BUFFER_MS = 2000

/**
 * 修改播放器缓冲的参数
 * 主要修改是让缓冲的最大时间由默认的50s 增加到10分钟
 */
fun CZURLoadControl(): LoadControl {
    return DefaultLoadControl.Builder()
        .setBufferDurationsMs(
            MIN_BUFFER_MS,
            MAX_BUFFER_MS,
            BUFFER_FOR_PLAYBACK_MS,
            BUFFER_FOR_PLAYBACK_AFTER_RE_BUFFER_MS
        )
        .build()
}