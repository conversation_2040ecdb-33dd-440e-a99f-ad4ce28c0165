package com.czur.starry.device.file.manager

import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_CZUR_SHARE
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_DOWNLOAD
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_ENCRYPTION
import com.czur.starry.device.file.filelib.EXT_TYPE_LOCAL_SCREEN_SHOT
import com.czur.starry.device.file.filelib.EXT_TYPE_NORMAL
import com.czur.starry.device.file.manager.usb.UsbHelper

/**
 * Created by 陈丰尧 on 12/27/20
 */
object FileManager {

    /**
     * 获取所有根路径
     */
    val roots: List<FileEntity>
        get() = getAllRootEntity()


    /**
     * 根据类型创建FileAccess
     */
    fun createFileAccess(
        type: AccessType,
        extType: Int = EXT_TYPE_NORMAL,
        usbPath: String = ""
    ): FileAccess {
        return when (type) {
            AccessType.LOCAL -> {
                when (extType) {
                    EXT_TYPE_LOCAL_DOWNLOAD -> DownloadFileAccess
                    EXT_TYPE_LOCAL_SCREEN_SHOT -> ScreenShotFileAccess
                    EXT_TYPE_LOCAL_CZUR_SHARE -> CZURShareFileAccess
                    EXT_TYPE_LOCAL_ENCRYPTION -> EncryptFileAccess
                    else -> LocalFileAccess
                }
            }
            AccessType.USB -> USBFileAccess(usbPath)
            AccessType.RECORD -> RecordFileAccess
            AccessType.LOCAL_MEETING -> LocalMeetingAccess
            AccessType.AI_TRANS -> AITransAccess
        }
    }

    private fun getAllRootEntity(): List<FileEntity> {

        val listEntity = ArrayList<FileEntity>().apply {
            add(LocalFileAccess.getRootEntity())
            add(EncryptFileAccess.getRootEntity())      // 加密文件夹
            add(DownloadFileAccess.getRootEntity())
            add(CZURShareFileAccess.getRootEntity())
            add(ScreenShotFileAccess.getRootEntity())
            add(RecordFileAccess.getRootEntity())
            add(LocalMeetingAccess.getRootEntity())
            val usbList = UsbHelper.getusb()
            usbList.forEach { it ->
                add(UsbHelper.getUsbPath(it))
            }
        }
        return listEntity

    }


}
