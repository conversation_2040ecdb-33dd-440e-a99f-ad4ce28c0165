package com.czur.starry.device.file.bean


/**
 *
 * 会议记录audio and text类
 */
data class MeetAudioEntity(
    val audioUrl: String,
    val audioText: List<AudioTextEntity>
)
data class AudioTextEntity(
    val id: Int,
    val meetingId: Int,
    val agoraSid: String,
    val textTaskId: String,
    val bg: Int,
    val ed: Int,
    val si: Int,
    val speaker: Int,
    val createTime: Long,
    val updateTime: Long,
    val onebest: String,
    val wordsResultList: String,
)

data class TextStatus(
    val textStatus : Int
)