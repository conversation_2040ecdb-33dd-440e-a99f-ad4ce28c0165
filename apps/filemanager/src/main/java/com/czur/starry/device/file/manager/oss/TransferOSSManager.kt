package com.czur.starry.device.file.manager.oss

import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.model.MultipartUploadRequest
import com.alibaba.sdk.android.oss.model.ObjectMetadata
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.file.exp.UploadException
import com.czur.starry.device.file.manager.oss.manager.OSSRequestManager
import com.czur.starry.device.file.manager.oss.manager.OssTaskState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

object TransferOSSManager {
    private const val TAG = "TransferOSSManager"

    /**
     * 用来管理异步请求的
     */
    private val asyncRequestManager by lazy { OSSRequestManager(fileOss!!) }
    private var ossInstance = OSSInstance()
    private val fileOss: OSS?
        get() = ossInstance.getTranferOSS()

    private const val PART_COUNT = 100 * 1024L // 分片大小  阿里云下限是100k, 小一点比较不容易失败
    private const val transferBucketName = "czur-meeting-audio"
    private const val transferPrefix = "translate-source"

    private var currentTaskID:String = ""
    private var stopUpload:Boolean = false


    suspend fun uploadTransferFiles(
        file: File,
        listener: (finishSize: Long) -> Unit,
        ossPath: (ossPath: String, uploadStatus:Boolean) -> Unit
    ) {
        withContext(Dispatchers.IO) {
            stopUpload = false
            val finishSize = mutableMapOf<String, Long>()
            /**
             * 更新总完成进度
             */
            fun updateFinishSize(taskId: String, size: Long) {
                finishSize[taskId] = size
                val sumSize = finishSize.values.sum()
                launch {
                    listener(sumSize)
                }
            }

            // 每次循环之前都进行检查
            if (!isActive) {
                logTagI(TAG, "上传取消")
                // 上传取消, 跳出循环
                return@withContext
            }

            // 拼接文件名
            val ossKey = "${transferPrefix}/${Constants.SERIAL}/${file.name}"
            logTagD(TAG, "上传:ossKey:$ossKey")

            val rq: MultipartUploadRequest<MultipartUploadRequest<*>> =
                MultipartUploadRequest(transferBucketName, ossKey, file.absolutePath,
                    ObjectMetadata()
                )
            rq.partSize = PART_COUNT

            // 添加Task
            val taskId = asyncRequestManager.addUploadRequest(rq, file.length())
            logTagV(TAG, "taskID:${taskId} (${ossKey})")
            currentTaskID = taskId

            try {
                // 循环等待 上传完成
                doUpload(ossKey, taskId, ::updateFinishSize)
                if (stopUpload) {
                    ossPath(ossKey,false)
                    logTagV(TAG, "取消上传:${ossKey}")
                } else {
                    ossPath(ossKey,true)
                    logTagV(TAG, "上传完成:${ossKey}")
                }
            } catch(e: Exception){
                ossPath(ossKey,false)
                logTagV(TAG, "上传失败:${e}")
            }
        }
    }

    /**
     * 执行上传操作
     */
    private suspend fun CoroutineScope.doUpload(
        ossKey: String,
        taskId: String,
        updateFinishSize: (String, Long) -> Unit
    ) {
        while (true) {
            if (!isActive) {
                logTagD(TAG, "上传已取消:${ossKey}")
                asyncRequestManager.clearTask(taskId)
                break
            }
            try {
                // 每 200ms检查一次进度
                delay(200)
            } catch (exp: Exception) {
                logTagD(TAG, "上传已取消:${ossKey}")
                asyncRequestManager.clearTask(taskId)
                break
            }
            when (asyncRequestManager.getTaskStatus(taskId)) {
                OssTaskState.RUNNING -> {
                    // Task正在运行, 获取进度
                    val finishSize = asyncRequestManager.getFinishSize(taskId)
                    if (finishSize != asyncRequestManager.getTotalSize(taskId)) { // 判断是否达到100%
                        updateFinishSize(taskId, finishSize)
                    }
                }
                OssTaskState.SUCCESS -> {
                    val finishSize = asyncRequestManager.getTotalSize(taskId)
                    updateFinishSize(taskId, finishSize)
                    asyncRequestManager.clearTask(taskId)
                    break
                }
                OssTaskState.FAIL -> {
                    logTagW(TAG, "上传发生异常!")
                    val exp = asyncRequestManager.getException(taskId)
                    asyncRequestManager.clearTask(taskId)
                    // 上传失败, 抛出异常
                    throw UploadException(exp)
                }
                null -> {
                    logTagW(TAG, "根据TaskID:${taskId},没有找到对应Task")
                    break
                }
            }
        }
    }

    fun cancelUpload() {
        stopUpload = true
        asyncRequestManager.clearTask(currentTaskID)
    }
}