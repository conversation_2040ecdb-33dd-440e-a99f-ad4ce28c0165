package com.czur.starry.device.file.server.upload

import android.content.Intent
import android.util.Base64
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.ONE_GB
import com.czur.starry.device.file.base.FileApp
import com.czur.starry.device.file.db.FileDataBase
import com.czur.starry.device.file.db.entity.UploadFile
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.common.FileShareConstant.DEVICE_ID
import com.czur.starry.device.file.server.common.FileShareConstant.FILE_MD5
import com.czur.starry.device.file.server.common.FileShareConstant.FILE_NAME
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.fileUploadPath
import com.czur.starry.device.file.service.FileWatcherService
import com.czur.starry.device.file.service.FileWatcherService.Companion.UPLOAD_FILE_NAME_KEY
import io.netty.handler.codec.http.HttpHeaders
import io.netty.handler.codec.http.HttpMethod
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.internal.toLongOrDefault
import java.io.File

/**
 *  author : WangHao
 *  time   :2023/10/17
 */


object UploadHelper {
    const val TAG = "UploadHelper"


    //获取数据库中未完成文件
    suspend fun getFileInfo(name: String, md5: String, deviceId: String): UploadFile? {
        return FileDataBase.instance.uploadDao().getUploadFile(name, md5, deviceId)
    }

    //生产上传文件到数据库
    suspend fun updateFileInfoData(uploadFile: UploadFile): Long {
        return FileDataBase.instance.uploadDao().insert(uploadFile)
    }
    
    //上传完成删除文件
    suspend fun delFileInfoFromData(id: Long) =
        FileDataBase.instance.uploadDao().delete(id)


    // 上传文件失败
    suspend fun onUploadError(uploadFile: UploadFile) {
        logTagV(TAG, "interruptUpload:uploadFile:${uploadFile}")
        getFileInfo(uploadFile.fileName, uploadFile.md5, uploadFile.deviceId)?.let {
            it.isUploading = false
            updateFileInfoData(it)
        }
    }


    /**
     * 本地存储空间
     */
    suspend fun isNotEnough(): Boolean = withContext(Dispatchers.IO) {
        val freeSpace = File(fileUploadPath).freeSpace

        logTagD(TAG, "===freeSpace=$freeSpace")
        freeSpace < ONE_GB
    }

    fun getHeaderInfo(request: CZHttpRequest): HeaderInfo {
        val headers = request.rawHeaders
        val fileMd5 = headers.getAsString(FILE_MD5)
        val fileName = headers.getAsBase64(FILE_NAME)
        val deviceId = headers.getAsString(DEVICE_ID)
        var startByte = 0L
        var fileSize = 0L
        if (request.method == HttpMethod.POST) {
            startByte = headers.getLong(FileShareConstant.BYTES_START, 0L)
            fileSize = headers.getLong(FileShareConstant.FILE_SIZE, 0L)
        }

        return HeaderInfo(fileName, fileMd5, deviceId, startByte, fileSize)
    }

    data class HeaderInfo(
        val fileName: String,
        val fileMd5: String,
        val deviceId: String,
        val startByte: Long,
        val fileSize: Long
    )

    fun sendBroadCastToFileWatch(fileName: String) {
        val intent = Intent(FileWatcherService.ACTION_UPLOAD_FILE_COMPLETE).apply {
            putExtra(UPLOAD_FILE_NAME_KEY, fileName)
        }
        FileApp.instance.sendBroadcast(intent)
    }
}

fun HttpHeaders.getAsBase64(key: String): String {
    return String(Base64.decode(get(key), Base64.DEFAULT))
}

fun HttpHeaders.getLong(key: String, def: Long): Long {
    return get(key)?.toLongOrDefault(def) ?: def
}