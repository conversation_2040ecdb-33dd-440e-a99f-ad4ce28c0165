package com.czur.starry.device.file.utils


import android.os.Environment
import android.os.FileUtils
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.file.bean.ATTR_KEY_IMG_THUMB
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.FileAccess
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File
import java.util.zip.ZipFile

private const val TAG = "FileUtils"
val filterMap = mapOf(
    FileAccess.FilterMode.ALL to FileType.entries,
    FileAccess.FilterMode.DOC to listOf(
        FileType.DOC,
        FileType.EXCEL,
        FileType.PPT,
        FileType.PDF,
        FileType.DOCUMENT,
    ),
    FileAccess.FilterMode.FOLDER to listOf(FileType.FOLDER),
    FileAccess.FilterMode.MEDIA to listOf(FileType.AUDIO, FileType.VIDEO),
    FileAccess.FilterMode.PIC to listOf(FileType.IMAGE)
)

/**
 * 根据指定条件过滤文件
 * @param files 待过滤的文件集合
 * @param mode 过滤条件
 * @return 过滤后的集合
 */
fun filterFiles(files: List<FileEntity>, mode: FileAccess.FilterMode): List<FileEntity> {
    return files.filter {
        it.fileType in filterMap.getOrDefault(mode, emptyList())
    }
}

/**
 * 判断文件是否是[filter]类型的
 */
fun File.isFilter(filter: FileAccess.FilterMode): Boolean {
    val fileType = getFileType()
    return fileType in filterMap.getOrDefault(filter, emptyList())
}

/**
 * 将文件转换成FileEntity类型
 */
fun File.toFileEntity(): FileEntity {
    return FileEntity(this).apply {
        if (isTypeOf(FileType.IMAGE)) {
            // 如果是图片文件
            putAttribute(ATTR_KEY_IMG_THUMB, absolutePath)
        }
    }
}

/**
 * 排序
 */
fun sortFiles(files: List<FileEntity>, sortType: FileAccess.SortType): List<FileEntity> {
    return when (sortType) {
        FileAccess.SortType.TYPE_NAME_ASC -> files.sortedBy { it.pinyinName }
        FileAccess.SortType.TYPE_NAME_DESC -> files.sortedByDescending { it.pinyinName }
        FileAccess.SortType.TYPE_TIME_ASC -> files.sortedWith(compareBy<FileEntity> { it.lastModifyTime }.thenBy { it.pinyinName })
        FileAccess.SortType.TYPE_TIME_DESC -> files.sortedWith(compareByDescending<FileEntity> { it.lastModifyTime }.thenBy { it.pinyinName })
    }
}

/**
 * 删除全部, 删除失败时,有可能部分删除
 */
fun deleteFiles(files: List<FileEntity>): Boolean {
    return files.fold(true) { result, fileEntity ->
        // kotlin 的 and 与 && 不同, 没有逻辑短路, 会同时计算左右两侧的值
        // deleteRecursively 可以递归删除
        File(fileEntity.absPath).deleteRecursively() and result
    }
}

/**
 * 递归计算文件大小
 */
suspend fun File.sizeRecursive(): Long {
    return withContext(Dispatchers.IO) {
        walkTopDown().sumOf {
            it.length()
        }
    }
}

//复制或剪切文件
// 如果目标位置有的话, 则则直接覆盖
suspend fun File.copyTo(
    target: File,
    bufferSize: Int = DEFAULT_BUFFER_SIZE,
    handleSize: (handleSize: Long) -> Unit,
): Unit = withContext(Dispatchers.IO) {
    val tag = "File.copyTo(协程)"
    val srcFile = this@copyTo
    if (target.exists()) {
        // 删除源文件
        logTagD(tag, "删除源文件")
        if (!target.deleteRecursively())
            throw FileAlreadyExistsException(
                file = srcFile,
                other = target,
                reason = "Tried to overwrite the destination, but failed to delete it."
            )
    }

    // 如果是文件夹
    if (srcFile.isDirectory) {
        logTagD(tag, "复制文件夹")
        // 创建文件夹
        if (!target.mkdirs()) {
            throw FileSystemException(
                file = srcFile,
                other = target,
                reason = "Failed to create target directory."
            )
        }


        // 复制文件夹内的数据
        run loopForFolder@{
            listFiles()?.forEach { subFile ->
                if (isActive) {
                    subFile.copyTo(File(target, subFile.name), bufferSize, handleSize)
                } else {
                    logTagV(tag, "跳过其他文件")
                    return@loopForFolder
                }
            }
        }
    } else {
        // 如果是文件
        logTagV(tag, "${srcFile.name} 是文件")
        target.parentFile?.mkdirs()

        try {
            srcFile.inputStream().use { input ->
                target.outputStream().use { output ->
                    val buffer = ByteArray(bufferSize)
                    var bytes = input.read(buffer)
                    while (bytes >= 0) {
                        if (!isActive) {
                            throw CancellationException()
                        }
                        output.write(buffer, 0, bytes)
                        // 更新进度
                        handleSize(bytes.toLong())
                        bytes = input.read(buffer)
                    }
                }
            }
        } catch (tr: Throwable) {
            target.delete()
            if (tr is CancellationException) {
                logTagW(tag, "复制被取消")
            } else if (srcFile.name == ".nomedia") {
                logTagW(tag, "直接创建.nomedia文件")
                target.createNewFile()
            } else {
                logTagW(tag, "复制文件出错", tr = tr)
                throw tr
            }
        }


    }

}


val File.isEmpty: Boolean
    get() = length() == 0L

/**
 * 执行同步文件系统的代码
 */
suspend fun syncFileSystem() {
    withContext(Dispatchers.IO) {
        doWithoutCatch {
            val process = Runtime.getRuntime().exec("sync")
            process.waitFor()
        }
    }
}

val File.accessTime: Long
    get() = NativeCZFileUtil.getAccessTime(absolutePath)


private val SUB_CARD_PATH = Environment.getExternalStorageDirectory().path
private const val X_APK_FOLDER = "XApkFolder"

// 解压xApk文件
fun unzipXAPK(filePath: String, xApkName: String): Boolean {
    return try {
        val xApkFile = File(filePath)
        val xApkSavePath = "$SUB_CARD_PATH/$X_APK_FOLDER/$xApkName"

        File(SUB_CARD_PATH, X_APK_FOLDER).apply {
            if (!exists()) {
                mkdirs()
            }
        }
        File(xApkSavePath).apply {
            if (!exists()) {
                mkdirs()
            }
        }
        ZipFile(xApkFile).use { zip ->
            zip.entries().asSequence().forEach { entry ->
                zip.getInputStream(entry).use { input ->
                    val entryFile = File(xApkSavePath, entry.name)
                    entryFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
            }
        }
        true
    } catch (e: Exception) {
        e.printStackTrace()
        false
    }
}

// 获得xApk内所有的apk list
fun findXApkFiles(xApkName: String): List<File> {
    val xApkFolder = "$SUB_CARD_PATH/$X_APK_FOLDER/$xApkName"

    val directory = File(xApkFolder)
    if (!directory.exists()) {
        return emptyList()
    }

    val parentFile = directory.parentFile
    parentFile?.let {
        return it.walk()
            .filter { file ->
                file.isFile && file.extension.equals(
                    "apk",
                    ignoreCase = true
                ) && !file.extension.equals("xapk", ignoreCase = true)
            }.toList()
    } ?: return emptyList()
}

fun deleteDirectory(xApkName: String): Boolean {
    val xApkPath = "$SUB_CARD_PATH/$X_APK_FOLDER/$xApkName"
    val directory = File(xApkPath)
    logTagV(TAG, "xAPKPath:${xApkPath}")
    if (directory.exists()) {
        val files = directory.listFiles()
        if (null != files) {
            for (file in files) {
                if (file.isDirectory) {
                    //递归删除子文件夹
                    deleteDirectory(file.path)
                } else {
                    //删除子文件
                    file.delete()
                }
            }
        }
    }
    //删除主文件夹
    return directory.delete()
}

// 从manifest中获得packageName
fun getXApkPackageName(xApkName: String): String {
    val xApkFolder = "$SUB_CARD_PATH/$X_APK_FOLDER/$xApkName"
    val directory = File(xApkFolder)
    if (!directory.exists()) {
        return ""
    }

    val manifestFile = File(directory, "manifest.json")
    if (!manifestFile.exists()) {
        return ""
    }

    try {
        val manifestJson = manifestFile.readText()
        val jsonObject = JSONObject(manifestJson)
        val packageName = jsonObject.getString("package_name")
        return packageName
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return ""
}

// 从manifest中获得name
fun getXApkName(xApkName: String): String {
    val xApkFolder = "$SUB_CARD_PATH/$X_APK_FOLDER/$xApkName"
    val directory = File(xApkFolder)
    if (!directory.exists()) {
        return ""
    }

    val manifestFile = File(directory, "manifest.json")
    if (!manifestFile.exists()) {
        return ""
    }

    try {
        val manifestJson = manifestFile.readText()
        val jsonObject = JSONObject(manifestJson)
        val fileName = jsonObject.getString("name")
        return fileName
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return ""
}

