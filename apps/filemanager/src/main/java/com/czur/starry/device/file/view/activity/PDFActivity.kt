package com.czur.starry.device.file.view.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.ContextMenu
import android.view.KeyEvent
import android.view.KeyEvent.KEYCODE_DPAD_DOWN
import android.view.KeyEvent.KEYCODE_DPAD_LEFT
import android.view.KeyEvent.KEYCODE_DPAD_RIGHT
import android.view.KeyEvent.KEYCODE_DPAD_UP
import android.view.KeyEvent.KEYCODE_PAGE_DOWN
import android.view.KeyEvent.KEYCODE_PAGE_UP
import android.view.MenuItem
import android.view.View
import androidx.core.view.MenuCompat
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.widget.PDFView
import com.czur.starry.device.baselib.widget.showPopupWindow
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.ActivityPdfBinding


/**
 * created by wangh 22.0715
 */


class PDFActivity : CZViewBindingAty<ActivityPdfBinding>(), View.OnClickListener {
    //上一次点击事件时间
    private var lastClickTime = 0L

    //当前页数
    private var currentIndex = 0
    private var totalCount = 0

    //缩放
    private var zoomScale = 10
    private var zoomStep = 2


    companion object {
        private const val MAX_SCALE = 30
        private const val MIN_SCALE = 4
        private const val UP_INVISIBLE = 0
        private const val DOWN_INVISIBLE = 1
        private const val DOUBLE_INVISIBLE = 2

        // 小于间隔500ms点击事件丢弃
        private const val CLICK_THRESHOLD = 500

        const val EXTRA_FILE_PATH = "filePath"
    }

    override fun ActivityPdfBinding.initBindingViews() {
        registerForContextMenu(pdfView)


        pdfView.setOnPageChangedListener(object : PDFView.OnPageChangedListener {
            @SuppressLint("SetTextI18n")
            override fun onPageChanged(currentPageIndex: Int, totalPageCount: Int, isVisible: Int) {
                pageCurrentTv.text = "${currentPageIndex + 1}"
                pageSumTv.text = "$totalPageCount"
                totalCount = totalPageCount
                currentIndex = currentPageIndex
                when (isVisible) {
                    UP_INVISIBLE -> {
                        imPageUp.isEnabled = false
                        imPageDown.isEnabled = true
                    }

                    DOWN_INVISIBLE -> {
                        imPageUp.isEnabled = true
                        imPageDown.isEnabled = false
                    }

                    DOUBLE_INVISIBLE -> {
                        imPageUp.isEnabled = false
                        imPageDown.isEnabled = false
                    }

                    else -> {
                        imPageUp.isEnabled = true
                        imPageDown.isEnabled = true
                    }
                }

            }
        })

        imPageUp.setOnClickListener(this@PDFActivity)
        imPageDown.setOnClickListener(this@PDFActivity)
        imZoomIn.setOnClickListener(this@PDFActivity)
        imZoomOut.setOnClickListener(this@PDFActivity)
        pageClickLayer.setOnClickListener(this@PDFActivity)


    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        val path = intent.getStringExtra(EXTRA_FILE_PATH) ?: return

        binding.pdfView.showPdfFromPath(path)
    }


    override fun onCreateContextMenu(
        menu: ContextMenu,
        v: View?,
        menuInfo: ContextMenu.ContextMenuInfo?
    ) {
        menuInflater.inflate(R.menu.menu_document, menu)

        // 检查上一张和下一张 是否可用
        menu.getItem(0).apply {
            isEnabled = binding.imPageUp.isEnabled
        }
        menu.getItem(1).apply {
            isEnabled = binding.imPageDown.isEnabled
        }

        MenuCompat.setGroupDividerEnabled(menu, true)
        super.onCreateContextMenu(menu, v, menuInfo)
    }

    override fun onContextItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menuDocumentPageUp -> {
                launch {
                    binding.pdfView.pageChangeView(true)
                }
                true
            }

            R.id.menuDocumentPageDown -> {
                launch {
                    binding.pdfView.pageChangeView(false)
                }
                true
            }

            R.id.menuExit -> {
                finish()
                true
            }

            else -> super.onContextItemSelected(item)
        }
    }

    //翻页
    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            //向上滑动
            KEYCODE_DPAD_UP -> {
                binding.pdfView.updateScrollHeight(true)
            }
            //向下滑动
            KEYCODE_DPAD_DOWN -> {
                binding.pdfView.updateScrollHeight(false)
            }
            //向上翻页
            KEYCODE_DPAD_LEFT, KEYCODE_PAGE_UP -> {
                binding.pdfView.pageChangeView(true)
            }
            //向下翻页
            KEYCODE_DPAD_RIGHT, KEYCODE_PAGE_DOWN -> {
                binding.pdfView.pageChangeView(false)
            }
        }


        return super.onInterceptKeyDown(keyCode, event)
    }

    override fun onClick(v: View) {
        if (System.currentTimeMillis() - lastClickTime < CLICK_THRESHOLD) {
            return
        }
        lastClickTime = System.currentTimeMillis()
        when (v.id) {
            R.id.im_page_down -> {
                binding.pdfView.pageChangeView(false)
            }

            R.id.im_page_up -> {
                binding.pdfView.pageChangeView(true)
            }

            R.id.im_zoom_in -> {
                zoomScale += zoomStep

                if (zoomScale == MAX_SCALE) {
                    binding.imZoomIn.isEnabled = false
                }
                binding.imZoomOut.isEnabled = true
                binding.pdfView.onZoomView(true)
            }

            R.id.im_zoom_out -> {
                zoomScale -= zoomStep

                if (zoomScale == MIN_SCALE) {
                    binding.imZoomOut.isEnabled = false
                }
                binding.imZoomIn.isEnabled = true
                binding.pdfView.onZoomView()

            }

            R.id.pageClickLayer -> {
                if (totalCount == 1) return
                showPopupWindow(
                    this,
                    binding.pageSeparateTv,
                    currentIndex,
                    totalCount
                ) { position ->
                    binding.pdfView.selectPage(position)
                }
            }
        }
    }

    override fun onDestroy() {
        binding.pdfView.closePdfRender()
        super.onDestroy()
    }
}