package com.czur.starry.device.file.bean

import android.content.Context
import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.file.base.FileApp
import java.util.Locale

/**
 * Created by 陈丰尧 on 2022/1/17
 */
data class SpaceInfo(
    val usageLimit: Long,   //可用容量,单位字节
    val usage: Long,        //已用容量,单位字节
) {
    /**
     * 转化为字符串
     */
    fun toString(templateRes: Int, context: Context = FileApp.instance): String {
        return context.getString(templateRes, usage.toSizeStr(), usageLimit.toSizeStr())
    }

    fun toStingNew(templateRes: Int, context: Context = FileApp.instance): String {
        val gbRepresentation = getGBRepresentation(context)
        return context.getString(
            templateRes,
            usage.toSizeStr().substringBeforeLast(gbRepresentation),
            usageLimit.toSizeStr().substringBeforeLast(gbRepresentation)
        )
    }

    private fun getGBRepresentation(context: Context): String {
        val locale = context.resources.configuration.locales.get(0)
        return when (locale.language) {
            Locale.ENGLISH.language -> "G"
            "ru" -> "ГБ"
            else -> "G"
        }
    }

}