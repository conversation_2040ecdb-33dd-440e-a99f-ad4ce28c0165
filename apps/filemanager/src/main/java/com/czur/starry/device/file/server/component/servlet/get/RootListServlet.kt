package com.czur.starry.device.file.server.component.servlet.get

import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.file.R
import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.entity.RootFileEntity
import com.czur.starry.device.file.server.entity.RootFileItemEntity
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse

/**
 * Created by 陈丰尧 on 2024/12/16
 * 获取根目录列表
 */

private const val TAG = "RootList"

@Servlet(path = FileShareConstant.GET_ROOT_LIST)
class RootListServlet : HttpServlet() {
    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        logTagV(TAG, "RootListServlet onConnect")
        val clientApiVersion =
            request.rawHeaders.getInt(FileShareConstant.HEADER_KEY_CLIENT_API_VERSION, 0)
        logTagV(TAG, "clientAPIVersion: $clientApiVersion")
        response.content = RootFileEntity(getRootList(clientApiVersion))
    }

    private suspend fun getRootList(clientApiVersion: Int): List<List<RootFileItemEntity>> {
        return buildList {
            add(
                buildList {
                    add(
                        RootFileItemEntity(
                            FileShareConstant.FILE_LOCAL,
                            getString(R.string.file_folders_local)
                        )
                    )
                    // 旧版本的客户端不显示该路径
                    // 新版本的客户端 但是 用户没有设置密码的时候,也不显示该路径
                    if (clientApiVersion >= 1 && LocalEncryptionManager.hasEncryptionInfo()) {
                        logTagV(TAG, "添加加密路径")
                        add(
                            RootFileItemEntity(
                                FileShareConstant.FILE_ENCRYPT,
                                getString(R.string.file_folders_encrypt),
                                true
                            )
                        )
                    }
                    add(
                        RootFileItemEntity(
                            FileShareConstant.FILE_DOWNLOAD,
                            getString(R.string.file_folders_local_download)
                        )
                    )
                    add(
                        RootFileItemEntity(
                            FileShareConstant.FILE_SHARE,
                            getString(R.string.file_folders_czur)
                        )
                    )
                }
            )
            add(
                buildList {
                    add(
                        RootFileItemEntity(
                            FileShareConstant.FILE_MEETING,
                            getString(R.string.file_folders_meeting_record)
                        )
                    )
                    add(
                        RootFileItemEntity(
                            FileShareConstant.FILE_PICTURE,
                            getString(R.string.file_folders_local_screenshot)
                        )
                    )
                }
            )
        }
    }
}