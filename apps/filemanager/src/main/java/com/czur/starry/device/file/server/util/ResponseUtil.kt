package com.czur.starry.device.file.server.util

import android.net.Uri
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.common.FileShareConstant.FUNC_DOWNLOAD
import com.czur.starry.device.file.server.common.FileShareConstant.FUNC_UPLOAD
import com.czur.starry.device.file.server.msg.ResultMessage
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.netty.buffer.ByteBuf
import io.netty.buffer.Unpooled
import io.netty.channel.ChannelFutureListener
import io.netty.channel.ChannelHandlerContext
import io.netty.handler.codec.http.DefaultFullHttpResponse
import io.netty.handler.codec.http.HttpHeaders
import io.netty.handler.codec.http.HttpMethod
import io.netty.handler.codec.http.HttpRequest
import io.netty.handler.codec.http.HttpResponseStatus
import io.netty.handler.codec.http.HttpVersion
import java.nio.ByteBuffer


/**
 *  author : WangHao
 *  time   :2024/02/22
 */


object ResponseUtil {

    /**
     * 返回并断开
     */
    fun response(
        ctx: ChannelHandlerContext,
        msg: ResultMessage = ResultMessage()
    ) {
        val mapper = jacksonObjectMapper()
        val bytes = mapper.writeValueAsBytes(msg)
        val byteBuf = Unpooled.wrappedBuffer(bytes)
        val response = DefaultFullHttpResponse(
            HttpVersion.HTTP_1_1,
            HttpResponseStatus.OK,
            byteBuf
        )
        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE)
    }

    /**
     * 将任意数据类型转成Netty的ByteBuf类型
     * @param resultCode 返回码,在json
     */
    private fun Any.toByteBuf(resultCode: Int? = null): ByteBuf {
        // 如果是ByteBuffer,直接包装
        if (this is ByteBuffer) {
            return Unpooled.wrappedBuffer(this)
        }
        // 其他类型转成字节数组后包装
        val bytes = when (this) {
            is ResultMessage -> this.toByteArray()        // 通用返回消息
            is ByteArray -> this                          // 文件数据
            else -> ResultMessage(                      // 其他数据,放到content中
                code = resultCode ?: FileShareConstant.TRANS_SUCCESS,
                content = this
            ).toByteArray()
        }
        return Unpooled.wrappedBuffer(bytes)
    }

    fun response(
        ctx: ChannelHandlerContext,
        msg: CZHttpResponse
    ) {
        val byteBuf = msg.content?.toByteBuf(msg.resultCode) ?: Unpooled.buffer(0)

        val response = DefaultFullHttpResponse(
            HttpVersion.HTTP_1_1,
            HttpResponseStatus.OK,
            byteBuf
        )
        msg.header.forEach { (key, value) ->
            response.headers().set(key, value)
        }
        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE)
    }


    /**
     * 返回文件列表
     */
    fun response(
        ctx: ChannelHandlerContext,
        list: List<FileEntity>
    ) {
        val mapper = jacksonObjectMapper()
        val msg = ResultMessage()
        msg.code = FileShareConstant.TRANS_SUCCESS
        msg.content = mapper.writeValueAsString(list)
        val bytes = mapper.writeValueAsBytes(msg)
        val byteBuf = Unpooled.wrappedBuffer(bytes)
        val response = DefaultFullHttpResponse(
            HttpVersion.HTTP_1_1,
            HttpResponseStatus.OK,
            byteBuf
        )
        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE)
    }

}

class CZHttpRequest(private val nettyRequest: HttpRequest) {
    val uri: Uri by lazy {
        Uri.parse(nettyRequest.uri())
    }
    val path: String by lazy {
        uri.path ?: ""
    }

    val method: HttpMethod
        get() = nettyRequest.method()

    val rawHeaders: HttpHeaders
        get() = nettyRequest.headers()

    fun getPathParam(key: String): String? {
        return uri.getQueryParameter(key)
    }
}

class CZHttpResponse {
    val header = mutableMapOf<String, String>()
    var content: Any? = null
    var resultCode = FileShareConstant.TRANS_SUCCESS

    fun mkMsg(key: Int, msg: String) {
        content = ResultMessage(key, content = msg)
    }

    init {
        // 添加默认请求头
        header[FileShareConstant.HEADER_KEY_STARRY_API_VERSION] = FileShareConstant.API_VERSION.toString()
    }
}

