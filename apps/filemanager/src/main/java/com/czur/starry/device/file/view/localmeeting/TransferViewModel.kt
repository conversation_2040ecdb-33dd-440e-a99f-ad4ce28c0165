package com.czur.starry.device.file.view.localmeeting

import android.app.Application
import android.widget.ImageView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.file.R
import com.czur.starry.device.file.bean.FileTransferListEntity
import com.czur.starry.device.file.manager.localMeetingTransfer.LocalMeetingTransferAccess
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay


private const val TAG = "TransferViewModel"

class TransferViewModel(application: Application) : AndroidViewModel(application) {

    /// 共多少页
    private val totalPageSizeLive = MutableLiveData<Int>()
    val totalPageSize: LiveData<Int>  get() = totalPageSizeLive

    /// 数据列表
    private val transferListLive = MutableLiveData<List<FileTransferListEntity>>()
    val transferList: LiveData<List<FileTransferListEntity>> get() = transferListLive

    /// 已使用时长
    private val usedDurationLive = MutableLiveData<Int>()
    val usedDuration: LiveData<Int>  get() = usedDurationLive

    private var transferJob: Job? = null
    var hasTranslatingRecord:Boolean
    var currentPageSize:Int
    var isFirstLoad:Boolean = true

    init {
        totalPageSizeLive.value = 1
        currentPageSize = 1
        hasTranslatingRecord = false
    }

    fun getTransferList(pageSize: Int) {
        launch {
            if (currentPageSize != pageSize) {
                clearTransferJob()
            }
            val numberOfPage = 7
            val transferListRecord = LocalMeetingTransferAccess.getTransferRecord(pageSize,numberOfPage)
            var totalCount = if (transferListRecord.total > numberOfPage) {
                if (transferListRecord.total % numberOfPage == 0) transferListRecord.total / numberOfPage else transferListRecord.total / numberOfPage + 1
            } else {
                1
            }
            totalPageSizeLive.value = totalCount
            if (currentPageSize == transferListRecord.currentPage) {
                isFirstLoad = false
                transferListLive.value = transferListRecord.list
                checkCurrentPageHasTranslating(transferListRecord.list, pageSize)
            }
        }
    }

    private fun checkCurrentPageHasTranslating(list:List<FileTransferListEntity>, pageSize: Int) {
        val hasTranslatingRecord = list.any { it.taskStatus == 0 || it.taskStatus == 1 }
        if (hasTranslatingRecord) {
            if (transferJob == null) {
                transferJob = launch {
                    while (true) {
                        getTransferList(pageSize)
                        delay(5000) // 5秒
                    }
                }
            }
        } else {
            clearTransferJob()
        }
    }

    private fun clearTransferJob() {
        launch {
            transferJob?.cancel()
            transferJob?.join()
            transferJob = null
        }
    }


    fun getUsedDuration() {
        launch {
            val duration = LocalMeetingTransferAccess.getUsedDuration()
            logTagD(TAG, "获取已用时长：${duration}")
            usedDurationLive.value = duration
        }
    }

    fun clearData() {
        totalPageSizeLive.value = 1 // 重置页码信息
        transferListLive.value = emptyList() // 清空传输列表
        isFirstLoad = true
        clearTransferJob()
    }
}