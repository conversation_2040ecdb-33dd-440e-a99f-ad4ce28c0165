package com.czur.starry.device.file.manager

import android.os.Environment
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.MusicEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.utils.KEY_DURATION
import com.czur.starry.device.file.utils.KEY_DURATION_STR
import com.czur.starry.device.file.utils.deleteFiles
import com.czur.starry.device.file.utils.isAudio
import com.czur.starry.device.file.utils.isDocument
import com.czur.starry.device.file.utils.isVideo
import com.czur.starry.device.file.utils.parseDuration
import com.czur.starry.device.file.utils.sortFiles
import com.czur.starry.device.file.utils.toDuration
import com.czur.starry.device.file.utils.toFileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2022/8/9
 */
object LocalMeetingAccess : FileAccess {
    private const val VIDEO_FILE_FOLDER_NAME = "localMeetingVideo"

    private val rootDir: File by lazy {
        val sdCardPath = Environment.getExternalStorageDirectory().path
        File(sdCardPath, VIDEO_FILE_FOLDER_NAME).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    private val delFlag: AtomicBoolean = AtomicBoolean(false)
    override val isDeleting: Boolean
        get() = delFlag.get()


    override fun getRootEntity() = FileEntity(
        rootDir.absolutePath,
        FileType.ROOT,VIDEO_FILE_FOLDER_NAME,
        0, "", AccessType.LOCAL_MEETING, 0L
    )

    override suspend fun getItemsByTarget(
        targetDir: FileEntity,
        start: Int,
        count: Int,
        filterMode: FileAccess.FilterMode,  // 不关心该项
        sortType: FileAccess.SortType
    ): List<FileEntity> {
        // 录像文件不能创建文件夹, 所以直接就是rootEntity下的所有file
        return withContext(Dispatchers.IO) {
            val entities = rootDir.listFiles { item ->
                item.isVideo() || item.isAudio() || item.isDocument()  // 只显示视频文件
            }?.map {
                async {
                    // 获取 视频时长
                    val entity = it.toFileEntity()
                    val duration = parseDuration(entity)
                    entity.putAttribute(KEY_DURATION, duration.toString())
                    entity.putAttribute(KEY_DURATION_STR, duration.toDuration())
                    entity
                }
            }?.map {
                it.await()
            }?.toList() ?: emptyList()
            sortFiles(entities, sortType)
        }
    }

    override suspend fun getNextFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String {
        TODO("Not yet implemented")
    }

    override suspend fun createNewFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String? {
        TODO("Not yet implemented")
    }

    override suspend fun delFiles(
        delFiles: List<FileEntity>,
        reqCloudLock: Boolean
    ): FileAccess.Result {
        return withContext(Dispatchers.IO) {
            delFlag.set(true)
            val res = deleteFiles(delFiles) // 删除选中的文件
            delFlag.set(false)
            if (res) FileAccess.Result.SUCCESS else FileAccess.Result.FAIL
        }
    }

    override suspend fun renameFile(
        target: FileEntity,
        newName: String,
        targetDir: FileEntity
    ): FileAccess.Result {
        return withContext(Dispatchers.IO) {
            (newName == target.name)
                .yes {
                    FileAccess.Result.SUCCESS
                }.otherwise {
                    if (hasThisName(newName, targetDir)) {
                        // 有同名文件
                        FileAccess.Result.FILE_NAME_REPETITION
                    } else {
                        val srcFile = File(target.absPath)
                        val destFile = File(rootDir, newName)
                        // 重命名
                        srcFile.renameTo(destFile)
                            .yes { FileAccess.Result.SUCCESS }
                            .otherwise { FileAccess.Result.SYSTEM_ERR }
                    }
                }
        }
    }

    private suspend fun hasThisName(name: String, targetDir: FileEntity): Boolean {
        val files = getItemsByTarget(targetDir)
        files.forEach {
            if (name == it.name) {
                return true
            }
        }
        return false
    }

    override fun getMusicList(target: List<FileEntity>): ArrayList<MusicEntity> {
        TODO("Not yet implemented")
    }

    override suspend fun updateUsage() {
        LocalInfoManager.refreshUsageOnce()
    }
}