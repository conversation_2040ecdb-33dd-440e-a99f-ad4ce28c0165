package com.czur.starry.device.file.view.dialog

import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.addEmojiFilter
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.keyboard.SoftKeyboardStateHelper
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.view.floating.FloatShowMode
import com.czur.starry.device.baselib.view.floating.KeyBackFloatFragment
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.file.R

/**
 * Created by 陈丰尧 on 2022/3/22
 * 新建文件夹对话框
 * 如果输入纯空白字符 则认为没有输入, 使用默认名称
 * 否则去除前后空格后 作为文件夹名称
 * 中间有空格则不处理
 */
class NewFolderDialog(
    val onConfirmListener: (folderName: String) -> Unit,
) : KeyBackFloatFragment(
    bgDark = true,
    inputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE,
    showMode = FloatShowMode.SINGLE,
    blockOutSideClickBeforeAnim = true
) {
    override fun getLayoutId(): Int = R.layout.dialog_new_folder

    private val inputDialogEt by findView<EditText>(R.id.inputDialogEt)
    private val cancelBtn by findView<CommonButton>(R.id.cancelBtn)
    private val confirmBtn by findView<CommonButton>(R.id.confirmBtn)
    private var hasCallback = false

    private var defY = 0F // float 的初始位置

    private val screenHeight by lazy {
        getScreenHeight()
    }

    private val softKeyboardStateHelper by lazy {
        SoftKeyboardStateHelper(this)
    }

    override fun initView() {
        super.initView()
        softKeyboardStateHelper.doOnImeOpen {keyboardHeightInPx ->
            defY = subView.y
            val offset = -50
            val dy = (defY + subView.height - (screenHeight - keyboardHeightInPx) + offset).coerceAtLeast(0F)
            subView.animate().apply {
                duration = 100
                yBy(-dy)
            }
        }

        softKeyboardStateHelper.doOnImeClose {
            if (!isDismissing) {
                subView.animate().apply {
                    duration = 100
                    y(defY)
                }
            }
        }

        // 取消按钮
        cancelBtn.setOnClickListener {
            dismiss()
        }

        // 确定按钮
        confirmBtn.setOnDebounceClickListener {
            if (hasCallback) return@setOnDebounceClickListener
            hasCallback = true
            val newFolderName = inputDialogEt.text.toString().trim()
            if (newFolderName.isEmpty()) {
                onConfirmListener(getString(R.string.dialog_new_folder_hint_prefix))
            } else {
                onConfirmListener(newFolderName)
            }
            dismiss()
        }
        // 由于之前没有长度限制，测试给出长度限制：字符长度为50, 中文2个字符
        inputDialogEt.showTextLength = 50
        inputDialogEt.addEmojiFilter()
        // 输入法响应键盘事件
        inputDialogEt.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
            ) {
                confirmBtn.performClick()
                true
            } else {
                true
            }
        }

        // 显示输入法
        inputDialogEt.requestFocus()
        inputDialogEt.keyboardShow()
    }
}