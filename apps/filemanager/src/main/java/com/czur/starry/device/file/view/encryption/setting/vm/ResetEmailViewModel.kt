package com.czur.starry.device.file.view.encryption.setting.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.isEmail
import com.czur.starry.device.file.R
import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.view.encryption.setting.common.EmailStateMachine
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flowOn

/**
 * Created by 陈丰尧 on 2024/12/12
 */
class ResetEmailViewModel : ViewModel() {
    private val _userInputVerificationCodeOriginalFlow = MutableStateFlow("")
    private val _userInputVerificationCodeNewFlow = MutableStateFlow("")
    private val _userInputEmailNewFlow = MutableStateFlow("")
    val emailNew
        get() = _userInputEmailNewFlow.value

    // 完成按钮的可用性
    val finishEnableFlow = combine(
        _userInputVerificationCodeOriginalFlow.debounce(100),
        _userInputVerificationCodeNewFlow.debounce(100),
        _userInputEmailNewFlow.debounce(100)
    ) { original, new, newEmail ->
        original.length == LocalEncryptionManager.EMAIL_VERIFY_CODE_LENGTH
                && new.length == LocalEncryptionManager.EMAIL_VERIFY_CODE_LENGTH
                && newEmail.isEmail()
    }.flowOn(Dispatchers.Default)

    private val emailStateMachineOriginal = EmailStateMachine(viewModelScope)
    private val emailStateMachineNew = EmailStateMachine(viewModelScope)

    val processStatusOriginalFlow
        get() = emailStateMachineOriginal.processStatusFlow
    val processStatusNewFlow
        get() = emailStateMachineNew.processStatusFlow

    val emailColdDownTimeStrFlowOriginal
        get() = emailStateMachineOriginal.coldDownTimeStrFlow
    val emailColdDownTimeStrFlowNew
        get() = emailStateMachineNew.coldDownTimeStrFlow


    fun onUserInputVerificationCodeOriginalChanged(verificationCode: String) {
        _userInputVerificationCodeOriginalFlow.value = verificationCode
    }

    fun onUserInputVerificationCodeNewChanged(verificationCode: String) {
        _userInputVerificationCodeNewFlow.value = verificationCode
    }

    fun onUserInputEmailNewChanged(email: String) {
        _userInputEmailNewFlow.value = email
    }

    suspend fun sendEmailOriginal(email: String): Result<Unit> {
        val code = LocalEncryptionManager.createEmailVerificationCode()
        return emailStateMachineOriginal.sendEmail(email, code)
    }

    suspend fun sendEmailNew(email: String): Result<Unit> {
        val code = LocalEncryptionManager.createEmailVerificationCode()
        return emailStateMachineNew.sendEmail(email, code)
    }

    fun clearVerifyData() {
        emailStateMachineOriginal.clearVerifyData()
        emailStateMachineNew.clearVerifyData()
    }

    /**
     * 更新密保邮箱
     */
    suspend fun verificationEncryptionEmail(): Result<Unit> {
        val inputCodeOrigin = _userInputVerificationCodeOriginalFlow.value
        val inputCodeNew = _userInputVerificationCodeNewFlow.value
        val emailOrigin = LocalEncryptionManager.getEmail()
        val emailNew = _userInputEmailNewFlow.value

        // 校验
        val verifyOriginal = emailStateMachineOriginal.checkVerifyCode(emailOrigin, inputCodeOrigin,
            mapOf(
                EmailStateMachine.VerifyErrorType.VERIFICATION_CODE_ERROR to getString(R.string.toast_illegal_verification_code_original),
            )
        )
        if (verifyOriginal.isFailure) {
            return verifyOriginal
        }
        val verifyNew = emailStateMachineNew.checkVerifyCode(emailNew, inputCodeNew,
            mapOf(
                EmailStateMachine.VerifyErrorType.VERIFICATION_CODE_ERROR to getString(R.string.toast_illegal_verification_code_new),
            )
        )
        if (verifyNew.isFailure) {
            return verifyNew
        }
        return Result.success(Unit)
    }
}