package com.czur.starry.device.file.view.encryption.pwd

import androidx.lifecycle.ViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.file.R
import com.czur.starry.device.file.db.entity.EncryptionStrategy
import com.czur.starry.device.file.manager.LocalEncryptionManager
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import kotlin.math.ceil
import kotlin.math.max

/**
 * Created by 陈丰尧 on 2024/12/11
 */
private const val TAG = "PwdEnterViewModel"

class PwdEnterViewModel : ViewModel() {
    private val userSetPwdFlow = MutableStateFlow("")
    private val userSetPwd
        get() = userSetPwdFlow.value
    val enterBtnEnableFlow = userSetPwdFlow.debounce(100).map { it.length >= 6 }

    // 密码显示策略Flow
    val encryptionStrategyFlow
        get() = LocalEncryptionManager.encryptionStrategyFlow

    val lockInputFlow
        get() = LocalEncryptionManager.lockInputFlow

    // 剩余锁定时间
    val remainingLockTimeFlow = MutableStateFlow(0L)
    private var refRemainingLockTimeJob: Job? = null

    init {
        launch {
            // 初始化剩余锁定时间
            val lockTime = LocalEncryptionManager.getLockTime()
            val remainingLockTime =
                lockTime - System.currentTimeMillis()
            if (remainingLockTime > 0) {
                remainingLockTimeFlow.value = remainingLockTime
                startRemainingLockTimeJob()
            } else if (lockTime > 0) {
                logTagD(TAG, "清除锁定时间")
                LocalEncryptionManager.clearLockTime()
            }
        }

        launch {
            lockInputFlow.collect {
                if (it) {
                    startRemainingLockTimeJob()
                } else {
                    refRemainingLockTimeJob?.cancel()
                    remainingLockTimeFlow.value = 0
                }
            }
        }
    }

    fun setUserSetPwd(pwd: String) {
        userSetPwdFlow.value = pwd
    }

    suspend fun setEncryptionStrategy(strategy: EncryptionStrategy) {
        LocalEncryptionManager.updateEncryptionStrategy(strategy)
    }

    suspend fun checkPwd(): Boolean {
        return LocalEncryptionManager.checkPwd(userSetPwd).also {
            logTagV(TAG, "checkPwd res:${it}")
        }
    }

    suspend fun saveInputRes(pwdOK: Boolean) {
        logTagV(TAG, "saveInputRes:${pwdOK}")
        LocalEncryptionManager.saveInputRes(pwdOK)
    }

    // 获取剩余可输入次数
    suspend fun getRemainingInputCount(): Int {
        return max(
            0,
            LocalEncryptionManager.ERROR_COUNT_LIMIT - LocalEncryptionManager.getErrorCount()
        )
    }

    private fun startRemainingLockTimeJob() {
        refRemainingLockTimeJob?.cancel()
        refRemainingLockTimeJob = launch {
            while (isActive) {
                val remainingLockTime =
                    LocalEncryptionManager.getLockTime() - System.currentTimeMillis()
                if (remainingLockTime <= 0) {
                    remainingLockTimeFlow.value = 0
                    LocalEncryptionManager.clearLockTime()
                    break
                }
                remainingLockTimeFlow.value = remainingLockTime
                // 等待下一次刷新
                if (remainingLockTime > ONE_HOUR) {
                    delay(ONE_MIN)
                } else if (remainingLockTime > ONE_MIN) {
                    delay(ONE_SECOND)
                } else {
                    delay(100)
                }
            }
        }
    }

    /**
     * 格式化剩余锁定时间(于洋25.1.7)
     * 小时按小时向上取整，低于59分钟时按分钟取整。
     *  如 1小时1分，则显示2小时;
     *  如 59:39 显示为1小时;
     *  如 59：00显示为59;
     *  如 低于60s时，显示为1分钟;
     */
    fun formatLockTime(time: Long): String {
        // 1小时以上,只显示小时
        return if (time > (ONE_HOUR - ONE_MIN)) {
            val hour = ceil(time / ONE_HOUR.toFloat()).toInt()
            "${hour}${getString(R.string.str_time_unit_hour)}"
        } else {
            // 只显示分钟
            val minute = ceil(time / ONE_MIN.toFloat()).toInt()
                .coerceAtLeast(1)   // 最低显示1分钟
            "${minute}${getString(R.string.str_time_unit_minute)}"
        }
    }

    override fun onCleared() {
        super.onCleared()
        refRemainingLockTimeJob?.cancel()
    }
}