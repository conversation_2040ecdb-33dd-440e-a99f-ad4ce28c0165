package com.czur.starry.device.file.view.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.ServerLocation
import com.czur.starry.device.baselib.common.ServerLocationOversea
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_ENCRYPTION
import com.czur.starry.device.file.bean.MainTabKey.SHOW_TYPE_LOCAL
import com.czur.starry.device.file.db.FileDataBase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2022/7/9
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    var serverLo = Constants.serverLocation
        private set

    // 是否要显示拒绝页面
    val showRefuse: Boolean
        get() = Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland && serverLo is ServerLocationOversea

    val uploadFileFlow
        get() = FileDataBase.instance.uploadDao().getAllUploadFiles()
    var uploadFileList: MutableList<String> = mutableListOf("")
    var uploadFilePauseList: MutableList<String>? = mutableListOf("")

    private val _currentTabKeyFlow = MutableStateFlow(SHOW_TYPE_LOCAL)
    val currentTabKeyFlow = _currentTabKeyFlow.asStateFlow()
    val currentTabKey: Int
        get() = _currentTabKeyFlow.value

    //语音打开文件
    var voiceOpenFileName = MutableStateFlow("")
    /**
     * 是否显示加密页面
     */
    val showEncryptionPageFlow = _currentTabKeyFlow.map {
        it == SHOW_TYPE_ENCRYPTION
    }.distinctUntilChanged()

    fun setCurrentFragment(showType: Int) {
        _currentTabKeyFlow.value = showType
    }


    /**
     * 更新到通用的海外服务器
     */
    fun updateToOverseasServer() {
        serverLo = ServerLocation.create("oversea")
    }

}