package com.czur.starry.device.file.manager

import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.MusicEntity
import com.czur.starry.device.file.manager.transfer.FileTransfer

/**
 * Created by 陈丰尧 on 12/27/20
 *
 * 文件获取器接口
 */
interface FileAccess {
    companion object {
        const val COUNT_ALL = Int.MAX_VALUE

        /**
         * 显示的子分类
         */
        val showSubFilterMode = listOf(FilterMode.DOC, FilterMode.PIC, FilterMode.MEDIA)
    }

    val isDeleting: Boolean

    /**
     * 过滤规则, 因为涉及到云盘访问
     * 正常来说,会采取分页加载, 所以排序没有办法在本地做
     */
    enum class FilterMode {
        ALL, DOC, PIC, MEDIA, FOLDER;
    }

    enum class SortType {
        TYPE_NAME_ASC, // 文件名升序
        TYPE_NAME_DESC, // 文件名降序
        TYPE_TIME_ASC, // 修改时间升序
        TYPE_TIME_DESC; // 修改时间降序
    }

    enum class Result {
        SUCCESS, // 成功

        FAIL,                   // 通用失败
        FILE_NAME_REPETITION,   //文件名重复
        SYSTEM_ERR,             //系统错误
        NO_NET_WORK,             //无网络
        FILE_CONFLICT,          // 文件冲突
        CN_DEVICES_IN_OVERSEAS  // 国内设备在海外
    }

    enum class SortTime {
        ITEM_1, // 6小时
        ITEM_2, // 1天
        ITEM_3, // 7天
        ITEM_4, // 阅后即焚
        ITEM_5; // 永不删除
    }

    /**
     * 返回跟路径的FileEntity
     */
    fun getRootEntity(): FileEntity

    /**
     * 获取指定文件夹下的数据,耗时操作,需要放到携程中使用
     * @param targetDir 想要获取文件的指定目录
     *        如果想要获取根路径下全部文件,则传入 FileEntity.getRootFileEntity()
     * @param start : 从第几个item开始, 默认是从0开始
     * @param count : 想获取几个item, 默认-1表示获取全部
     * @param sortType: 排序类型, 默认为按照修改时间升序排列
     * @param filterMode: 过滤规则, 指定返回那些种类的数据
     * @return 指定路径下的全部文件
     */
    suspend fun getItemsByTarget(
        targetDir: FileEntity,
        start: Int = 0,
        count: Int = COUNT_ALL,
        filterMode: FilterMode = FilterMode.ALL,
        sortType: SortType = SortType.TYPE_TIME_DESC,
    ): List<FileEntity>


    /**
     * 获取下一个未使用的文件夹,当新建文件夹时,会用用户指定的文件夹名称作为前缀
     * @param targetDir 在哪个文件夹中查询
     * @param folderName 用户输入的文件夹名称
     * @return 要新建的文件夹名称
     */
    suspend fun getNextFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>,
    ): String

    /**
     * 在指定目录新建文件夹
     * @param folderName 要新建文件夹的名字
     * @param targetDir  要在哪新建文件夹
     * @return 如果新建成功,则返回新建的文件夹名字,否则返回null
     */
    suspend fun createNewFolder(
        folderName: String,
        targetDir: FileEntity,
        targetDirFileNames: List<String>
    ): String?

    /**
     * 删除指定文件
     * @param delFiles 要删除的文件集合
     * @return 如果全部删除成功, 则返回true, 删除失败返回False
     */
    suspend fun delFiles(delFiles: List<FileEntity>, reqCloudLock: Boolean = false): Result

    /**
     * 重命名指定文件
     * @param target    要重命名的文件
     * @param newName   新的文件名(包括扩展名)
     * @param targetDir 当前所在的文件夹
     * @return          重命名结果
     */
    suspend fun renameFile(target: FileEntity, newName: String, targetDir: FileEntity): Result

    /**
     * 获取文件复制工具,
     * 使用FileTransfer 进行在不同位置的文件复制
     * @param targetDir       目标文件夹
     * @param srcFiles   要复制的文件集合
     */
    suspend fun getTransfer(
        targetDir: FileEntity,
        srcFiles: List<FileEntity>,
        delOnFinish: Boolean = false,
    ): FileTransfer = FileTransfer(targetDir, srcFiles, delOnFinish)

    /**
     * 将FileEntity 转换为MusicEntity
     * @param target 要转换的文件
     * @return 因为涉及到Activity之间的传输, 所以使用ArrayList
     */
    fun getMusicList(target: List<FileEntity>): ArrayList<MusicEntity>

    suspend fun updateUsage() {}
}