package com.czur.starry.device.file.view.infobar

import android.os.Bundle
import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.file.databinding.FragmentCzShareInfoBarBinding
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.view.czurshare.FileShareFloat

/**
 * Created by 陈丰尧 on 2025/3/11
 */
private const val TAG = "CZShareInfoBar"

class CZShareInfoBar : BaseInfoBar<FragmentCzShareInfoBarBinding>() {
    override fun updateInfo(info: String) {

    }

    private var lastOutTime = 0L

    override fun FragmentCzShareInfoBarBinding.initBindingViews() {
        val hoverListener = object : View.OnHoverListener {
            override fun onHover(
                v: View?,
                event: MotionEvent
            ): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_HOVER_ENTER -> {
                        logTagD(TAG, "ACTION_HOVER_ENTER")
                        if (SystemClock.elapsedRealtime() - lastOutTime < 250) {
                            // 点击事件导致的, 屏蔽掉
                            return false
                        }
                        binding.tipsTv.show()
                    }

                    MotionEvent.ACTION_HOVER_EXIT -> {
                        logTagD(TAG, "ACTION_HOVER_EXIT")
                        binding.tipsTv.gone()
                        lastOutTime = SystemClock.elapsedRealtime()
                    }
                }
                return true
            }
        }

        binding.openLayout.setOnHoverListener(hoverListener)
        binding.closeLayout.setOnHoverListener(hoverListener)


        binding.openLayout.setOnDebounceClickListener(ONE_SECOND) {
            logTagV(TAG, "click czurShareIv - openLayout")
            FileShareFloat().show()
        }
        binding.closeLayout.setOnDebounceClickListener(ONE_SECOND) {
            logTagV(TAG, "click czurShareIv - closeLayout")
            FileShareFloat().show()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        FileHandlerLive.fileShareEnableLive.observe(viewLifecycleOwner) {
            logTagD(TAG, "fileShareEnableLive: $it")
            binding.openLayout.gone(!it)
            binding.closeLayout.gone(it)
        }
    }
}