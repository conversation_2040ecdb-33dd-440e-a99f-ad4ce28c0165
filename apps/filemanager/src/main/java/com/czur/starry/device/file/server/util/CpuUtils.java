package com.czur.starry.device.file.server.util;

import com.czur.czurutils.log.CZURLogUtilsKt;

import java.io.IOException;
import java.io.RandomAccessFile;

/**
 * author : Wang<PERSON>ao
 * time   :2023/12/05
 */

public class CpuUtils {

    private static final String TAG = "CpuUtils";

    public static boolean CpuUsageIsHigh() {
        float cpuUsage = 0f;
        RandomAccessFile reader = null;
        try {
            reader = new RandomAccessFile("/proc/stat", "r");
            String load = reader.readLine();
            String[] tokens = load.split("\\s+");


            long totalCpuTime1 = 0;
            long idleCpuTime1 = Long.parseLong(tokens[4]); // 这里 tokens[4] 对应的是第一个闲置时间

            for (int i = 1; i < tokens.length; i++) {
                totalCpuTime1 += Long.parseLong(tokens[i]);
            }

            Thread.sleep(300);

            reader.seek(0);
            load = reader.readLine();
            tokens = load.split("\\s+");

            long totalCpuTime2 = 0;
            long idleCpuTime2 = Long.parseLong(tokens[4]);

            for (int i = 1; i < tokens.length; i++) {
                totalCpuTime2 += Long.parseLong(tokens[i]);
            }

            long totalCpuTimeDiff = totalCpuTime2 - totalCpuTime1;
            long idleCpuTimeDiff = idleCpuTime2 - idleCpuTime1;

            cpuUsage = (totalCpuTimeDiff - idleCpuTimeDiff) * 100f / totalCpuTimeDiff;
            CZURLogUtilsKt.logTagD(TAG, "========== cpuUsage: " + cpuUsage);
            // 将读取到的数据进行解析，获取CPU使用时间
        } catch (IOException ex) {
            CZURLogUtilsKt.logTagE(TAG, new String[]{"cpuUsage: error"}, ex);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return cpuUsage >= 90;
    }

}
