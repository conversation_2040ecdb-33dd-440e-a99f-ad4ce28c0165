package com.czur.starry.device.file.manager.transfer.del

import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileManager

/**
 * Created by 陈丰尧 on 2021/9/8
 */

/**
 * 删除器
 */
interface IDeleter {
    /**
     * 执行删除操作
     */
    suspend fun del(src: FileEntity): Boolean
}

/**
 * 本地文件删除器
 */
class LocalDeleter : IDeleter {
    override suspend fun del(src: FileEntity): Boolean {
        val fileAccess = FileManager.createFileAccess(AccessType.LOCAL)
        return fileAccess.delFiles(listOf(src)) == FileAccess.Result.SUCCESS
    }
}