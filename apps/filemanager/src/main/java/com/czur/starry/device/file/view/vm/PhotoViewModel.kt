package com.czur.starry.device.file.view.vm

import android.app.Application
import android.graphics.BitmapFactory
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.bean.PhotoInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import java.io.File

/**
 * Created by 陈丰尧 on 2021/9/23
 */
class PhotoViewModel(application: Application) : BasePAVViewModel(application) {
    /**
     * 图片的本地路径
     * 如果为空字符串, 表示图片正在下载, 此时应该展示Loading画面
     */
    val photoPathInfoLive: LiveData<PhotoInfo> = MutableLiveData()
    private var photoPathInfo: PhotoInfo by LiveDataDelegate(photoPathInfoLive)
    val photoPathInfoFlow = MutableSharedFlow<PhotoInfo>()

    private var loadPhotoInfoJob: Job? = null

    companion object {
        private const val TAG = "PhotoViewModel"

        private const val KEY_CACHE = "key_cache"
    }


    /**
     * 加载当前的选中的图片
     */
    fun loadPhotoPath() {
        logTagD(TAG, "loadPhotoPath")
        val fileEntity = currentEntity
        loadLocal(fileEntity)
    }

    fun loadPhotoPath(index: Int) {
        val fileEntity = fileEntities[index]
        loadLocal(fileEntity)
    }

    fun loadNeighborPhotoPath(index: Int) {
        if (index > 0) {
            val frontIndex = index - 1
            loadPhotoPath(frontIndex)
        }

        if (index < fileEntities.size - 1) {
            val nextIndex = index + 1
            loadPhotoPath(nextIndex)
        }


    }

    /**
     * 加载本地图片
     */
    private fun loadLocal(fileEntity: FileEntity) {
        if (fileEntity.containsTag(KEY_CACHE)) {
            // 有本地缓存,检查本地缓存文件还在不在
            val cache: PhotoInfo = fileEntity.getTag(KEY_CACHE)
            if (File(cache.filePath).exists()) {
                // 使用本地缓存的文件
                logTagV(TAG, "有本地缓存")
                photoPathInfo = cache
                launch(Dispatchers.Main) {
                    photoPathInfoFlow.emit(cache)
                }
                return
            }
        }
        loadPhotoInfo(fileEntity, fileEntity.absPath)
    }

    private fun loadPhotoInfo(fileEntity: FileEntity, filePath: String) {

//        loadPhotoInfoJob?.cancel()
//        loadPhotoInfoJob =
        launch(Dispatchers.IO) {
            logTagD(TAG, "loadPhotoInfo")
            // 获取图片范围
            val tmpOptions = BitmapFactory.Options()
            tmpOptions.inJustDecodeBounds = true
            BitmapFactory.decodeFile(filePath, tmpOptions)
            logTagD(TAG, "更新图片宽高:${filePath}")
            logTagV(TAG, "width:${tmpOptions.outWidth} height:${tmpOptions.outHeight}")
            val file = File(filePath)
            val photoInfo =
                PhotoInfo(
                    filePath,
                    tmpOptions.outWidth,
                    tmpOptions.outHeight,
                    file.length(),
                    fileEntity.absPath
                )
            fileEntity.putTag(KEY_CACHE, photoInfo)
            if (photoInfo.entityPath == currentEntity.absPath) {
                logTagD(TAG, "更新PhotoInfo:${photoInfo}")
                photoPathInfo = photoInfo
                launch(Dispatchers.Main) {
                    photoPathInfoFlow.emit(photoInfo)
                }
            } else {
                logTagI(TAG, "图片已经改变, 不做修改-entityPath:${photoInfo.entityPath}")
                logTagI(TAG, "图片已经改变, 不做修改-absPath:${currentEntity.absPath}")
            }
        }

    }

    override fun onSwitchPre() {
        loadPhotoPath()
    }

    override fun onSwitchNext() {
        loadPhotoPath()
    }

    // 不需要处理
    override fun onNoFileToSwitch() {}

}