package com.czur.starry.device.file.view.encryption.pwd

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.file.databinding.FragmentEncryptionPwdBinding
import com.czur.starry.device.file.view.encryption.LocalEncryptionViewModel

/**
 * Created by 陈丰尧 on 2024/12/10
 */
private const val FRAGMENT_TAG_PWD_CREATE = "PwdCreateFragment"
private const val FRAGMENT_TAG_PWD_ENTER = "PwdEnterFragment"
class EncryptionPwdFragment : CZViewBindingFragment<FragmentEncryptionPwdBinding>() {
    private val localEncryptionViewModel: LocalEncryptionViewModel by activityViewModels()

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(localEncryptionViewModel.hasEncryptionInfoFlow) {
            val targetTag = if (it) FRAGMENT_TAG_PWD_ENTER else FRAGMENT_TAG_PWD_CREATE
            val currentFragment = childFragmentManager.findFragmentByTag(targetTag)
            if (currentFragment == null) {
                childFragmentManager.commit {
                    if (it) {
                        replace(binding.container.id, PwdEnterFragment(), FRAGMENT_TAG_PWD_ENTER)
                    } else {
                        replace(binding.container.id, PwdCreateFragment(), FRAGMENT_TAG_PWD_CREATE)
                    }
                }
            }
        }

    }
}