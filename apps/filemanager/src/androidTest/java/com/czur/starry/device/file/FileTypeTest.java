package com.czur.starry.device.file;

import android.content.Context;

import androidx.test.platform.app.InstrumentationRegistry;

import com.czur.starry.device.file.filelib.FileType;
import com.czur.starry.device.file.utils.FileTypeUtilKt;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.io.File;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;

@RunWith(Parameterized.class)
public class FileTypeTest {

    // 测试数据集
    @Parameterized.Parameters
    public static List<?> data(){
        return Arrays.asList(new Object[][]{
                {"pic.png", FileType.IMAGE},
                {"pic.jpeg", FileType.IMAGE},
                {"pic.gif", FileType.IMAGE},
                {"pic.jpg", FileType.IMAGE},
                {"pic.bmp", FileType.IMAGE},
                {"word1.docx", FileType.DOC},
                {"word1.doc", FileType.DOC},
                {"ppt1.ppt",FileType.PPT},
                {"ppt1.pptx",FileType.PPT},
                {"excel1.xlsx",FileType.EXCEL},
                {"excel1.xls",FileType.EXCEL},
                {"excel1.XLSX",FileType.EXCEL},
                {"otherFile",FileType.OTHER},
                {"pdf.pdf",FileType.PDF},
                {"",FileType.FOLDER},
                {"text.txt",FileType.OTHER},
                {"html.html",FileType.OTHER},
        });
    }

    private final String filePath;
    private final FileType expected;
    private File rootDir;


    public FileTypeTest(String input, FileType expected) {
        this.filePath = input;
        this.expected = expected;
    }

    @Before
    public void init(){
        // 根路径选择报名/file
        Context targetContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        rootDir = targetContext.getFilesDir();
    }

    /**
     * 测试文件类型判断是否正确
     */
    @Test
    public void testFileType(){
        File file = new File(rootDir,filePath);
        // 测试代码
        assertEquals(expected, FileTypeUtilKt.getFileType(file));
    }


}
