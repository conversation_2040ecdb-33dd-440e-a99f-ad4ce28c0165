package com.czur.starry.device.file

import com.czur.starry.device.file.view.encryption.pwd.PwdEnterViewModel
import org.junit.Test
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

/**
 * Created by 陈丰尧 on 2025/1/7
 */
class EncryptionTest {
    @Test
    fun testFormatLockTime() {
        val model = PwdEnterViewModel()
        val testMap = mapOf(
            24.hours to "24小时",
            1.hours + 1.minutes to "2小时",
            1.hours to "1小时",
            (1.hours - 30.seconds) to "1小时",
            59.minutes to "59分钟",
            58.minutes + 1.seconds to "59分钟",
            1.minutes to "1分钟",
            59.seconds to "1分钟",
        )

        testMap.forEach { (k, v) ->
            val formatTimeStr = model.formatLockTime(k.inWholeMilliseconds)
            assert(formatTimeStr == v) { "formatLockTime failed, $k -> $v, but get $formatTimeStr" }
        }
    }
}