package com.czur.starry.device.startupsettings

import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.listener.StarryApp
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 2021/9/2
 */
class StartUpApp : StarryApp() {
    companion object {
        var instance: StartUpApp by Delegates.notNull()
        private const val TAG = "StartUpApp"
    }

    val eventFlow = MutableSharedFlow<StartupEvent>()

    enum class StartupEvent {
        STARTUP_FINISH
    }

    override fun onCreate() {
        super.onCreate()
        logTagI(TAG,"StartUpApp 启动:${this}")
        instance = this
    }
}