package com.czur.starry.device.startupsettings

import android.content.Context
import android.content.Intent
import android.os.storage.StorageManager
import android.os.storage.VolumeInfo
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.view.getVolumes
import com.czur.starry.device.startupsettings.FactoryTestHelper.USBBootType.DEVICE_TEST
import com.czur.starry.device.startupsettings.FactoryTestHelper.USBBootType.NONE
import com.czur.starry.device.startupsettings.FactoryTestHelper.USBBootType.STRESS_TEST
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

object FactoryTestHelper {
    private const val PKG_NAME_DEVICE_TEST = "com.DeviceTest"
    private const val CLZ_NAME_DEVICE_TEST = "com.DeviceTest.DeviceTest"
    private const val PKG_NAME_DEVICE_TEST_14 = "com.czur.starry.devicetest"
    private const val CLZ_NAME_DEVICE_TEST_14 = "com.czur.starry.devicetest.BootActivity"
    private const val PKG_NAME_STRESS_TEST = "com.cghs.stresstest"
    private const val CLZ_NAME_STRESS_TEST = "com.cghs.stresstest.StressTestActivity"
    private const val DEVICE_TEST_CONTENT = "000.="
    private const val STRESS_TEST_CONTENT = "83991906="
    private const val TAG = "FactoryTestHelper"

    private const val DEVICE_TEST_FILE_NAME = "czur_deviceTest.txt"
    private const val STRESS_TEST_FILE_NAME = "czur_stressTest.txt"

    private val storageManager: StorageManager by lazy {
        globalAppCtx.getSystemService(Context.STORAGE_SERVICE) as StorageManager
    }

    enum class USBBootType {
        DEVICE_TEST,
        STRESS_TEST,
        NONE
    }

    suspend fun getUSBBootType(): USBBootType {
        val volumes: MutableList<VolumeInfo> = storageManager.getVolumes()
        volumes.forEach {
            if (it.type == VolumeInfo.TYPE_PUBLIC) {
                val disk = it.getDisk()
                if (disk != null && disk.isUsb) {
                    val str = it.path
                    if (str != null) {
                        if (doTestReadAndWrite(it.path)) {
                            logTagD(TAG, "testReadAndWrite Success" + it.path)
                            val device = getAvailableFile(File(it.path, DEVICE_TEST_FILE_NAME))
                            logTagV(TAG, "$DEVICE_TEST_FILE_NAME available:$device")
                            if (device) {
                                return DEVICE_TEST
                            }

                            val stress = getAvailableFile(File(it.path, STRESS_TEST_FILE_NAME))
                            logTagV(TAG, "$STRESS_TEST_FILE_NAME available:$stress")
                            if (stress) {
                                return STRESS_TEST
                            }
                        }
                    }
                }
            }
        }

        logTagD(TAG, "不符合启动厂测/老化程序条件")
        return NONE

    }

    /**
     * 启动设备测试
     */
    fun bootUSBTestApp(context: Context, usbBootType: USBBootType) {
        logTagI(TAG, "bootUSBTest: $usbBootType")

        val intent = when (usbBootType) {
            DEVICE_TEST -> Intent().apply {
                when (Constants.starryHWInfo.series.model) {
                    StarryModel.Q1Model.Q1, StarryModel.Q1Model.Q1S, StarryModel.Q1Model.Q1Pro,
                    StarryModel.Q1Model.Q1SPlus, StarryModel.Q1Model.Q1SPro -> setClassName(
                        PKG_NAME_DEVICE_TEST,
                        CLZ_NAME_DEVICE_TEST
                    )

                    else -> setClassName(
                        PKG_NAME_DEVICE_TEST_14,
                        CLZ_NAME_DEVICE_TEST_14
                    )
                }
            }

            STRESS_TEST -> Intent().apply {
                setClassName(
                    PKG_NAME_STRESS_TEST,
                    CLZ_NAME_STRESS_TEST
                )
            }

            NONE -> null
        }?.apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }

        intent?.let {
            context.startActivity(it)
        }
    }


    private suspend fun doTestReadAndWrite(usbPath: String): Boolean = withContext(Dispatchers.IO) {
        val directory = File(usbPath,"test")
        try {
            if (!directory.isDirectory) {
                logTagW(TAG, "${directory.absolutePath} is not a directory or not exists")
                if (!directory.mkdirs()) {
                    return@withContext false
                } else {
                    logTagD(TAG, "===directory.mkdirs()=")
                    return@withContext true
                }
            }
            val f = File(directory, "usb.txt")

            // Remove stale file if any
            if (f.exists()) {
                f.delete()
            }
            if (!f.createNewFile()) {
                return@withContext false
            } else {
                logTagD(TAG, "===f.delete()=")
                f.delete()
                return@withContext true
            }

        } catch (e: Exception) {
            e.printStackTrace()
            return@withContext false
        }
    }

    private suspend fun getAvailableFile(file: File): Boolean {
        var result = ""
        if (file.exists()) {
            try {
                result = withContext(Dispatchers.IO) {
                    file.readText().trim { it <= ' ' }
                }
                logTagD(TAG, "${file.name}内容:" + result)
                return result == DEVICE_TEST_CONTENT || result == STRESS_TEST_CONTENT
            } catch (e: IOException) {
                logTagE(TAG, "IO Exception", tr = e)
            }
        } else {
            logTagW(TAG, "file not exists:${file.absolutePath}")
        }
        return false
    }


}