package com.czur.starry.device.startupsettings

import android.content.Intent
import android.os.Bundle
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StudioSeries

/**
 * Created by 陈丰尧 on 2/24/21
 */
private const val PKG_PERSONAL_CENTER = "com.czur.starry.device.personalcenter"
private const val PKG_SETTINGS = "com.czur.starry.device.settings"

const val STEP_WELCOME = 200

const val STEP_LANGUAGE = 201

const val STEP_WIFI = 202
const val STEP_BEGIN = 203

const val STEP_TIME_ZONE = 211  // 修改时区

const val STEP_DATA_RECOVERY = 210  // 备份数据

const val STEP_NONE = -1
const val STEP_FLOW = -2 //代表这个流程的下一步可能是多个分支

const val STEP_FIRST_FLOW = STEP_WELCOME
const val STEP_FINISH_FLOW = 300 //初期设定流程结束

private val stepFlow by lazy { getSteps() }

// 是否忽略语言选择
private val ignoreLanguage by lazy {
    Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland
}
// 是否忽略数据恢复
private val ignoreDataRecovery by lazy {
    Constants.starryHWInfo.series != Q1Series
}

private val normalInitStep = listOf(
    object : Step(
        STEP_LANGUAGE,
        when (Constants.starryHWInfo.series) {
            Q1Series -> STEP_DATA_RECOVERY
            Q2Series -> STEP_TIME_ZONE      // Q2系列不显示数据恢复
            StudioSeries -> STEP_TIME_ZONE  // Studio系列不显示数据恢复
        },
        STEP_NONE,
        // 国内设备不显示语言选择(于洋24.12.7)
        ignore = ignoreLanguage
    ) {
        override fun getIntent() = Intent().apply {
            setClassName(
                PKG_SETTINGS,
                "${PKG_SETTINGS}.startup.StartUpLanguageActivity"
            )
        }
    },
    object : Step(
        STEP_DATA_RECOVERY, STEP_TIME_ZONE, STEP_NONE,
        ignore = ignoreDataRecovery
    ) {
        override fun getIntent() = Intent().apply {
            setClassName(
                PKG_SETTINGS,
                "${PKG_SETTINGS}.startup.StartUpDataRecoveryActivity"
            )
        }
    },

    object : Step(STEP_TIME_ZONE, STEP_WIFI, STEP_NONE) {
        override fun getIntent() = Intent().apply {
            setClassName(
                PKG_SETTINGS,
                "${PKG_SETTINGS}.startup.StartUpTimeZoneActivity"
            )

            putExtra("isFirst", ignoreLanguage && ignoreDataRecovery)
        }
    },

    //wifi 设定
    object : Step(STEP_WIFI, STEP_BEGIN, STEP_FINISH_FLOW) {
        override fun getIntent() = Intent().apply {
            setClassName(
                PKG_SETTINGS,
                "${PKG_SETTINGS}.startup.StartUpWifiActivity"
            )
        }

    },
    // 用户信息开始开始画面
    object : Step(STEP_BEGIN, STEP_FINISH_FLOW, STEP_FINISH_FLOW) {
        override fun getIntent() = Intent().apply {
            setClassName(
                PKG_PERSONAL_CENTER,
                "${PKG_PERSONAL_CENTER}.begin.UserBeginNavActivity"
            )
        }

    }
)

private val armyInitStep = listOf(
    object : Step(STEP_LANGUAGE, STEP_FINISH_FLOW, STEP_FINISH_FLOW) {
        override fun getIntent() = Intent().apply {
            setClassName(
                PKG_SETTINGS,
                "${PKG_SETTINGS}.startup.StartUpLanguageActivity"
            )
        }
    }
)

private fun getSteps(): List<Step> {
    val step = if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
        return armyInitStep
    } else {
        normalInitStep
    }.filterNot {
        it.ignore   // 过滤掉忽略的
    }
    return step
}

fun findStep(stepCode: Int): Step {
    return stepFlow.find { step ->
        step.requestCode == stepCode
    } ?: stepFlow[0]
}

abstract class Step(
    val requestCode: Int,
    val nextCode: Int = STEP_FINISH_FLOW,
    val skipCode: Int = STEP_FINISH_FLOW,
    val ignore: Boolean = false,     // 是否忽略掉
) {
    var data: Bundle? = null
    abstract fun getIntent(): Intent
    open fun resultFlows(): Map<Int, Int> = mapOf()
    open fun inHistory() = true
}