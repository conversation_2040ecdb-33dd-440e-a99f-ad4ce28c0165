<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">スタートアップ設定</string>
    <string name="welcome">ようこそ</string>
    <string name="str_startup_finish_hint">システム構成中です。電源をオフにしないでください。</string>
    <string name="title_touch_pad_guide">ドラッグして設定を完了します</string>
    <string name="str_touch_pad_guide_hint">目標をドラッグ：ダブルクリックしてから指をスライドして目標をドラッグします。（ダブルクリックの後に指を離さないでください。）</string>
    <string name="str_touch_pad_guide_hint_v2">目標をドラッグ：目標をタップしてスライドさせ、ドラッグ/移動します。（目標をタップした後に指を離さないでください。）</string>
    <string name="str_touch_pad_guide_slide_hint">右側へドラッグします。</string>
    <string name="title_connect_touch_pad">TouchBoardを接続してください。</string>
    <string name="connect_touch_pad_hint">1.電源ボタンをオンに切り替えます。 \n2. 左上の[TouchControl]ボタンを3秒間押し続けます。\n     ライトが青色に点滅したら、TouchBoardを充電ドックへ戻します。 \n3. 画面に\"TouchBoard is connected!\"（TouchBoardが接続済）と表示されます。.</string>
    <string name="connect_touch_pad_hint_mainland">1.TouchBoardの電源ボタン（背面）をオンに切り替えます。\n2.左上の[TouchControl]ボタンを5秒間押し続けます。ライトが青色に点滅したら、TouchBoardを充電位置へ戻します。\n3.StarryHubにポップアップウィンドウが出現し、「Successfully Paired!」（正常にペアリングされました）と表示されるまでお待ちください。</string>
    <string name="startup_next_step">次</string>
</resources>
