<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Starteinstellungen</string>
    <string name="welcome">Willkommen</string>
    <string name="str_startup_finish_hint">System wird konfiguriert. Gerät nicht ausschalten.</string>
    <string name="title_touch_pad_guide">Zum Einstellen ziehen</string>
    <string name="str_touch_pad_guide_hint">Ziel ziehen: Ziel zum Z<PERSON>hen doppeltippen (beim zweiten Tippen Finger auf dem Display lassen)</string>
    <string name="str_touch_pad_guide_hint_v2">Ziel ziehen: Zum Ziehen lange auf Ziel tippen (beim Tippen Finger auf dem Display lassen)</string>
    <string name="str_touch_pad_guide_slide_hint">Nach rechts ziehen.</string>
    <string name="title_connect_touch_pad">Schließen Sie das TouchBoard an.</string>
    <string name="connect_touch_pad_hint">1. Schalten Sie den Hauptschalter ein. \n2. Tippen Sie drei Sekunden lang oben links auf [TouchControl]. \nWenn die blaue LED blinkt, legen Sie das TouchBoard in die Ladestation ein. \n3. Warten Sie bis am Bildschirm die Meldung angezeigt wird, dass das TouchBoard verbunden ist.</string>
    <string name="startup_next_step">Weiter</string>
    <string name="connect_touch_pad_hint_mainland">1. Schalten Sie das TouchBoard ein, indem Sie den Hauptschalter auf der Rückseite in Position ON bringen. \n2. Halten Sie die [TouchControl]-Taste oben links am Gerät 5 Sekunden lang gedrückt. Wenn die LED blau blinkt, versetzen Sie das TouchBoard zurück in die Ladeposition. \n3. Nach kurzer Zeit wird auf dem Projektorbildschirm des StarryHub ein Popup-Fenster mit dem Hinweis „Pairing erfolgreich abgeschlossen“ angezeigt.</string>
</resources>
