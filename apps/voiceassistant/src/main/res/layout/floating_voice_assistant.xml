<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <com.czur.starry.device.voiceassistant.widget.AnimateExpandingView
        android:id="@+id/backgroundView"
        android:layout_width="400px"
        android:layout_height="108px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.voiceassistant.widget.AnimatedImageBallView
        android:id="@+id/ballView"
        android:layout_width="140px"
        android:layout_height="140px"
        android:layout_marginLeft="-16px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/backgroundView"
        app:layout_constraintLeft_toLeftOf="@+id/backgroundView"
        app:layout_constraintTop_toTopOf="@+id/backgroundView" />

    <com.czur.starry.device.voiceassistant.widget.AnimatedImageCloudsView
        android:id="@+id/cloudsView"
        android:layout_width="140px"
        android:layout_height="140px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ballView"
        app:layout_constraintLeft_toLeftOf="@+id/ballView"
        app:layout_constraintRight_toRightOf="@+id/ballView"
        app:layout_constraintTop_toTopOf="@id/ballView" />

    <com.czur.starry.device.voiceassistant.widget.ChatTextView
        android:id="@+id/sunView"
        android:layout_width="240px"
        android:layout_height="wrap_content"
        android:layout_marginEnd="15px"
        android:gravity="center_vertical"
        android:lineSpacingExtra="6px"
        android:lines="2"
        includeFontPadding="false"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/backgroundView"
        app:layout_constraintRight_toRightOf="@id/backgroundView"
        app:layout_constraintTop_toTopOf="@id/backgroundView" />
</androidx.constraintlayout.widget.ConstraintLayout>