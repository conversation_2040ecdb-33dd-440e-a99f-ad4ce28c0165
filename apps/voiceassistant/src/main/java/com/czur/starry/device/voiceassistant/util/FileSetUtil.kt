package com.czur.starry.device.voiceassistant.util

import android.app.ActivityManager
import android.app.Instrumentation
import android.content.Context
import android.content.Intent
import android.view.KeyEvent
import com.czer.starry.device.meetlib.MeetingHandler.localMeetingRecording
import com.czer.starry.device.meetlib.MeetingHandler.localRecordMode
import com.czer.starry.device.meetlib.MeetingHandler.stopLocalRecordState
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_TRANSCRIPTION
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.file.filelib.FileHandlerLive.currentSortTypeState
import com.czur.starry.device.file.filelib.FileHandlerLive.fileMediaState
import com.czur.starry.device.file.filelib.FileHandlerLive.fileMeetingEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareEnable
import com.czur.starry.device.voiceassistant.helper.Adjustable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 *  author : WangHao
 *  time   :2025/02/27
 */


object FileSetUtil {
    private const val TAG = "FileUtil"

    private const val CLASS_NAME_FILE =
        "com.czur.starry.device.file.view.activity.FileMainPageActivity"
    private const val CLASS_NAME_FILE_PHOTO =
        "com.czur.starry.device.file.view.activity.PhotoPagerActivity"
    private const val CLASS_NAME_FILE_MEDIA =
        "com.czur.starry.device.file.view.activity.MediaActivity"
    private const val CLASS_NAME_LOCAL_MEETING =
        "com.czur.starry.device.localmeetingrecord.MainActivity"
    private val activityManager by lazy { CZURAtyManager.appContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager }
    private val instrumentation by lazy {
        Instrumentation()
    }

    enum class SortType {
        TYPE_NAME_ASC, // 文件名升序
        TYPE_NAME_DESC, // 文件名降序
        TYPE_TIME_ASC, // 修改时间升序
        TYPE_TIME_DESC; // 修改时间降序
    }

    enum class FileMediaState {
        PAUSE,
        PLAY,
        FORWARD,
        BACKWARD,
        MUTE
    }

    enum class RecordModel {
        AUDIO_MODE,
        SCREEN_MODE,
        VIDEO_MODE,
        CAMERA_MODE
    }

    /**
     * 文件传输开关
     */
    fun setFileTransfer(isOn: Boolean) {
        fileShareEnable = isOn
    }

    /**
     * 文件传输验证码开关
     */
    fun setFileTransferCode(isOn: Boolean) {
        fileShareCodeEnable = isOn
    }

    /**
     * 下载会议录制文件开关
     */
    fun setFileTransferDownload(isOn: Boolean) {
        fileMeetingEnable = isOn
    }

    /**
     * 图片大小
     */
    fun updatePicSize(paramsRes: Adjustable) {
        if (isCurrentActivity(CLASS_NAME_FILE_PHOTO)) {
            if (paramsRes == Adjustable.ADD) {
                MainScope().launch(Dispatchers.IO) {
                    instrumentation.sendKeyDownUpSync(KeyEvent.KEYCODE_ZOOM_IN)
                }
            } else {
                MainScope().launch(Dispatchers.IO) {
                    instrumentation.sendKeyDownUpSync(KeyEvent.KEYCODE_ZOOM_OUT)
                }
            }
        }
    }

    /**
     * 文件时间排序
     */
    fun updateFileSort() {
        if (isCurrentActivity(CLASS_NAME_FILE)) {
            currentSortTypeState = SortType.TYPE_TIME_ASC.ordinal
        } else {

        }
    }

    /**
     * 停止视频播放
     */
    fun stopVideo() {
        if (isCurrentActivity(CLASS_NAME_FILE_MEDIA)) {
            fileMediaState = FileMediaState.PAUSE.name
            resetVideo()
        }
    }

    /**
     * 播放视频
     */
    fun playVideo() {
        if (isCurrentActivity(CLASS_NAME_FILE_MEDIA)) {
            fileMediaState = FileMediaState.PLAY.name
            resetVideo()
        }
    }

    /**
     * 快进
     */
    fun videoFastForward() {
        if (isCurrentActivity(CLASS_NAME_FILE_MEDIA)) {
            fileMediaState = FileMediaState.FORWARD.name
            resetVideo()
        }
    }

    /**
     * 快退
     */
    fun videoFastBackward() {
        if (isCurrentActivity(CLASS_NAME_FILE_MEDIA)) {
            fileMediaState = FileMediaState.BACKWARD.name
            resetVideo()
        }
    }

    private fun resetVideo() {
        MainScope().launch {
            delay(ONE_SECOND)
            fileMediaState = "null"
        }
    }

    private fun stopLocalRecord() {
        MainScope().launch {
            try {
                stopLocalRecordState = true
                delay(ONE_SECOND)
            } finally {
                stopLocalRecordState = false
            }
        }
    }

    /**
     * 停止录音
     */
    fun stopAudioRecording() {
        if (localMeetingRecording && localRecordMode == RecordModel.AUDIO_MODE.name) {
            stopLocalRecord()
        }
    }

    /**
     * 停止录屏
     */
    fun stopScreenRecording() {
        if (localMeetingRecording && localRecordMode == RecordModel.SCREEN_MODE.name) {
            stopLocalRecord()
        }
    }

    /**
     * 停止录像
     */
    fun stopVideoRecording() {
        if (localMeetingRecording && localRecordMode == RecordModel.VIDEO_MODE.name) {
            stopLocalRecord()
        }

    }
    /**
     * AI互译字幕
     */
    fun setTranscription(open: Boolean) {
        logTagD(TAG, "============setTranscription open:$open")
        val intent = Intent().apply {
            action = ACTION_BOOT_TRANSCRIPTION
            putExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, open)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
        CZURAtyManager.appContext.startActivity(intent)
    }

    fun bootTranscription() {
        val intent = Intent().apply {
            action = ACTION_BOOT_TRANSCRIPTION
        }.newTask()
        CZURAtyManager.appContext.startActivity(intent)
    }
    /**
     * 判断当前Activity
     */
    private fun isCurrentActivity(className: String): Boolean {
        val tasks = activityManager.getRunningTasks(Int.MAX_VALUE) ?: return false
        if (tasks.isNotEmpty()) {
            for (task in tasks) {
                if (task.topActivity?.className == className) {
                    logTagD(TAG, "当前Activity: $className")
                    return true
                }
            }
        }
        return false
    }
}