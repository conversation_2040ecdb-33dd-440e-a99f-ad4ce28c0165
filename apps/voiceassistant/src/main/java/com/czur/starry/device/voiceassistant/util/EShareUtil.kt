package com.czur.starry.device.voiceassistant.util

import android.content.Intent
import com.czur.czurutils.global.globalAppCtx
import com.czur.starry.device.baselib.base.BaseActivity.Companion.ESHARE_SETTING_ACTION
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.CZURAtyManager.appContext
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.sharescreen.esharelib.util.showDeviceNameAlertWindow
import com.czur.starry.device.sharescreen.esharelib.util.stopDeviceNameAlertWindow
import com.eshare.serverlibrary.api.EShareServerSDK
import com.eshare.serverlibrary.api.IEShareServerSDK
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 *  author : WangHao
 *  time   :2025/02/27
 *  无线投屏设置
 */

object EShareUtil {
    private val eShareServerSDK: IEShareServerSDK = EShareServerSDK.getSingleton(globalAppCtx)

    /**
     * Miracast开关
     */
    fun setMiracastEnable(enable: Boolean) {
        eShareServerSDK.miracastEnable = enable
    }

    /**
     * Airplay开关
     */
    fun setAirplayEnable(enable: Boolean) {
        eShareServerSDK.isClientAirPlayEnable = enable
    }

    /**
     * DLNA开关
     */
    fun setDLNAEnable(enable: Boolean) {
        eShareServerSDK.isDlnaEnable = enable
    }

    /**
     * 多屏同投
     */
    fun setMultiScreen(targetSize: Int) {
        when (targetSize) {
            1, 2, 4 -> {
                eShareServerSDK.multiScreenMode = targetSize
                sendBroadCastToShareScreen()
            }

            9 -> {
                if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
                    eShareServerSDK.multiScreenMode = targetSize
                    sendBroadCastToShareScreen()
                }
            }
        }

    }

    /**
     * HDMI混投
     */
    fun setHdmiMuxStatus(open: Boolean) {
        setBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, open)
        sendBroadCastToShareScreen()
    }

    /**
     * 设备名悬浮窗
     */
    fun setDeviceNameShow(show: Boolean) {
        MainScope().launch {
            SettingUtil.ShareScreenSetting.setEnableNameAlertWin(show)
            eShareServerSDK.isShowPinWindow = show
            if (show) {
                showDeviceNameAlertWindow(appContext)
            } else {
                stopDeviceNameAlertWindow(appContext)
            }
            sendBroadCastToShareScreen()
        }

    }

    /**
     * 智能满屏
     */
    fun setSmartFullScreen(open: Boolean) {
        eShareServerSDK.castFullscreen = if (open) 1 else 0
        sendBroadCastToShareScreen()
    }

    /**
     * 投屏询问
     */
    fun setScreenShareAsk(open: Boolean) {
        SettingUtil.ShareScreenSetting.setAskBeforeCast(open)
        sendBroadCastToShareScreen()
    }

    private fun sendBroadCastToShareScreen() {
        val intent = Intent(ESHARE_SETTING_ACTION).apply {
            setPackage("com.czur.starry.device.sharescreen")
        }
        CZURAtyManager.appContext.sendBroadcast(intent)
    }
}