package com.czur.starry.device.voiceassistant.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import com.android.internal.R.attr.lineHeight
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/22
 *  desc   :展开动画View
 */

private const val TAG = "AnimateExpandingView"

class AnimateExpandingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    //尺寸
    private val width = 400f
    private val height = 108f
    private val cornerRadius = 80f
    private val lineHeight = 1.5f // 线条的高度
    private var animatedWidth = 0f
    private var animatedHeight = lineHeight

    //动画时长
    private val widthExpansionDuration = 400L // 点到线的动画时长
    private val heightExpansionDuration = 400L // 线到完整矩形的动画时长
    private val colorChangerDuration = 900L // 线到完整矩形的动画时长

    //颜色
    private val animateStartColor = 0xFF00F7FF.toInt()
    private val animateEndColor = 0xD919276B.toInt()
    private var currentColor = animateStartColor

    // 预初始化动画对象
    private lateinit var widthAnimator: ValueAnimator
    private lateinit var heightAnimator: ValueAnimator
    private lateinit var colorAnimator: ValueAnimator
    private lateinit var reverseWidthAnimator: ValueAnimator
    private lateinit var reverseHeightAnimator: ValueAnimator
    private lateinit var reverseColorAnimator: ValueAnimator

    // 复用Rect对象
    private val animRect = RectF()

    init {
        paint.color = animateStartColor
        // 启用硬件加速层
        setLayerType(LAYER_TYPE_HARDWARE, null)
        initAnimations()
    }

    private fun initAnimations() {
        // 正向动画初始化
        widthAnimator = ValueAnimator.ofFloat(0f, width).apply {
            duration = widthExpansionDuration
            interpolator = FastOutSlowInInterpolator()
            addUpdateListener {
                animatedWidth = it.animatedValue as Float
                animatedHeight = lineHeight
                postInvalidateOnAnimation()
            }
        }

        heightAnimator = ValueAnimator.ofFloat(lineHeight, height).apply {
            duration = heightExpansionDuration
            interpolator = FastOutSlowInInterpolator()
            addUpdateListener {
                animatedHeight = it.animatedValue as Float
                postInvalidateOnAnimation()
            }
        }

        colorAnimator =
            ValueAnimator.ofObject(ArgbEvaluator(), animateStartColor, animateEndColor).apply {
                duration = colorChangerDuration
                addUpdateListener {
                    currentColor = it.animatedValue as Int
                    paint.color = currentColor
                }
            }

        // 反向动画初始化
        reverseWidthAnimator = ValueAnimator.ofFloat(width, 0f).apply {
            duration = widthExpansionDuration
            interpolator = FastOutSlowInInterpolator()
            addUpdateListener {
                animatedWidth = it.animatedValue as Float
                postInvalidateOnAnimation()
            }
        }

        reverseHeightAnimator = ValueAnimator.ofFloat(height, lineHeight).apply {
            duration = heightExpansionDuration
            interpolator = FastOutSlowInInterpolator()
            addUpdateListener {
                animatedHeight = it.animatedValue as Float
                postInvalidateOnAnimation()
            }
        }

        reverseColorAnimator =
            ValueAnimator.ofObject(ArgbEvaluator(), animateEndColor, animateStartColor).apply {
                duration = colorChangerDuration
                addUpdateListener {
                    currentColor = it.animatedValue as Int
                    paint.color = currentColor
                }
            }

        // 动画链绑定
        widthAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                heightAnimator.start()
            }
        })

        reverseHeightAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                reverseWidthAnimator.start()
            }
        })
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        animRect.set(
            (width - animatedWidth) / 2,
            (height - animatedHeight) / 2,
            (width + animatedWidth) / 2,
            (height + animatedHeight) / 2
        )
        canvas.drawRoundRect(animRect, cornerRadius, cornerRadius, paint)
    }

    fun startAnimation() {
        // 停止所有正在进行的动画
        cancelAllAnimations()

        // 重置初始状态
        animatedWidth = 0f
        animatedHeight = lineHeight
        currentColor = animateStartColor
        paint.color = currentColor
        // 启动动画组
        widthAnimator.start()
        colorAnimator.start()
    }

    fun stopAnimation() {
        // 反向执行动画
        reverseHeightAnimator.start()
        reverseColorAnimator.start()
    }

    private fun cancelAllAnimations() {
        widthAnimator.cancel()
        heightAnimator.cancel()
        colorAnimator.cancel()
        reverseWidthAnimator.cancel()
        reverseHeightAnimator.cancel()
        reverseColorAnimator.cancel()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cancelAllAnimations()
    }
}