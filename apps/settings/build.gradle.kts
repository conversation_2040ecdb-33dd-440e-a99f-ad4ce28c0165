@Suppress("DSL_SCOPE_VIOLATION") // TODO: Remove once KTIJ-19369 is fixed
plugins {
    alias(libs.plugins.application)
    alias(libs.plugins.kotlinAndroid)
    id("kotlin-parcelize")
    id("kotlinx-serialization")
}

private val pkgName = "com.czur.starry.device.settings"
private val apkName = "CZSettings"
android.buildFeatures.buildConfig = true
android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        applicationId = namespace
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()

        versionCode = libs.versions.appVersionCode.get().toInt()
        versionName = libs.versions.appVersionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        renderscriptSupportModeEnabled = true

        setFlavorDimensions(listOf("constantEnv"))

        manifestPlaceholders["atyPlaceHolder"] = rootProject.ext["atyConfigChange"].toString()
        manifestPlaceholders["startUpatyPlaceHolder"] = rootProject.ext["startUpatyConfigChange"].toString()

    }

    // 签名
    signingConfigs {
        create("keyStore") {
            storeFile = file("${rootDir}/signature/starry.jks")
            storePassword = rootProject.ext["storePassword"].toString()
            keyAlias = rootProject.ext["keyAlias"].toString()
            keyPassword = rootProject.ext["keyPassword"].toString()
        }
    }

    buildTypes {
        val signConfig = signingConfigs.getByName("keyStore")
        debug {
            signingConfig = signConfig
            isMinifyEnabled = false
        }

        create("unsigned") {
            signingConfig = signConfig
            isDebuggable = false
            isMinifyEnabled = false
        }
    }

    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        aidl = true
        viewBinding = true
    }

    lint {
        abortOnError = false
        checkReleaseBuilds = false
    }

    android.applicationVariants.all {
        outputs.all {
            if (this is com.android.build.gradle
                .internal.api.ApkVariantOutputImpl
            ) {
                this.outputFileName = "${apkName}.apk"
            }
        }
    }
}

dependencies {
    implementation(files("libs/i18n.jar"))

    // 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

    compileOnly(files("${rootDir}/libs/framework.jar"))
    implementation(files("${rootDir}/libs/settingslib.jar"))


    implementation(project(":base:meetlib"))
    implementation(project(":base:noticeLib"))
    implementation(project(":baselib"))
    implementation(project(":base:otalib"))
    implementation(project(":base:DiagnosisLib"))
    implementation(project(":base:filelib"))
    implementation(project(":base:SettingsLib"))
    implementation(project(":base:BluetoothLib"))
    implementation(project(":base:eShareLib"))
    implementation(project(":base:UILib"))
    implementation(project(":thirdlibs:photoview"))
    implementation(project(":baselib"))
    // 阿里云OSS依赖
    implementation(libs.oss)
    // markdown依赖
    implementation(libs.markwon)
    // 下载库
    implementation(libs.net.download)
    // 蓝牙依赖
    implementation(libs.bundles.dfu)
    implementation(libs.fastble)
}