<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions"
    package="com.czur.starry.device.settings">


    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.czur.starry.contentProvider.rw.sp" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 蓝牙 -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_MAP" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.BLUETOOTH_STACK" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" /> <!-- 广播权限 -->
    <permission android:name="com.czur.starry.device.settings.FOCUS" />

    <uses-permission android:name="android.permission.MASTER_CLEAR" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />

    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
    <uses-permission android:name="android.permission.DELETE_CACHE_FILES" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <uses-feature android:name="android.hardware.usb.host" />
    <uses-permission android:name="android.permission.LOCATION_HARDWARE"/>
    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL"/>
    <uses-permission android:name="android.permission.MANAGE_USB"/>
    <uses-permission android:name="android.permission.TETHER_PRIVILEGED"/>
    <uses-permission android:name="android.permission.USB_PERMISSION" />
    <uses-permission android:name="com.android.usb.permission.USB_SERVICE" />
    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

    <application
        android:name=".app.App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <provider
            android:name=".provider.CZURSettingsProvider"
            android:authorities="com.czur.starry.device.settings.czurprovider"
            android:enabled="true"
            android:exported="true" />

        <meta-data
            android:name="design_width_in_dp"
            android:value="1920" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="1080" />

        <activity
            android:name=".SettingActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@style/CZUIAppTheme"
            android:windowSoftInputMode="adjustPan">

            <intent-filter>
                <action android:name="com.czur.starry.device.settings.BOOT_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.czur.uilib.debug.DebugUIActivity"
            android:exported="true" />


        <service
            android:name=".ui.projector.FocusWindowService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.settings.startFocus" />
            </intent-filter>
        </service>

        <service
            android:name=".debug.EyeProtectionWindowService"
            android:exported="true" />

        <activity
            android:name=".ui.projector.KeystoneManualActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:theme="@style/KeystoneManualAty" />

        <activity
            android:name=".startup.StartUpLanguageActivity"
            android:configChanges="${startUpatyPlaceHolder}"
            android:exported="true"
            android:theme="@style/BaseAppTheme" />

        <activity
            android:name=".startup.StartUpDataRecoveryActivity"
            android:exported="true"
            android:configChanges="${atyPlaceHolder}"
            android:theme="@style/BaseAppTheme"/>

        <activity
            android:name=".startup.StartUpTimeZoneActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="true"
            android:theme="@style/BaseAppTheme" />

        <activity
            android:name=".startup.StartUpWifiActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/BaseAppTheme" />

        <activity
            android:name=".ui.cameraandmic.camera.CameraUpdateActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="false"
            android:theme="@style/CZUIAppBlueTheme" />

        <activity
            android:name=".ui.projector.touchpad.TouchPadUpdateV1Aty"
            android:configChanges="${atyPlaceHolder}"
            android:exported="false"
            android:theme="@style/CZUIAppBlueTheme" />

        <activity
            android:name=".ui.net.wifi.CaptivePortalWebViewActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@style/BaseAppTheme" />

        <service
            android:name=".touchpad.TouchPadService"
            android:process=":touchPad" />

        <activity
            android:name=".ui.projector.touchpad.dfu.TouchPadUpdateDfuAty"
            android:configChanges="${atyPlaceHolder}"
            android:exported="false"
            android:theme="@style/CZUIAppBlueTheme" />
        <service android:name=".ui.projector.touchpad.dfu.DfuService" />

        <receiver
            android:name=".touchpad.BootReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <!--            下面的action,主要是为了防止Service被杀死    -->
                <action android:name="com.android.action.CZUR_BTB_DOCKED" />
                <action android:name="android.intent.action.TIME_TICK" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".console.CZConsoleActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.settings.console" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.personalization.wallpaper.WallpaperPreviewActivity"
            android:exported="true"
            android:theme="@style/BaseAppTheme">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.personalization.backdrop.ScreenBackdropPreviewAty"
            android:configChanges="${atyPlaceHolder}" />

        <receiver
            android:name=".manager.bt.BluetoothConnectActivityReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.bluetooth.device.action.PAIRING_REQUEST" />
            </intent-filter>
        </receiver>

        <service android:name=".ui.personalization.backdrop.BackdropChangeService" />

        <receiver
            android:name=".army.ArmyKeyboardUsbReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </receiver>

        <activity
            android:name=".ReNameDialogActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:process=":rename"
            android:theme="@style/DialogActivity"
            android:windowSoftInputMode="stateHidden|adjustResize" >
            <intent-filter>
                <action android:name="com.czur.starry.device.settings.launcherDialogActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

    </application>

</manifest>