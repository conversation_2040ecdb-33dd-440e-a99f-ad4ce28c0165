package com.czur.starry.device.settings.ui.projector.audio

import android.app.Application
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.utils.displayName
import com.czur.starry.device.settings.utils.isBluetoothDevice
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

/**
 * Created by 陈丰尧 on 2025/4/7
 */
private const val TAG = "SpeakerAndMicChooseViewModel"

class SpeakerAndMicChooseViewModel(application: Application) : AndroidViewModel(application) {
    private val audioManager by lazy {
        application.getSystemService(Application.AUDIO_SERVICE) as AudioManager
    }

    private val _audioDeviceListFlow = MutableStateFlow<List<AudioDeviceInfo>>(emptyList())
    private val _currentSelSpeakerIDFlow = MutableStateFlow(-1)
    private val _currentSelMicIDFlow = MutableStateFlow(-1)

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    val speakerDeviceFlow = _audioDeviceListFlow.map { list ->
        list.filter {
            it.isSink && it.type != AudioDeviceInfo.TYPE_BLUETOOTH_SCO
        }
    }.stateIn(
        viewModelScope,
        initialValue = emptyList(),
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    val micDeviceFlow = _audioDeviceListFlow.map { list ->
        list.filter {
            !it.isSink
        }
    }.stateIn(
        viewModelScope,
        initialValue = emptyList(),
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    val selSpeakerIndexFlow = combine(_currentSelSpeakerIDFlow, speakerDeviceFlow) { id, speakers ->
        if (speakers.isEmpty()) {
            return@combine -1
        }
        var index = speakers.indexOfFirst {
            it.id == id
        }
        if (index == -1) {
            logTagW(TAG, "没有找到对应的Speaker ${speakers.map { it.displayName + "(${it.id})" }}")
            index = speakers.indexOfFirst {
                it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
            }
        }
        index.coerceAtLeast(0)
    }.stateIn(
        viewModelScope,
        initialValue = 0,
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    val selMicIndexFlow = combine(_currentSelMicIDFlow, micDeviceFlow) { id, mics ->
        var index = mics.indexOfFirst {
            it.id == id
        }
        if (index == -1) {
            logTagW(TAG, "没有找到对应的Mic")
            index = mics.indexOfFirst {
                it.type == AudioDeviceInfo.TYPE_BUILTIN_MIC
            }
        }
        index.coerceAtLeast(0)
    }.stateIn(
        viewModelScope,
        initialValue = 0,
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    private val audioDeviceCb = object : AudioDeviceCallback() {
        override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesAdded(addedDevices)
            updateDeviceList()
        }

        override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesRemoved(removedDevices)
            updateDeviceList()
        }
    }

    init {
        launch {
            audioManager.registerAudioDeviceCallback(audioDeviceCb, null)
        }

        launch {
            _currentSelSpeakerIDFlow.value = SettingUtil.CameraAndMicSetting.getDefaultSpeaker()
            _currentSelMicIDFlow.value = SettingUtil.CameraAndMicSetting.getDefaultMic()
        }
    }

    /**
     * 获取属于同一设备对应音频设备, 比如出入一个扬声器,找到对应的Mic
     * @param targetDevice 目标设备
     * @return 对应的设备信息,如果不存在则为Null
     */
    fun getPairedDeviceInfo(targetDevice: AudioDeviceInfo): AudioDeviceInfo? {
        val targetDevice = _audioDeviceListFlow.value
            .filterNot {
                it.isSink && it.type == AudioDeviceInfo.TYPE_BLUETOOTH_SCO
            }
            .firstOrNull { it.address == targetDevice.address && it.isSink != targetDevice.isSink }
        return targetDevice
    }

    suspend fun updateToBuiltInDevice() {
        logTagI(TAG, "updateToBuiltInDevice")
        val builtInSpeaker =
            _audioDeviceListFlow.value.firstOrNull { it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER }
        val builtInMic =
            _audioDeviceListFlow.value.firstOrNull { it.type == AudioDeviceInfo.TYPE_BUILTIN_MIC }
        if (builtInSpeaker != null) {
            updateSelDevice(builtInSpeaker, builtInMic)
        } else if (builtInMic != null) {
            updateSelDevice(builtInMic, builtInSpeaker)
        } else {
            logTagW(TAG, "没有找到内置设备")
        }
    }

    suspend fun updateSelDevice(
        device: AudioDeviceInfo,
        withDevice: AudioDeviceInfo? = null,
        needCheck: Boolean = true
    ) {
        logTagD(
            TAG,
            "updateSelDevice:${device.displayName} ${device.isSink},withDevice:${withDevice?.displayName}"
        )
        var realWithDevice = withDevice

        if (withDevice == null && needCheck) {
            // 用户没有指定同步修改, 需要检查当前是否是蓝牙设备
            val currentDevice = if (device.isSink)
                speakerDeviceFlow.value.getOrNull(selSpeakerIndexFlow.value)
            else
                micDeviceFlow.value.getOrNull(selMicIndexFlow.value)
            if (currentDevice?.isBluetoothDevice == true) {
                logTagD(TAG, "当前是蓝牙设备,需要同步修改")
                realWithDevice = if (device.isSink) {
                    logTagD(TAG, "需要回到系统mic")
                    _audioDeviceListFlow.value.firstOrNull {
                        it.type == AudioDeviceInfo.TYPE_BUILTIN_MIC
                    }
                } else {
                    logTagD(TAG, "需要回到系统扬声器")
                    _audioDeviceListFlow.value.firstOrNull {
                        it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
                    }
                }
                logTagD(TAG, "同步自动修改设备:${realWithDevice?.displayName}")
            }
        }


        val res = systemManager.setPreferredAudioDevice(device)
        if (res) {
            logTagD(TAG, "设置成功:${device.displayName}")
            if (device.isSink) {
                SettingUtil.CameraAndMicSetting.setDefaultSpeaker(device.id)
                _currentSelSpeakerIDFlow.value = device.id
            } else {
                SettingUtil.CameraAndMicSetting.setDefaultMic(device.id)
                _currentSelMicIDFlow.value = device.id
            }

            realWithDevice?.let {
                logTagD(TAG, "设置对应的另一个设备")
                updateSelDevice(it, null, needCheck = false)
            }

        } else {
            logTagW(TAG, "设置失败")
        }
    }

    /**
     * 更新音频设备列表
     */
    fun updateDeviceList() {
        val audioDevices =
            audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS or AudioManager.GET_DEVICES_INPUTS)
        _audioDeviceListFlow.value = audioDevices.filter {
            if (it.type == AudioDeviceInfo.TYPE_REMOTE_SUBMIX) {
                return@filter false
            }
            if (!it.isSink && (it.type == AudioDeviceInfo.TYPE_HDMI ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_ARC ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_ARC ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_EARC)
            ) {
                return@filter false
            }
            true
        }
    }

    override fun onCleared() {
        audioManager.unregisterAudioDeviceCallback(audioDeviceCb)
        super.onCleared()
    }
}