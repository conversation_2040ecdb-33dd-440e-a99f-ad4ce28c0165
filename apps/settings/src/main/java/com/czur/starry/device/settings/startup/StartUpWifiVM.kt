package com.czur.starry.device.settings.startup

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.wifi.ScanResult
import android.net.wifi.WifiManager
import androidx.fragment.app.Fragment
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.model.WifiInfoEntity
import com.czur.starry.device.settings.utils.connectWIFI
import com.czur.starry.device.settings.utils.enableWifi
import com.czur.starry.device.settings.utils.getLockType
import com.czur.starry.device.settings.utils.getWifiScanResults
import com.czur.starry.device.settings.utils.isWifiEnable
import com.czur.starry.device.settings.utils.startScanWifi
import com.czur.starry.device.settings.utils.transformToWifiInfoEntityList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2/24/21
 */

private const val TAG = "StartUpWifiVM"
private const val REPEAT_SCAN_INTERVAL = 15 * ONE_SECOND

class StartUpWifiVM(application: Application) : AndroidViewModel(application) {
    private val wifiReceiver = WifiReceiver()

    private val intentFilter = IntentFilter().apply {
        addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)    //WIFI状态改变
        addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)    //WIFI扫描完成
    }

    // wifi扫描的结果
    val scanResultList: MutableLiveData<List<WifiInfoEntity>> = MutableLiveData()

    // 从wifiManager中扫描到的结果
    private val scanResultListRaw: MutableLiveData<List<ScanResult>> = MutableLiveData()

    //wifi连接结果
    val wifiConnectResult: MutableLiveData<Int> = MutableLiveData()

    // 选中项
    val selBssid = DifferentLiveData("")
    val pwd: MutableLiveData<String> = MutableLiveData("")

    val currentFragment: MutableLiveData<Fragment> = MutableLiveData()

    init {
        // 系统的WifiManager会一直持续的扫描
        // 初始化时, 拿取之前wifiManager扫描的结果
        handleWifiScan()

        // 扫描数据每次发生改变, 都需要让他生成WIFI的连接信息
        scanResultListRaw.observeForever {
            handleWifiInfoList()
        }

        appContext.registerReceiver(wifiReceiver, intentFilter)
    }


    fun setPwd(password: String) {
        val p = pwd.value
        if (password != p) {
            pwd.value = password
        }
    }

    fun setCurrentFragment(fragment: Fragment) {
        val f = currentFragment.value
        if (fragment != f) {
            currentFragment.value = fragment
        }
    }

    fun setSelPos(bssid: String) {
        selBssid.value = bssid
    }

    /**
     * 能否直接加入选中的wifi
     */
    fun canJoinSelWifiWithoutPwd(): Boolean {
        // 没有选择wifi
        val bssid = selBssid.value ?: ""
        if (bssid.isEmpty()) return false
        return isOpenWifi(bssid)

    }

    /**
     * 是否是没有密码的wifi
     * @param bssid: 指定的bssid,默认是选中的bssid
     */
    private fun isOpenWifi(bssid: String): Boolean {
        val wifiInfoEntity = scanResultList.value?.find {
            it.bssid == bssid
        }
        return !(wifiInfoEntity?.locked ?: true)
    }

    fun getSSID(bssid: String): String {
        val result = scanResultList.value
        return result?.find {
            it.bssid == bssid
        }?.ssid ?: ""
    }

    /**
     * 扫描wifi数据
     */
    private fun scanWifi() {
        launch(Dispatchers.IO) {
            if (!isWifiEnable()) {
                logTagW(TAG, "WIFI没有打开, 开启WIFI")
                enableWifi()
                delay(ONE_SECOND * 2)   // 延迟1s, 等待WIFI打开
            }
            while (isActive) {
                // 没过10s都发起一次扫描请求
                logTagD(TAG, "开始扫描WIFI")
                startScanWifi()
                delay(REPEAT_SCAN_INTERVAL)
            }
        }
    }

    fun resume() {
        scanWifi()
    }


    private fun handleWifiScan() {
        viewModelScope.launch(Dispatchers.IO) {
            val result = getWifiScanResults()
            logTagD(TAG, "扫描WIFI信号完成, 扫描到了${result.size}个信号")
            scanResultListRaw.postValue(result)
        }
    }

    /**
     * 处理连接信息的结果集合
     */
    private fun handleWifiInfoList() = viewModelScope.launch(Dispatchers.IO) {
        val result = scanResultListRaw.value ?: run {
            // 如果没有扫描的数据,就扫描一次
            startScanWifi()
            return@launch
        }

        val wifiInfoEntities = transformToWifiInfoEntityList(result)

        scanResultList.postValue(wifiInfoEntities)
    }

    suspend fun join() = withContext(Dispatchers.IO) {
        val scanList = scanResultListRaw.value
        val scanResult = scanList?.find {
            val selBSSID = selBssid.value ?: ""
            it.BSSID == selBSSID
        }
        scanResult?.let {
            return@withContext connectWIFI(
                it.SSID,
                pwd.value ?: "",
                it.getLockType(),
                isRetry = true,
                needSpeed = true
            )
        }
        false
    }

    override fun onCleared() {
        super.onCleared()
        appContext.unregisterReceiver(wifiReceiver)

    }


    private inner class WifiReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action ?: ""
            logTagD(TAG, "receiver#action: $action")
            when (action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    logTagD(TAG, "WIFI状态改变")
                    when (intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, -1)) {
                        WifiManager.WIFI_STATE_DISABLED -> {
                            logTagW(TAG, "WIFI已经关闭")
                            enableWifi()    // 打开wifi
                        }
                    }
                }

                WifiManager.SCAN_RESULTS_AVAILABLE_ACTION -> {
                    handleWifiScan()
                }
            }

        }

    }
}