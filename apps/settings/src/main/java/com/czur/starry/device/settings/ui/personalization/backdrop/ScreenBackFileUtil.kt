package com.czur.starry.device.settings.ui.personalization.backdrop

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.BackdropItem
import com.czur.starry.device.settings.model.BackdropListItem
import com.czur.starry.device.settings.model.BackdropTag
import com.czur.starry.device.settings.model.BackdropType
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getValueToList
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.setValueToString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

const val CUSTOM_ASSETS = "ScreenBackDrop"

// 获取存储时的数据(FileEntity类型)
suspend fun getNormalCustomData() = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    val customList = mutableListOf<FileEntity>()
    list.forEach { customList.add(it) }
    customList
}

// 获取类型转换后的数据(BackdropListItem类型)
suspend fun getCustomData() = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    val customList = mutableListOf<BackdropListItem>()
    customList.addAll(
        list.map {
            BackdropListItem(
                fileType = BackdropType.CUSTOM,
                customEntity = it
            )
        }
    )
    customList
}

suspend fun getHasAvailableCustomData() = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    val customList = mutableListOf<BackdropListItem>()
    customList.addAll(
        list.map {
            BackdropListItem(
                fileType = BackdropType.CUSTOM,
                customEntity = it
            )
        }
    )
    customList.add(
        0, BackdropListItem(
            fileType = BackdropType.CUSTOM,
            customEntity = FileEntity()
        )
    )
    customList
}

// 获取内置图片数据
fun getFixedData(): List<BackdropListItem> {
    return buildList {
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_a", BackdropTag.Natural, R.raw.backdrop_img_natural_a), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_b", BackdropTag.Natural, R.raw.backdrop_img_natural_b), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_c", BackdropTag.Natural, R.raw.backdrop_img_natural_c), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_d", BackdropTag.Natural, R.raw.backdrop_img_natural_d), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_e", BackdropTag.Natural, R.raw.backdrop_img_natural_e), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_f", BackdropTag.Natural, R.raw.backdrop_img_natural_f), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_g", BackdropTag.Natural, R.raw.backdrop_img_natural_g), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_h", BackdropTag.Natural, R.raw.backdrop_img_natural_h), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("natural_i", BackdropTag.Natural, R.raw.backdrop_img_natural_i), null
            )
        )
        if (Constants.starryHWInfo.series == StudioSeries) {
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "natural_j",
                        BackdropTag.Natural,
                        R.raw.backdrop_img_studio_natural_j
                    ), null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "natural_k",
                        BackdropTag.Natural,
                        R.raw.backdrop_img_studio_natural_k
                    ), null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "natural_n",
                        BackdropTag.Natural,
                        R.raw.backdrop_img_studio_natural_n
                    ), null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "natural_o",
                        BackdropTag.Natural,
                        R.raw.backdrop_img_studio_natural_o
                    ), null
                )
            )
        }
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_a",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_a
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_b",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_b
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_c",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_c
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_d",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_d
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_e",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_e
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_f",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_f
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_g",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_g
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_h",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_h
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem(
                    "aestheticism_i",
                    BackdropTag.Aestheticism,
                    R.raw.backdrop_img_aestheticism_i
                ), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("abstract_a", BackdropTag.Abstract, R.raw.backdrop_img_abstract_a),
                null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("abstract_b", BackdropTag.Abstract, R.raw.backdrop_img_abstract_b),
                null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("abstract_c", BackdropTag.Abstract, R.raw.backdrop_img_abstract_c),
                null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("abstract_d", BackdropTag.Abstract, R.raw.backdrop_img_abstract_d),
                null
            )
        )
        if (Constants.starryHWInfo.series == StudioSeries) {
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_f",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_f
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_g",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_g
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_i",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_i
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_j",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_j
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_k",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_k
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_m",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_m
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_n",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_n
                    ),
                    null
                )
            )
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem(
                        "abstract_o",
                        BackdropTag.Abstract,
                        R.raw.backdrop_img_studio_abstract_o
                    ),
                    null
                )
            )
        }
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("art_a", BackdropTag.Art, R.raw.backdrop_img_art_a), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("art_b", BackdropTag.Art, R.raw.backdrop_img_art_b), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("art_c", BackdropTag.Art, R.raw.backdrop_img_art_c), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("art_d", BackdropTag.Art, R.raw.backdrop_img_art_d), null
            )
        )
        add(
            BackdropListItem(
                BackdropType.FIXED,
                BackdropItem("art_e", BackdropTag.Art, R.raw.backdrop_img_art_e), null
            )
        )
        if (Constants.starryHWInfo.series == StudioSeries) {
            add(
                BackdropListItem(
                    BackdropType.FIXED,
                    BackdropItem("art_f", BackdropTag.Art, R.raw.backdrop_img_studio_art_f), null
                )
            )
        }
    }
}

suspend fun getAllData() = withContext(Dispatchers.IO) {
    val newEntityList = mutableListOf<BackdropListItem>()
    newEntityList.addAll(getCustomData())
    newEntityList.addAll(getFixedData())
    newEntityList
}

//添加自定义模板
suspend fun addDataLocal(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    list.add(data)
    setValueToString(CUSTOM_ASSETS, list)
}

//删除指定图片信息
suspend fun removeCustomData(itemEntity: BackdropListItem) = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    val data = list.toMutableList()
    data.forEach {
        if (it.name == itemEntity.customEntity?.name) {
            list.remove(it)
        }
    }
    setValueToString(CUSTOM_ASSETS, list)
}