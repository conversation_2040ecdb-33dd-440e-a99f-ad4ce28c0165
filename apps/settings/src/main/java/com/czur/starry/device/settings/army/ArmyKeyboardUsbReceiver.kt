package com.czur.starry.device.settings.army

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_ARMY_NEED_SET_TIME
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2024/9/19
 */
private const val TAG = "ArmyKeyboardUsbReceiver"

class ArmyKeyboardUsbReceiver : BroadcastReceiver(), CoroutineScope by MainScope() {
    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action ?: return
        if (Constants.versionIndustry != VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagV(TAG, "非军工版,不执行对应逻辑")
        }
        when (action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                logTagD(TAG, "ACTION_USB_DEVICE_ATTACHED")
                launch {
                    if (getBooleanSystemProp(KEY_ARMY_NEED_SET_TIME, false)) {
                        logTagV(TAG, "需要设置时间到键盘")
                        ArmyUsbManager.uploadSystemTimeToKeyboard() // 上传系统时间到键盘
                    } else {
                        logTagV(TAG, "需要同步时间到系统")
                        ArmyUsbManager.setTimeFromKeyboard() // 从键盘获取时间并设置到系统上
                    }
                }
            }

            Intent.ACTION_TIME_CHANGED -> {
                logTagD(TAG, "ACTION_TIME_CHANGED")
                launch {
                    setSystemProp(KEY_ARMY_NEED_SET_TIME, true)
                    ArmyUsbManager.uploadSystemTimeToKeyboard()
                }
            }

            Intent.ACTION_BOOT_COMPLETED -> {
                logTagD(TAG, "ACTION_BOOT_COMPLETED")
                launch {
                    ArmyUsbManager.setTimeFromKeyboard()
                }
            }
        }
    }
}