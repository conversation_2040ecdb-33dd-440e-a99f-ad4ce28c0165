package com.czur.starry.device.settings.ui.system

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.view.View
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.SettingActivity
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentUpdateVersionBinding
import com.czur.starry.device.update.IUpdateManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean

class UpdateVersionFragment : BaseBindingMenuFragment<FragmentUpdateVersionBinding>() {
    companion object {
        private const val TAG = "UpdateVersionFragment"
        private const val KEY_CACHE_RECOVERY_DEL = "vendor.czur.cache.del"
        private const val ACTION_UPDATE_SERVICE = "android.starry.update.service"
        private const val PACKAGE_UPDATE_SERVICE = "com.czur.starry.device.update"
        private var currentVersion = Constants.FIRMWARE_NAME
        private var updateVersion = "0"
        var isDownLoading = AtomicBoolean(false)
        var updateManager: IUpdateManager? = null
        var isDownLoadFinished: MutableLiveData<Boolean> = MutableLiveData(false)
    }

    private val loadingDialog by lazy { LoadingDialog() }

    override fun FragmentUpdateVersionBinding.initBindingViews() {
        if (SPHandler.isDownLoading) {
            if (loadingDialog != null)
                loadingDialog.dismiss()
            showFragment(DownLoadFirFragment())
        }
        viewUpdate.btUpdateBtn.setOnClickListener {
            if (!getBooleanSystemProp(KEY_CACHE_RECOVERY_DEL, false)) {
                logTagI(TAG, "系统正在初始化中, 无法升级")
                toast(R.string.system_init)
                return@setOnClickListener
            }
            if (SPHandler.isreadyForUpdate) {
                logTagV(TAG, "isReadyForUpdate")
                DoubleBtnCommonFloat(
                    content = getString(R.string.upgradle_alart),
                    cancelBtnText = getString(R.string.str_upgrade_version_stop),
                    confirmBtnText = getString(R.string.str_upgrade_version_continue)
                ) { doubleCommonFlat, position ->
                    if (position == 0) {
                        doubleCommonFlat.dismiss()
                    } else {
                        doubleCommonFlat.dismiss()
                        updateManager!!.upgrade()
                    }
                }.show()


            } else {
                logTagV(TAG, "isNotReadyForUpdate")
                try {
                    SPHandler.isSupportDownload = true
                    updateManager!!.downLoadFirmware()
                } catch (e: Exception) {
                    logTagE(TAG, "downLoadFirmware error", tr = e)
                }
                SPHandler.isDownLoading = true
                logTagV(TAG, "showDownloading")
                showFragment(DownLoadFirFragment())

            }


        }

        viewUpdate.updateVersionInfoView.setOnClickListener {
            val url = Constants.updateVersionUrl(updateVersion)
            showFragment(WebInfoFragment.getInstance(url, getString(R.string.update_info_title)))
        }

        isDownLoadFinished.observe(viewLifecycleOwner) {
            if (it) {
                viewUpdateNot.visibility = View.GONE
                viewUpdate.root.visibility = View.VISIBLE
                viewUpdate.versionTitleTv.text =
                    resources.getString(R.string.latest_version, updateVersion)
                viewUpdate.versionSubTv.text =
                    resources.getString(R.string.current_version, Constants.FIRMWARE_NAME)
                viewUpdate.btUpdateBtn.text = resources.getString(R.string.download_info_title)

            }
        }

        loadingDialog.show()
        launch {
            bindUpdateService()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        binding.versionTitleNoUp.text = resources.getString(R.string.current_version, Constants.FIRMWARE_NAME)
    }

    override fun showFragment(fa: CZBaseFragment) {
        val aty = requireActivity()
        if (aty is SettingActivity) {
            aty.showSubFragment(this, fa)
        }
    }


    private val mUpdateServiceConnection = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName?) {
            setValue()
        }

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            updateManager = IUpdateManager.Stub.asInterface(service)
            launch(Dispatchers.IO) {
                try {
                    val Version = updateManager!!.checkVersion()
                    updateVersion = Version
                    logTagD(TAG, "=====updateVersion=$updateVersion")
                    setValue()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

        }
    }


    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        //download 下载返回刷新状态
        if (!hidden && isAdded && binding.viewUpdate.btUpdateBtn != null) {
            if (SPHandler.isreadyForUpdate) {
                binding.viewUpdate.btUpdateBtn.text =
                    resources.getString(R.string.download_info_title)
            } else {
                binding.viewUpdate.btUpdateBtn.text =
                    resources.getString(R.string.upgradle_download)
            }
        }
    }

    private fun setValue() {

        if (updateVersion != "0" && updateVersion != currentVersion) {
            SPHandler.firmwareNeedUpdate = 1
            OTAHandler.newVersionStatus = true
        } else {
            OTAHandler.newVersionStatus = false
            SPHandler.firmwareNeedUpdate = 0
            SPHandler.isDownLoading = false
        }
        launch {
            try {
                refreshUI()
            } catch (e: Exception) {
                loadingDialog.dismiss()
                e.printStackTrace()
            }

        }
    }

    private fun refreshUI() {
        if (1 == SPHandler.firmwareNeedUpdate) {
            binding.viewUpdateNot.visibility = View.GONE
            binding.viewUpdate.root.visibility = View.VISIBLE
            binding.viewUpdate.versionTitleTv.text =
                resources.getString(R.string.latest_version, updateVersion)
            binding.viewUpdate.versionSubTv.text =
                resources.getString(R.string.current_version, Constants.FIRMWARE_NAME.toString())
            if (SPHandler.isreadyForUpdate) {
                binding.viewUpdate.btUpdateBtn.text =
                    resources.getString(R.string.download_info_title)
            } else {
                binding.viewUpdate.btUpdateBtn.text =
                    resources.getString(R.string.upgradle_download)
            }

        } else {
            binding.viewUpdateNot.visibility = View.VISIBLE
            binding.viewUpdate.root.visibility = View.GONE
            binding.versionTitleNoUp.text =
                resources.getString(R.string.current_version, Constants.FIRMWARE_NAME.toString())
        }
        loadingDialog.dismiss()
    }


    private suspend fun bindUpdateService() = withContext(Dispatchers.IO) {
        val intent = Intent()
        intent.action = ACTION_UPDATE_SERVICE
        intent.`package` = PACKAGE_UPDATE_SERVICE
        activity?.bindService(intent, mUpdateServiceConnection, Context.BIND_AUTO_CREATE)
    }

    private fun unbind() {
        activity?.unbindService(mUpdateServiceConnection)
    }


    override fun onDestroy() {
        super.onDestroy()
        unbind()
    }

}