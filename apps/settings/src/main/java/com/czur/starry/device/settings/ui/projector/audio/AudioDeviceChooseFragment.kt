package com.czur.starry.device.settings.ui.projector.audio

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentSpeakerMicChooseBinding
import com.czur.starry.device.settings.utils.isBuiltinDevice
import com.czur.uilib.extension.rv.addCornerRadius

/**
 * Created by 陈丰尧 on 2025/4/10
 */
abstract class AbsAudioDeviceChooseFragment<T> :
    BaseBindingMenuFragment<FragmentSpeakerMicChooseBinding>() {
    protected val speakerAndMicChooseViewModel: SpeakerAndMicChooseViewModel by activityViewModels()
    protected val audioDeviceInfoAdapter = AudioDeviceInfoAdapter()

    override fun FragmentSpeakerMicChooseBinding.initBindingViews() {
        titleTv.setText(R.string.title_mic_choose)
        speakerRv.adapter = audioDeviceInfoAdapter
        speakerRv.addCornerRadius(10F, 0xFFF1F3FE.toInt())
        speakerRv.doOnItemClick { holder, view ->
            val position = holder.bindingAdapterPosition
            if (position != audioDeviceInfoAdapter.selItem) {
                val device = audioDeviceInfoAdapter.getData(position)
                if (device.isBuiltinDevice) {
                    // 内置设备, 直接切换
                    speakerAndMicChooseViewModel.updateToBuiltInDevice()
                } else {
                    ChangeMicOrSpeakerWarningFloating(device).show()
                }
            }
            true
        }
        initCustomView()
    }

    abstract fun FragmentSpeakerMicChooseBinding.initCustomView()

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatOnResume {
            speakerAndMicChooseViewModel.updateDeviceList()
        }
    }
}


class MicChooseFragment : AbsAudioDeviceChooseFragment<FragmentSpeakerMicChooseBinding>() {
    override fun FragmentSpeakerMicChooseBinding.initCustomView() {
        titleTv.setText(R.string.title_mic_choose)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(speakerAndMicChooseViewModel.selMicIndexFlow) {
            audioDeviceInfoAdapter.updateSelItem(it)
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.micDeviceFlow) {
            audioDeviceInfoAdapter.setData(it)
        }
    }
}

class SpeakerChooseFragment : AbsAudioDeviceChooseFragment<FragmentSpeakerMicChooseBinding>() {
    override fun FragmentSpeakerMicChooseBinding.initCustomView() {
        titleTv.setText(R.string.title_speaker_choose)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(speakerAndMicChooseViewModel.selSpeakerIndexFlow) {
            audioDeviceInfoAdapter.updateSelItem(it)
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.speakerDeviceFlow) {
            audioDeviceInfoAdapter.setData(it)
        }
    }
}