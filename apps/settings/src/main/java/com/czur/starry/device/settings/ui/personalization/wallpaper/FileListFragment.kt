package com.czur.starry.device.settings.ui.personalization.wallpaper

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.file.filelib.ShareFile
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.FloatFileListBinding
import com.czur.starry.device.settings.ui.personalization.wallpaper.adapter.FileChooseAdapter
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.showSortPop
import com.czur.starry.device.settings.ui.personalization.wallpaper.vm.DisplayVM
import kotlinx.coroutines.launch
import java.io.File


class FileListFragment(private val customTitleRes: Int) : CZViewBindingFragment<FloatFileListBinding>() {

    companion object {
        private const val TAG = "FileListFragment"
    }

    private val shareFileVM: DisplayVM by viewModels({ requireActivity() })

    private val floatParent: FileChooseFloatFragment by lazy {
        requireParentFragment() as FileChooseFloatFragment
    }

    private val adapter = FileChooseAdapter()

    override fun FloatFileListBinding.initBindingViews() {
        shareFileVM.isSelectLive.observe(this@FileListFragment) {
            if (!it) {
               adapter.clearSel()
            }
        }
        // dismiss
        closeIv.setOnClickListener {
            floatParent.dismiss()
        }

        shareFileRv.layoutManager = LinearLayoutManager(requireContext())
        shareFileRv.adapter = adapter
        shareFileRv.closeDefChangeAnimations()
        adapter.currentShareFile = shareFileVM.shareFile

        shareFileRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            val data = adapter.getData(pos)
            doClickItem(pos, data)
            true
        }

        // 离开当前文件夹
        shareFileBackIv.setOnClickListener {
            adapter.clearSel()
            shareFileVM.leaveFolder()
        }

        // 显示预览页面
        previewTv.setOnClickListener {
            try {
                if (isFileOver5MB(shareFileVM.requireSelFile().absPath)) {
                    showTipDialog()
                } else {
                    floatParent.showPreviewFragment()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }

        // 排序按钮
        shareFileSortIv.setOnClickListener {
            showSortPop(requireContext(), it, shareFileVM.currentSortType) { sortType ->
                shareFileVM.changeSortType(sortType) { newSelIndex ->
                    adapter.selPos = newSelIndex
                }
            }
        }


        // 选择
        selectTv.setOnClickListener {
            try {
                if (isFileOver5MB(shareFileVM.requireSelFile().absPath)) {
                    showTipDialog()
                } else {
                    selectTv.isEnabled = false
                    floatParent.dismiss()
                    // 方法内部维护了分享的文件
                    shareFileVM.updateShareFile(shareFileVM.requireSelFile())
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
    }


    private fun isFileOver5MB(filePath: String): Boolean {
        val file = File(filePath)
        val fileSizeInBytes = file.length()
        val fileSizeInKB = fileSizeInBytes / 1024
        val fileSizeInMB = fileSizeInKB / 1024
        logTagD(TAG, "=fileSizeInMB==$fileSizeInMB=")
        return fileSizeInBytes >= 5 * 1024 * 1024
    }


    private var warningDialog: SingleBtnCommonFloat? = null
    private fun showTipDialog() {
        if (warningDialog != null) return
        lifecycleScope.launch {
            warningDialog = SingleBtnCommonFloat(
                content = getString(R.string.wallpaper_pic_limit),
                confirmBtnText = getString(R.string.dialog_normal_ok)
            ) { commonFloat ->
                commonFloat.dismiss()
                warningDialog = null
            }
            warningDialog?.show()
        }
    }

    private fun doClickItem(pos: Int, data: ShareFile): Boolean {


        if (data.canEnter) {
            shareFileVM.enterFolder(data)
            adapter.clearSel()
            return true
        }

        shareFileVM.selFile(data)
        // 选中UI
        adapter.selPos = pos

        return true
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        shareFileVM.fileList.observe(this) {
            if (it.isEmpty()) {
                binding.emptyViewGroup.show()
            } else {
                binding.emptyViewGroup.invisible()
            }
            adapter.dataList = it
            binding.shareFileRv.scrollToPosition(0)

        }

        shareFileVM.title.observe(this) { title ->
            binding.shareFileTitleTv.text = title ?: getString(customTitleRes)
        }

        shareFileVM.folderActionGroupShow.observe(this) {
            binding.folderVisibilityGroup.visibility = if (it) View.VISIBLE else View.GONE
        }

        shareFileVM.showActionGroup.observe(this) {
            if (it) {
                binding.actionFlow.show()
            } else {
                binding.actionFlow.invisible()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        adapter.clearSel()
        shareFileVM.updateFolder()
    }
}