package com.czur.starry.device.settings.base

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.viewbinding.ViewBinding
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.keyboard.injectKeyBack
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.SettingActivity

/**
 * Created by 陈丰尧 on 2023/6/6
 */
private const val TAG = "BaseBindingMenuFragment"

abstract class BaseBindingMenuFragment<VB : ViewBinding> : CZViewBindingFragment<VB>() {
    private val className: String by lazy(LazyThreadSafetyMode.NONE) { javaClass.simpleName }

    override fun FragmentParams.initFragmentParams() {
        this.viewLifecycleObserver = object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                super.onResume(owner)
                val customTitle = setCustomTitle()
                customTitle?.let {
                    val aty = requireActivity()
                    if (aty is ChangeTitle) {
                        logTagD(TAG, "$className - 设置Title:$it")
                        aty.changeTitle(it)
                    }
                }
            }

            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                val customTitle = setCustomTitle()
                customTitle?.let {
                    val aty = requireActivity()
                    if (aty is ChangeTitle) {
                        logTagD(TAG, "$className - 请求重置Title")
                        aty.resetTitle()
                    }
                }
            }
        }

    }

    open fun showFragment(fa: CZBaseFragment) {
        val aty = requireActivity()
        if (aty is SettingActivity) {
            aty.showSubFragment(this, fa)
        }
    }

    fun pressBackKey() {
        launch {
            injectKeyBack()
        }
    }

    /**
     * 设置定制的Title
     */
    open fun setCustomTitle(): String? = null
}