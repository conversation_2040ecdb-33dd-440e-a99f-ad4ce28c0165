package com.czur.starry.device.settings.ui.system.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.adapter.CATEGORY_SEL_MODE_ALL
import com.czur.starry.device.settings.adapter.CATEGORY_SEL_MODE_NONE
import com.czur.starry.device.settings.adapter.CATEGORY_SEL_MODE_PART
import com.czur.starry.device.settings.adapter.CleanUpAdapterCategory
import com.czur.starry.device.settings.adapter.CleanUpAdapterItem
import com.czur.starry.device.settings.manager.MemoryRoomInfo
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.getStorageAllocation
import com.czur.starry.device.settings.model.CacheSecondData
import com.czur.starry.device.settings.model.CleanUpStorageCategory
import com.czur.starry.device.settings.model.StorageInfoEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * created by wangh 22.0513
 */
private const val TAG = "CleanRoomModel"

class CleanRoomModel(application: Application) : AndroidViewModel(application) {
    enum class CleanUpStatus {
        INITIALIZATION, // 初始化中
        CLEANING,       // 清理中
        IDLE            // 空闲, 等待用户操作
    }

    // 存储容量
    val storeValueFlow = MutableSharedFlow<StorageInfoEntity>()

    //计算清除数据
    private val uselessDataFlow = MutableSharedFlow<List<List<CacheSecondData>>>()

    // 处于展开状态的类别
    private val expendCategoryFlow = MutableStateFlow<Set<CleanUpStorageCategory>>(emptySet())

    // 选中的清理项
    private val selectCleanItemFlow =
        MutableStateFlow(emptySet<String>())

    // 用于展示的数据
    val showCleanUpDataFlow = combine(
        uselessDataFlow,
        expendCategoryFlow,
        selectCleanItemFlow
    ) { uselessData, expendCategorySet, selectCleanItemSet ->
        if (uselessData.isEmpty()) {
            createLoadingData()
        } else {
            uselessData.mapIndexed { index, cacheSecondData ->
                val category = when (index) {
                    0 -> CleanUpStorageCategory.APP_CACHE
                    1 -> CleanUpStorageCategory.APK_FILE
                    2 -> CleanUpStorageCategory.UNINSTALL_RESIDUE
                    3 -> CleanUpStorageCategory.RUN_PROGRAM
                    else -> NotImpl()
                }
                val subList = cacheSecondData.map {
                    val clearKey = when (category) {
                        CleanUpStorageCategory.APP_CACHE -> it.pkgName + category.name
                        CleanUpStorageCategory.APK_FILE -> it.pkgName + category.name
                        CleanUpStorageCategory.UNINSTALL_RESIDUE -> it.pkgName + it.name + category.name
                        CleanUpStorageCategory.RUN_PROGRAM -> it.pkgName + category.name
                    }
                    CleanUpAdapterItem(
                        clearKey,
                        it.pkgName,
                        it.name,
                        it.size,
                        category,
                        clearKey in selectCleanItemSet
                    )
                }.filter {
                    it.size > 0 // 不显示大小为0的数据
                }

                var sel = when {
                    subList.all { it.isSelect } -> CATEGORY_SEL_MODE_ALL
                    subList.all { it.isSelect.not() } -> CATEGORY_SEL_MODE_NONE
                    else -> CATEGORY_SEL_MODE_PART
                }

                // 运行程序 默认不选中
                if (category == CleanUpStorageCategory.RUN_PROGRAM) {
                    sel = CATEGORY_SEL_MODE_NONE
                }

                CleanUpAdapterCategory(
                    category, subList.sumOf { it.size }, sel, subList,
                    expand = category in expendCategorySet,
                    isLoading = false
                ).also {
                    if (cleanUpStatusFlow.value == CleanUpStatus.INITIALIZATION) {
                        // 初始化完成
                        cleanUpStatusFlow.value = CleanUpStatus.IDLE
                    }
                }
            }
        }
    }


    // 已发现垃圾数量
    val canCleanUpSizeFlow = showCleanUpDataFlow.map { showCleanUpData ->
        showCleanUpData.sumOf { it.sizeSum }
    }

    // 已选中的垃圾数量
    val selCleanUpSizeFlow = showCleanUpDataFlow.map { showCleanUpData ->
        showCleanUpData.sumOf { category ->
            category.itemList.sumOf { item ->
                if (item.isSelect) item.size else 0
            }
        }
    }

    val cleanUpStatusFlow = MutableStateFlow(CleanUpStatus.INITIALIZATION)

    // 清理按钮是否可用
    val cleanUpBtnEnableFlow = combine(
        cleanUpStatusFlow,
        selCleanUpSizeFlow
    ) { cleanUpStatus, selCleanUpSize ->
        cleanUpStatus == CleanUpStatus.IDLE && selCleanUpSize > 0
    }.distinctUntilChanged()


    suspend fun initStorageInfo() = withContext(Dispatchers.Default) {
        val result = getStorageAllocation()
        storeValueFlow.emit(result)
    }

    suspend fun initUselessData() = withContext(Dispatchers.Default) {
        val uselessData = MemoryRoomInfo.getUselessData()
        resetSelect(uselessData)
        uselessDataFlow.emit(uselessData)
    }

    private fun resetSelect(uselessData: MutableList<MutableList<CacheSecondData>>) {
        // 处理默认选中状态
        val selectCleanItemSet = uselessData.mapIndexed { index, cacheSecondData ->
            val category = when (index) {
                0 -> CleanUpStorageCategory.APP_CACHE
                1 -> CleanUpStorageCategory.APK_FILE
                2 -> CleanUpStorageCategory.UNINSTALL_RESIDUE
                3 -> CleanUpStorageCategory.RUN_PROGRAM
                else -> NotImpl()
            }
            if (category == CleanUpStorageCategory.RUN_PROGRAM) {
                // 运行程序 默认不选中
                emptySet()
            } else {
                cacheSecondData.map { it.pkgName + category.name }.toSet()
            }
        }.flatten().toSet()
        selectCleanItemFlow.value = selectCleanItemSet
    }


    /**
     * 刷新数据, 不会改变状态等信息
     */
    fun refreshStorageData(resetSelect: Boolean = false) {
        logTagD(TAG, "刷新数据,resetSelect=$resetSelect")
        launch {
            suspendRefreshStorageData(resetSelect)
        }
    }

    private suspend fun suspendRefreshStorageData(resetSelect: Boolean = false) {
        initStorageInfo()
        val uselessData = MemoryRoomInfo.getUselessData()
        if (resetSelect) {
            resetSelect(uselessData)
        }
        uselessDataFlow.emit(uselessData)
    }

    suspend fun clearCache(items: List<CleanUpAdapterItem>) = withContext(Dispatchers.IO) {
        logTagD(TAG, "清理开始")
        cleanUpStatusFlow.value = CleanUpStatus.CLEANING  // 清理中
        // 清理应用缓存
        val cacheList = mutableListOf<String>()
        val apkList = mutableListOf<String>()
        val uninstallList = mutableListOf<String>()
        val runningList = mutableListOf<String>()
        items.forEach {
            when (it.category) {
                CleanUpStorageCategory.APP_CACHE -> cacheList.add(it.packageName)
                CleanUpStorageCategory.APK_FILE -> apkList.add(it.packageName)
                CleanUpStorageCategory.UNINSTALL_RESIDUE -> uninstallList.add(it.packageName)
                CleanUpStorageCategory.RUN_PROGRAM -> runningList.add(it.packageName)
            }
        }

        MemoryRoomInfo.clearCache(cacheList)
        MemoryRoomInfo.clearAPKFiles(apkList)
        MemoryRoomInfo.clearUninstallData(uninstallList)
        MemoryRoomInfo.killRunning(runningList)
        suspendRefreshStorageData(true)
        cleanUpStatusFlow.value = CleanUpStatus.IDLE  // 空闲
        expendCategoryFlow.value = emptySet()
        logTagD(TAG, "清理结束")
    }

    fun onClearInterrupt() {
        logTagW(TAG, "清理被中断")
        cleanUpStatusFlow.value = CleanUpStatus.IDLE  // 空闲
    }

    private fun createLoadingData(): List<CleanUpAdapterCategory> {
        logTagD(TAG, "创建加载数据")
        return CleanUpStorageCategory.entries.map {
            CleanUpAdapterCategory(
                category = it,
                sizeSum = 0L,
                selMode = CATEGORY_SEL_MODE_NONE,
                itemList = emptyList(),
                expand = false,
                isLoading = true
            )
        }
    }

    fun onExpandChangeListener(category: CleanUpStorageCategory, expand: Boolean) {
        if (expand) {
            expendCategoryFlow.value += category
        } else {
            expendCategoryFlow.value -= category
        }
    }

    fun onCategorySelModeChangeListener(category: CleanUpAdapterCategory, selMode: Boolean) {
        val itemKeys = category.itemList.map { it.clearKey }
        val newSelCleanItemSet = if (selMode) {
            selectCleanItemFlow.value + itemKeys
        } else {
            selectCleanItemFlow.value - itemKeys.toSet()
        }
        selectCleanItemFlow.value = newSelCleanItemSet
    }

    fun onItemSelChangeListener(item: CleanUpAdapterItem, selMode: Boolean) {
        val newSelCleanItemSet = if (selMode) {
            selectCleanItemFlow.value + item.clearKey
        } else {
            selectCleanItemFlow.value - item.clearKey
        }
        selectCleanItemFlow.value = newSelCleanItemSet
    }

}