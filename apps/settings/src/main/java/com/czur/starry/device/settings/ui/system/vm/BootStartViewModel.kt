package com.czur.starry.device.settings.ui.system.vm

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.content.pm.PackageManager.NameNotFoundException
import android.graphics.drawable.Drawable
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.app.App
import com.czur.starry.device.settings.model.BootStartAppEntity
import com.czur.starry.device.settingslib.blackPkgList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/2/2
 * 启动应用选择画面
 */
class BootStartViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "BootStartViewModel"
    }

    // 全部的App
    private val allAppsFlow = MutableStateFlow<List<BootStartAppEntity>>(emptyList())
    private val _startBootApp = MutableStateFlow<BootStartAppEntity?>(null)
    val startBootApp = _startBootApp.asStateFlow()

    // 可选的app,包括当前已设置的app
    val canChooseAppsFlow = combine(allAppsFlow, startBootApp) { allApps, startApp ->
        if (startApp == null) {
            return@combine allApps
        }
        allApps.map {
            if (it.pkgName == startApp.pkgName) {
                startApp
            } else {
                it
            }
        }
    }.flowOn(Dispatchers.Default)

    // 开机启动应用功能是否开启
    private val _enableBootStartAppFlow =
        MutableStateFlow(SettingUtil.PersonalizationSetting.DEF_VALUE_LAUNCHER_BOOT_APP_ENABLE)
    val enableBootStartAppFlow = _enableBootStartAppFlow.asStateFlow()

    private val appUtil by lazy(LazyThreadSafetyMode.NONE) {
        AppUtil()
    }

    /**
     * 设置开机启动的app
     */
    fun setBootStartApp(appInfo: BootStartAppEntity) {
        if (!enableBootStartAppFlow.value) {
            logTagD(TAG, "开机启动功能未开启")
            return
        }
        _startBootApp.value = appInfo.copy(isSelect = true)
        launch {
            SettingUtil.PersonalizationSetting.setLauncherBootAppPkg(appInfo.pkgName)
        }
    }

    /**
     * 设置开机启动App功能是否开启
     */
    fun setEnableBootStartApp(on: Boolean) {
        _enableBootStartAppFlow.value = on
        if (!on) {
            _startBootApp.value = null  // 清空已经设置的app
        }
        launch {
            SettingUtil.PersonalizationSetting.setLauncherBootAppEnable(on)
        }
    }


    /**
     * 刷新用户选择的app
     */
    suspend fun refreshBootStartAppInfo() {
        val allApps = getAllApps()  // 全部应用
        val startBootAppInfo = loadBootStartAppInfo()
        allAppsFlow.value = allApps
        _startBootApp.value = startBootAppInfo
    }

    private val packageManager: PackageManager by lazy {
        val context = App.context.applicationContext
        context.packageManager
    }

    private val iconDpi: Int by lazy {
        val context = App.context.applicationContext
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        activityManager.launcherLargeIconDensity
    }

    /**
     * 获取可以开机启动的应用
     */
    private suspend fun getAllApps(): List<BootStartAppEntity> = withContext(Dispatchers.IO) {
        val filterIntent = Intent(Intent.ACTION_MAIN, null)
        filterIntent.addCategory(Intent.CATEGORY_LAUNCHER)
        val apps = packageManager.queryIntentActivities(filterIntent, PackageManager.MATCH_ALL)
        val appsInfo = mutableListOf<BootStartAppEntity>()
        apps.filter {
            // 去除系统自带的应用
            it.activityInfo.packageName !in blackPkgList
        }.forEach { resolveInfo ->
            val iconDrawable =
                getResIconFromActivityInfo(resolveInfo.activityInfo)
                    ?: packageManager.getApplicationIcon(resolveInfo.activityInfo.packageName)
            iconDrawable.let {
                doWithoutCatch {
                    val packageName = resolveInfo.activityInfo.packageName
                    val appName = resolveInfo.loadLabel(packageManager).toString()
                    val pkgInfo = packageManager.getPackageInfo(packageName, 0)
                    val installTime = pkgInfo.firstInstallTime
                    val appInfo = BootStartAppEntity(appName, packageName, it, installTime, false)
                    appsInfo.add(appInfo)
                }
            }
        }
        // 用安装时间排序
        appsInfo.sortBy {
            it.installTime
        }
        appsInfo
    }

    private suspend fun loadBootStartAppInfo(): BootStartAppEntity? {
        _enableBootStartAppFlow.value = SettingUtil.PersonalizationSetting.isLauncherBootAppEnable().also {
            logTagV(TAG, "开机启动功能是否开启: $it")
        }

        if (!_enableBootStartAppFlow.value) {
            logTagD(TAG, "开机启动功能未开启")
            return null
        }


        val savedPkgName = SettingUtil.PersonalizationSetting.getLauncherBootAppPkg()
        if (savedPkgName.isEmpty()) {
            logTagD(TAG, "没有保存开机启动app")
            return null
        }
        return try {
            // 根据包名来加载对应的图标等信息
            val appName = appUtil.getApplicationName(savedPkgName) ?: ""
            val icon = appUtil.getIconFromPkgWithDef(savedPkgName)
            val pkgInfo = packageManager.getPackageInfo(savedPkgName, 0)
            val installTime = pkgInfo.firstInstallTime
            BootStartAppEntity(appName, savedPkgName, icon, installTime, true)
        } catch (exp: NameNotFoundException) {
            logTagW(TAG, "${savedPkgName}已经被删除")
            SettingUtil.PersonalizationSetting.setLauncherBootAppPkg("")
            null
        }

    }

    private fun getResIconFromActivityInfo(info: ActivityInfo): Drawable? {
        return try {
            val iconId = info.iconResource
            val resource = packageManager.getResourcesForApplication(info.applicationInfo)
            if (iconId != 0) {
                resource.getDrawableForDensity(iconId, iconDpi, null)
            } else {
                null
            }
        } catch (exp: Exception) {
            logTagW(TAG, "getResIconFromActivityInfo", tr = exp)
            null
        }
    }

    suspend fun findStartBootAppIndex(): Int {
        var foundIndex = getIndex()
        if (foundIndex == -1) {
            delay(800L)
            foundIndex = getIndex()
        }
        return foundIndex
    }

    private suspend fun getIndex(): Int {
        var foundIndex = -1
        startBootApp.value?.let { bootStartApp ->
            canChooseAppsFlow.firstOrNull()?.let { appList ->
                foundIndex = appList.indexOfFirst { it.pkgName == bootStartApp.pkgName }
            }
        }
        return foundIndex
    }
}