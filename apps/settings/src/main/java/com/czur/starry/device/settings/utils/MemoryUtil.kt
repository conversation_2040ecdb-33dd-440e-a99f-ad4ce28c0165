package com.czur.starry.device.settings.utils

import android.app.ActivityManager
import android.app.usage.StorageStatsManager
import android.content.ContentResolver
import android.content.Context
import android.content.pm.*
import android.os.Environment
import android.os.StatFs
import android.os.UserHandle
import android.os.UserManager
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.os.storage.VolumeInfo
import com.android.internal.R.attr.*
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.download.startDownload
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.file.filelib.FileHandler
import com.czur.starry.device.file.filelib.FileSizeItem
import com.czur.starry.device.file.filelib.SizeFileType
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.settings.app.App.Companion.context
import com.czur.starry.device.settings.manager.MemoryRoomInfo
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.TAG
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.appCache
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.appSize
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.filePackage
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.isStopClean
import com.czur.starry.device.settings.manager.MemoryRoomInfo.Companion.unInstallFile
import com.czur.starry.device.settings.manager.storage.FeatureInfo
import com.czur.starry.device.settings.manager.storage.InnerService
import com.czur.starry.device.settings.model.CacheSecondData
import com.czur.starry.device.settings.model.UninstallEntity
import com.czur.starry.device.settings.utils.StorageManagerVolumeProvider.packageManager
import com.czur.starry.device.settings.utils.StorageManagerVolumeProvider.storageManager
import com.czur.starry.device.settingslib.systemPkgList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.*
import java.util.UUID


/**
 * created by wangHao 22.0608
 */

private const val filePath = "sdcard/"
private const val fileName = "app_feature.json"
private const val FILE_PACKAGE_NAME = "com.czur.starry.device.file"
private val appUtil by lazy(LazyThreadSafetyMode.NONE) { AppUtil() }
private fun getTotalSize(info: VolumeInfo, totalInternalStorage: Long): Long {
    return if (info.getType() == VolumeInfo.TYPE_PRIVATE
        && info.getFsUuid() == StorageManager.UUID_PRIVATE_INTERNAL
        && totalInternalStorage > 0
    ) {
        totalInternalStorage
    } else {
        val path = info.getPath()
        if (path == null) {
            // Should not happen, caller should have checked.
            logTagE(MemoryRoomInfo.TAG, "info's path is null on getTotalSize(): $info")
            return 0
        }
        path.totalSpace
    }
}

suspend fun ParseJson() = withContext(Dispatchers.IO) {
    try {
        val file = File(filePath + fileName)
        //解析sdcard json表,如果不存在copy一份
        if (!file.exists()) copyAssetsToSdcard()
        val input = FileInputStream(file)
        val inputStreamReader = InputStreamReader(input, "UTF-8")
        val bufferReader = BufferedReader(inputStreamReader)
        val stringBuilder = StringBuilder()
        var line: String
        while (true) {
            //当有内容时读取一行数据，否则退出循环
            line = bufferReader.readLine() ?: break
            stringBuilder.append(line)
        }

        bufferReader.close()
        inputStreamReader.close()

        val jsonObject = JSONObject(stringBuilder.toString())

        //更新到本地
        val jsonArry = jsonObject.getJSONArray("appInfo")
        logTagD(TAG, "=stringBuilder===${jsonArry}==")
        var entityList = mutableListOf<UninstallEntity>()
        for (i in 0 until jsonArry.length()) {
            val json = jsonArry.getJSONObject(i)
            val packageName = json.getString("packageName")
            var FileNames = json.getString("FileName")

            val entity = getUninstallList(packageName, FileNames)
            entityList.add(entity)
        }
        loadLocalData(entityList)


    } catch (e: Exception) {
        e.printStackTrace()
    }
}

//获取信息列表
suspend fun getUninstallList(packageName: String, FileName: String) = withContext(Dispatchers.IO) {
    var fileList = mutableListOf<String>()
    if (FileName.contains(",")) {
        //多个目录分割添加
        val listName = FileName.split(",")
        listName.forEach {
            fileList.add(it)
        }
    } else {
        fileList.add(FileName)
    }
    UninstallEntity(packageName, fileList)
}

private fun loadLocalData(list: MutableList<UninstallEntity>) {

    unInstallFile.clear()
    var mylist = mutableListOf<UninstallEntity>()

    list.forEach {
        if (!isInstalled(it.packageName))
            mylist.add(it)
    }
    logTagD(TAG, "=====mylist==${mylist}")

    mylist.forEach {
        it.FileName.forEach { name ->
            val size = getFileSize(File(filePath + name))
            if (size != -1L) {
                unInstallFile!!.add(CacheSecondData(it.packageName, name, size))
            }
        }
    }
}

//加载卸载残留表
suspend fun loadAppCharacterList() = withContext(Dispatchers.IO) {
    var featureInfo: FeatureInfo? = null
    //检测是否下载新表
    featureInfo = requestCheckInfo()
    if (featureInfo != null && featureInfo?.update == 1) {
        //下载特征表到sdcard
        startDownload(featureInfo.url, File(filePath + fileName))
        OTAHandler.uninstall_info = featureInfo.version
        logTagD(TAG, "=====更新成功")
    }
    //解析
    ParseJson()

}


suspend fun requestCheckInfo() = withContext(Dispatchers.IO) {

    try {
        val version = OTAHandler.uninstall_info
        logTagD(TAG, "===version==$version")
        val httpEntity = HttpManager.getService<InnerService>(Constants.OTA_BASE_URL)
            .checkUninstallInfo(version, FeatureInfo::class.java)
        if (httpEntity != null && httpEntity.code == 1000) {
            logTagD(TAG, "httpEntity.body==${httpEntity.body}")
            httpEntity.body
        } else {
            null
        }
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}


private fun copyAssetsToSdcard() {
    try {
        val Name = filePath + fileName// "sdcard/app_feature.json"
        val myOutput = FileOutputStream(Name)
        val myInput: InputStream = context.assets.open(fileName)
        val buffer = ByteArray(1024)
        var length = myInput.read(buffer)
        while (length > 0) {
            myOutput.write(buffer, 0, length)
            length = myInput.read(buffer)
        }
        myOutput.flush()
        myInput.close()
        myOutput.close()

    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun getFileSize(file: File): Long {
    var size = 0L
    if (file.exists()) {
        val fileList = file.listFiles()
        fileList.forEach {
            if (it.isDirectory) {
                size += getFileSize(it)
            } else {
                size += it.length()
            }
        }
        return size
    }
    return -1

}


private fun deleteFile(file: File) {
    try {
        if (file.exists()) {
            if (file.isFile) {
                file.delete()
            } else if (file.isDirectory) {
                val list = file.listFiles()
                if (list.isNullOrEmpty()) {
                    file.delete()
                } else {
                    list.forEach {
                        deleteFile(it)
                    }
                }
                file.delete()
            }

        }

    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun isInstalled(packageName: String): Boolean {
    val appInfoList = packageManager.getInstalledPackages(0)
    appInfoList.forEach {
        if (it.packageName == packageName) {
            return true
        }
    }
    return false
}

val dataPath: File = Environment.getDataDirectory()
const val MEMORY_32G = 32L * 1000 * 1000 * 1000
const val MEMORY_64G = 64L * 1000 * 1000 * 1000
fun getStorageTotal() {
    val sm = StorageManagerVolumeProvider
    val totalInternalStorage: Long = sm.getPrimaryStorageSize()
    var privateFreeBytes: Long = 0
    var privateUseBytes: Long = 0
    var privateTotalBytes: Long = 0

    for (info in sm.getVolumes()) {
        val path: File = info.getPath()
        if (info.getType() != VolumeInfo.TYPE_PRIVATE || path == null) {
            continue
        }

        privateTotalBytes += getTotalSize(info, totalInternalStorage)
        privateFreeBytes += path.freeSpace
//        privateUseBytes = privateTotalBytes - privateFreeBytes
    }
    //由于需求要显示对外宣传的存储容量32G，后续扩展64G，做如下判断
    if (privateTotalBytes <= MEMORY_32G) {
        MemoryRoomInfo.totalSize = MEMORY_32G
    } else if (privateTotalBytes in (MEMORY_32G + 1) until MEMORY_64G) {
        MemoryRoomInfo.totalSize = MEMORY_64G
    } else {
        MemoryRoomInfo.totalSize = privateTotalBytes
    }

    //剩余容量和提醒不足计算方式统一
    val mDataFileStats = StatFs(dataPath.absolutePath)
    val mFreeMem = mDataFileStats!!.availableBlocksLong *
            mDataFileStats!!.blockSizeLong

    MemoryRoomInfo.freeSize = mFreeMem

    MemoryRoomInfo.useSize = MemoryRoomInfo.totalSize - mFreeMem

}


fun storageDetailSystem() {
    val cacheSize = 364504L * 1000 //去除cache分区大小
    val file = File(Environment.getExternalStorageDirectory().absolutePath)
    MemoryRoomInfo.systemSize =
        MemoryRoomInfo.totalSize - file.totalSpace - Environment.getVendorDirectory().totalSpace - Environment.getProductDirectory().totalSpace - Environment.getSystemExtDirectory().totalSpace - cacheSize
}


suspend fun getPrograms() {
    appCache.clear()
    var totalSize = 0L
    val ssm =
        CZURAtyManager.appContext.getSystemService(Context.STORAGE_STATS_SERVICE) as StorageStatsManager
    //获取UUID
    val storageUuid = storageManager.primaryStorageVolume.storageUuid
    logTagD(TAG, "===storageUUID=$storageUuid")
    val systemApps = packageManager.getInstalledPackages(0)
    systemApps.forEach {
        val itemSize = StorageManagerVolumeProvider.queryPackageSize(
            it.packageName,
            storageUuid!!,
            ssm
        )
        totalSize += itemSize
    }

    appSize = totalSize
}

suspend fun getMediaPhotoDoc() {
    FileHandler.getFileSize().forEach {
//        logTagD(TAG, "type:${it.type} + ${it.size}")
        if (it.type == SizeFileType.MEDIA) {
            MemoryRoomInfo.mediaSize = it.size
        }

        if (it.type == SizeFileType.IMAGE) {
            MemoryRoomInfo.picSize = it.size
        }

        if (it.type == SizeFileType.DOCUMENT) {
            MemoryRoomInfo.docSize = it.size
        }

        if (it.type == SizeFileType.APK) {
            filePackage.clear()
            filePackage = it.fileItems
        }

    }
}


suspend fun loadRunningApp() = withContext(Dispatchers.IO) {
    MemoryRoomInfo.appRunning.clear()
    //获取正在运行的程序
    val activityManager =
        CZURAtyManager.appContext?.getSystemService(ActivityManager::class.java)
            ?: return@withContext
    val list = activityManager.runningAppProcesses

    val runningMap = HashMap<String, Int>()
    val runningList = mutableListOf<Triple<String, String, Int>>()
    list.forEach { runningInfo ->
        runningInfo.pkgList.forEach {
            runningMap[it] = runningInfo.pid
        }
    }

    //所有安装的程序第三方
    val apps = packageManager.getInstalledPackages(0)
    apps.filter {
        val appInfo = it.applicationInfo
        appInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0
    }.forEach { it ->
        if (runningMap.containsKey(it.packageName)) {
            runningList.add(
                Triple(
                    it.packageName,
                    it.applicationInfo.loadLabel(packageManager).toString(),
                    runningMap[it.packageName]!!
                )
            )
        }
    }
    runningList.forEach {

        val memoryInfo = activityManager.getProcessMemoryInfo(intArrayOf(it.third))
        val size = memoryInfo[0].totalPss * 1000L
        MemoryRoomInfo.appRunning!!.add(
            CacheSecondData(
                it.first,
                it.second,
                size,
                false
            )
        )
    }

}


//删除apk
suspend fun deleteFilesByDirectory(filePathList: List<String>) =
    withContext(Dispatchers.IO) {
        try {
            logTagD(TAG, "清理APK文件:${filePathList.size} - ${filePathList.joinToString()}")
            run loop@{
                if (isStopClean || !isActive) return@loop
                filePathList.forEach {
                    val file = File(it)
                    file.delete()
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


//kill running
suspend fun forceStopAPP(runningAppPkg: List<String>) = withContext(Dispatchers.IO) {
    try {
        logTagD(TAG, "清理运行程序:${runningAppPkg.size} - ${runningAppPkg.joinToString()}")
        val activityManager =
            CZURAtyManager.appContext?.getSystemService(ActivityManager::class.java)
                ?: return@withContext

        run loop@{
            if (isStopClean || !isActive) return@loop
            runningAppPkg.forEach {
                activityManager.forceStopPackage(it)
            }
        }

    } catch (e: Exception) {
        e.printStackTrace()
    }
}

//删除卸载sdcard 残留
suspend fun removeUninstallData(clearKey: List<String>) =
    withContext(Dispatchers.IO) {
        try {
            logTagD(TAG, "清理残留文件:${clearKey.size} - ${clearKey.joinToString()}")
            run loop@{
                if (isStopClean || !isActive) return@loop
                clearKey.forEach {
                    val file = File(it)
                    file.delete()
                }
            }


        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


object StorageManagerVolumeProvider {
    val storageManager: StorageManager by lazy {
        CZURAtyManager.appContext.getSystemService(StorageManager::class.java)
    }
    val mUserManager: UserManager by lazy {
        CZURAtyManager.appContext.getSystemService(UserManager::class.java)
    }
    val packageManager: PackageManager by lazy {
        CZURAtyManager.appContext.packageManager
    }
    val contentResolver: ContentResolver = CZURAtyManager.appContext.contentResolver
    fun getPrimaryStorageSize(): Long {
        return storageManager.getPrimaryStorageSize()
    }

    fun getVolumes(): List<VolumeInfo> {
        return storageManager.getVolumes()
    }

    fun getStorageVolumes(): List<StorageVolume> {
        return storageManager.getStorageVolumes()
    }


    suspend fun queryPackageSize(
        pkgName: String,
        storageUuid: UUID,
        ssm: StorageStatsManager
    ): Long {
        return suspendCancellableCoroutine<Long> { continuation ->

            try {
                val info = packageManager.getApplicationInfo(pkgName, 0)
                appUtil.getApplicationName(pkgName)
                val appName: String = appUtil.getApplicationName(pkgName) ?: ""
                if (info != null) {
                    val stats = ssm.queryStatsForPackage(
                        storageUuid,
                        pkgName,
                        UserHandle.getUserHandleForUid(info.uid)
                    )
                    val appSize = stats.appBytes
                    val dataSize = stats.dataBytes
                    val cacheSize = stats.cacheBytes
                    logTagD(TAG, "==appName=$appName")
                    logTagD(TAG, "===$appSize - $dataSize - $cacheSize")
                    var totalSize = appSize + cacheSize
                    if (pkgName != FILE_PACKAGE_NAME)
                        totalSize += dataSize
                    if ((pkgName in systemPkgList || info.flags and ApplicationInfo.FLAG_SYSTEM == 0)
                    ) {
                        //pair 添加大小，包名
                        logTagD(TAG, "====cacheSize=$cacheSize")
                        appCache.add(CacheSecondData(pkgName, appName, cacheSize, true))
                    }
                    continuation.resumeWith(Result.success(totalSize))
                }
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }

        }
    }


    //获取apk文件
    fun getApkPackage(list: MutableList<FileSizeItem>): MutableList<CacheSecondData> {
        val mutableList = mutableListOf<CacheSecondData>()
        list.forEach {
            mutableList.add(CacheSecondData(it.absPath, it.fileName, it.size, true))
        }
        return mutableList
    }

    /**
     * 清除应用缓存
     */
    suspend fun clearAppCache(appPkgList: List<String>) = withContext(Dispatchers.IO) {
        logTagD(TAG, "clearAppCache:${appPkgList.size} - ${appPkgList.joinToString()}")
        try {
            run loop@{
                appPkgList.forEach {
                    if (isStopClean || !isActive) return@loop
                    val mClearCacheObserver = ClearCacheObserver()
                    packageManager.deleteApplicationCacheFiles(it, mClearCacheObserver)
                }
            }

        } catch (e: Exception) {

            e.printStackTrace()
        }
    }

    internal class ClearCacheObserver : IPackageDataObserver.Stub() {
        override fun onRemoveCompleted(packageName: String, succeeded: Boolean) {
        }
    }


}