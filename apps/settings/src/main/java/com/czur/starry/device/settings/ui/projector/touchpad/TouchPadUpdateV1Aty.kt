package com.czur.starry.device.settings.ui.projector.touchpad

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.settings.touchpad.isTouchPad
import com.czur.starry.device.settings.ui.projector.touchpad.update.TouchPadUpdateActivity
import com.czur.starry.device.settings.ui.projector.touchpad.update.UpdateDevice
import kotlinx.coroutines.*
import java.io.File

/**
 * V1触控板固件升级页面
 */
private const val TAG = "TouchPadUpdateAty"

@SuppressLint("MissingPermission")
class TouchPadUpdateV1Aty : TouchPadUpdateActivity() {

    private lateinit var otaBytes: Deferred<ByteArray>
    private var updateDevice: UpdateDevice? = null

    private val btReceiver = BTReceiver()

    private val updateDeviceCallback: UpdateDevice.Callback = object : UpdateDevice.Callback {
        override fun onConnected(updateDevice: UpdateDevice?) {}

        override fun onDisconnected(updateDevice: UpdateDevice?) {
            logTagW(TAG, "onDisconnected")
            finishByDisconnect()
        }

        override fun onServicesDiscovered(updateDevice: UpdateDevice?) {
            logTagD(TAG, "onServicesDiscovered")
            updateDevice?.let {
                launch {
                    val bytes = otaBytes.await()
                    if (bytes.isEmpty()) {
                        logTagW(TAG, "固件为空!")
                        finishSelf(false)
                    } else {
                        it.startOta(otaBytes.await())
                    }
                }
            }
        }

        override fun onOtaStateChanged(updateDevice: UpdateDevice, state: Int) {
            logTagD(TAG, "onOtaStateChanged")
            when (state) {
                UpdateDevice.STATE_PROGRESS -> {
                    logTagD(TAG, "ota progress: ${updateDevice.otaProgress}")
                    launch {
                        val progress = updateDevice.otaProgress
                        updateProgressUI(if (progress >= 100) 99 else progress)
                    }
                }

                UpdateDevice.STATE_SUCCESS -> {
                    logTagV(TAG, "触控板升级成功")
                    finishSelf(true)
                }

                UpdateDevice.STATE_FAILURE -> {
                    logTagV(TAG, "触控板升级失败")
                    finishSelf(false)
                }
            }
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        launch {
            // 找到连接的触控板
            val device = getTouchPadDevice()

            if (device == null) {
                logTagW(TAG, "没有找到连接的触控板")
                finishSelf(false)
                return@launch
            }

            otaBytes = async { readFirmware(firmwarePath) }

            startConnect(device)
        }

    }

    override fun onStart() {
        super.onStart()
        registerBTReceiver()
    }

    override fun onStop() {
        unregisterBTReceiver()
        super.onStop()
    }

    /**
     * 注册蓝牙连接广播
     */
    private fun registerBTReceiver() {
        registerReceiver(btReceiver, IntentFilter().apply {
            addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
        })
    }

    /**
     * 取消注册蓝牙连接广播
     */
    private fun unregisterBTReceiver() {
        unregisterReceiver(btReceiver)
    }

    /**
     * 开始连接
     */
    private fun startConnect(hidDevice: BluetoothDevice) {
        updateDevice = UpdateDevice(
            hidDevice
        ).apply {
            setCallback(updateDeviceCallback)
            connect(applicationContext)
        }
    }

    override fun onDestroy() {
        doWithoutCatch {
            updateDevice?.disconnect()
        }
        super.onDestroy()
    }


    /**
     * 读取固件数组
     */
    private suspend fun readFirmware(fileName: String): ByteArray = withContext(Dispatchers.IO) {
        val file = File(fileName)
        file.readBytes()
    }

    /**
     * 获取触控板
     */
    @SuppressLint("MissingPermission")
    private suspend fun getTouchPadDevice(): BluetoothDevice? = withContext(Dispatchers.IO) {
        BluetoothAdapter.getDefaultAdapter().bondedDevices.find {
            it.isTouchPad()
        }
    }


    /**
     * 蓝牙广播
     */
    private inner class BTReceiver : BroadcastReceiver() {
        private fun getBluetoothDevice(intent: Intent): BluetoothDevice {
            return intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE) as BluetoothDevice
        }

        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == BluetoothDevice.ACTION_ACL_DISCONNECTED) {
                // 蓝牙断开连接
                val device = getBluetoothDevice(intent)

                if (device.isTouchPad()) {
                    logTagD(TAG, "蓝牙触控板断开连接")
                    finishByDisconnect()
                }
            }
        }

    }
}