package com.czur.starry.device.settings.manager

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

/**
 * Created by 陈丰尧 on 3/8/21
 */
object NetManager {
    private const val EMPTY_MAC = "02:00:00:00:00:00"

    private const val WIFI_MAC_FILE_PATH = "/sys/class/net/wlan0/address"
    private const val ETH_MAC_FILE_PATH = "/sys/class/net/eth0/address"

    /**
     * 获取wifi mac地址
     */
    suspend fun getWIFIMacAddress(): String = getMacAddress(WIFI_MAC_FILE_PATH)

    /**
     * 获取有线网 mac地址
     */
    @Deprecated("使用SystemManager#getEthernetMac()方法获取")
    suspend fun getEtherMacAddress():String = getMacAddress(ETH_MAC_FILE_PATH)

    private suspend fun getMacAddress(filePath: String): String = withContext(Dispatchers.IO) {
        var wifiAddress = EMPTY_MAC
        try {
            wifiAddress = File(filePath).readText()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        wifiAddress
    }

}