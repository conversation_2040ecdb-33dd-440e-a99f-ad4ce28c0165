package com.czur.starry.device.settings.widget

import com.czur.czurutils.log.logTagW
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import kotlin.math.min

/**
 * Created by 陈丰尧 on 2021/8/26
 * 触控板的电池显示
 */

private const val TAG = "BatteryView"

class BatteryView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val BORDER_WIDTH = 2F      // 边框宽度
        private const val CONTENT_PADDING = 2F   // 边框与内容的间距

        // 由于线条有宽度, 所有外边框需要向内收缩 线宽/2的距离
        private const val SHRINKAGE = BORDER_WIDTH / 2
    }

    private val paint = Paint().apply {
        color = Color.WHITE
        isAntiAlias = true
    }


    var power: Int = -1
        set(value) {
            if (value !in -1..100) {
                logTagW(TAG, "电量只能是[-1,100]范围内")
                return
            }
            field = value
            invalidate()
        }

    /**
     * 边框的范围
     */
    private val borderRectF by lazy {
        RectF(SHRINKAGE, SHRINKAGE, width - SHRINKAGE, height - SHRINKAGE)
    }

    /**
     * 电量的范围
     */
    private val powerRectF by lazy {
        borderRectF.getSmaller(CONTENT_PADDING + BORDER_WIDTH)
    }

    /**
     * 没有电量的空白范围
     */
    private val blackRectF by lazy {
        borderRectF.getSmaller(CONTENT_PADDING + BORDER_WIDTH)
    }

    /**
     * 用户绘制电池电量的混合模式
     */
    private val xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (power < 0) {
            return
        }

        // 绘制边框
        drawBorder(canvas)
        // 绘制电量
        drawPower(canvas)
    }

    /**
     * 绘制边框
     */
    private fun drawBorder(canvas: Canvas) {
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = BORDER_WIDTH
        paint.color = Color.WHITE

        val r = borderRectF.roundR

        canvas.drawRoundRect(borderRectF, r, r, paint)
    }

    /**
     * 绘制容量
     */
    private fun drawPower(canvas: Canvas) {
        paint.style = Paint.Style.FILL
        paint.color = Color.WHITE
        // 设置离屏缓存
        val saved = canvas.saveLayer(null, null)

        // 绘制电量
        val r = powerRectF.roundR
        canvas.drawRoundRect(powerRectF, r, r, paint)

        paint.xfermode = xfermode
        paint.color = Color.BLACK
        // 根据电量计算 要扣除的半分比
        blackRectF.left = powerRectF.left + powerRectF.width() * (power / 100F)
        // 扣除没有电的部分
        canvas.drawRect(blackRectF, paint)
        paint.xfermode = null

        canvas.restoreToCount(saved)
    }

}

/**
 * 获取圆角半径
 * 半径大小为,宽高的小值/2
 */
private val RectF.roundR: Float
    get() = min(width(), height()) / 2F

/**
 * 根据当前RectF生成一个四周小[xy]的RectF
 */
private fun RectF.getSmaller(xy: Float): RectF = RectF(this).apply {
    left += xy
    top += xy
    right -= xy
    bottom -= xy
}