package com.czur.starry.device.settings.ui.system.time

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.utils.darkContentTextColor
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.keyboard.focusAndShowKeyboard
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentTimeBinding
import com.czur.starry.device.settings.ui.system.vm.TimeViewModel
import com.czur.uilib.et.AutoChangeFocusEditText

/**
 * Created by 陈丰尧 on 2024/8/5
 */
class TimeFragment : BaseBindingMenuFragment<FragmentTimeBinding>() {
    private val timeViewModel: TimeViewModel by viewModels()

    override fun FragmentTimeBinding.initBindingViews() {
        changeTimeBtn.setDebounceTouchClickListener {
            timeViewModel.changeMode()
            binding.timeYear3Et.focusAndShowKeyboard()
        }

        applyTimeBtn.setOnClickListener {
            val year = binding.timeYear1Et.textOrHint.toInt() * 1000 +
                    binding.timeYear2Et.textOrHint.toInt() * 100 +
                    binding.timeYear3Et.textOrHint.toInt() * 10 +
                    binding.timeYear4Et.textOrHint.toInt()
            val month = binding.timeMonth1Et.textOrHint.toInt() * 10 +
                    binding.timeMonth2Et.textOrHint.toInt()
            val day = binding.timeDay1Et.textOrHint.toInt() * 10 +
                    binding.timeDay2Et.textOrHint.toInt()
            val hour = binding.timeHour1Et.textOrHint.toInt() * 10 +
                    binding.timeHour2Et.textOrHint.toInt()
            val minute = binding.timeMinute1Et.textOrHint.toInt() * 10 +
                    binding.timeMinute2Et.textOrHint.toInt()
            val second = binding.timeSecond1Et.textOrHint.toInt() * 10 +
                    binding.timeSecond2Et.textOrHint.toInt()
            launch {
                if (!timeViewModel.verifyLegality(year, month, day, hour, minute, second)) {
                    toast(R.string.toast_set_time_error)
                    return@launch
                }
                timeViewModel.setTime(year, month, day, hour, minute, second)
            }
        }
    }

    private fun refreshYear(year: Int) {
        val year1 = year / 1000
        val year2 = year / 100 % 10
        val year3 = year / 10 % 10
        val year4 = year % 10

        binding.timeYear1Et.setText(year1.toString())
        binding.timeYear2Et.setText(year2.toString())
        binding.timeYear3Et.setText(year3.toString())
        if (!binding.timeYear4Et.isEditedStatus) {
            binding.timeYear4Et.setText(year4.toString())
        }

        binding.timeYear1Et.setHint(year1.toString())
        binding.timeYear2Et.setHint(year2.toString())
        binding.timeYear3Et.setHint(year3.toString())
        binding.timeYear4Et.setHint(year4.toString())
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        // 年
        repeatCollectOnResume(timeViewModel.yearFlow) {
            refreshYear(it)
        }

        // 月
        repeatCollectOnResume(timeViewModel.monthFlow) {
            val month1 = it / 10
            val month2 = it % 10

            if (!binding.timeMonth1Et.isEditedStatus) {
                binding.timeMonth1Et.setText(month1.toString())
            }
            if (!binding.timeMonth2Et.isEditedStatus) {
                binding.timeMonth2Et.setText(month2.toString())
            }

            binding.timeMonth1Et.setHint(month1.toString())
            binding.timeMonth2Et.setHint(month2.toString())
        }

        // 日
        repeatCollectOnResume(timeViewModel.dayFlow) {
            val day1 = it / 10
            val day2 = it % 10

            if (!binding.timeDay1Et.isEditedStatus) {
                binding.timeDay1Et.setText(day1.toString())
            }
            if (!binding.timeDay2Et.isEditedStatus) {
                binding.timeDay2Et.setText(day2.toString())
            }

            binding.timeDay1Et.setHint(day1.toString())
            binding.timeDay2Et.setHint(day2.toString())
        }

        // 时
        repeatCollectOnResume(timeViewModel.hourFlow) {
            val hour1 = it / 10
            val hour2 = it % 10
            if (!binding.timeHour1Et.isEditedStatus) {
                binding.timeHour1Et.setText(hour1.toString())
            }
            if (!binding.timeHour2Et.isEditedStatus) {
                binding.timeHour2Et.setText(hour2.toString())
            }

            binding.timeHour1Et.setHint(hour1.toString())
            binding.timeHour2Et.setHint(hour2.toString())
        }

        // 分
        repeatCollectOnResume(timeViewModel.minuteFlow) {
            val minute1 = it / 10
            val minute2 = it % 10
            if (!binding.timeMinute1Et.isEditedStatus) {
                binding.timeMinute1Et.setText(minute1.toString())
            }
            if (!binding.timeMinute2Et.isEditedStatus) {
                binding.timeMinute2Et.setText(minute2.toString())
            }

            binding.timeMinute1Et.setHint(minute1.toString())
            binding.timeMinute2Et.setHint(minute2.toString())
        }

        // 秒
        repeatCollectOnResume(timeViewModel.secondFlow) {
            val second1 = it / 10
            val second2 = it % 10
            if (!binding.timeSecond1Et.isEditedStatus) {
                binding.timeSecond1Et.setText(second1.toString())
            }
            if (!binding.timeSecond2Et.isEditedStatus) {
                binding.timeSecond2Et.setText(second2.toString())
            }

            binding.timeSecond1Et.setHint(second1.toString())
            binding.timeSecond2Et.setHint(second2.toString())
        }

        repeatCollectOnResume(timeViewModel.editModeFlow) {
            binding.clickMarkView.gone(it)
            binding.changeTimeBtn.gone(it)
            binding.applyTimeBtn.gone(!it)
            if (!it) {
                binding.timeYear3Et.darkContentTextColor()
                binding.timeYear4Et.darkContentTextColor()
                binding.timeMonth1Et.darkContentTextColor()
                binding.timeMonth2Et.darkContentTextColor()
                binding.timeDay1Et.darkContentTextColor()
                binding.timeDay2Et.darkContentTextColor()
                binding.timeHour1Et.darkContentTextColor()
                binding.timeHour2Et.darkContentTextColor()
                binding.timeMinute1Et.darkContentTextColor()
                binding.timeMinute2Et.darkContentTextColor()
                binding.timeSecond1Et.darkContentTextColor()
                binding.timeSecond2Et.darkContentTextColor()
            }

            // 因为年份可能会自动还原,所以这里再次获取一次
            refreshYear(timeViewModel.yearFlow.value)
        }
    }

    private val AutoChangeFocusEditText.textOrHint: String
        get() = if (text.isNullOrEmpty()) hint.toString() else (text?.toString() ?: "")
}