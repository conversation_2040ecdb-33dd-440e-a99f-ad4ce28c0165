package com.czur.starry.device.settings.utils;

import android.os.Build;

import com.czur.czurutils.log.CZURLogUtilsKt;
import com.czur.starry.device.baselib.utils.LogUtilKt;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Created by 陈丰尧 on 3/8/21
 */
public class WebViewUtil {
    public static final String TAG = "WebViewUtil";
    /**
     * 给WebViewFactory sProviderInstance赋值，避免进行进程判断而抛出异常（系统应用不能使用WebView）
     */
    @SuppressWarnings("unchecked")
    public static void hookWebView() {
        int sdkInt = Build.VERSION.SDK_INT;
        try {
            Class<?> factoryClass = Class.forName("android.webkit.WebViewFactory");
            Field field = factoryClass.getDeclaredField("sProviderInstance");
            field.setAccessible(true);
            Object sProviderInstance = field.get(null);
            if (sProviderInstance != null) {
                logI(TAG, "sProviderInstance isn't null",null);
                return;
            }

            Method getProviderClassMethod;
            if (sdkInt > 22) {
                getProviderClassMethod = factoryClass.getDeclaredMethod("getProviderClass");
            } else if (sdkInt == 22) {
                getProviderClassMethod = factoryClass.getDeclaredMethod("getFactoryClass");
            } else {
                logI(TAG, "Don't need to Hook WebView",null);
                return;
            }
            getProviderClassMethod.setAccessible(true);
            Class<?> factoryProviderClass = (Class<?>) getProviderClassMethod.invoke(factoryClass);
            Class<?> delegateClass = Class.forName("android.webkit.WebViewDelegate");
            Constructor<?> delegateConstructor = delegateClass.getDeclaredConstructor();
            delegateConstructor.setAccessible(true);
            if (sdkInt < 26) {//低于Android O版本
                Constructor<?> providerConstructor = factoryProviderClass.getConstructor(delegateClass);
                if (providerConstructor != null) {
                    providerConstructor.setAccessible(true);
                    sProviderInstance = providerConstructor.newInstance(delegateConstructor.newInstance());
                }
            } else {
                Field chromiumMethodName = factoryClass.getDeclaredField("CHROMIUM_WEBVIEW_FACTORY_METHOD");
                chromiumMethodName.setAccessible(true);
                String chromiumMethodNameStr = (String) chromiumMethodName.get(null);
                if (chromiumMethodNameStr == null) {
                    chromiumMethodNameStr = "create";
                }
                Method staticFactory = factoryProviderClass.getMethod(chromiumMethodNameStr, delegateClass);
                if (staticFactory != null) {
                    sProviderInstance = staticFactory.invoke(null, delegateConstructor.newInstance());
                }
            }

            if (sProviderInstance != null) {
                field.set("sProviderInstance", sProviderInstance);
                logI(TAG, "Hook success!",null);
            } else {
                logI(TAG, "Hook failed!",null);
            }
        } catch (Throwable e) {
            logW(TAG,"HookWebView 失败", e);
        }
    }

    private static void logI(String tag, String message) {
        CZURLogUtilsKt.logTagI(tag, new String[]{message}, null);
    }
    private static void logI(String tag, String message, Throwable tr) {
        CZURLogUtilsKt.logTagI(tag, new String[]{message}, tr);
    }
    private static void logW(String tag, String message, Throwable throwable) {
        CZURLogUtilsKt.logTagW(tag, new String[]{message}, throwable);
    }
}
