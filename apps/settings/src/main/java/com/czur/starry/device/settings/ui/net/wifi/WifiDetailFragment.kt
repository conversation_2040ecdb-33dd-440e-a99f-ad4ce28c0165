package com.czur.starry.device.settings.ui.net.wifi

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.net.*
import android.net.wifi.SupplicantState
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.os.Bundle
import android.text.format.Formatter
import android.view.View
import android.view.ViewTreeObserver
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentWifiDetailBinding
import com.czur.starry.device.settings.ui.net.ethernet.IpConfigurationProxy
import com.czur.starry.device.settings.ui.net.ethernet.WifiConfigurationProxy
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel.Companion.CONNECTED_FIRST_TAG
import com.czur.starry.device.settings.utils.*
import com.czur.starry.device.settings.utils.WifiConfigUtil.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.net.Inet4Address
import java.net.InetAddress


class WifiDetailFragment : BaseBindingMenuFragment<FragmentWifiDetailBinding>() {
    companion object {
        private const val TAG = "WifiDetailFragment"
        var positionCursor = 0

        //已连接状态
        private const val STATE_IS_CONNECTED = 0

        //未连接已保存状态
        private const val STATE_IS_SAVE = 1

        //未连接未保存
        private const val STATE_IS_DISCONNECTED = 2

        private const val IP_MODE_DHCP = 0
        private const val IP_MODE_STATIC = 1
    }

    private val ETHNET_MASK = "ethnet_mask"
    private val ETHNET_GATE_WAY = "ethnet_gate_way"
    private val ETHNET_DNS = "ethnet_dns"
    private var mEthIpAddress: String = "***********"
    private var mEthNetmask = WIFIUtilConstant.NET_MASK_DEFAULT
    private var mEthGateway = "***********"
    private var mEthdns = "***********"
    private val nullIpInfo = "0.0.0.0"
    private var mIntentFilter: IntentFilter? = null
    private var config: WifiConfiguration? = null
    private var dhcpInfo = 600
    private var ssid = ""
    private val AUTOSSID = "autossid"
    private var sp: SharedPreferences? = null
    private val wifiViewModel: WifiViewModel by viewModels({ requireActivity() })
    private val loadingDialog by lazy { LoadingDialog() }
    private val wifiManager by lazy { wifiViewModel.wifiManager }
    val connectivityManager: ConnectivityManager by lazy {
        val context = CZURAtyManager.getContext().applicationContext
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    //保存后是否返回wifi列表标志
    private var isSave = false

    //是否连接或保存状态ssid
    private var isInSave = false

    //当前连接状态
    private var currentState = 0

    private var ipMode: Int = IP_MODE_DHCP

    override fun FragmentWifiDetailBinding.initBindingViews() {
        ssid = wifiViewModel.getSSIDByBSSID()
        ssidNameTv.text = ssid
        sp = activity?.getSharedPreferences(SPHandler.SP_NAME, Context.MODE_PRIVATE)
        isInSave = isInSaveList(sp!!, ssid)

        if (wifiViewModel.isConnectedSSID) {
            currentState = STATE_IS_CONNECTED
        } else if (isInSave) {
            currentState = STATE_IS_SAVE
        } else {
            currentState = STATE_IS_DISCONNECTED
        }
        launch {
            btnVerify.visibility =
                if (wifiViewModel.isConnectedSSID && isWifiSetPortal().getOrDefault(false)) View.VISIBLE else View.GONE
        }

        availableNetWorkUI()

        etIpAddr.background = null
        etNetmask.background = null
        etGateway.background = null
        etDns.background = null
    }

    override fun initData(savedInstanceState: Bundle?) {
        wifiViewModel.wifiState.observe(viewLifecycleOwner) {
            if (!wifiViewModel.isConnectedSSID) return@observe
            if (it) {
                handleWifiStateChange(WifiManager.WIFI_STATE_ENABLED)
            } else {
                handleWifiStateChange(WifiManager.WIFI_STATE_DISABLED)
            }
        }

        // 判断当前的wifi是否处于连接状态
        wifiViewModel.wifInfo.observe(viewLifecycleOwner) {
            /// 同一个wifi名，可能对应多个路由(ssid)，所以添加 是否同一wifi名 且 是否连接完成。
            var connectModel = wifiViewModel.wifiManager.connectionInfo
            var connectSsid = connectModel.ssid.trimStart('"').trimEnd('"')
            if (it.bssid == wifiViewModel.joinWifiBSSID || (connectSsid == wifiViewModel.getSSIDByBSSID() && connectModel.supplicantState == SupplicantState.COMPLETED)) {
                binding.ssidNameTv.setTextColor(resources.getColor(R.color.white))
                binding.ssidNameLabelTv.setTextColor(resources.getColor(R.color.white))
                binding.ssidNameBg.isSelected = true
                wifiViewModel.isConnectedSSID = true
            } else {
                binding.ssidNameTv.setTextColor(resources.getColor(R.color.text_common))
                binding.ssidNameLabelTv.setTextColor(resources.getColor(R.color.text_common))
                binding.ssidNameBg.isSelected = false
                wifiViewModel.isConnectedSSID = false
            }
        }
    }

    override fun onResume() {
        super.onResume()
        refreshUI()
        binding.wifiDetailLayout.viewTreeObserver.addOnGlobalFocusChangeListener(focusChangeListener)
    }


    private fun getNetworkId(ssid: String): Int {
        val wifiConfigurationList = wifiManager.configuredNetworks
        for (wifiConfig in wifiConfigurationList) {
            if (wifiConfig.SSID == "\"$ssid\"") {
                return wifiConfig.networkId
            }
        }
        return 0
    }


    private fun availableNetWorkUI() {
        //未连接并未保存过
        if (currentState == STATE_IS_DISCONNECTED) {
            binding.ipModeBg.isEnabled = false
            binding.ignoreWifiBtn.visibility = View.GONE
            binding.joinWifiBtn.visibility = View.VISIBLE

        } else {
            binding.ipModeBg.isEnabled = true
            binding.ignoreWifiBtn.visibility = View.VISIBLE
            binding.joinWifiBtn.visibility = View.GONE
        }


        binding.ignoreWifiBtn.setOnDebounceClickListener {
            showDoubleDialog()
        }

        binding.btnBack.setOnClickListener {

            wifiViewModel.showDisplay()
        }
        binding.btnSave.setOnClickListener {
            launch {
                isSave = true

                if (setStaticIpConfig(wifiViewModel.isConnectedSSID)) {
                    toast(R.string.save_sucess)
                    delay(300)
                    wifiViewModel.showDisplay()
                } else {
                    toast(R.string.save_error)
                    delay(300)
                    wifiViewModel.showDisplay()
                }
            }
        }

        binding.btnVerify.setOnClickListener {
            startCaptivePortalLogin()
        }

        binding.ipModeBg.setOnClickListener {
            if (ipMode == IP_MODE_STATIC) {
                logTagD(TAG, "切换到自动")
                launch {
                    loadingDialog.show()
                    if (currentState == STATE_IS_SAVE) {
                        setDHCPIpConfig(false)
                        refreshUI()
                        delay(2000)
                        if (loadingDialog != null)
                            loadingDialog.dismiss()
                    } else {
                        if (setDHCPIpConfig(true)) {
                            autoUI()
                        } else {
                            staticUI()
                        }
                    }
                    delay(5000)
                    if (loadingDialog != null)
                        loadingDialog.dismiss()
                }
            } else {
                logTagD(TAG, "切换到手动")
                launch {
                    loadingDialog.show()
                    if (currentState == STATE_IS_SAVE) {
                        setStaticIpConfig(false)
                        refreshUI()
                        delay(2000)
                        if (loadingDialog != null)
                            loadingDialog.dismiss()
                    } else {
                        isSave = false
                        if (setStaticIpConfig(true)) {
                            staticUI()
                        } else {
                            autoUI()
                        }
                    }
                    delay(5000)
                    if (loadingDialog != null)
                        loadingDialog.dismiss()
                }
            }
        }

        binding.joinWifiBtn.setOnClickListener {
            if (wifiViewModel.getBSSID() != null) {
                wifiViewModel.showJoin(wifiViewModel.getBSSID())
            }
        }

        binding.etIpAddr.setOnIPChangeListener {
            changeSaveBenEnable()
        }
        binding.etNetmask.setOnIPChangeListener {
            changeSaveBenEnable()
        }
        binding.etGateway.setOnIPChangeListener {
            changeSaveBenEnable()
        }
        binding.etDns.setOnIPChangeListener {
            changeSaveBenEnable()
        }

    }

    private fun changeSaveBenEnable() {
        val currentIP = binding.etIpAddr.getText()
        val currentNetmask = binding.etNetmask.getText()
        val currentGateway = binding.etGateway.getText()
        val currentDns = binding.etDns.getText()
        binding.btnSave.isEnabled = ((currentIP != nullIpInfo && currentIP != mEthIpAddress)
                || (currentNetmask != nullIpInfo && currentNetmask != mEthNetmask)
                || (currentGateway != nullIpInfo && currentGateway != mEthGateway)
                || (currentDns != nullIpInfo && currentDns != mEthdns))
    }

    /**
     * 启动CaptivePortal页面登陆验证
     */
    private fun startCaptivePortalLogin() {
        val intent = Intent(CZURAtyManager.appContext, CaptivePortalWebViewActivity::class.java)
        startVerify.launch(intent)
    }

    private val startVerify =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data = result.data
                val result = data?.getBooleanExtra(WifiViewModel.SHOW_VERIFY_KEY, true) as Boolean
                if (!result) {
                    wifiViewModel.isNeedVerify.postValue(result)
                    binding.btnVerify.gone()
                }
            }
        }

    private fun autoUI() {
        ipMode = IP_MODE_DHCP
        binding.ipModeTv.text = resources.getString(R.string.ip_dhcp)

        binding.etIpAddr.setFocus(false)
        binding.etNetmask.setFocus(false)
        binding.etGateway.setFocus(false)
        binding.etDns.setFocus(false)

        binding.etIpAddr.setEnable(false)
        binding.etNetmask.setEnable(false)
        binding.etGateway.setEnable(false)
        binding.etDns.setEnable(false)

        binding.etIpAddr.setTextColor(resources.getColor(R.color.text_common))
        binding.etNetmask.setTextColor(resources.getColor(R.color.text_common))
        binding.etGateway.setTextColor(resources.getColor(R.color.text_common))
        binding.etDns.setTextColor(resources.getColor(R.color.text_common))

        binding.btnSave.visibility = View.GONE
        binding.dhcpBgGroup.show()
    }

    private fun staticUI() {
        ipMode = IP_MODE_STATIC
        binding.ipModeTv.text = resources.getString(R.string.ip_static)

        binding.etIpAddr.setCursor(true)
        binding.etNetmask.setCursor(true)
        binding.etGateway.setCursor(true)
        binding.etDns.setCursor(true)

        binding.etIpAddr.setEnable(true)
        binding.etNetmask.setEnable(true)
        binding.etGateway.setEnable(true)
        binding.etDns.setEnable(true)

        binding.etIpAddr.setFocus(true)
        binding.etNetmask.setFocus(true)
        binding.etGateway.setFocus(true)
        binding.etDns.setFocus(true)

        binding.etIpAddr.setTextColor(resources.getColor(R.color.bg_main_blue))
        binding.etNetmask.setTextColor(resources.getColor(R.color.bg_main_blue))
        binding.etGateway.setTextColor(resources.getColor(R.color.bg_main_blue))
        binding.etDns.setTextColor(resources.getColor(R.color.bg_main_blue))

        binding.btnSave.visibility = View.VISIBLE
        binding.dhcpBgGroup.gone()
    }


    override fun onPause() {
        super.onPause()
        binding.wifiDetailLayout.viewTreeObserver.removeOnGlobalFocusChangeListener(
            focusChangeListener
        )
    }

    @SuppressLint("NewApi")
    private fun refreshUI() {
        logTagD(TAG, "==currentState===$currentState")
        when (currentState) {
            STATE_IS_CONNECTED -> {
                binding.etIpAddr.setText(mEthIpAddress)
                binding.etNetmask.setText(mEthNetmask)
                binding.etGateway.setText(mEthGateway)
                binding.etDns.setText(mEthdns)
                dhcpInfo = wifiManager.dhcpInfo.leaseDuration
                if (dhcpInfo == 0) {
                    staticUI()
                } else {
                    autoUI()
                }
            }

            STATE_IS_SAVE -> {
                val networkConfig = getIpAssignment()
                if (networkConfig != null) {
                    val ipAssignmentCls = Class.forName("android.net.IpConfiguration\$IpAssignment")
                    val ipAssignment =
                        ipAssignmentCls.kotlin.java.enumConstants.first { it.toString() == "DHCP" }
                    val networkProxy = WifiConfigurationProxy(networkConfig)
                    if (networkProxy.getIpAssignment() == ipAssignment) {
                        //置灰禁用
                        mEthIpAddress = "***********"
                        mEthNetmask = WIFIUtilConstant.NET_MASK_DEFAULT
                        mEthGateway = "***********"
                        mEthdns = "***********"
                        binding.etIpAddr.setText(mEthIpAddress)
                        binding.etNetmask.setText(mEthNetmask)
                        binding.etGateway.setText(mEthGateway)
                        binding.etDns.setText(mEthdns)
                        autoUI()
                    } else {
                        val staticIpConfig =
                            networkProxy.getIpConfiguration().staticIpConfiguration!!
                        logTagD(TAG, "==staticIpConfig===￥staticIpConfig")
                        var indext = staticIpConfig.ipAddress.toString().indexOf("/")
                        mEthIpAddress =
                            staticIpConfig.ipAddress.toString().substring(0, indext)
                        mEthNetmask = WIFIUtilConstant.NET_MASK_DEFAULT
                        mEthGateway = staticIpConfig.gateway.toString().substring(1)
                        mEthdns = staticIpConfig.dnsServers[0].toString().substring(1)
                        binding.etIpAddr.setText(mEthIpAddress)
                        binding.etNetmask.setText(mEthNetmask)
                        binding.etGateway.setText(mEthGateway)
                        binding.etDns.setText(mEthdns)
                        staticUI()
                    }
                } else {
                    logTagD(TAG, "==404===")
                    binding.etIpAddr.setText(mEthIpAddress)
                    binding.etNetmask.setText(mEthNetmask)
                    binding.etGateway.setText(mEthGateway)
                    binding.etDns.setText(mEthdns)
                    autoUI()
                    binding.ipModeBg.isEnabled = false
                }


            }

            STATE_IS_DISCONNECTED -> {
                binding.etIpAddr.setText(mEthIpAddress)
                binding.etNetmask.setText(mEthNetmask)
                binding.etGateway.setText(mEthGateway)
                binding.etDns.setText(mEthdns)
                autoUI()
                binding.ipModeBg.isEnabled = false
            }
        }

        changeSaveBenEnable()
    }


    private fun handleWifiStateChange(state: Int) {
        when (state) {
            WifiManager.WIFI_STATE_DISABLED,
            WifiManager.WIFI_STATE_DISABLING,
            WifiManager.WIFI_STATE_UNKNOWN -> {
            }

            WifiManager.WIFI_STATE_ENABLING -> {
                val mStatusString = resources.getString(R.string.ethernet_info_getting)
            }

            WifiManager.WIFI_STATE_ENABLED -> {
                getWifiInfo()
                if (loadingDialog != null)
                    loadingDialog.dismiss()

            }
        }
        if (!isSave) refreshUI()
    }

    private fun getWifiInfo() {
        dhcpInfo = wifiManager.dhcpInfo.leaseDuration
        //手动
        if (dhcpInfo == 0) {
            val info = wifiManager.connectionInfo
            mEthIpAddress = Formatter.formatIpAddress(info.ipAddress)
            mEthNetmask = sp!!.getString(ETHNET_MASK, "*************").toString()
            mEthGateway = sp!!.getString(ETHNET_GATE_WAY, "***********").toString()
            mEthdns = sp!!.getString(ETHNET_DNS, "*******").toString()

        } else {
            //自动
            val dhcpInfo = wifiManager.dhcpInfo
            mEthIpAddress = Formatter.formatIpAddress(dhcpInfo.ipAddress)
            mEthNetmask = Formatter.formatIpAddress(dhcpInfo.netmask)
            mEthNetmask = getWlan0Mask()    // 获取子网掩码
            mEthGateway = Formatter.formatIpAddress(dhcpInfo.gateway)
            mEthdns = Formatter.formatIpAddress(dhcpInfo.dns1)
        }

    }


    private fun getIpAssignment(): WifiConfiguration? {
        val wifiConfigurationList = wifiManager.configuredNetworks
        for (network in wifiConfigurationList) {
            if (network.SSID == "\"$ssid\"") {
                return network
            }
        }
        val wifiConfig: WifiConfiguration? = null
        return wifiConfig
    }


    private suspend fun setDHCPIpConfig(isConnected: Boolean): Boolean =
        withContext(Dispatchers.IO) {
            config = WifiConfiguration()
            val result = setWifiDhcp(getIpAssignment(), wifiManager, isConnected)
            result
        }


    private suspend fun setStaticIpConfig(isConnected: Boolean): Boolean =
        withContext(Dispatchers.IO) {
            saveIpSettingInfo()

            /*
               * get ip address, netmask,dns ,gw etc.
               */
            val inetAddr: Inet4Address = getIPv4Address(mEthIpAddress)!!
            val prefixLength: Int = maskStr2InetMask(mEthNetmask)
            val gatewayAddr: InetAddress = getIPv4Address(mEthGateway)!!
            val dnsAddr: InetAddress = getIPv4Address(mEthdns)!!
            val result = setStaticConfig(
                getIpAssignment(),
                inetAddr,
                prefixLength,
                gatewayAddr,
                dnsAddr,
                wifiManager,
                isConnected
            )
            result
        }


    private fun saveIpSettingInfo() {
        val ipAddr = binding.etIpAddr.getText()
        val netMask = binding.etNetmask.getText()
        val gateway = binding.etGateway.getText()
        val dns = binding.etDns.getText()
        mEthIpAddress = ipAddr
        mEthNetmask = netMask
        mEthGateway = gateway
        mEthdns = dns
        sp!!.edit().putString(ETHNET_MASK, mEthNetmask).apply()
        sp!!.edit().putString(ETHNET_GATE_WAY, mEthGateway).apply()
        sp!!.edit().putString(ETHNET_DNS, mEthdns).apply()
    }

    private fun getIPv4Address(text: String): Inet4Address? {
        return try {
            InetAddress.getByName(text) as Inet4Address
        } catch (e: IllegalArgumentException) {
            null
        } catch (e: ClassCastException) {
            null
        }
    }

    val focusChangeListener = object : ViewTreeObserver.OnGlobalFocusChangeListener {
        override fun onGlobalFocusChanged(oldFocus: View?, newFocus: View?) {
            //PositionCusor = 0
            logTagD(TAG, "=111==positionCursor==$positionCursor")
            if (newFocus != null) {
                if (newFocus.parent?.parent == binding.etIpAddr) {
                    binding.etIpAddr.setCursor(true)
                    binding.etIpAddr.setBackgroundResource(R.drawable.bg_net_et_dark)
                    binding.etNetmask.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etGateway.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etDns.setBackgroundResource(R.drawable.bg_net_et_light)
                    positionCursor = 1
                } else if (newFocus.parent?.parent == binding.etNetmask) {
                    binding.etNetmask.setCursor(true)
                    binding.etIpAddr.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etNetmask.setBackgroundResource(R.drawable.bg_net_et_dark)
                    binding.etGateway.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etDns.setBackgroundResource(R.drawable.bg_net_et_light)
                    positionCursor = 2
                } else if (newFocus.parent?.parent == binding.etGateway) {
                    binding.etGateway.setCursor(true)
                    binding.etIpAddr.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etNetmask.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etGateway.setBackgroundResource(R.drawable.bg_net_et_dark)
                    binding.etDns.setBackgroundResource(R.drawable.bg_net_et_light)
                    positionCursor = 3
                } else if (newFocus.parent?.parent == binding.etDns) {
                    binding.etDns.setCursor(true)
                    binding.etIpAddr.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etNetmask.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etGateway.setBackgroundResource(R.drawable.bg_net_et_light)
                    binding.etDns.setBackgroundResource(R.drawable.bg_net_et_dark)
                    positionCursor = 4
                    logTagD(TAG, " dnsCusor = true")
                } else {
                    logTagD(TAG, "newFocus?.parent?.parent=${newFocus.parent?.parent}")
                }
                logTagD(TAG, "=222==positionCursor==$positionCursor")
            }

        }

    }


    private fun showDoubleDialog() {
        DoubleBtnCommonFloat(
            content = getString(R.string.ignore_the_network_content)
        ) { doubleFloat, position ->
            if (position == 0) {
                doubleFloat.dismiss()
            } else {
                try {
                    logTagI(TAG, "尝试移除wifi:${ssid}")
                    wifiManager.removeNetwork(getNetworkId(ssid))
                    //从保存删除
                    sp!!.edit().remove(ssid).apply()
                    //清除首次连接认证标记
                    sp!!.edit().remove(ssid + CONNECTED_FIRST_TAG).apply()
                    forgetConfiguration(wifiManager, ssid)
                    doubleFloat.dismiss()
                    wifiViewModel.showDisplay()
                } catch (e: Exception) {
                    logTagE(TAG, "忽略网络${ssid}失败", tr = e)
                    wifiViewModel.showDisplay()
                }
            }
        }.show()
    }
}