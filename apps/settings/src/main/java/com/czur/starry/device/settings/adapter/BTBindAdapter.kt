package com.czur.starry.device.settings.adapter

import android.view.View
import android.view.ViewGroup
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.bluetoothlib.bluetooth.HidProfile
import com.czur.starry.device.bluetoothlib.util.isConnect
import com.czur.starry.device.bluetoothlib.util.isWritePad
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.BTBoundDeviceEntity
import com.czur.starry.device.settings.model.BTBoundDeviceEntity.Companion.BATTERY_LEVEL_NONE
import com.czur.starry.device.settings.widget.BTBatteryView
import com.czur.starry.device.settings.widget.SettingItemBg

/**
 * Created by 陈丰尧 on 1/29/21
 * 已绑定的设备
 */
class BTBindAdapter : BaseDifferAdapter<BTBoundDeviceEntity>() {
    companion object {
        private const val TAG = "BTBindAdapter"
    }

    private val iconColorFilterConnect: Int by lazy(LazyThreadSafetyMode.NONE) {
        CZURAtyManager.currentActivity().getColor(R.color.white)
    }
    private val iconColorFilterDisconnect: Int by lazy(LazyThreadSafetyMode.NONE) {
        CZURAtyManager.currentActivity().getColor(R.color.bg_main_blue)
    }

    private val textColorConnect: Int by lazy(LazyThreadSafetyMode.NONE) {
        CZURAtyManager.currentActivity().getColor(R.color.white)
    }
    private val textColorDisconnect: Int by lazy(LazyThreadSafetyMode.NONE) {
        CZURAtyManager.currentActivity().getColor(R.color.text_common)
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BaseVH(R.layout.item_bt_bind_devices, parent)


    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: BTBoundDeviceEntity) {
        holder.setImgResource(
            HidProfile.getHidClassDrawable(itemData.cachedDevice.btClass),
            R.id.bindBtIconIv
        )
        // 蓝牙设备没有名字,就用蓝牙地址作为名字
        holder.setText(itemData.name, R.id.bindBTDeviceNameTv)
        logTagD(
            TAG,
            "cache:${itemData.cachedDevice.isConnected},device:${itemData.cachedDevice.device.isConnect}"
        )
        val itemBg = holder.getView<SettingItemBg>(R.id.itemBg)
        itemBg.setHoverItemPosition(position, getDataList().size)
        if (itemData.ignoreConnectStatus()) {
            logTagV(
                TAG,
                "屏蔽连接状态显示: ${itemData.name} - ${itemData.cachedDevice.btClass.deviceClass}"
            )
            if (itemData.cachedDevice.isWritePad) {
                holder.setImgResource(
                    R.drawable.ic_bt_writepad,
                    R.id.bindBtIconIv
                )
            }
            holder.setText("", R.id.btConnectStateTv)
            holder.getView<View>(R.id.connectIv).invisible()
            itemBg.isSelected = false
            holder.setImageColorFilter(iconColorFilterDisconnect, R.id.bindBtIconIv)
            holder.setImageColorFilter(iconColorFilterDisconnect, R.id.btDetailIv)
            holder.setTextColor(textColorDisconnect, R.id.bindBTDeviceNameTv)
            holder.setTextColor(textColorDisconnect, R.id.btConnectStateTv)
        } else {
            // 判断是否连接
            if (itemData.cachedDevice.isConnected) {
                holder.setText(R.string.bt_state_connected, R.id.btConnectStateTv)
                holder.getView<View>(R.id.connectIv).show()

                itemBg.isSelected = true
                holder.setImageColorFilter(iconColorFilterConnect, R.id.bindBtIconIv)
                holder.setImageColorFilter(iconColorFilterConnect, R.id.btDetailIv)
                holder.setTextColor(textColorConnect, R.id.bindBTDeviceNameTv)
                holder.setTextColor(textColorConnect, R.id.btConnectStateTv)

            } else {
                holder.setText(R.string.bt_state_disconnected, R.id.btConnectStateTv)
                holder.getView<View>(R.id.connectIv).invisible()

                itemBg.isSelected = false
                holder.setImageColorFilter(iconColorFilterDisconnect, R.id.bindBtIconIv)
                holder.setImageColorFilter(iconColorFilterDisconnect, R.id.btDetailIv)
                holder.setTextColor(textColorDisconnect, R.id.bindBTDeviceNameTv)
                holder.setTextColor(textColorDisconnect, R.id.btConnectStateTv)
            }
        }

        val isShow = itemData.batteryLevel != BATTERY_LEVEL_NONE && itemData.batteryLevel > 0
        holder.visible(isShow, R.id.btBatteryView)
        if (isShow) {
            val batteryView = holder.getView<BTBatteryView>(R.id.btBatteryView)
            batteryView.batteryLevel = itemData.batteryLevel
            batteryView.invalidate()
        }
    }

    /**
     * 是否要屏蔽连接状态的显示, 因为部分设备能正常使用, 但是连接状态显示不正确(原生系统也是如此)
     * 所以先屏蔽掉(鹏飞: 2024/1/18)\
     * @return true:屏蔽
     */
    private fun BTBoundDeviceEntity.ignoreConnectStatus(): Boolean {
        return cachedDevice.isWritePad  // 目前只剩下这一个条件了
    }

    override fun areItemsTheSame(
        oldItem: BTBoundDeviceEntity,
        newItem: BTBoundDeviceEntity
    ): Boolean {
        return oldItem.address == newItem.address
    }

    override fun areContentsTheSame(
        oldItem: BTBoundDeviceEntity,
        newItem: BTBoundDeviceEntity
    ): Boolean {
        return false
    }
}