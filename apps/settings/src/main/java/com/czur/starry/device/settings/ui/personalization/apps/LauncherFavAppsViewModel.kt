package com.czur.starry.device.settings.ui.personalization.apps

import android.app.Application
import android.content.Intent
import android.content.pm.PackageManager
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.settings.adapter.FavAppItem
import com.czur.starry.device.settingslib.blackPkgList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/7/4
 */
private const val TAG = "LauncherFavAppsViewModel"

class LauncherFavAppsViewModel(application: Application) : AndroidViewModel(application) {
    // 收藏应用包名列表
    private val favAppPkgFlow = MutableStateFlow(emptyList<String>())
    private val installedAppPkgFlow = MutableStateFlow(emptyList<FavAppItem>())

    /**
     * 可选择的Apps
     */
    val couldChooseApps =
        installedAppPkgFlow.combine(favAppPkgFlow) { installApps, favApps ->
            installApps.filterNot { it.packageName in favApps }
        }.flowOn(Dispatchers.IO)

    val favAppsFlow = installedAppPkgFlow.combine(favAppPkgFlow) { installApps, favApps ->
        favApps.mapNotNull { pkgName ->
            installApps.find {
                it.packageName == pkgName
            }
        }
    }.flowOn(Dispatchers.IO)

    // 最多可以选择4个常用应用
    val favAppsFullFlow = favAppsFlow.map { favApps ->
        favApps.size >= 4
    }.flowOn(Dispatchers.IO)

    private val appUtil by lazy { AppUtil() }

    suspend fun refresh() {
        logTagD(TAG, "刷新常用应用列表")
        loadFavAppPkgList()
        loadInstalledAppPkgList()
        checkFavAppPkgList()
    }

    private suspend fun checkFavAppPkgList() {
        if (favAppPkgFlow.value.isEmpty()) {
            logTagW(TAG, "常用应用列表为空")
            return
        }
        val installedPkgList = installedAppPkgFlow.value.map { it.packageName }
        val favList = favAppPkgFlow.value.filter { it in installedPkgList }
        if (favList != favAppPkgFlow.value) {
            logTagW(TAG, "常用应用列表与安装列表不一致, 重新加载")
            favAppPkgFlow.value = favList
            SettingUtil.PersonalizationSetting.setLauncherFavApps(favList)
        }
    }

    /**
     * 添加常用应用
     */
    suspend fun addFavApp(app: FavAppItem) {
        logTagD(TAG, "addFavApp: ${app.packageName}")
        favAppPkgFlow.value = favAppPkgFlow.value + app.packageName
        SettingUtil.PersonalizationSetting.setLauncherFavApps(favAppPkgFlow.value)
    }

    suspend fun delFavApp(app: FavAppItem) {
        logTagD(TAG, "delFavApp: ${app.packageName}")
        favAppPkgFlow.value = favAppPkgFlow.value - app.packageName
        SettingUtil.PersonalizationSetting.setLauncherFavApps(favAppPkgFlow.value)
    }

    private suspend fun loadFavAppPkgList() {
        favAppPkgFlow.value = SettingUtil.PersonalizationSetting.getLauncherFavApps()
    }

    private suspend fun loadInstalledAppPkgList() {
        installedAppPkgFlow.value = getInstalledApps()
    }

    /**
     * 扫描系统中安装的应用
     */
    private suspend fun getInstalledApps(): List<FavAppItem> = withContext(Dispatchers.IO) {
        val filterIntent = Intent(Intent.ACTION_MAIN, null)
        filterIntent.addCategory(Intent.CATEGORY_LAUNCHER)
        val apps =
            appUtil.packageManager.queryIntentActivities(filterIntent, PackageManager.MATCH_ALL)
        val appsInfo = mutableListOf<FavAppItem>()
        apps.filter {
            // 去除系统自带的应用
            it.activityInfo.packageName !in blackPkgList
        }.forEach { resolveInfo ->
            val iconDrawable =
                appUtil.getResIconFromActivityInfo(resolveInfo.activityInfo)
                    ?: appUtil.getIconFromPkg(
                        resolveInfo.activityInfo.packageName
                    )
            iconDrawable?.let {
                doWithoutCatch {
                    val packageName = resolveInfo.activityInfo.packageName
                    val appName = resolveInfo.loadLabel(appUtil.packageManager).toString()
                    val appInfo = FavAppItem(packageName, appName, it)
                    appsInfo.add(appInfo)
                }
            }
        }
        // 用安装时间排序
        appsInfo.sortBy {
            it.packageName
        }
        appsInfo
    }
}