package com.czur.starry.device.settings.ui.personalization.wallpaper.dialog

import android.graphics.BitmapFactory
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.czur.czurutils.img.QrCodeUtil
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.settings.R


/**
 * created by wangh 22.0809
 */

class ScanQRCodeDialog : BaseDialog() {

    var confirmClickListener: (() -> Unit)? = null
    var cancelClickListener: (() -> Unit)? = null
    var selectClickListener: (() -> Unit)? = null
    var link: String? = null

    private val titleTv by findView<TextView>(R.id.titleTv)
    private val imageView by findView<ImageView>(R.id.im_view)
    private val normalDialogConfirmBtn by findView<CommonButton>(R.id.normalDialogConfirmBtn)
    private val normalDialogCancelBtn by findView<CommonButton>(R.id.normalDialogCancelBtn)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_qrcode_select, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        launch {
            val bitmap = BitmapFactory.decodeResource(resources, R.drawable.czur_logo)
            link?.let {
                val codeImage = QrCodeUtil.generateQrCodeBmp(it) {
                    pointColor = 0xFF5879FC.toInt()
                    bgColor = Color.WHITE
                    edge = 320
                    delPadding = true
                    logoConfig {
                        logoBmp = bitmap
                    }
                }
                imageView.setImageBitmap(codeImage)
            }

        }

        titleTv.text =
            com.czur.starry.device.baselib.utils.getString(R.string.wallpaper_tv_qrcode_content)
        normalDialogConfirmBtn.setOnClickListener {
            confirmClickListener?.invoke()
            dismiss()
        }
        normalDialogCancelBtn.setOnClickListener {
            cancelClickListener?.invoke()
            dismiss()
        }

    }

    class Builder {
        private var link = ""
        private var confirmClickListener: (() -> Unit)? = null
        private var cancelClickListener: (() -> Unit)? = null
        private var selectClickListener: (() -> Unit)? = null


        fun setConfirmClickListener(listener: () -> Unit): Builder {
            confirmClickListener = listener
            return this
        }


        fun setSelectClickListener(listener: () -> Unit): Builder {
            selectClickListener = listener
            return this
        }

        fun setLink(link: String): Builder {
            this.link = link
            return this
        }


        fun build(): ScanQRCodeDialog {
            val dialog = ScanQRCodeDialog()
            dialog.link = link
            dialog.confirmClickListener = confirmClickListener
            dialog.cancelClickListener = cancelClickListener
            dialog.selectClickListener = selectClickListener
            return dialog
        }
    }
}