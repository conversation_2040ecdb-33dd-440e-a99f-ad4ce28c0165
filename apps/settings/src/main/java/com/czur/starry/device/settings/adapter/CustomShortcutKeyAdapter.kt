package com.czur.starry.device.settings.adapter

import android.graphics.drawable.Drawable
import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.ui.personalization.shortcut.CustomShortcutEventType

/**
 * Created by 陈丰尧 on 2023/7/4
 */
data class CustomShortcutItem(
    val name: String,
    val icon: Drawable,
    val eventType: CustomShortcutEventType,
    val packageName: String,
) {
    override fun toString(): String {
        return "${eventType};${packageName}"
    }
}

class CustomShortcutKeyAdapter : BaseDifferAdapter<CustomShortcutItem>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: CustomShortcutItem) {
        holder.setText(itemData.name, R.id.favAppNameTv)
        holder.setImgDrawable(itemData.icon, R.id.favAppIconIv)
        holder.setImgResource(R.drawable.ic_fav_app_add, R.id.favAppActionIv)
    }

    override fun areItemsTheSame(
        oldItem: CustomShortcutItem,
        newItem: CustomShortcutItem
    ): Boolean {
        return oldItem.packageName == newItem.packageName && oldItem.eventType == newItem.eventType
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_launcher_fav_app, parent)
    }
}