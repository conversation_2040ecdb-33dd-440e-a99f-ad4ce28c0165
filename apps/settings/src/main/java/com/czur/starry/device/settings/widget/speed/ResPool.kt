package com.czur.starry.device.settings.widget.speed

import android.graphics.Path
import android.graphics.PointF
import java.util.concurrent.LinkedBlockingQueue

/**
 * Created by 陈丰尧 on 2021/9/1
 * 资源池
 * 因为绘制过程中, 会大量的产生临时的PointF对象,Path对象, 所以将其池化
 */
open class ResPool<T>(private val creator: () -> T, private val reset: ((t: T) -> Unit)? = null) {
    // 空闲资源池
    private val idlePool: LinkedBlockingQueue<T> = LinkedBlockingQueue()

    // 使用中的资源
    private val usePoll = mutableListOf<T>()

    /**
     * 获取一个资源
     */
    fun obtain(): T {
        val res = idlePool.poll() ?: creator()
        usePoll.add(res)
        reset?.invoke(res)
        return res
    }

    /**
     * 释放一个资源
     */
    fun release(t: T) {
        usePoll.remove(t)
        idlePool.offer(t)
    }

    fun releaseAll() {
        idlePool.addAll(usePoll)
        usePoll.clear()
    }
}

class PathResPool : ResPool<Path>({ Path() }, {
    it.reset()
})

class PointFResPoll : ResPool<PointF>({ PointF() }) {
    fun obtain(x: Float, y: Float): PointF {
        return obtain().apply { set(x, y) }
    }
}

