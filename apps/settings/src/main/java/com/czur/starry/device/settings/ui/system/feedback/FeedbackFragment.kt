package com.czur.starry.device.settings.ui.system.feedback

import android.os.Bundle
import android.view.View
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnSuspendClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.baselib.utils.toastSuccess
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.diagnosislib.DiagnosisUtil
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentFeedbackBinding
import com.czur.starry.device.settings.ui.system.vm.DEF_UPLOAD_LOG
import com.czur.starry.device.settings.ui.system.vm.FeedbackViewModel
import com.czur.uilib.extension.rv.addCornerRadius
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.performTouch
import com.czur.starry.device.settings.SettingMainViewModel


/**
 * Created by 陈丰尧 on 2021/9/26
 * 意见反馈页面
 */
private const val TAG = "FeedbackFragment"
class FeedbackFragment : BaseBindingMenuFragment<FragmentFeedbackBinding>() {
    private val feedbackVM: FeedbackViewModel by viewModels()

    private val diagnosisUtil = DiagnosisUtil()
    private val loadingDialog by lazy { LoadingDialog() }
    private var initTime = System.currentTimeMillis()

    private val categoryAdapter = FeedbackCategoryAdapter()
    private val feedbackItemAdapter = FeedbackItemAdapter()
    private val mainViewModel: SettingMainViewModel by viewModels({ requireActivity() })
    // 是否是海外版本
    private val isOversea: Boolean = Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas

    override fun FragmentFeedbackBinding.initBindingViews() {
        initTime = System.currentTimeMillis()

        diagnosisUtil.bindService(requireContext())

        feedbackCategoryRv.apply {
            closeDefChangeAnimations()
            addCornerRadius(backgroundColor = requireContext().getColor(R.color.bg_setting_normal))
            adapter = categoryAdapter

            doOnItemClick { vh, view ->
                val pos = vh.bindingAdapterPosition
                val item = categoryAdapter.getData(pos)
                feedbackVM.selectCategory(item.category)
                true
            }
        }

        feedbackItemRv.apply {
            closeDefChangeAnimations()
            addCornerRadius(backgroundColor = requireContext().getColor(R.color.bg_setting_normal))
            adapter = feedbackItemAdapter
            feedbackItemAdapter.setOnItemSelectChange { feedbackItem, isOn ->
                feedbackVM.selectItem(feedbackItem, isOn)
            }
        }

        // 反馈内容
        feedbackContentEt.doOnTextChanged { text, _, _, _ ->
            feedbackVM.updateFeedbackContent(text?.toString() ?: "")
        }

        // 联系方式
        contactInformationEt.doOnTextChanged { text, _, _, _ ->
            feedbackVM.updateContactInformation(text?.toString() ?: "")
        }
        // 上传系统日志
        updateLogCb.setChecked(DEF_UPLOAD_LOG)
        updateLogCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser) {
                feedbackVM.updateUploadLog(isOn)
            }
        }

        // 提交
        feedbackSubmitBtn.setOnSuspendClickListener(viewLifecycleOwner.lifecycleScope) {
            loadingDialog.show()
            val res = diagnosisUtil.uploadFeedback(
                feedbackType = feedbackVM.getFeedbackItems(),
                contactInformation = feedbackVM.getContactInformation(),
                collectLog = feedbackVM.getUploadLog(),
                feedback = feedbackVM.getFeedbackContent()
            )
            loadingDialog.dismiss()
            if (res) {
                if (feedbackVM.getUploadLog()) {
                    toast(R.string.feedback_log_hint)
                } else {
                    toastSuccess()
                }
                resetUserInput()
            } else {
                toastFail()
            }
        }

        if (isOversea) {
            customerServiceBg.visibility = View.GONE
            customerServiceQRCodeIv.visibility = View.GONE
            customerServiceTv.visibility = View.GONE
        }

        repeatCollectOnResume(mainViewModel.currentViewNavigateFlow) {
            if (it == getString(R.string.voice_change_feedback_log_submit)) {
                mainViewModel.onNavigateReset()
                feedbackSubmitBtn.performClick()
            }
        }
    }

    private fun resetUserInput() {
        logTagV(TAG, "清空用户输入")
        feedbackVM.resetCategory()  // 重置反馈类型
        binding.updateLogCb.setChecked(DEF_UPLOAD_LOG)// 重置上传日志
        feedbackVM.updateUploadLog(DEF_UPLOAD_LOG)
        binding.feedbackContentEt.clearContentText() // 重置反馈内容
        binding.feedbackContentEt.clearFocus()
        binding.contactInformationEt.clearContentText() // 重置联系方式
        binding.contactInformationEt.clearFocus()
    }

    override fun initData(savedInstanceState: Bundle?) {
        // 提交按钮是否可点击
        repeatCollectOnResume(feedbackVM.submitEnableFlow) {
            binding.feedbackSubmitBtn.isEnabled = it
        }

        repeatCollectOnResume(feedbackVM.showCategoryFlow) {
            categoryAdapter.setData(it)
        }
        repeatCollectOnResume(feedbackVM.showItemsFlow) {
            feedbackItemAdapter.setData(it)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        diagnosisUtil.unbindService(requireContext())
    }
}