package com.czur.starry.device.settings.app

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.starry.device.baselib.base.listener.StarryApp
import com.czur.starry.device.baselib.data.provider.StarryDataProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import kotlin.properties.Delegates

class App : StarryApp() {

    companion object {
        var context: Context by Delegates.notNull()
        var app: App by Delegates.notNull()
        private const val DATA_STORE_NAME = "WallPaperSettings"
    }

    //数据迁移
    val dataStore: DataStore<Preferences> by preferencesDataStore(
        name = DATA_STORE_NAME,
        corruptionHandler = createDefCorruptionHandler(DATA_STORE_NAME)
    )

    override fun onCreate() {
        super.onCreate()
        context = this
        app = this
        StarryDataProvider.init(this)
    }


}