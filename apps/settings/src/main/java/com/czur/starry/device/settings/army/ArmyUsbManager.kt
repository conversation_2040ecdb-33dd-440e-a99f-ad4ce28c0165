package com.czur.starry.device.settings.army

import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.SystemClock
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.KEY_ARMY_NEED_SET_TIME
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.withContext
import okhttp3.internal.and
import java.util.Calendar
import java.util.concurrent.Executors

/**
 * Created by 陈丰尧 on 2024/9/19
 */
object ArmyUsbManager {
    private const val TAG = "ArmyUsbManager"

    private const val KEY_BOARD_PID = 0xc20a
    private const val KEY_BOARD_VID = 0x0a5c

    private val usbManager: UsbManager by lazy {
        globalAppCtx.getSystemService(UsbManager::class.java)!!
    }

    private val singleDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()

    /**
     * 将系统时间更新到键盘
     */
    suspend fun uploadSystemTimeToKeyboard() = withContext(singleDispatcher) {
        logTagV(TAG, "记录时间到键盘")
        val device = findKeyboardDevice()
        withDevice(device) {
            val buffer = ByteArray(8)
            buffer[0] = 0x15.toByte()
            buffer[1] = 0xd0.toByte()

            val calendar = Calendar.getInstance()
            buffer[2] = calendar.get(Calendar.SECOND).toByte()   // 秒
            buffer[3] = calendar.get(Calendar.MINUTE).toByte()  // 分
            buffer[4] = calendar.get(Calendar.HOUR_OF_DAY).toByte()  // 时
            buffer[5] = calendar.get(Calendar.DAY_OF_MONTH).toByte()  // 日
            buffer[6] = (calendar.get(Calendar.MONTH) + 1).toByte()  // 月
            buffer[7] = (calendar.get(Calendar.YEAR) - 2000).toByte()  // 年

            logTagV(
                TAG,
                "setToKeyBoard year:${buffer[7]}, month:${buffer[6]}, day:${buffer[5]}, hour:${buffer[4]}, minute:${buffer[3]}, second:${buffer[2]}"
            )

            // 杜世旺提供的代码
            val result = it.controlTransfer(0x21, 0x09, 0x315, 0x1, buffer, buffer.size, 200)
            logTagV(TAG, "controlTransfer result: $result")
            if (result > 0) {
                setSystemProp(KEY_ARMY_NEED_SET_TIME, false)    // 设置成功后, 将标志位设置为false
                logTagV(TAG, "设置时间到键盘 success")
            } else {
                logTagW(TAG, "设置时间到键盘 failed")
            }
        }
    }

    /**
     * 从键盘获取时间设置到Starry
     */
    suspend fun setTimeFromKeyboard() = withContext(singleDispatcher) {
        logTagV(TAG, "从键盘获取时间 START")
        val device = findKeyboardDevice()
        withDevice(device) { connection ->
            val buffer = ByteArray(8)
            val result =
                connection.controlTransfer(0xa1, 0x01, 0x315, 0x1, buffer, buffer.size, 200)
            logTagW(TAG, "controlTransfer result: $result")
            if (result > 0) {
                logTagV(TAG, "controlTransfer success")
                val intArray = IntArray(buffer.size)
                for (i in buffer.indices) {
                    intArray[i] = intArray[i] or (buffer[i] and 0xFF)
                }

                val year = intArray[7] + 2000
                val month = (intArray[6] and 0x0F) - 1
                val day = intArray[5]
                val hour = intArray[4]
                val minute = intArray[3]
                val second = intArray[2]

                logTagV(
                    TAG,
                    "year: $year, month: ${month}(rawValue:${intArray[6]}), day: $day, hour: $hour, minute: $minute, second: $second"
                )
                val calendar = Calendar.getInstance()
                calendar.set(year, month, day, hour, minute, second)
                // 设置系统时间
                SystemClock.setCurrentTimeMillis(calendar.timeInMillis)
                logTagV(TAG, "设置到会议星时间完成")
            } else {
                logTagW(TAG, "controlTransfer failed")
            }
        }
    }

    private suspend fun withDevice(
        device: UsbDevice?,
        block: suspend (connection: android.hardware.usb.UsbDeviceConnection) -> Unit
    ) {
        if (device == null) {
            logTagW(TAG, "没有找到USB键盘设备")
            return
        }

        val connection = usbManager.openDevice(device) ?: kotlin.run {
            logTagW(TAG, "打开USB键盘设备失败")
            return
        }
        val usbInterface = device.getInterface(1)
        val usbEndpoint = usbInterface.getEndpoint(0)
        if (usbEndpoint == null) {
            logTagW(TAG, "没有找到键盘设备的endpoint")
            connection.close()
            return
        }

        if (connection.claimInterface(usbInterface, true)) {
            block(connection)
            connection.releaseInterface(usbInterface)
        } else {
            logTagW(TAG, "claimInterface 失败")
        }
        connection.close()
    }

    private fun findKeyboardDevice(): UsbDevice? {
        return usbManager.deviceList.values.find {
            it.vendorId == KEY_BOARD_VID && it.productId == KEY_BOARD_PID
        }
    }
}