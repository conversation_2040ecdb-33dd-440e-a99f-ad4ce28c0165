package com.czur.starry.device.settings.adapter

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.bluetoothlib.bluetooth.CachedBluetoothDevice
import com.czur.starry.device.bluetoothlib.bluetooth.HidProfile
import com.czur.starry.device.settings.R

/**
 * Created by 陈丰尧 on 1/29/21
 */
class BTOtherAdapter : BaseDifferAdapter<CachedBluetoothDevice>() {
    companion object {
        private const val TAG = "BTOtherAdapter"
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BaseVH(R.layout.item_bt_other_devices, parent)

    @SuppressLint("NewApi")
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: CachedBluetoothDevice) {
        holder.setImgResource(
            HidProfile.getHidClassDrawable(itemData.btClass),
            R.id.otherBtIconIv
        )
        // 蓝牙设备没有名字,就用蓝牙地址作为名字
        holder.setText(itemData.name, R.id.otherBTDeviceNameTv)
        holder.visible(itemData.bondState == BluetoothDevice.BOND_BONDING, R.id.boundingProgress)
    }

    override fun areItemsTheSame(oldItem: CachedBluetoothDevice, newItem: CachedBluetoothDevice): Boolean {
        return oldItem.device.address == newItem.device.address
    }

    @SuppressLint("NewApi")
    override fun areContentsTheSame(oldItem: CachedBluetoothDevice, newItem: CachedBluetoothDevice): Boolean {
        return oldItem.btClass == newItem.btClass
                && oldItem.name == newItem.name
                && oldItem.bondState == newItem.bondState
    }
}