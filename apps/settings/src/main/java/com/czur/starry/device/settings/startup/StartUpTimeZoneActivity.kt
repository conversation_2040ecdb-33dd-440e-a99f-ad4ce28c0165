package com.czur.starry.device.settings.startup

import android.os.Bundle
import android.view.KeyEvent
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import android.content.Intent
import android.provider.Settings
import android.widget.ProgressBar
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.starry.device.baselib.base.BaseStartupActivity
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.app.App
import com.czur.starry.device.settings.manager.TimeManager
import com.czur.starry.device.settings.utils.datetime.getSortedTimeZoneList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/11/16
 */
class StartUpTimeZoneActivity : BaseStartupActivity() {
    override fun getLayout() = R.layout.activity_startup_timezone
    private val timeManager = TimeManager()

    companion object {
        private const val TAG = "StartUpTimeZoneActivity"
    }

    // 关闭启动动画
    override val closeBootAnim: Boolean = true

    private val startUpTimeZoneAdapter = StartUpTimeZoneAdapter()

    private val timeZoneRv by findView<RecyclerView>(R.id.timeZoneRv)
    private val startUpTimeZoneNextTv by findView<TextView>(R.id.startUpTimeZoneNextTv)
    private val startUpTimeZonePreTv by findView<TextView>(R.id.startUpTimeZonePreTv)
    private val progressBar by findView<ProgressBar>(R.id.progressBar)

    private val isFirstStep by lazy {
        intent.getBooleanExtra("isFirst", false)
    }

    override fun initViews() {
        super.initViews()

        if (isFirstStep) {
            startUpTimeZonePreTv.gone()
        }

        launch {
            checkAirPlaneStatus()
        }

        timeZoneRv.apply {
            closeDefChangeAnimations()
            adapter = startUpTimeZoneAdapter
            doOnItemClick { vh, _ ->
                startUpTimeZoneAdapter.selPos = vh.bindingAdapterPosition
                true
            }
        }

        startUpTimeZoneNextTv.setOnClickListener {
            // 变更时区
            startUpTimeZoneAdapter
                .getDataOrNull(startUpTimeZoneAdapter.selPos)?.let { timeZoneEntity ->
                    logTagD(TAG, "设置时区:${timeZoneEntity}")
                    timeManager.setTimeZone(timeZoneEntity.id)
                }
            moveToNextStep()
        }
        startUpTimeZonePreTv.setOnClickListener {
            moveToPreStep()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)


        launch {
            val timeZoneList = getSortedTimeZoneList(this@StartUpTimeZoneActivity)
            startUpTimeZoneAdapter.setData(timeZoneList)
            initSelectTimeZone()
        }
    }

    private suspend fun initSelectTimeZone() {
        logTagV(TAG, "初始化选中时区")
        val currentId = timeManager.getCurrentTimeZoneId()
        var selIndex = withContext(Dispatchers.Default) {
            startUpTimeZoneAdapter.getDataList().indexOfFirst {
                it.id == currentId
            }
        }
        if (selIndex == -1) {
            logTagW(TAG, "没有找到当前选中的时区, 默认选中东八区")
            selIndex = 0    // 没找到使用默认的
        }
        startUpTimeZoneAdapter.selPos = selIndex
        val layoutManager = timeZoneRv.layoutManager as LinearLayoutManager
        layoutManager.scrollToPositionWithOffset(selIndex, 0)
        delay(600)
        progressBar.gone()
        timeZoneRv.show()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 返回上一页
            if (isFirstStep) {
                return true // 拦截返回键, 第一步不允许返回
            }
            moveToPreStep()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private suspend fun checkAirPlaneStatus() = withContext(Dispatchers.Default) {
        val airPlaneStatus = Settings.Global.getInt(
            App.context.contentResolver,
            Settings.Global.AIRPLANE_MODE_ON,
            0
        ) != 0
        if (airPlaneStatus) {
            /// 设备处于飞行模式-->开启
            Settings.Global.putInt(contentResolver, Settings.Global.AIRPLANE_MODE_ON, 0)
            val intent = Intent(Intent.ACTION_AIRPLANE_MODE_CHANGED).apply {
                putExtra("state", false)
            }
            sendBroadcast(intent)
        }
    }
}