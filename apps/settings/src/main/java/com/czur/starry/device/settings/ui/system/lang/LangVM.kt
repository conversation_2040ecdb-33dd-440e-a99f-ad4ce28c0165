package com.czur.starry.device.settings.ui.system.lang

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.settings.manager.LanguageManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.util.Locale

/**
 * Created by 陈丰尧 on 2023/6/19
 */
private const val TAG = "LangVM"

class LangVM(application: Application) : AndroidViewModel(application) {
    private val langChangeFlow = MutableStateFlow(false)
    val saveBtnEnableFlow = langChangeFlow.map { it }


    fun updateLanguageSelect(locale: Locale) {
        langChangeFlow.value = LanguageManager.isLanguageChange(locale)
    }

    suspend fun saveLanguage(selLocale: Locale) = withContext(Dispatchers.IO) {
        LanguageManager.updateLanguage(selLocale)
        langChangeFlow.value = false
    }
}