package com.czur.starry.device.settings.manager

import com.czur.czurutils.log.logTagD
import android.app.AlarmManager
import android.content.Context
import com.czur.starry.device.baselib.base.CZURAtyManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

/**
 * Created by 陈丰尧 on 2021/11/16
 */
class TimeManager {
    companion object {
        private const val TAG = "TimeManager"
    }

    private val alarmManager: AlarmManager by lazy {
        CZURAtyManager.appContext.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    }

    fun setTimeZone(timeZoneId: String) {
        logTagD(TAG, "设置时区:${timeZoneId}")
        alarmManager.setTimeZone(timeZoneId)
    }

    fun getCurrentTimeZoneId(): String {
        return TimeZone.getDefault().id
    }

    suspend fun getCurrentTimeZoneShort(): String {
        return withContext(Dispatchers.IO) {
            TimeZone.getDefault().getDisplayName(false, TimeZone.SHORT)
        }
    }
}