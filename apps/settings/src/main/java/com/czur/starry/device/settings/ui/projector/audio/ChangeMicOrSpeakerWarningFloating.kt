package com.czur.starry.device.settings.ui.projector.audio

import android.media.AudioDeviceInfo
import androidx.fragment.app.activityViewModels
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.FloatingChangeMicOrSpeakerWarningBinding
import com.czur.starry.device.settings.utils.isBluetoothDevice
import kotlin.getValue

/**
 * Created by 陈丰尧 on 2025/4/8
 */
class ChangeMicOrSpeakerWarningFloating(private val targetDevice: AudioDeviceInfo) :
    CZVBFloatingFragment<FloatingChangeMicOrSpeakerWarningBinding>() {
    private val speakerAndMicChooseViewModel: SpeakerAndMicChooseViewModel by activityViewModels()

    override fun FloatingChangeMicOrSpeakerWarningBinding.initBindingViews() {
        val pairedDevice = speakerAndMicChooseViewModel.getPairedDeviceInfo(targetDevice)
        val hasPairedDevice = pairedDevice != null

        if (hasPairedDevice && pairedDevice.isBluetoothDevice) {
            // 蓝牙设备, 需要同步设置
            withPairDeviceCb.isChecked = true
            withPairDeviceCb.isEnabled = false // 禁止点击
            withPairDeviceCb.alpha = 0.5F
            withPairDeviceTv.isEnabled = false // 禁止点击
            withPairDeviceTv.alpha = 0.5F
        } else {
            // 非蓝牙设备
            withPairDeviceCb.isEnabled = true
            withPairDeviceCb.alpha = 1F
            withPairDeviceTv.isEnabled = true
            withPairDeviceTv.alpha = 1F
        }

        when ((targetDevice.isSink to hasPairedDevice)) {
            true to true -> {
                // Speaker 有对应的 mic
                contentTv.setText(R.string.info_change_speaker_and_mic)
                withPairDeviceTv.setText(R.string.cb_change_speaker_and_mic)
                withPairDeviceGroup.show()
            }

            true to false -> {
                // Speaker 没有对应的 mic
                contentTv.setText(R.string.info_change_speaker_only)
                withPairDeviceGroup.gone()
            }

            false to true -> {
                // Mic 有对应的 speaker
                contentTv.setText(R.string.info_change_mic_and_speaker)
                withPairDeviceTv.setText(R.string.cb_change_mic_and_speaker)
                withPairDeviceGroup.show()
            }

            false to false -> {
                // Mic 没有对应的 speaker
                contentTv.setText(R.string.info_change_mic_only)
                withPairDeviceGroup.gone()
            }
        }

        // 取消按钮
        cancelBtn.setOnDebounceClickListener {
            dismiss()
        }

        // 确定按钮
        confirmBtn.setOnDebounceClickListener {
            launch {
                if (withPairDeviceCb.isChecked) {
                    speakerAndMicChooseViewModel.updateSelDevice(targetDevice, pairedDevice)
                } else {
                    speakerAndMicChooseViewModel.updateSelDevice(targetDevice, null)
                }
                dismiss()
            }
        }
    }
}