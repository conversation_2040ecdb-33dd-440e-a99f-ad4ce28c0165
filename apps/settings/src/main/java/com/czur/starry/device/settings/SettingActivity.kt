package com.czur.starry.device.settings

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.base.v2.fragment.floating.IDismissible
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.currentCameraVersion
import com.czur.starry.device.otalib.OTAHandler.newVersionStatusLive
import com.czur.starry.device.settings.adapter.menu.MenuAdapter
import com.czur.starry.device.settings.databinding.ActivitySettingBinding
import com.czur.starry.device.settings.touchpad.TouchPadService
import com.czur.starry.device.settings.utils.MenuUtil
import com.czur.starry.device.settings.utils.SubMenu

class SettingActivity : CZViewBindingAty<ActivitySettingBinding>() {
    companion object {
        private const val TAG = "SettingActivity"
        private const val FRAGMENT_MENU_TAG = "fragmentMenu"
    }

    private val menuAdapter = MenuAdapter()
    private val menuUtil by lazy {
        MenuUtil()
    }

    private val mainViewModel: SettingMainViewModel by viewModels()

    override fun ActivitySettingBinding.initBindingViews() {
        menuRv.layoutManager = LinearLayoutManager(this@SettingActivity)
        menuRv.adapter = menuAdapter
        menuRv.closeDefChangeAnimations()

        val startIntent = Intent(this@SettingActivity, TouchPadService::class.java)
        startService(startIntent)

        menuAdapter.onSubMenuChangeListener = {
            // 选中页面改变
            mainViewModel.onSubMenuSel(it)
        }

    }

    override fun initData(
        savedInstanceState: Bundle?
    ) {
        handleIntent(intent)
        menuAdapter.setMenuSheet(menuUtil.menuSheet)

        newVersionStatusLive.observe(this@SettingActivity) {
            menuAdapter.updateRemindStatus(MenuUtil.MENU_GROUP_KEY_SYSTEM, it)
            menuAdapter.updateRemindStatus(MenuUtil.SUB_MENU_KEY_UPDATE, it)
        }
        OTAHandler.newTouchPadVersionLive.observe(this@SettingActivity) {
            menuAdapter.updateRemindStatus(MenuUtil.MENU_GROUP_KEY_PERIPHERAL, it)
            menuAdapter.updateRemindStatus(MenuUtil.SUB_MENU_KEY_TOUCH_PAD, it)
        }
        OTAHandler.newCameraVersionStatusLive.observe(this@SettingActivity) {
            var current = currentCameraVersion
            var isShow = it != "null" && it != current
            menuAdapter.updateRemindStatus(MenuUtil.MENU_GROUP_KEY_CAMERA_MIC, isShow)
            menuAdapter.updateRemindStatus(MenuUtil.SUB_MENU_KEY_MEETING_CAMERA, isShow)
        }

        if (UserHandler.isLogin) {
            UserHandler.isLoginLive.observe(this@SettingActivity) { login ->
                if (!login) {
                    logTagI(TAG, "被踢掉, 销毁页面")
                    finish()
                }
            }
        }

        repeatCollectOnResume(mainViewModel.currentSubMenuFlow) {
            showSelect(it)
            menuAdapter.updateSelectItem(it)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (!intent.getStringExtra(BOOT_KEY_SETTING_PAGE_MENU_KEY).isNullOrEmpty()
            || !intent.getStringExtra(BOOT_KEY_PAGE_MENU_NAME).isNullOrEmpty()
        ) {
            handleIntent(intent)
        }
    }

    override fun onDestroy() {
        NoticeHandler.clearAll()
        super.onDestroy()
    }

    /**
     * 处理传过来的页面信息
     */
    private fun handleIntent(intent: Intent) {
        mainViewModel.handleIntent(intent)
    }



    private fun showSelect(subMenu: SubMenu) {
        val lastFragment = supportFragmentManager.findFragmentByTag(FRAGMENT_MENU_TAG)
        if (lastFragment != null && lastFragment::class.java == subMenu.menuFragment) {
            // 不需要重复替换
            return
        }
        supportFragmentManager.commit {
            replace(
                R.id.container,
                subMenu.menuFragment,
                null,
                FRAGMENT_MENU_TAG
            )
        }
        freePopBackStack()
    }

    fun showSubFragment(hideFa: Fragment, showFa: Fragment) {
        supportFragmentManager.commit {
            hide(hideFa)
            add(R.id.container, showFa)
            addToBackStack("back")
        }
    }

    override fun onRestart() {
        super.onRestart()
        supportFragmentManager.fragments.forEach {
            if (it is LoadingDialog) {
                val tag = it.tag
                if (tag == "WifiJoinFragment") {
                    logTagW(TAG, "删除多余的Loading")
                    supportFragmentManager.commit(true) {
                        remove(it)
                    }
                }
            }
        }
    }


    private fun freePopBackStack() {
        logTagD(TAG, "freePopBackStack")
        val backStackEntryCount = supportFragmentManager.backStackEntryCount
        for (index in 0 until backStackEntryCount) {
            val backStackEntryAt = supportFragmentManager.getBackStackEntryAt(index)
            if (backStackEntryAt.name == "back") {
                supportFragmentManager.popBackStack()
            }
        }

        val needRemoveFragment = supportFragmentManager.fragments.filterIsInstance<CZVBFloatingFragment<*>>()
        needRemoveFragment.forEach {
            it.dismiss()
        }

    }

}