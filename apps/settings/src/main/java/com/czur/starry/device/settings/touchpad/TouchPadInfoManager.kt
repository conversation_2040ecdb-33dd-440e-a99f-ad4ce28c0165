package com.czur.starry.device.settings.touchpad

import android.content.Context
import com.czur.starry.device.baselib.utils.data.StateFlowDelegate
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * Created by 陈丰尧 on 2021/10/18
 * Update by 陈丰尧 on 2022/11/04
 * 只用来记录触控板的电量和版本信息
 */
class TouchPadInfoManager(val context: Context) {
    companion object {
        /**
         * 电量还没有获取到
         */
        const val BATTERY_DID_NOT_GET = -1
    }

    /**
     * 触控板电量
     */
    val batteryLeveFlow = MutableStateFlow(BATTERY_DID_NOT_GET)
    var batteryLevel: Int by StateFlowDelegate(batteryLeveFlow)

    /**
     * 触控板的版本信息
     */
    val touchPadVersionFlow = MutableStateFlow(TouchPadVersionWrapper(""))
    var touchPadVersion: String
        set(value) {
            touchPadVersionFlow.value = TouchPadVersionWrapper(value)
        }
        get() = touchPadVersionFlow.value.version


    fun resetTouchPadInfo() {
        batteryLeveFlow.value = BATTERY_DID_NOT_GET
        touchPadVersionFlow.value = TouchPadVersionWrapper("")
    }
}