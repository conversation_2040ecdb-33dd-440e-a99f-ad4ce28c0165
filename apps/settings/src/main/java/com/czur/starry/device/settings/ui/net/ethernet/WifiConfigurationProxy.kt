package com.czur.starry.device.settings.ui.net.ethernet

import android.annotation.SuppressLint
import android.net.IpConfiguration
import android.net.wifi.WifiConfiguration

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/08
 */


class WifiConfigurationProxy(private val config: WifiConfiguration) {
    private val wifiConfig = config

    private val wifiConfigClass by lazy {
        Class.forName("android.net.wifi.WifiConfiguration")
    }
    private val getIpAssignment by lazy {
            wifiConfigClass.getDeclaredMethod("getIpAssignment").apply { isAccessible = true }
    }
    private val getIpConfiguration by lazy {
        wifiConfigClass.getDeclaredMethod("getIpConfiguration").apply { isAccessible = true }
    }

    fun getIpAssignment():Any{
        return getIpAssignment.invoke(wifiConfig)
    }
    @SuppressLint("NewApi")
    fun getIpConfiguration():IpConfiguration{
        return getIpConfiguration.invoke(wifiConfig) as IpConfiguration
    }
}