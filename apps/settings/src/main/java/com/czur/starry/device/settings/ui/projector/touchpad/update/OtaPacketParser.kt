package com.czur.starry.device.settings.ui.projector.touchpad.update

import kotlin.math.ceil
import kotlin.math.floor

/**
 * Created by 陈丰尧 on 2021/12/27
 * OTA 数据包解析类
 */
class OtaPacketParser {
    companion object {
        private const val DATA_PACKET_SIZE = 16
    }

    private var total = 0
    private var index = -1
    private var data = ByteArray(0)
    var progress = 0
        private set

    fun set(data: ByteArray) {
        clear()
        this.data = data
        val length = data.size
        // 向上取整
        total = ceil(length.toFloat() / DATA_PACKET_SIZE).toInt()
    }

    /**
     * 是否有下一个数据包
     */
    fun hasNextPacket(): Boolean {
        return total > 0 && index + 1 < total
    }

    fun getNextPacketIndex() = index + 1

    fun getCurIndex(): Int = index

    fun getNextPacket(): ByteArray {
        val index = getNextPacketIndex()
        val packet = getPacket(index)
        this.index = index
        return packet
    }

    private fun getPacket(index: Int): ByteArray {
        val length = data.size
        var packetSize = if (length > DATA_PACKET_SIZE) {
            if (index + 1 == total) {
                length - index * DATA_PACKET_SIZE
            } else {
                DATA_PACKET_SIZE
            }
        } else {
            length
        }

        packetSize += 4
        val packet = ByteArray(20)
        for (i in 0 until 20) {
            packet[i] = 0xFF.toByte()
        }
        // 复制数据
        System.arraycopy(data, index * DATA_PACKET_SIZE, packet, 2, packetSize - 4)
        // 前两个字节是index
        fillIndex(packet, index)
        // 后两个字节是crc
        val crc = crc16(packet)
        fillCrc(packet, crc)
        return packet
    }

    fun fillIndex(packet: ByteArray, index: Int) {
        var offset = 0
        packet[offset++] = (index and 0xFF).toByte()
        packet[offset] = (index shr 8 and 0xFF).toByte()
    }

    fun fillCrc(packet: ByteArray, crc: Int) {
        var offset = packet.size - 2
        packet[offset++] = (crc and 0xFF).toByte()
        packet[offset] = (crc shr 8 and 0xFF).toByte()
    }

    fun crc16(packet: ByteArray): Int {
        val length = packet.size - 2
        val poly = shortArrayOf(0, 0xA001.toShort())
        var crc = 0xFFFF
        var ds: Int
        for (j in 0 until length) {
            ds = packet[j].toInt()
            for (i in 0..7) {
                crc = crc shr 1 xor poly[crc xor ds and 1].toInt() and 0xFFFF
                ds = ds shr 1
            }
        }
        return crc
    }

    fun invalidateProgress(): Boolean {
        val a = getNextPacketIndex().toFloat()
        val b = total.toFloat()
        val progress = floor((a / b * 100)).toInt()
        if (progress == this.progress) return false
        this.progress = progress
        return true
    }

    fun clear() {
        progress = 0
        total = 0
        index = -1
        data = ByteArray(0)
    }
}