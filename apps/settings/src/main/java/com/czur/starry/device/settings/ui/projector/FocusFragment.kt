package com.czur.starry.device.settings.ui.projector

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.view.CenteredImageSpan
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentFocusBinding
import com.czur.starry.device.settings.manager.FocusManager
import com.czur.starry.device.settings.manager.FocusManager.Companion.getAutoFocusStatus
import com.czur.starry.device.settings.manager.FocusManager.Companion.setAutoFocusStatus
import com.czur.starry.device.settings.ui.projector.FocusWindowService.Companion.MODE_AUTO

/**
 * Created by 陈丰尧 on 1/22/21
 * 对焦画面
 */
class FocusFragment : BaseBindingMenuFragment<FragmentFocusBinding>() {
    companion object {
        const val TAG = "FocusFragment"
    }

    override fun FragmentFocusBinding.initBindingViews() {
        val expand = 3
        val imgDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.ic_focus)?.apply {
            setBounds(0, 0, minimumWidth + expand, minimumHeight + expand)
        }

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            manualFocusHint1Tv.gone()
            autoFocusHintTv.text = getString(R.string.auto_focus_hint_army_build)
        } else {
            val manualText = getString(R.string.manual_focus_hint)
            setForceImage(imgDrawable!!, manualFocusHint1Tv, manualText)

            val autoText = getString(R.string.auto_focus_hint)
            setForceImage(imgDrawable!!, autoFocusHintTv, autoText)
        }




        manualFocusGroup.setOnDebounceClickListener {
            // 手动对焦
            FocusManager.executeManualFocus()
        }

        focusAutoTv.setOnDebounceClickListener {
            FocusManager.executeAutoFocus()
        }

        focusAutoSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                launch {
                    setAutoFocusStatus(isOn)
                }
                if (isOn) {
                    logTagV(TAG, "开关打开,触发自动对焦")
                    FocusManager.executeAutoFocus()
                }
            }
        }
    }

    private fun setForceImage(imgDrawable: Drawable, tv: TextView, text: String) {
        val placeholderCharacter = "[focus]"
        val index = text.indexOf("[focus]")
        val span = SpannableString(text)

        val imgSpan = CenteredImageSpan(imgDrawable)
        span.setSpan(
            imgSpan,
            index,
            index + placeholderCharacter.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )

        tv.text = span
    }

    override fun initData(savedInstanceState: Bundle?) {
        launch {
            val autoFocusState = getAutoFocusStatus()
            logTagD(TAG, "自动对焦状态:$autoFocusState")
            binding.focusAutoSwitch.setSwitchOn(autoFocusState)
        }
    }
}