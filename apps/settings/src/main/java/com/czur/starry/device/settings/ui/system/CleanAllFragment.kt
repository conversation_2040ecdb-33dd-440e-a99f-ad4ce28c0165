package com.czur.starry.device.settings.ui.system

import android.content.Intent
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.performTouch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.SettingMainViewModel
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentClearDataBinding
import kotlinx.coroutines.delay


class CleanAllFragment : BaseBindingMenuFragment<FragmentClearDataBinding>() {
    private val mainViewModel: SettingMainViewModel by viewModels({ requireActivity() })

    private val systemManager: SystemManagerProxy by lazy(LazyThreadSafetyMode.NONE) {
        SystemManagerProxy()
    }

    override fun FragmentClearDataBinding.initBindingViews() {

        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas || Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            updateVersionInfoTextView.setText(R.string.tv_clear_all_oversea)
        } else {
            updateVersionInfoTextView.setText(R.string.tv_clear_all)
        }

        updateVersionInfoView.setDebounceTouchClickListener {
            DoubleBtnCommonFloat(content = getString(R.string.clean_all_data)) { doublecommonFloat, position ->
                doublecommonFloat.dismiss()
                if (position != 0) {
                    willRecoveryBroadcast()
                    launch {
                        withLoading {
                            //延迟1秒
                            delay(100)
                            systemManager.saveDlp3439Looks(
                                -1,
                                SystemManagerProxy.LooksMode.LOOKS_MODE_8880K
                            )
                            recoveryBroadcast()
                            delay(ONE_SECOND * 3)   // 让恢复出厂广播有足够的时间被处理
                        }
                    }
                }
            }.show()
        }

        repeatCollectOnResume(mainViewModel.currentViewNavigateFlow) {
            if (it == getString(R.string.voice_change_recovery)) {
                mainViewModel.onNavigateReset()
                updateVersionInfoView.performTouch()
            }

        }
    }

    //即将恢复出厂广播
    private fun willRecoveryBroadcast() {
        val intent = Intent("android.intent.action.WILL_FACTORY_RESET")
        activity?.sendBroadcast(intent)
    }

    //恢复出厂广播
    private fun recoveryBroadcast() {
        val intent = Intent("android.intent.action.FACTORY_RESET")
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
        intent.setPackage("android")
        intent.putExtra("android.intent.extra.REASON", "MasterClearConfirm")
        intent.putExtra("android.intent.extra.WIPE_EXTERNAL_STORAGE", false)
        activity?.sendBroadcast(intent)
    }


}