package com.czur.starry.device.settings.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.net.ConnectivityManager
import android.net.wifi.ScanResult
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiEnterpriseConfig
import android.net.wifi.WifiManager
import android.os.Build
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.BASE_HOST
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.basic.alsoTrue
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.settings.model.WifiInfoEntity
import com.czur.starry.device.settings.utils.WIFIUtilConstant.NET_MASK_DEFAULT
import com.google.common.reflect.TypeToken
import com.google.gson.Gson
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.lang.reflect.Type
import java.net.HttpURLConnection
import java.net.URL
import java.util.Locale


/**
 * Created by 陈丰尧 on 2/24/21
 */
private const val TAG = "WifiUtil"

data class WifiBlackInfo(
    val regex: Regex,
)

private val WIFI_SSID_BLACK_LIST = listOf(
    WifiBlackInfo(
        "^${Constants.SERIAL}$".toRegex(),
    )
)

const val WEP = 1
const val WPA = 2
const val WPA2 = 3
const val WPA3_SAE = 6
const val EAP = 4
const val OPEN = 5

private val appContext: Context by lazy { CZURAtyManager.appContext }
private val wifiManager: WifiManager by lazy { initWifiManager() }

object WIFIUtilConstant {
    const val NET_MASK_DEFAULT = "255.255.255.0"
}

/**
 * 初始化WIFIManager
 */
private fun initWifiManager(): WifiManager {
    return appContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
}

fun isWifiEnable() = wifiManager.isWifiEnabled

fun enableWifi() {
    logTagI(TAG, "enableWifi")
    if (!wifiManager.isWifiEnabled) {
        wifiManager.isWifiEnabled = true
    }
}

fun startScanWifi() {
    logTagD(TAG, "开始扫描WIFI")
    wifiManager.startScan()
}

@SuppressLint("MissingPermission")
fun getWifiScanResults() = wifiManager.scanResults.filter {
    val ssid = if (Build.VERSION.SDK_INT >= 33) {
        it.wifiSsid?.toString()?.replace("\"", "") ?: ""
    } else {
        it.SSID.replace("\"", "")
    }

    !WIFI_SSID_BLACK_LIST.any { blackInfo ->
        ssid.matches(blackInfo.regex)
    }
}

/**
 * 判断是否是加密的WIFI
 */
fun ScanResult.isLocked(): Boolean {
    return capabilities.contains("WEP", true)
            || capabilities.contains("PSK", true)
            || capabilities.contains("EAP", true)
            || capabilities.contains("X802_1", true)
            || capabilities.contains("SAE", true)
}

/**
 * 获取加密类型
 */
fun ScanResult.getLockType(): Int {
    logTagD(TAG, "SSID:${SSID} ,capabilities:${capabilities}")
    return when {
        capabilities.uppercase(Locale.getDefault()).contains("SAE") -> WPA3_SAE
        capabilities.uppercase(Locale.getDefault()).contains("WPA2") -> WPA2
        capabilities.uppercase(Locale.getDefault()).contains("WPA") -> WPA
        capabilities.uppercase(Locale.getDefault()).contains("EAP") -> WEP
        else -> OPEN
    }
}

/**
 * 处理连接信息的结果集合
 * 将从wifiManager中获取的信息处理成UI需要的信息
 * 包括: 确定当前连接的wifi, 合并ssid相同的信号(保留信号强度大的), 去除空的ssid信息, 判断是否是加密wifi
 */
fun transformToWifiInfoEntityList(result: List<ScanResult>): List<WifiInfoEntity> {
    val info = wifiManager.connectionInfo
    logTagD(TAG, "连接信息: $info")
    return result
        .asSequence()
        .filter {
            // 去除ssid为空的数据
            it.SSID.isNotEmpty()
        }.map {
            // 处理成UI显示需要的数据类型
            WifiInfoEntity(
                ssid = it.SSID,
                levelRaw = it.level,
                locked = it.isLocked(),
                bssid = it.BSSID,
                connecting = "\"${it.SSID}\"" == info.ssid
            ) // info中的ssid会带双引号
        }.sortedWith { a, b ->
            // 优先显示 连接的
            var sortedResult = -a.connecting.compareTo(b.connecting)

            // 信号相同的 按照SSID名字排序
            if (sortedResult == 0) {
                sortedResult = a.ssid.compareTo(b.ssid)
            }
            if (sortedResult == 0) {
                // 信号好的优先
                sortedResult = a.levelRaw.compareTo(b.levelRaw) * -1
            }
            sortedResult // 降序
        }.distinctBy { // 根据ssid进行去重,因为信号好的在前面,所以信号好的会覆盖信号不好的
            it.ssid
        }.sortedDescending()
        .toList() //结果降序排列
}

// 系统Setting模块, 不会有权限问题
@SuppressLint("MissingPermission")
suspend fun connectWIFI(
    ssid: String,
    password: String,
    security: Int,
    isRetry: Boolean = false,
    hiddenSSID: Boolean = false,
    needSpeed: Boolean = false
): Boolean {
    logTagV(TAG, "connectWIFI ssid:$ssid, password:$password, security:$security")
    val config = WifiConfiguration()
    config.SSID = "\"$ssid\""
    config.status = WifiConfiguration.Status.ENABLED


    when (security) {
        WEP -> setWEPSecurity(config, password)
        WPA, WPA2 -> setWPASecurity(config, password)
        WPA3_SAE -> setWPA3SAESecurity(config, password)
        EAP -> setEAPSecurity(config, password)
        OPEN -> setOpenWifiConfig(config)
        else -> throw NotImplementedError("Unknown Wifi-security-type: $security")
    }
    //隐藏ssid
    if (hiddenSSID) config.hiddenSSID = true
    wifiManager.addNetwork(config);

    val networks = wifiManager.configuredNetworks
    if (networks.isNullOrEmpty()) {
        logTagE(TAG, "wifiManager.configuredNetworks 为空")
        return false
    }
    for (network in networks!!) {
        if (network.SSID == "\"$ssid\"") {
            if (wifiManager.connectionInfo.ssid != "<unknown ssid>" && wifiManager.connectionInfo.ssid != null) {
                wifiManager.disconnect()
            }
            wifiManager.enableNetwork(network.networkId, true)
            val res = wifiManager.reconnect()
            if (res) {
                try {
                    // 循环等待 判断是否连接成功
                    repeat(30) {
                        logTagV(TAG, "等待wifi连接成功:第 $it 次")
                        if (wifiManager.connectionInfo.ssid == "\"$ssid\"" && wifiManager.connectionInfo.linkSpeed > 0) {
                            val sp = appContext.getSharedPreferences(
                                SPHandler.SP_NAME,
                                Context.MODE_PRIVATE
                            )
                            //保存ssid密码及添加连接失败标记，连接成功后清除失败标记
                            sp.edit().putString(ssid, password).apply()
                            // 表示连接成功
                            logTagI(TAG, "连接WIFI成功")
                            return true
                        }
                        delay(500) // 每次检测等待半秒钟的时间
                    }
                    logTagW(TAG, "连接WIFI超时")
                    return false
                } catch (e: CancellationException) {
                    // 协程被取消
                    logTagD(TAG, "协程被取消")
                    wifiManager.removeNetwork(network.networkId)
                    WifiConfigUtil.forgetConfiguration(wifiManager, ssid)
                    return false
                } catch (tr: Throwable) {
                    logTagE(TAG, "连接WIFI异常", tr = tr)
                    return false

                }
            }
        }
        logTagD(TAG, "network.SSID=" + network.SSID)

    }
    logTagE(TAG, "连接的SSID不在wifiManager.configuredNetworks列表")
    return false
}

private fun setOpenWifiConfig(config: WifiConfiguration) {
    config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE)
    config.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
    config.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
    config.allowedAuthAlgorithms.clear();
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)

}

private fun setWPASecurity(config: WifiConfiguration, password: String) {
    config.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
    config.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
    config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)

    if (password.matches(Regex("^[0-9a-fA-F]{64}"))) {
        config.preSharedKey = password
    } else {
        config.preSharedKey = '"' + password + '"'
    }

}

private fun setWPA3SAESecurity(config: WifiConfiguration, password: String) {
    config.setSecurityParams(WifiConfiguration.SECURITY_TYPE_SAE)
    config.preSharedKey = '"' + password + '"'
}


private fun setWEPSecurity(config: WifiConfiguration, password: String) {
    config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE)
    config.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
    config.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
    config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
    config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.SHARED)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104)
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40)
    config.wepKeys[0] = if (password.matches(Regex("^[0-9a-fA-F]+$]"))) {
        password; } else {
        "\"$password\""
    }
    config.wepTxKeyIndex = 0
}

private fun setEAPSecurity(config: WifiConfiguration, password: String) {
    config.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
    config.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
    config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_EAP)
    config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.IEEE8021X)
    config.allowedGroupCiphers.clear()
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
    config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
    config.enterpriseConfig = WifiEnterpriseConfig()
    config.enterpriseConfig.caCertificates = null
    config.enterpriseConfig.domainSuffixMatch



    config.enterpriseConfig.password = password


}


fun isInSaveList(sp: SharedPreferences, ssid: String): Boolean {
    val isSave = ((sp.getString(ssid, "") != null) && sp.getString(
        ssid,
        ""
    )?.length!! > 0)
    return isSave
}

private val WALLED_GARDEN_URL = "http://${BASE_HOST}/api/starry/share/204"
private const val WALLED_GARDEN_SOCKET_TIMEOUT_MS = 10 * ONE_SECOND.toInt()

/**
 * 判断是否是portal认证
 * @return true 是portal认证
 *       false 不是portal认证
 *       出现异常时返回Failure
 */
suspend fun isWifiSetPortal(): Result<Boolean> = withContext(Dispatchers.IO) {
    logTagV(TAG, "isWifiSetPortal Start:url=$WALLED_GARDEN_URL")
    var urlConnection: HttpURLConnection? = null
    try {
        val url = URL(WALLED_GARDEN_URL)
        urlConnection = url.openConnection() as HttpURLConnection
        urlConnection.instanceFollowRedirects = false
        urlConnection.connectTimeout = WALLED_GARDEN_SOCKET_TIMEOUT_MS
        urlConnection.readTimeout = WALLED_GARDEN_SOCKET_TIMEOUT_MS
        urlConnection.useCaches = false
        val responseCode = urlConnection.responseCode
        logTagD(TAG, "isWifiSetPortal responseCode=$responseCode")
        Result.success(responseCode != 204)
    } catch (e: Throwable) {
        logTagE(TAG, "isWifiSetPortal exception ${e.message}", tr = e)
        Result.failure(e)
    } finally {
        doWithoutCatch {
            urlConnection?.disconnect()
        }
    }
}

/**
 * 获取wlan0的子网掩码
 */
fun getWlan0Mask(): String {
    try {
        val process = Runtime.getRuntime().exec(arrayOf("ifconfig", "wlan0"))
        val result = process.inputStream.bufferedReader().use {
            it.readText()
        }
        logTagD(TAG, "getWlan0Mask result: $result")
        val mask = getLineMask(result)
        logTagD(TAG, "getWlan0Mask mask: $mask")
        return mask
    } catch (e: Exception) {
        logTagE(TAG, "getWlan0Mask exception", tr = e)
    }
    return NET_MASK_DEFAULT
}

private fun getLineMask(line: String): String {
    val pattern = "Mask:(\\d+\\.\\d+\\.\\d+\\.\\d+)".toRegex()
    val matchResult = pattern.find(line)
    return matchResult?.groupValues?.get(1) ?: ""
}

