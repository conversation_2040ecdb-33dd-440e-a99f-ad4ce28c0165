package com.czur.starry.device.settings.ui.system.time

import android.os.Bundle
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentTimeZoneBinding
import com.czur.uilib.extension.rv.addCornerRadius

/**
 * Created by 陈丰尧 on 2023/6/19
 */
private const val TAG = "TimeZoneFragment"

class TimeZoneFragment : BaseBindingMenuFragment<FragmentTimeZoneBinding>() {
    private val timeZoneAdapter = TimeZoneAdapter()
    private val timeZoneVM: TimeZoneViewModel by viewModels()
    private var isInitSel = true

    override fun FragmentTimeZoneBinding.initBindingViews() {
        timeZoneRv.apply {
            addCornerRadius(backgroundColor = requireContext().getColor(R.color.bg_setting_normal))
            closeDefChangeAnimations()
            adapter = timeZoneAdapter
            doOnItemClick { vh, view ->
                val pos = vh.bindingAdapterPosition
                val data = timeZoneAdapter.getData(pos)
                when (view.id) {
                    R.id.leftClickView -> {
                        logTagD(TAG, "点击TimeZone左侧")
                        timeZoneVM.changeIndexBarState()
                        true
                    }

                    else -> {
                        logTagD(TAG, "切换选中TimeZone")
                        timeZoneVM.changeSelectId(data.id)
                        true
                    }
                }
            }
        }

        timeZoneAdapter.onSelPosChange = {
            onSelPosInit()
        }

        timeZoneIndexBar.doOnChoose {
            launch {
                val index = timeZoneVM.getIndexByChar(it)
                if (index != -1) {
                    (timeZoneRv.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                        index,
                        0
                    )
                }
            }
        }

        // 切换到搜索模式
        searchBgView.setOnDebounceClickListener {
            timeZoneVM.changeSearchMode(true)
        }
        searchEtMark.setOnDebounceClickListener {
            timeZoneVM.changeSearchMode(true)
        }
        closeSearchIv.setOnDebounceClickListener {
            timeZoneVM.changeSearchMode(false)// 关闭搜索模式
        }

        searchEt.doAfterTextChanged {
            timeZoneVM.updateSearchWord(it.toString())
        }

        applyBtn.setOnDebounceClickListener {
            logTagI(TAG, "点击应用时区按钮")
            timeZoneVM.applyTimeZone()
            timeZoneVM.changeSearchMode(false)// 关闭搜索模式
            timeZoneVM.changeIndexBarState(false)   // 关闭索引条
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(timeZoneVM.selTimeZoneIdFlow) {
            timeZoneAdapter.updateSelectId(it)
        }
        repeatCollectOnResume(timeZoneVM.timeZoneDataFlow) {
            timeZoneAdapter.setData(it.second)
            if (it.second.isNotEmpty()) {
                binding.timeZonePB.gone()
            }
            binding.timeZoneIndexBar.setWords(it.first)
        }

        repeatCollectOnResume(timeZoneVM.showIndexBar) {
            binding.timeZoneIndexBar.gone(it.not())
        }

        repeatCollectOnResume(timeZoneVM.applyBtnEnableFlow) {
            binding.applyBtn.isEnabled = it
        }

        // 搜索模式
        repeatCollectOnResume(timeZoneVM.searchModeFlow) {
            logTagD(TAG, "搜索模式:${it}")
            binding.searchLayer.gone(it)
            binding.searchTimeZoneGroup.gone(it.not())
            // 系统的bug, 只有View的位置发生变化, 才能更新鼠标指针的样式, 所以弄一个View来改变对应范围的鼠标指针样式
            // 在编辑模式时, 给他移走, 在非编辑模式时, 给他移回来来挡住EditText, 防止鼠标指针变成 "|" 样式
            binding.searchEtMark.translationY = if (it) -200F else 0F
            if (it) {
                // 搜索模式
                binding.searchEt.requestFocus()
                binding.searchEt.keyboardShow()
            } else {
                // 非搜索模式
                binding.searchEt.clearFocus()
                binding.searchEt.clearContentText()
            }
        }

        // 搜索结果为空
        repeatCollectOnResume(timeZoneVM.showSearchEmptyFlow) {show ->
            binding.searchTimeZoneEmptyTv.gone(!show)
        }
    }

    private fun onSelPosInit() {
        if (isInitSel && timeZoneAdapter.currentSelPos >= 0) {
            // 初始化, 自动滚动到选中的位置
            isInitSel = false
            (binding.timeZoneRv.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
                timeZoneAdapter.currentSelPos,
                0
            )
        }
    }
}