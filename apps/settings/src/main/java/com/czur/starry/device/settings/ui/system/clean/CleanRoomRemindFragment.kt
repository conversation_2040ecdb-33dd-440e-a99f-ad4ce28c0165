package com.czur.starry.device.settings.ui.system.clean

import android.os.Bundle
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.otalib.OTAHandler.cycleRemindMemory
import com.czur.starry.device.otalib.OTAHandler.cycleRemindTime
import com.czur.starry.device.settings.databinding.FloatingCleanRoomRemindBinding

/**
 * Created by 陈丰尧 on 2023/8/7
 */
private const val REMAIN_LEFT_SMALL = 1f
private const val REMAIN_LEFT_MID = 1.5f
private const val REMAIN_LEFT_LARGE = 2f

private const val REMAIN_NONE = 0
private const val EVERY_1_DAY = 1
private const val EVERY_3_DAY = 3
private const val EVERY_7_DAY = 7

class CleanRoomRemindFragment : CZVBFloatingFragment<FloatingCleanRoomRemindBinding>() {

    private val remindTimeTvs by lazy(LazyThreadSafetyMode.NONE) {
        mapOf(
            REMAIN_NONE to binding.remindItemTimeNone,
            EVERY_1_DAY to binding.remindItemTimeEveryday,
            EVERY_3_DAY to binding.remindItemTime3Day,
            EVERY_7_DAY to binding.remindItemTime7Day
        )
    }

    private val remindStorageTvs by lazy(LazyThreadSafetyMode.NONE) {
        mapOf(
            REMAIN_LEFT_SMALL to binding.remindItemLeftSmall,
            REMAIN_LEFT_MID to binding.remindItemLeftMid,
            REMAIN_LEFT_LARGE to binding.remindItemLeftLarge
        )
    }

    override fun FloatingFragmentParams.initFloatingParams() {
        floatingBgMode = FloatingBgMode.NONE
    }

    override fun FloatingCleanRoomRemindBinding.initBindingViews() {
        remindTimeTvs.forEach { (key, value) ->
            value.setOnDebounceClickListener {
                cycleRemindTime = key
                updateRemindTime(key)
            }
        }

        remindStorageTvs.forEach { (key, value) ->
            value.setOnDebounceClickListener {
                cycleRemindMemory = key
                updateStorageRemain(key)
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        updateRemindTime(cycleRemindTime)
        updateStorageRemain(cycleRemindMemory)
    }

    private fun updateRemindTime(remindTime: Int) {
        remindTimeTvs.forEach { (key, value) ->
            value.isSelect = key == remindTime
        }
    }

    private fun updateStorageRemain(remain: Float) {
        remindStorageTvs.forEach { (key, value) ->
            value.isSelect = key == remain
        }
    }


}