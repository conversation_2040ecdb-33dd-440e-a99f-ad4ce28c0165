package com.czur.starry.device.settings.manager.speed

import com.czur.czurutils.log.logTagV
import android.net.TrafficStats

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow

/**
 * Created by 陈丰尧 on 3/9/21
 */
enum class NetSpeedType {
    DOWNLOAD,
    UPLOAD
}

class NetSpeedRecordUtil(
    private val type: NetSpeedType,
    private val testCount: Int,
    private val testInterval: Long,
) {
    val sizeFlow = MutableSharedFlow<Long>()

    private var lastTraffic: Long = 0L


    companion object {
        private const val TAG = "NetSpeedRecordUtil"
    }

    private fun getCurrentBytes(): Long = when (type) {
        NetSpeedType.DOWNLOAD -> TrafficStats.getTotalRxBytes()
        NetSpeedType.UPLOAD -> TrafficStats.getTotalTxBytes()
    }

    /**
     * 刷新初始数据
     */
    private fun refresh() {
        lastTraffic = getCurrentBytes()
    }

    /**
     * 获取数据变化量
     */
    private fun getTransBytes(): Long {
        val current = getCurrentBytes()
        val dBytes = current - lastTraffic
        lastTraffic = current
        logTagV(TAG, "${type}数据变化了:${dBytes}字节")
        return dBytes
    }

    suspend fun startRecord() {
        withContext(Dispatchers.IO) {
            refresh()
            repeat(testCount) {
                delay(testInterval)
                if (isActive) {
                    sizeFlow.emit(getTransBytes())
                }
            }
        }
    }

}