package com.czur.starry.device.settings.touchpad

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.SystemProperties
import com.czur.czurutils.log.logTagV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/3/7
 * 充电底座的工具类
 */
class PowerDockUtil(
    val onPowerDockStatusChange: (dockStatus: Boolean) -> Unit
) {
    companion object {
        private const val TAG = "TouchPad-PowerDockUtil"
        private const val PROP_KEY_DOCK_STATUS = "vendor.czur.btbdocked" // 触控板是否处于连接状态
        private const val KEY_EXTRAS_STATUS = "state"
        private const val ACTION_DOCKED = "com.android.action.CZUR_BTB_DOCKED"
    }

    private val receiver by lazy {
        DockChangeReceiver()
    }

    /**
     * 查询触控板是否处于充电位置
     */
    suspend fun loadDockStatus(): Boolean = withContext(Dispatchers.IO) {
        SystemProperties.getBoolean(PROP_KEY_DOCK_STATUS, false)
    }

    /**
     * 注册触控板状态广播
     */
    fun registerDockReceiver(context: Context) {
        receiver.register(context)
    }

    /**
     * 取消注册触控板状态广播
     */
    fun unregisterDockReceiver(context: Context) {
        receiver.unregister(context)
    }

    inner class DockChangeReceiver : BroadcastReceiver() {
        private var hasRegister = false

        @SuppressLint("UnspecifiedRegisterReceiverFlag")
        fun register(context: Context) {
            context.registerReceiver(this, IntentFilter().apply {
                addAction(ACTION_DOCKED)
            })
            hasRegister = true
        }

        fun unregister(context: Context) {
            if (hasRegister) {
                context.unregisterReceiver(this)
                hasRegister = false
            }
        }

        override fun onReceive(context: Context?, intent: Intent?) {
            val dockStatus = intent?.extras?.getBoolean(KEY_EXTRAS_STATUS, false) ?: false
            logTagV(TAG, "触控板状态放置更新:${dockStatus}")
            onPowerDockStatusChange(dockStatus)
        }

    }
}