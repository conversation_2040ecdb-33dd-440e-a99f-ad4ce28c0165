package com.czur.starry.device.settings.ui.projector.bluetooth

import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.bluetoothlib.bluetooth.CachedBluetoothDevice
import com.czur.starry.device.bluetoothlib.util.isHeadPhones
import com.czur.starry.device.bluetoothlib.util.isKeyBoard
import com.czur.starry.device.bluetoothlib.util.isMouse
import com.czur.starry.device.bluetoothlib.util.isSpeaker
import com.czur.starry.device.bluetoothlib.util.isWritePad
import com.czur.starry.device.bluetoothlib.util.ismicroPhone
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.FloatBtDetailBinding
import com.czur.starry.device.settings.model.BTBoundDeviceEntity

/**
 * 蓝牙详情对话框
 */
class BTDetailFloat(
    private val device: BTBoundDeviceEntity,
    private val onIgnoreClick: (dialog: BTDetailFloat, device: CachedBluetoothDevice) -> Unit,
) : CZVBFloatingFragment<FloatBtDetailBinding>() {

//    private val closeBtn by findView<ImageView>(R.id.closeBtn)
//    private val btImgIv by findView<ImageView>(R.id.btImgIv)
//    private val batteryGroup by findView<LinearLayout>(R.id.batteryGroup)
//    private val batteryTv by findView<TextView>(R.id.batteryTv)
//    private val batteryView by findView<BatteryView>(R.id.batteryView)
//    private val deviceNameTv by findView<TextView>(R.id.deviceNameTv)
//    private val ignoreBtn by findView<CZButton>(R.id.ignoreBtn)


    override fun FloatBtDetailBinding.initBindingViews() {

        // 关闭按钮
        closeBtn.setOnClickListener {
            dismiss()
        }

        // 图标
        if (device.cachedDevice.isWritePad) {
            // 手写板 先判断手写板, 因为手写板是通过名字判断的 比较特殊
            btImgIv.setImageResource(R.drawable.img_bt_writepad)
        } else if (device.cachedDevice.isKeyBoard) {
            // 键盘
            btImgIv.setImageResource(R.drawable.img_bt_keyboard)
        } else if (device.cachedDevice.isMouse) {
            // 鼠标
            btImgIv.setImageResource(R.drawable.img_bt_mouse)
        } else if (device.cachedDevice.ismicroPhone) {
            // 麦克风
            btImgIv.setImageResource(R.drawable.img_bt_microphone)
        } else if (device.cachedDevice.isSpeaker) {
            // 扬声器
            btImgIv.setImageResource(R.drawable.img_bt_speaker)
        } else if (device.cachedDevice.isHeadPhones) {
            // 耳机
            btImgIv.setImageResource(R.drawable.img_bt_headphones)
        } else {
            /// 暂未有图片，先用鼠标的顶替
            btImgIv.setImageResource(R.drawable.img_bt_bluetooth)
        }

        // 电量
        if (device.batteryLevel > 0) {
            batteryGroup.show()
            batteryTv.text = "${device.batteryLevel}%"
            batteryView.power = device.batteryLevel
        } else {
            batteryGroup.gone()
        }
        // 设备名称
        deviceNameTv.text = device.name

        // 按钮点击事件
        ignoreBtn.setOnClickListener {
            onIgnoreClick(this@BTDetailFloat, device.cachedDevice)
        }

        // 提示文言
        if (device.cachedDevice.isWritePad) {
            detailHintTv.setText(R.string.str_hint_bt_disconnect_writepad)
            detailHintSpaceTv.setText(R.string.str_hint_bt_disconnect_writepad)
        }
    }
}