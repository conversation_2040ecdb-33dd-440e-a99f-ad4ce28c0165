package com.czur.starry.device.settings.debug

import android.view.View
import android.view.WindowManager
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.settings.R

/**
 * Created by 陈丰尧 on 2023/3/24
 */
class EyeProtectionWindowService : AlertWindowService() {
    override val windowType: Int = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
    override val layoutId: Int = R.layout.window_eye_protection
    override val windowWidthParam: Int = getScreenWidth()
    override val windowHeightParam: Int = getScreenHeight()
    override val careKeyEvent: Boolean = false
    override val customWindowFlag: Int =
        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE

    override fun View.initViews() {
    }

    override fun initData() {
        super.initData()
        SettingUtil.SystemSetting.registerObserver(this) {
            if (!SettingUtil.SystemSetting.isEyeProtectionEnable()) {
                stopSelf()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

}