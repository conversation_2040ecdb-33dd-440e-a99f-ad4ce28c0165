package com.czur.starry.device.settings.manager

import android.content.Context
import android.content.Intent
import android.os.RkDisplayOutputManager
import android.os.SystemProperties
import android.provider.Settings
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.prop.getSystemProp
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.settings.manager.DisplayManager.KeyStonePoint.LEFT_BOTTOM
import com.czur.starry.device.settings.manager.DisplayManager.KeyStonePoint.LEFT_TOP
import com.czur.starry.device.settings.manager.DisplayManager.KeyStonePoint.RIGHT_BOTTOM
import com.czur.starry.device.settings.manager.DisplayManager.KeyStonePoint.RIGHT_TOP
import com.czur.starry.device.settings.manager.DisplayManager.PIC_SIZE_MAX
import com.czur.starry.device.settings.manager.DisplayManager.PIC_SIZE_MIN
import com.czur.starry.device.settings.manager.keystone.Vertex
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.max
import kotlin.math.min

/**
 * Created by 陈丰尧 on 1/22/21
 * 集成 Demo:BizLineAdjustWithBash中的部分内容
 * 用来设置显示相关的属性,例如 亮度等
 */
object DisplayManager {
    private const val TAG = "DisplayManager"

    private const val MAIN_SCREEN = 0

    /*
    画面缩放直接设定property，底层已经实装好，并在500ms内自动刷新画面。
    设定范围51-100,默认100。。
    setprop persist.vendor.overscan.main "overscan 80,80,80,80"
     */
    private const val SYSTEM_KEY_PIC_SIZE = "persist.vendor.overscan.main"
    private const val PIC_SIZE_VALUE_PREFIX = "overscan"
    const val PIC_SIZE_MIN = 51
    const val PIC_SIZE_MAX = 100
    const val PIC_SIZE_DEFAULT = PIC_SIZE_MAX
    private const val PIC_SIZE_LATENCY_TIME = 500L

    // 梯形矫正
    // 点击时的步长
    const val KEYSTONE_DEFAULT_CLICK_STEP = 1L
    private const val KEYSTONE_MAX_STEP = 50L

    // 通知系统更新梯形的key
    private const val KEYSTONE_PROP_UPDATE = "persist.sys.keystone.update"

    // 梯形矫正在设定中的key
    private const val KEY_AUTO_KEYSTONE = "settingAutoKeystone"
    private const val AUTO_KEYSTONE_ON = 1
    private const val AUTO_KEYSTONE_OFF = 0

    private const val KEYSTONE_POINT_SPACE_MIN = 500 // 梯形矫正点之间的最小间距

    private val adjustment = AtomicBoolean(false)

    private val forceAutoCorrectIntent: Intent = Intent("com.czur.keystone.ACTION.START").apply {
        `package` = "com.czur.keystone"
        putExtra("forceAutoCorrection", true)
    }

    enum class SetPicSizeRes {
        SUCCESS,
        FAIL,
        IGNORE;
    }


    // 调整点的位置
    enum class KeyStonePoint(val systemPropKey: String) {
        LEFT_TOP("persist.sys.keystone.lt"),       // 左上
        RIGHT_TOP("persist.sys.keystone.rt"),      // 右上
        LEFT_BOTTOM("persist.sys.keystone.lb"),    // 坐下
        RIGHT_BOTTOM("persist.sys.keystone.rb")    // 右下
    }

    // 调整方向
    enum class KeyStoneDirection {
        UP,     // 上
        DOWN,   // 下
        LEFT,   // 左
        RIGHT,  // 右
    }

    private val rkDisplayOutputManager by lazy {
        RkDisplayOutputManager()
    }

    enum class BrightnessMode(val value: Int) {
        Standard(50), //标准
        Highlight(100) //高亮
    }

    /**
     * 设置光机亮度
     */
    suspend fun setBrightnessMode(mode: BrightnessMode) = withContext(Dispatchers.IO) {
        setBrightness(mode.value)
    }

    /**
     * 获取光机亮度
     */
    suspend fun getBrightnessMode(): BrightnessMode = withContext(Dispatchers.IO) {
        val value = getBrightness()
        // 最大值是100, 但是可能在别的地方调整了这个值
        // 所以没有使用定值进行判断
        if (value < 80) {
            BrightnessMode.Standard
        } else {
            BrightnessMode.Highlight
        }
    }

    private fun setBrightness(brightness: Int): Boolean {
        val ret: Int =
            rkDisplayOutputManager.setBrightness(MAIN_SCREEN, brightness)
        logTagD(TAG, "设置亮度:${ret}")
        if (ret == 0) {
            logTagD(TAG, "保存设置")
            rkDisplayOutputManager.saveConfig()
            return true
        }
        return false
    }

    private fun getBrightness(): Int {
        return rkDisplayOutputManager.getBrightness(MAIN_SCREEN)
    }

    /***** 画面尺寸 *****/

    /**
     * 获取画面尺寸
     * @return 画面尺寸百分比
     */
    suspend fun getPicSize(): Int = withContext(Dispatchers.IO) {
        val picSizeValue = SystemProperties.get(SYSTEM_KEY_PIC_SIZE)
        parsePisSizeStr(picSizeValue)
    }

    /**
     * 解析系统画面尺寸的值
     * 格式为:overscan 80,80,80,80
     */
    private fun parsePisSizeStr(picSizeStr: String): Int {
        val regex = "[1-9][0-9]*".toRegex()
        val result = regex.find(picSizeStr)?.value
        // 解析失败,返回默认值
        return result?.toInt() ?: PIC_SIZE_DEFAULT
    }

    /**
     * 设置画面尺寸
     * @param size: 画面尺寸,范围在[PIC_SIZE_MIN]~[PIC_SIZE_MAX]之间
     */
    suspend fun setPicSize(size: Int, beforeStart: (beforeSize: Int) -> Unit = {}): SetPicSizeRes =
        withContext(Dispatchers.IO) {
            if (adjustment.get()) {
                logTagD(TAG, "正在调整中, 忽略本次调整")
                return@withContext SetPicSizeRes.IGNORE
            }
            adjustment.set(true)
            withContext(Dispatchers.Main) {
                beforeStart(getPicSize())
            }
            var result = SetPicSizeRes.SUCCESS
            if (size in PIC_SIZE_MIN..PIC_SIZE_MAX) {
                // 数据必须在范围内
                val valueStr = "$PIC_SIZE_VALUE_PREFIX $size,$size,$size,$size"
                try {
                    SystemProperties.set(SYSTEM_KEY_PIC_SIZE, valueStr)
                    // 通知更新提醒矫正
                    if (getAutoKeystoneStatus()) {
                        logTagV(TAG, "自动梯形校正开启,触发一次梯形校正")
                        doKeystone(quick = true)
                    }
                    // 需要等待画面更新完成,再进行下一次设定
                    delay(PIC_SIZE_LATENCY_TIME)
                } catch (e: Exception) {
                    logTagE(TAG, "设置画面尺寸错误($size):", tr = e)
                    result = SetPicSizeRes.FAIL
                }
            } else {
                logTagW(
                    TAG,
                    "画面尺寸需要在$PIC_SIZE_MIN ~ $PIC_SIZE_MAX 之间,要设定的值为:$size,所以忽略设定"
                )
                result = SetPicSizeRes.FAIL
            }
            adjustment.set(false)
            result
        }

    /****** 梯形矫正 *****/

    /**
     * 执行一次梯形校正
     */
    fun doKeystone(context: Context = CZURAtyManager.appContext, quick: Boolean = false) {
        when (Constants.starryHWInfo.series) {
            Q1Series -> context.startService(forceAutoCorrectIntent)
            Q2Series -> {
                val intent = Intent().apply {
                    setPackage("com.hysd.hyscreen.VAF2S")
                    setClassName("com.hysd.hyscreen", "com.hysd.hyscreen.VAF2S")
                    setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    putExtra("mode", 74)
                    putExtra("afswitch", 0)
                    putExtra("keyswitch", 1)
                    putExtra("dmode", 0)

                }
                runBlocking {
                    setSystemProp("persist.hysd.quickkey.run", "1")
                }
                if (!quick) {
                    context.startService(intent)
                }
            }

            else -> {
                logTagW(TAG, "不支持的机型,无法执行自动梯形矫正")
            }
        }
    }

    /**
     * 进行梯形矫正
     * @param point         要调整的点
     * @param direction     调整的方向
     * @return true:在这个方向上还可以继续调整
     */
    fun updateKeystone(
        point: KeyStonePoint,
        direction: KeyStoneDirection,
        step: Long = KEYSTONE_DEFAULT_CLICK_STEP,
        closeAutoKeystone: Boolean = false,
    ) {
        val realStep = min(KEYSTONE_MAX_STEP, step)
        val systemPropKey = point.systemPropKey
        val vertex = getVertex(systemPropKey)
        logTagD(TAG, "初始值:${vertex}")

        // 设置新值
        when (direction) {
            KeyStoneDirection.UP -> vertex.y += realStep
            KeyStoneDirection.DOWN -> vertex.y -= realStep
            KeyStoneDirection.LEFT -> vertex.x -= realStep
            KeyStoneDirection.RIGHT -> vertex.x += realStep
        }

        // 检查调整点之间的距离
        when (point) {
            LEFT_TOP -> {
                if (direction == KeyStoneDirection.RIGHT) {
                    val rightTop = getVertex(RIGHT_TOP.systemPropKey)
                    val currentWidth = getScreenWidth() + rightTop.x - vertex.x
                    if (currentWidth < KEYSTONE_POINT_SPACE_MIN) {
                        vertex.x -= KEYSTONE_POINT_SPACE_MIN - currentWidth
                    }
                }
                if (direction == KeyStoneDirection.DOWN) {
                    val leftBottom = getVertex(LEFT_BOTTOM.systemPropKey)
                    val currentHeight = getScreenHeight() - leftBottom.y + vertex.y
                    if (currentHeight < KEYSTONE_POINT_SPACE_MIN) {
                        vertex.y += KEYSTONE_POINT_SPACE_MIN - currentHeight
                    }
                }
            }

            RIGHT_TOP -> {
                if (direction == KeyStoneDirection.LEFT) {
                    val leftTop = getVertex(LEFT_TOP.systemPropKey)
                    val currentWidth = getScreenWidth() + vertex.x - leftTop.x
                    if (currentWidth < KEYSTONE_POINT_SPACE_MIN) {
                        vertex.x += KEYSTONE_POINT_SPACE_MIN - currentWidth
                    }
                }
                if (direction == KeyStoneDirection.DOWN) {
                    val rightBottom = getVertex(RIGHT_BOTTOM.systemPropKey)
                    val currentHeight = getScreenHeight() - rightBottom.y + vertex.y
                    if (currentHeight < KEYSTONE_POINT_SPACE_MIN.toLong()) {
                        vertex.y += KEYSTONE_POINT_SPACE_MIN - currentHeight
                    }
                }
            }

            LEFT_BOTTOM -> {
                if (direction == KeyStoneDirection.RIGHT) {
                    val rightBottom = getVertex(RIGHT_BOTTOM.systemPropKey)
                    val currentWidth = getScreenWidth() + rightBottom.x - vertex.x
                    if (currentWidth < KEYSTONE_POINT_SPACE_MIN) {
                        vertex.x -= KEYSTONE_POINT_SPACE_MIN - currentWidth
                    }
                }
                if (direction == KeyStoneDirection.UP) {
                    val leftTop = getVertex(LEFT_TOP.systemPropKey)
                    val currentHeight = getScreenHeight() - vertex.y + leftTop.y
                    if (currentHeight < KEYSTONE_POINT_SPACE_MIN) {
                        vertex.y -= KEYSTONE_POINT_SPACE_MIN - currentHeight
                    }
                }
            }

            RIGHT_BOTTOM -> {
                if (direction == KeyStoneDirection.LEFT) {
                    val leftBottom = getVertex(LEFT_BOTTOM.systemPropKey)
                    val currentWidth = getScreenWidth() + vertex.x - leftBottom.x
                    if (currentWidth < KEYSTONE_POINT_SPACE_MIN) {
                        vertex.x += KEYSTONE_POINT_SPACE_MIN - currentWidth
                    }
                }
                if (direction == KeyStoneDirection.UP) {
                    val rightTop = getVertex(RIGHT_TOP.systemPropKey)
                    val currentHeight = getScreenHeight() - vertex.y + rightTop.y
                    if (currentHeight < KEYSTONE_POINT_SPACE_MIN.toLong()) {
                        vertex.y -= KEYSTONE_POINT_SPACE_MIN - currentHeight
                    }
                }
            }
        }

        // 调整顶点
        adjustVertex(point, vertex)

        // 更新梯形矫正
        SystemProperties.set(systemPropKey, vertex.toString())
        SystemProperties.set(KEYSTONE_PROP_UPDATE, "1")

        if (closeAutoKeystone) {
            runBlocking {
                logTagD(TAG, "关闭自动梯形校正")
                setAutoKeystoneStatus(false)
            }
        }
    }

    fun updateKeystoneTranslation(
        direction: KeyStoneDirection,
        step: Long = KEYSTONE_DEFAULT_CLICK_STEP,
        closeAutoKeystone: Boolean = false,
    ) {
        val realStep = min(KEYSTONE_MAX_STEP, step)
        val vertexLT = getVertex(LEFT_TOP.systemPropKey)
        val vertexRT = getVertex(RIGHT_TOP.systemPropKey)
        val vertexLB = getVertex(LEFT_BOTTOM.systemPropKey)
        val vertexRB = getVertex(RIGHT_BOTTOM.systemPropKey)

        val vertexList = listOf(vertexLT, vertexRT, vertexLB, vertexRB)

        // 设置新值
        when (direction) {
            KeyStoneDirection.UP -> vertexList.forEach { it.y += realStep }
            KeyStoneDirection.DOWN -> vertexList.forEach { it.y -= realStep }
            KeyStoneDirection.LEFT -> vertexList.forEach { it.x -= realStep }
            KeyStoneDirection.RIGHT -> vertexList.forEach { it.x += realStep }
        }

        // 调整顶点
        adjustVertex(LEFT_TOP, vertexLT)
        adjustVertex(RIGHT_TOP, vertexRT)
        adjustVertex(LEFT_BOTTOM, vertexLB)
        adjustVertex(RIGHT_BOTTOM, vertexRB)

        // 更新梯形矫正
        SystemProperties.set(LEFT_TOP.systemPropKey, vertexLT.toString())
        SystemProperties.set(RIGHT_TOP.systemPropKey, vertexRT.toString())
        SystemProperties.set(LEFT_BOTTOM.systemPropKey, vertexLB.toString())
        SystemProperties.set(RIGHT_BOTTOM.systemPropKey, vertexRB.toString())

        SystemProperties.set(KEYSTONE_PROP_UPDATE, "1")

        if (closeAutoKeystone) {
            runBlocking {
                logTagD(TAG, "关闭自动梯形校正")
                setAutoKeystoneStatus(false)
            }
        }
    }

    fun canAdjust(point: KeyStonePoint, direction: KeyStoneDirection): Boolean {
        return when (point) {
            LEFT_TOP -> when (direction) {
                KeyStoneDirection.UP -> getVertex(point.systemPropKey).y < 0
                KeyStoneDirection.LEFT -> getVertex(point.systemPropKey).x > 0
                else -> true
            }

            RIGHT_TOP -> when (direction) {
                KeyStoneDirection.UP -> getVertex(point.systemPropKey).y < 0
                KeyStoneDirection.RIGHT -> getVertex(point.systemPropKey).x < 0
                else -> true
            }

            LEFT_BOTTOM -> when (direction) {
                KeyStoneDirection.DOWN -> getVertex(point.systemPropKey).y > 0
                KeyStoneDirection.LEFT -> getVertex(point.systemPropKey).x > 0
                else -> true
            }

            RIGHT_BOTTOM -> when (direction) {
                KeyStoneDirection.DOWN -> getVertex(point.systemPropKey).y > 0
                KeyStoneDirection.RIGHT -> getVertex(point.systemPropKey).x < 0
                else -> true
            }
        }
    }

    /**
     * 调整顶点信息
     */
    private fun adjustVertex(point: KeyStonePoint, vertex: Vertex) {
        logTagD(TAG, "调整前:${vertex},point:${point}")
        when (point) {
            LEFT_TOP -> {
                vertex.x = max(0, vertex.x)
                vertex.y = min(0, vertex.y)
            }

            RIGHT_TOP -> {
                vertex.x = min(0, vertex.x)
                vertex.y = min(0, vertex.y)
            }

            LEFT_BOTTOM -> {
                vertex.x = max(0, vertex.x)
                vertex.y = max(0, vertex.y)
            }

            RIGHT_BOTTOM -> {
                vertex.x = min(0, vertex.x)
                vertex.y = max(0, vertex.y)
            }
        }
        logTagD(TAG, "调整后:${vertex}")
    }

    /**
     * 获取顶点信息, 在调整的过程中, 值有可能发生变化,比如自动校正了
     * 所以每次用到Vertex都重新获取
     */
    private fun getVertex(propKey: String): Vertex {
        val value = SystemProperties.get(propKey, "0,0")
        return Vertex(value)
    }

    /**
     * 获取自动梯形矫正状态
     */
    suspend fun getAutoKeystoneStatus() = withContext(Dispatchers.IO) {
        val keystoneStatus = Settings.Global.getInt(
            CZURAtyManager.appContext.contentResolver,
            KEY_AUTO_KEYSTONE,
            AUTO_KEYSTONE_ON
        )
        if (Constants.starryHWInfo.series == Q2Series) {
            // Q2 需要设置给华研
            val prop = getSystemProp("persist.hysd.keystone.switch", -1)
            if (prop != keystoneStatus) {
                logTagW(TAG, "获取到的梯形状态和系统属性不一致, 设置华研的属性")
                setSystemProp("persist.hysd.keystone.switch", keystoneStatus)
            }
        }


        keystoneStatus == AUTO_KEYSTONE_ON
    }

    /**
     * 设置自动梯形矫正状态
     */
    suspend fun setAutoKeystoneStatus(autoKeystone: Boolean) = withContext(Dispatchers.IO) {
        val keystoneStatus = if (autoKeystone) AUTO_KEYSTONE_ON else AUTO_KEYSTONE_OFF
        Settings.Global.putInt(
            CZURAtyManager.appContext.contentResolver,
            KEY_AUTO_KEYSTONE, keystoneStatus
        )

        if (Constants.starryHWInfo.series == Q2Series) {
            logTagI(TAG, "Q2需要同步华研")
            setSystemProp("persist.hysd.keystone.switch", keystoneStatus)
        }
    }

}