<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/screenSizeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/screenSizeTitleTv"
            style="@style/TVContentTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pic_size"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/picSizeTv"
            style="@style/TVContentTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30px"
            android:includeFontPadding="false"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/picSizeHintSubscriptTv"
            app:layout_constraintTop_toBottomOf="@id/screenSizeTitleTv"
            tools:text="100" />

        <TextView
            android:id="@+id/picSizeHintSubscriptTv"
            style="@style/TVContentTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/suffix_percentage"
            app:layout_constraintBaseline_toBaselineOf="@id/picSizeTv"
            app:layout_constraintLeft_toRightOf="@id/picSizeTv"
            app:layout_constraintRight_toRightOf="parent" />

        <!--    画面尺寸进度条    -->
        <com.czur.uilib.seek.CZSeekBar
            android:id="@+id/picSizeSeekBar"
            android:layout_width="810px"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/picSizeTv" />

        <TextView
            android:id="@+id/screenSizeTipTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:textColor="@color/text_common_light"
            android:textSize="20px"
            android:text="@string/pic_size_tip"
            android:layout_marginTop="55px"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="@id/picSizeSeekBar"
            app:layout_constraintRight_toRightOf="@id/picSizeSeekBar"
            app:layout_constraintTop_toBottomOf="@id/picSizeSeekBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/displayModeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/displayModeTitleTv"
            style="@style/TVContentTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:text="@string/screen_tone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.czur.starry.device.settings.widget.SettingItemBg
            android:id="@+id/officeModeBg"
            android:layout_width="900px"
            android:layout_height="80px"
            android:layout_marginTop="30px"
            app:hoverWith="@id/officeModeTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/displayModeTitleTv"
            app:position="top" />

        <com.czur.starry.device.settings.widget.SettingItemBg
            android:id="@+id/viewingModeBg"
            android:layout_width="900px"
            android:layout_height="80px"
            app:hoverWith="@id/viewingModeTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/officeModeBg"
            app:position="bottom" />

        <TextView
            android:id="@+id/officeModeTv"
            android:layout_width="0px"
            android:layout_height="80px"
            android:gravity="center_vertical"
            android:paddingLeft="30px"
            android:text="@string/screen_tone_cool_shade"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/officeModeBg"
            app:layout_constraintLeft_toLeftOf="@id/officeModeBg"
            app:layout_constraintRight_toRightOf="@id/officeModeBg"
            app:layout_constraintTop_toTopOf="@id/officeModeBg" />

        <ImageView
            android:id="@+id/officeModeIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30px"
            android:src="@drawable/ic_setting_item_selected"
            app:layout_constraintBottom_toBottomOf="@id/officeModeTv"
            app:layout_constraintRight_toRightOf="@id/officeModeTv"
            app:layout_constraintTop_toTopOf="@id/officeModeTv" />

        <TextView
            android:id="@+id/viewingModeTv"
            android:layout_width="0px"
            android:layout_height="80px"
            android:gravity="center_vertical"
            android:paddingLeft="30px"
            android:text="@string/screen_tone_warm_shade"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/viewingModeBg"
            app:layout_constraintLeft_toLeftOf="@id/viewingModeBg"
            app:layout_constraintRight_toRightOf="@id/viewingModeBg" />

        <ImageView
            android:id="@+id/viewingModeIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30px"
            android:src="@drawable/ic_setting_item_selected"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/viewingModeTv"
            app:layout_constraintRight_toRightOf="@id/viewingModeTv"
            app:layout_constraintTop_toTopOf="@id/viewingModeTv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:orientation="vertical"
        app:constraint_referenced_ids="displayModeLayout,screenSizeLayout"
        app:flow_firstVerticalStyle="packed"
        app:flow_verticalGap="145px"
        app:layout_constraintBottom_toTopOf="@id/screenResetBtn"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/screenResetBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/common_reset_to_def"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>