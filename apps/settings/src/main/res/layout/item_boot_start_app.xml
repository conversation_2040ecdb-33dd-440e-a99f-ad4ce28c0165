<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="157px"
    tools:ignore="PxUsage"
    tools:viewBindingIgnore="true">

    <!--    -->
    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/appIconIV"
        android:layout_width="112px"
        android:layout_height="112px"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="8px" />

    <ImageView
        android:id="@+id/appIconHoverIv"
        android:layout_width="130px"
        android:layout_height="130px"
        android:src="@drawable/mark_hover_boot_start_app"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/appIconSelectIv"
        android:layout_width="112px"
        android:layout_height="112px"
        android:src="@drawable/mark_boot_start_app_click"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>