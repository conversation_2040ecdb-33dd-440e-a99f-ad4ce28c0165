<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <com.noober.background.view.BLScrollView
        android:layout_width="900px"
        android:layout_height="900px"
        android:clipToPadding="false"
        android:padding="40px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/setting_item_bg_normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="2px"
            android:textColor="@color/text_common"
            android:textSize="24px" />

    </com.noober.background.view.BLScrollView>

    <com.czur.uilib.btn.CZBackBtn
        android:id="@+id/backBtn"
        android:layout_width="90px"
        android:layout_height="60px"
        android:layout_margin="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>