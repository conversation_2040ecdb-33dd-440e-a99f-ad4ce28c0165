<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants">


    <include
        android:id="@+id/selectId"
        layout="@layout/item_wallpaper_select_button"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/imCustom"
        android:layout_width="396px"
        android:layout_height="223px"
        android:layout_margin="1px"
        android:background="@color/base_bg_color"
        android:scaleType="fitCenter"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="10px" />

    <ImageView
        android:id="@+id/imShader"
        android:layout_width="398px"
        android:layout_height="225px"
        android:src="@drawable/icon_wallpaper_shader"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvName"
        android:layout_width="400px"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:background="@android:color/transparent"
        android:gravity="center"
        android:text=""
        android:textSize="24px"
        app:layout_constraintLeft_toLeftOf="@id/imCustom"
        app:layout_constraintTop_toBottomOf="@+id/imCustom" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="70px"
        android:layout_height="70px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>