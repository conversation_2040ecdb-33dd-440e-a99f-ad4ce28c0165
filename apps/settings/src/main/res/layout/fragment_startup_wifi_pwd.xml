<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_blue">

    <TextView
        android:id="@+id/startUpSSIDTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="TP-LINK"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/startUpPwdEt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <EditText
        android:id="@+id/startUpPwdEt"
        android:layout_width="630px"
        android:layout_height="80px"
        android:layout_marginTop="36px"
        android:cursorVisible="true"
        android:gravity="center"
        android:inputType="textPassword"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:bl_corners_radius="20px"
        app:bl_solid_color="@color/dialog_editText_bg_color"
        android:imeOptions="actionDone"
        android:nextFocusDown="@id/startUpPwdEt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/startUpSSIDTv" />

    <com.czur.starry.device.baselib.widget.EyeView
        android:id="@+id/startUpShowPwdIv"
        android:layout_width="40px"
        android:layout_height="24px"
        app:layout_constraintLeft_toRightOf="@id/startUpPwdEt"
        app:layout_constraintTop_toTopOf="@id/startUpPwdEt"
        app:layout_constraintBottom_toBottomOf="@id/startUpPwdEt"
        android:layout_marginLeft="39px"/>
</androidx.constraintlayout.widget.ConstraintLayout>