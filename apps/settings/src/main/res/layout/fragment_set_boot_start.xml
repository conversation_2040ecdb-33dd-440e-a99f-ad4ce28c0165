<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/bootStartActionBg"
        android:layout_width="1080px"
        android:layout_height="80px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toTopOf="@id/bootStartAppSheetRv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/bootStartActionTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="32px"
        android:text="@string/str_boot_start_switch"
        app:layout_constraintBottom_toBottomOf="@id/bootStartActionBg"
        app:layout_constraintLeft_toLeftOf="@id/bootStartActionBg"
        app:layout_constraintTop_toTopOf="@id/bootStartActionBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/bootStartEnableSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        android:layout_marginLeft="25px"
        app:layout_constraintBottom_toBottomOf="@id/bootStartActionBg"
        app:layout_constraintLeft_toRightOf="@id/bootStartActionTv"
        app:layout_constraintTop_toTopOf="@id/bootStartActionBg" />

    <TextView
        android:id="@+id/bootStartAppNameTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="30px"
        app:layout_constraintBottom_toBottomOf="@id/bootStartActionBg"
        app:layout_constraintRight_toRightOf="@id/bootStartActionBg"
        app:layout_constraintTop_toTopOf="@id/bootStartActionBg" />

    <ImageView
        android:id="@+id/bootStartAppIconIv"
        android:layout_width="64px"
        android:layout_height="64px"
        android:layout_marginRight="25px"
        app:layout_constraintBottom_toBottomOf="@id/bootStartActionBg"
        app:layout_constraintRight_toLeftOf="@id/bootStartAppNameTv"
        app:layout_constraintTop_toTopOf="@id/bootStartActionBg" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/bootStartAppGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="bootStartAppIconIv,bootStartAppNameTv" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/bootStartAppSheetRv"
        android:layout_width="0px"
        android:layout_height="744px"
        android:layout_marginTop="55px"
        android:padding="55px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#F1F3FE"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/bootStartActionBg"
        app:layout_constraintRight_toRightOf="@id/bootStartActionBg"
        app:layout_constraintTop_toBottomOf="@id/bootStartActionBg"
        tools:background="#F1F3FE" />

    <View
        android:id="@+id/disableMarkView"
        android:layout_width="0px"
        android:layout_height="0px"
        android:alpha="0.9"
        android:clickable="true"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#F1F3FE"
        app:layout_constraintBottom_toBottomOf="@id/bootStartAppSheetRv"
        app:layout_constraintLeft_toLeftOf="@id/bootStartAppSheetRv"
        app:layout_constraintRight_toRightOf="@id/bootStartAppSheetRv"
        app:layout_constraintTop_toTopOf="@id/bootStartAppSheetRv"
        tools:background="#F1F3FE" />


</androidx.constraintlayout.widget.ConstraintLayout>