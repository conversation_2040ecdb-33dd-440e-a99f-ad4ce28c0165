<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription">

    <ImageView
        android:id="@+id/previewIv"
        android:background="@color/base_bg_color"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <ImageView
        android:id="@+id/backIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="43px"
        android:layout_marginTop="35px"
        android:src="@drawable/ic_backdrop_preview_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/backTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/back"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/backIv"
        app:layout_constraintLeft_toRightOf="@id/backIv"
        app:layout_constraintTop_toTopOf="@id/backIv" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/backClickView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="backIv,backTv" />

    <ImageView
        android:id="@+id/preIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginLeft="100px"
        android:src="@drawable/ic_backdrop_pre"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/nextIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginRight="100px"
        android:src="@drawable/ic_backdrop_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/deleteBtn"
        android:layout_width="200px"
        android:layout_height="80px"
        android:layout_marginRight="30px"
        android:text="@string/wallpaper_bt_delete"
        android:textSize="30px"
        app:baselib_theme="dark3"
        android:visibility="gone"
        app:layout_constraintRight_toLeftOf="@+id/useNowBtn"
        app:layout_constraintTop_toTopOf="@+id/useNowBtn" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/useNowBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginRight="80px"
        android:layout_marginBottom="60px"
        android:text="@string/btn_use_now"
        android:textSize="@dimen/use_now_btn_size"
        android:textStyle="bold"
        app:baselib_theme="white2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <FrameLayout
        android:id="@+id/loadingGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/float_out_color_dark" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_backdrop_change_loading"
            android:textColor="@color/white"
            android:textSize="36px"
            android:layout_gravity="center"
            android:textStyle="bold"/>
    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>