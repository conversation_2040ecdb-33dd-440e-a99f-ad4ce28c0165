<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage">

    <TextView
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/meeting_camera_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/meetingCameraFullIv"
        android:layout_width="500px"
        android:layout_height="360px"
        android:layout_marginTop="268px"
        android:scaleType="fitXY"
        android:src="@drawable/img_meeting_camera_full"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/meetingCameraSmartIv"
        app:layout_constraintTop_toTopOf="parent"
        app:round="10px" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/meetingCameraSmartIv"
        android:layout_width="500px"
        android:layout_height="360px"
        android:scaleType="fitXY"
        android:src="@drawable/img_meeting_camera_smart"
        app:layout_constraintLeft_toRightOf="@id/meetingCameraFullIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/meetingCameraFullIv"
        app:round="10px" />


    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/fullScreenCb"
        style="@style/CBNormalSize"
        android:layout_marginTop="40px"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/meetingCameraFullIv"
        app:layout_constraintRight_toLeftOf="@id/fullScreenTv"
        app:layout_constraintTop_toBottomOf="@id/meetingCameraFullIv"
        app:withClickIDs="fullScreenTv,meetingCameraFullIv" />

    <TextView
        android:id="@+id/fullScreenTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/meeting_camera_full_picture"
        app:layout_constraintBottom_toBottomOf="@id/fullScreenCb"
        app:layout_constraintLeft_toRightOf="@id/fullScreenCb"
        app:layout_constraintRight_toRightOf="@id/meetingCameraFullIv"
        app:layout_constraintTop_toTopOf="@id/fullScreenCb" />


    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/smartScreenCb"
        style="@style/CBNormalSize"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/meetingCameraSmartIv"
        app:layout_constraintRight_toLeftOf="@id/smartScreenTv"
        app:layout_constraintTop_toTopOf="@id/fullScreenCb"
        app:withClickIDs="smartScreenTv" />

    <TextView
        android:id="@+id/smartScreenTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/meeting_camera_smart_portrait"
        app:layout_constraintBottom_toBottomOf="@id/smartScreenCb"
        app:layout_constraintLeft_toRightOf="@id/smartScreenCb"
        app:layout_constraintRight_toRightOf="@id/meetingCameraSmartIv"
        app:layout_constraintTop_toTopOf="@id/smartScreenCb" />

    <!--点击图片后开启智能人像+追踪发言人-->
    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/autoTrackCb"
        style="@style/CBSubSize"
        android:layout_marginTop="50px"
        app:layout_constraintRight_toRightOf="@id/smartScreenCb"
        app:layout_constraintTop_toBottomOf="@id/smartScreenCb"
        app:withClickIDs="autoTrackTv" />

    <TextView
        android:id="@+id/autoTrackTv"
        style="@style/TVSettingItemContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/meeting_camera_automatic_track"
        app:layout_constraintBottom_toBottomOf="@id/autoTrackCb"
        app:layout_constraintLeft_toLeftOf="@id/smartScreenTv"
        app:layout_constraintTop_toTopOf="@id/autoTrackCb" />

    <TextView
        android:id="@+id/caNewVersionTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10px"
        android:text="@string/latest_version"
        android:textColor="#40434e"
        android:textSize="30px"
        app:layout_constraintBottom_toTopOf="@+id/currentVersionTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/currentVersionTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20px"
        android:text="@string/current_version"
        android:textColor="#B340434E"
        android:textSize="24px"
        app:layout_constraintBottom_toTopOf="@+id/updateBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/updateBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/update"
        android:textSize="30px"
        app:baselib_theme="blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/updateGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="caNewVersionTv,currentVersionTv,updateBtn" />

</androidx.constraintlayout.widget.ConstraintLayout>