<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded,RtlSymmetry">

    <TextView
        android:id="@+id/languageTitleTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/title_system_language"
        app:layout_constraintBottom_toTopOf="@id/languageRv"
        app:layout_constraintLeft_toLeftOf="@id/languageRv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/languageRv"
        android:layout_width="700px"
        android:layout_height="360px"
        android:layout_marginTop="20px"
        android:scrollbars="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/inputMethodTitleTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/languageTitleTv" />

    <TextView
        android:id="@+id/inputMethodTitleTv"
        style="@style/TVContentTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:text="@string/title_input_method"
        app:layout_constraintBottom_toTopOf="@id/gBoardBgView"
        app:layout_constraintLeft_toLeftOf="@id/languageTitleTv"
        app:layout_constraintTop_toBottomOf="@id/languageRv" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/gBoardBgView"
        android:layout_width="0px"
        android:layout_height="120px"
        android:layout_marginTop="20px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/languageRv"
        app:layout_constraintRight_toRightOf="@id/languageRv"
        app:layout_constraintTop_toBottomOf="@id/inputMethodTitleTv" />

    <ImageView
        android:id="@+id/gBoardIcon"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="30px"
        android:src="@drawable/ic_gboard"
        app:layout_constraintBottom_toBottomOf="@id/gBoardBgView"
        app:layout_constraintLeft_toLeftOf="@id/gBoardBgView"
        app:layout_constraintTop_toTopOf="@id/gBoardBgView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20px"
        android:includeFontPadding="false"
        android:text="@string/str_gboard_name"
        style="@style/TVContentTitle"
        app:layout_constraintBottom_toBottomOf="@id/gBoardBgView"
        app:layout_constraintLeft_toRightOf="@id/gBoardIcon"
        app:layout_constraintTop_toTopOf="@id/gBoardBgView" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_setting_item_next_small"
        app:layout_constraintBottom_toBottomOf="@id/gBoardBgView"
        app:layout_constraintRight_toRightOf="@id/gBoardBgView"
        app:layout_constraintTop_toTopOf="@id/gBoardBgView" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/applyBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/apply"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>