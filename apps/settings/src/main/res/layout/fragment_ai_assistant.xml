<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/bgItem"
        android:layout_width="900px"
        android:layout_height="80px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:position="single" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/label_ai_assistant_voice_wakeup"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgItem"
        app:layout_constraintLeft_toLeftOf="@id/bgItem"
        app:layout_constraintTop_toTopOf="@id/bgItem" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/voiceWakeupSwitch"
        android:layout_width="95px"
        android:layout_height="45px"
        android:layout_marginRight="30px"
        app:layout_constraintBottom_toBottomOf="@id/bgItem"
        app:layout_constraintRight_toRightOf="@id/bgItem"
        app:layout_constraintTop_toTopOf="@id/bgItem" />

    <TextView
        android:id="@+id/hintItemTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24px"
        android:alpha="0.7"
        android:textColor="@color/text_common"
        android:textSize="24px"
        app:layout_constraintLeft_toLeftOf="@id/bgItem"
        app:layout_constraintTop_toBottomOf="@id/bgItem" />


</androidx.constraintlayout.widget.ConstraintLayout>