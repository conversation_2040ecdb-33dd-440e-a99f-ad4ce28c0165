<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:paddingBottom="50px">


    <!--  使用静态加载的方式,显示WIFI开关  -->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/wifiEnableContent"
        android:name="com.czur.starry.device.settings.ui.net.wifi.WifiEnableFragment"
        android:layout_width="700px"
        android:layout_height="wrap_content"
        android:tag="WifiEnableFragment"
        app:layout_constraintBottom_toTopOf="@id/wifiListContent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--显示WIFI列表-->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/wifiListContent"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginTop="20px"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/wifiEnableContent"
        app:layout_constraintRight_toRightOf="@id/wifiEnableContent"
        app:layout_constraintTop_toBottomOf="@id/wifiEnableContent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/wifiDisableLayout"
        android:layout_width="0px"
        android:layout_height="0px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/wifiEnableContent"
        app:layout_constraintRight_toRightOf="@id/wifiEnableContent"
        app:layout_constraintTop_toBottomOf="@id/wifiEnableContent">

        <ImageView
            android:layout_width="160px"
            android:layout_height="112px"
            android:layout_marginBottom="20px"
            android:src="@drawable/img_wifi_disable"
            app:layout_constraintBottom_toTopOf="@+id/wifi_disable_tv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/wifi_disable_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="50px"
            android:text="@string/disable_wifi_switch"
            android:textColor="@color/text_common_light"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>