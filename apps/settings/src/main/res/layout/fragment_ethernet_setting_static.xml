<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/bg_main"
    tools:ignore="PxUsage">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/setIPItemBg"
        android:layout_width="350px"
        android:layout_height="80px"
        app:ignoreHover="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        style="@style/tv_ethernet_label"
        android:text="@string/ip_address_text"
        app:layout_constraintBottom_toBottomOf="@id/setIPItemBg"
        app:layout_constraintLeft_toLeftOf="@id/setIPItemBg"
        app:layout_constraintTop_toTopOf="@id/setIPItemBg" />

    <com.czur.starry.device.settings.widget.IPEditText
        android:id="@+id/ipValueTv"
        style="@style/tv_ethernet_static_ip"
        android:text="@string/ip_address_text"
        app:layout_constraintBottom_toBottomOf="@id/setIPItemBg"
        app:layout_constraintLeft_toRightOf="@id/setIPItemBg"
        app:layout_constraintTop_toTopOf="@id/setIPItemBg"
        tools:text="0.0.0.0" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/setSubnetMarkItemBg"
        android:layout_width="350px"
        android:layout_height="80px"
        android:layout_marginTop="30px"
        app:ignoreHover="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/setIPItemBg" />

    <TextView
        style="@style/tv_ethernet_label"
        android:text="@string/subnet_text"
        app:layout_constraintBottom_toBottomOf="@id/setSubnetMarkItemBg"
        app:layout_constraintLeft_toLeftOf="@id/setIPItemBg"
        app:layout_constraintTop_toTopOf="@id/setSubnetMarkItemBg" />

    <com.czur.starry.device.settings.widget.IPEditText
        android:id="@+id/subnetValueTv"
        style="@style/tv_ethernet_static_ip"
        android:text="@string/ip_address_text"
        app:layout_constraintBottom_toBottomOf="@id/setSubnetMarkItemBg"
        app:layout_constraintLeft_toRightOf="@id/setSubnetMarkItemBg"
        app:layout_constraintTop_toTopOf="@id/setSubnetMarkItemBg"
        tools:text="0.0.0.0" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/setGatewayItemBg"
        android:layout_width="350px"
        android:layout_height="80px"
        android:layout_marginTop="30px"
        app:ignoreHover="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/setSubnetMarkItemBg" />

    <TextView
        style="@style/tv_ethernet_label"
        android:text="@string/gateway_text"
        app:layout_constraintBottom_toBottomOf="@id/setGatewayItemBg"
        app:layout_constraintLeft_toLeftOf="@id/setGatewayItemBg"
        app:layout_constraintTop_toTopOf="@id/setGatewayItemBg" />

    <com.czur.starry.device.settings.widget.IPEditText
        android:id="@+id/gatewayValueTv"
        style="@style/tv_ethernet_static_ip"
        android:text="@string/ip_address_text"
        app:layout_constraintBottom_toBottomOf="@id/setGatewayItemBg"
        app:layout_constraintLeft_toRightOf="@id/setGatewayItemBg"
        app:layout_constraintTop_toTopOf="@id/setGatewayItemBg"
        tools:text="0.0.0.0" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/setDNSItemBg"
        android:layout_width="350px"
        android:layout_height="80px"
        android:layout_marginTop="30px"
        app:ignoreHover="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/setGatewayItemBg" />

    <TextView
        style="@style/tv_ethernet_label"
        android:text="@string/dns_text"
        app:layout_constraintBottom_toBottomOf="@id/setDNSItemBg"
        app:layout_constraintLeft_toLeftOf="@id/setDNSItemBg"
        app:layout_constraintTop_toTopOf="@id/setDNSItemBg" />

    <com.czur.starry.device.settings.widget.IPEditText
        android:id="@+id/dnsValueTv"
        style="@style/tv_ethernet_static_ip"
        android:text="@string/ip_address_text"
        app:layout_constraintBottom_toBottomOf="@id/setDNSItemBg"
        app:layout_constraintLeft_toRightOf="@id/setDNSItemBg"
        app:layout_constraintTop_toTopOf="@id/setDNSItemBg"
        tools:text="0.0.0.0" />

</androidx.constraintlayout.widget.ConstraintLayout>