<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80px">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:ignoreCorner="true" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="30px"
        android:paddingRight="30px"
        app:bl_focused_hovered="#0D000000"
        app:bl_unFocused_hovered="#00000000"
        tools:viewBindingIgnore="true">

        <ImageView
            android:id="@+id/otherBtIconIv"
            android:layout_width="35px"
            android:layout_height="35px"
            android:src="@drawable/ic_bt_tmp"
            app:tint="@color/bg_main_blue" />

        <TextView
            android:id="@+id/otherBTDeviceNameTv"
            style="@style/TVSettingItemTitle"
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_marginLeft="10px"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            tools:text="CZUR" />

        <ProgressBar
            android:id="@+id/boundingProgress"
            android:layout_width="30px"
            android:layout_height="30px"
            android:indeterminateTint="@color/bg_main_blue"
            android:visibility="gone" />
    </LinearLayout>

</FrameLayout>
