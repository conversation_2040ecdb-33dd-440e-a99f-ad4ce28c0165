<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/bg_main"
    tools:context=".ui.system.clean.CleanRoomFragment"
    tools:ignore="PxUsage,RtlHardcoded">

    <TextView
        android:id="@+id/memorySummaryInfoTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5px"
        android:layout_marginBottom="13px"
        android:ellipsize="end"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@id/memoryView"
        app:layout_constraintLeft_toLeftOf="@id/memoryView"
        tools:text="已使用 12.5G/32G   剩余 9.5G" />


    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/remindLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="remindTv,remindIv" />

    <ImageView
        android:id="@+id/remindIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="6px"
        android:src="@drawable/ic_clean_storage_remind_hide"
        app:layout_constraintTop_toTopOf="@+id/memorySummaryInfoTv"
        app:layout_constraintBottom_toBottomOf="@+id/memorySummaryInfoTv"
        app:layout_constraintRight_toRightOf="@id/memoryView" />

    <TextView
        android:id="@+id/remindTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20px"
        android:layout_marginRight="24px"
        android:text="@string/str_mem_clean_remind_title"
        android:textColor="#5879FC"
        android:textSize="24px"
        android:textStyle="bold"
        android:gravity="end"
        app:layout_constraintTop_toTopOf="@+id/memorySummaryInfoTv"
        app:layout_constraintBottom_toBottomOf="@+id/memorySummaryInfoTv"
        app:layout_constraintRight_toLeftOf="@+id/remindIv"
        app:layout_constraintLeft_toRightOf="@id/memorySummaryInfoTv" />



    <com.czur.starry.device.settings.widget.MemoryView
        android:id="@+id/memoryView"
        style="@style/memory_view_style"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="57px"
        android:orientation="horizontal"
        app:constraint_referenced_ids="systemFlow,programFlow,mediaFlow,picFlow,documentFlow,otherFlow"
        app:layout_constraintLeft_toLeftOf="@id/memoryView"
        app:layout_constraintRight_toRightOf="@id/memoryView"
        app:layout_constraintTop_toBottomOf="@id/memoryView" />

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/systemFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:constraint_referenced_ids="systemCircleView,systemLabelTv"
        app:flow_horizontalGap="10px" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/systemCircleView"
        android:layout_width="30px"
        android:layout_height="30px"
        app:circleColor="@color/memory_view_system" />

    <TextView
        android:id="@+id/systemLabelTv"
        style="@style/tv_memory_summary_item"
        android:text="@string/memory_system_txt"
        app:layout_constraintBottom_toBottomOf="@id/systemCircleView"
        app:layout_constraintLeft_toRightOf="@id/systemCircleView"
        app:layout_constraintTop_toTopOf="@id/systemCircleView" />

    <TextView
        android:id="@+id/systemSizeTv"
        style="@style/tv_memory_summary_item"
        android:layout_marginTop="4px"
        app:layout_constraintLeft_toLeftOf="@id/systemLabelTv"
        app:layout_constraintTop_toBottomOf="@id/systemLabelTv" />

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/programFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:constraint_referenced_ids="programCircleView,programLabelTv"
        app:flow_horizontalGap="10px" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/programCircleView"
        android:layout_width="30px"
        android:layout_height="30px"
        app:circleColor="@color/memory_view_program" />

    <TextView
        android:id="@+id/programLabelTv"
        style="@style/tv_memory_summary_item"
        android:text="@string/memory_progress_txt"
        app:layout_constraintBottom_toBottomOf="@id/programCircleView"
        app:layout_constraintLeft_toRightOf="@id/programCircleView"
        app:layout_constraintTop_toTopOf="@id/programCircleView" />

    <TextView
        android:id="@+id/programSizeTv"
        style="@style/tv_memory_summary_item"
        android:layout_marginTop="4px"
        app:layout_constraintLeft_toLeftOf="@id/programLabelTv"
        app:layout_constraintTop_toBottomOf="@id/programLabelTv"
        tools:text="0GB" />

    <!--  媒体  -->
    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/mediaFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:constraint_referenced_ids="mediaCircleView,mediaLabelTv"
        app:flow_horizontalGap="10px" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/mediaCircleView"
        android:layout_width="30px"
        android:layout_height="30px"
        app:circleColor="@color/memory_view_media" />

    <TextView
        android:id="@+id/mediaLabelTv"
        style="@style/tv_memory_summary_item"
        android:text="@string/memory_media_txt"
        app:layout_constraintBottom_toBottomOf="@id/mediaCircleView"
        app:layout_constraintTop_toTopOf="@id/mediaCircleView" />

    <TextView
        android:id="@+id/mediaSizeTv"
        style="@style/tv_memory_summary_item"
        android:layout_marginTop="4px"
        app:layout_constraintLeft_toLeftOf="@id/mediaLabelTv"
        app:layout_constraintTop_toBottomOf="@id/mediaLabelTv"
        tools:text="0GB" />

    <!--  图片  -->
    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/picFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:constraint_referenced_ids="picCircleView,picLabelTv"
        app:flow_horizontalGap="10px" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/picCircleView"
        android:layout_width="30px"
        android:layout_height="30px"
        app:circleColor="@color/memory_view_pic" />

    <TextView
        android:id="@+id/picLabelTv"
        style="@style/tv_memory_summary_item"
        android:text="@string/memory_picture_txt"
        app:layout_constraintBottom_toBottomOf="@id/picCircleView"
        app:layout_constraintTop_toTopOf="@id/picCircleView" />

    <TextView
        android:id="@+id/picSizeTv"
        style="@style/tv_memory_summary_item"
        android:layout_marginTop="4px"
        app:layout_constraintLeft_toLeftOf="@id/picLabelTv"
        app:layout_constraintTop_toBottomOf="@id/picLabelTv"
        tools:text="0GB" />

    <!--  文档  -->
    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/documentFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:constraint_referenced_ids="documentCircleView,documentLabelTv"
        app:flow_horizontalGap="10px" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/documentCircleView"
        android:layout_width="30px"
        android:layout_height="30px"
        app:circleColor="@color/memory_view_document" />

    <TextView
        android:id="@+id/documentLabelTv"
        style="@style/tv_memory_summary_item"
        android:text="@string/memory_document_txt"
        app:layout_constraintBottom_toBottomOf="@id/documentCircleView"
        app:layout_constraintTop_toTopOf="@id/documentCircleView" />

    <TextView
        android:id="@+id/documentSizeTv"
        style="@style/tv_memory_summary_item"
        android:layout_marginTop="4px"
        app:layout_constraintLeft_toLeftOf="@id/documentLabelTv"
        app:layout_constraintTop_toBottomOf="@id/documentLabelTv"
        tools:text="0GB" />

    <!--  其他  -->
    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/otherFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:constraint_referenced_ids="otherCircleView,otherLabelTv"
        app:flow_horizontalGap="10px" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/otherCircleView"
        android:layout_width="30px"
        android:layout_height="30px"
        app:circleColor="@color/memory_view_other" />

    <TextView
        android:id="@+id/otherLabelTv"
        style="@style/tv_memory_summary_item"
        android:text="@string/memory_other_txt"
        app:layout_constraintBottom_toBottomOf="@id/otherCircleView"
        app:layout_constraintTop_toTopOf="@id/otherCircleView" />

    <TextView
        android:id="@+id/otherSizeTv"
        style="@style/tv_memory_summary_item"
        android:layout_marginTop="4px"
        app:layout_constraintLeft_toLeftOf="@id/otherLabelTv"
        app:layout_constraintTop_toBottomOf="@id/otherLabelTv"
        tools:text="0GB" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/expandview"
        style="@style/memory_expand_view"
        android:scrollbars="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/systemSizeTv" />

    <!--    <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--        android:id="@+id/memory_group"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="120px"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        app:layout_constraintVertical_chainStyle="packed">-->

    <!--        -->

    <!--        <androidx.constraintlayout.widget.ConstraintLayout-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginTop="20px"-->
    <!--            app:layout_constraintLeft_toLeftOf="parent"-->
    <!--            app:layout_constraintRight_toRightOf="parent"-->
    <!--            app:layout_constraintTop_toBottomOf="@+id/expandview">-->

    <!--            <include-->
    <!--                android:id="@+id/memoryRemindGroup"-->
    <!--                layout="@layout/fragment_memory_remaind" />-->
    <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->

    <!--    </androidx.constraintlayout.widget.ConstraintLayout>-->


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/memoryInfoGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone">

        <ImageView
            android:id="@+id/memoryInfoIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_memory_info"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/memoryInfoSumTv"
            style="@style/memory_info_sum_tv_style"
            android:layout_marginLeft="36px"
            android:textColor="@color/text_common"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@id/memoryInfoIv"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="已发现 2.33GB 垃圾" />

        <TextView
            android:id="@+id/memoryInfoSelectedTv"
            style="@style/TVSettingItemContentBlack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1px"
            app:layout_constraintLeft_toLeftOf="@id/memoryInfoSumTv"
            app:layout_constraintTop_toBottomOf="@id/memoryInfoSumTv"
            tools:text="已选则 356MB" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.czur.uilib.btn.CZButton
        android:id="@+id/cleanBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/memory_clean_bt"
        app:colorStyle="blueWhite" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50px"
        android:orientation="horizontal"
        app:constraint_referenced_ids="memoryInfoGroup,cleanBtn"
        app:flow_horizontalStyle="spread_inside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/memoryView"
        app:layout_constraintRight_toRightOf="@id/memoryView" />


</androidx.constraintlayout.widget.ConstraintLayout>