<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/keystone_manual_bg"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.uilib.btn.CZButton
        android:id="@+id/backBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/back"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/pointBgIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_keystone_center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--  四个角的点  -->
    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/pointLeftTop"
        style="@style/keystone_point"
        android:layout_marginLeft="120px"
        android:layout_marginTop="120px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/pointRightTop"
        style="@style/keystone_point"
        android:layout_marginRight="120px"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/pointLeftTop" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/pointLeftBottom"
        style="@style/keystone_point"
        android:layout_marginBottom="120px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/pointLeftTop" />

    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/pointRightBottom"
        style="@style/keystone_point"
        app:layout_constraintLeft_toLeftOf="@id/pointRightTop"
        app:layout_constraintTop_toTopOf="@id/pointLeftBottom" />


    <com.czur.starry.device.baselib.widget.CircleView
        android:id="@+id/pointCenter"
        style="@style/keystone_point"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--  20个引导线  -->
    <View
        android:id="@+id/lineLeftTop2Top"
        style="@style/keystone_line_vertical"
        app:layout_constraintBottom_toTopOf="@id/pointLeftTop"
        app:layout_constraintLeft_toLeftOf="@id/pointLeftTop"
        app:layout_constraintRight_toRightOf="@id/pointLeftTop" />

    <View
        android:id="@+id/lineLeftTop2Bottom"
        style="@style/keystone_line_vertical"
        app:layout_constraintLeft_toLeftOf="@id/pointLeftTop"
        app:layout_constraintRight_toRightOf="@id/pointLeftTop"
        app:layout_constraintTop_toBottomOf="@id/pointLeftTop" />

    <View
        android:id="@+id/lineLeftTop2Left"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointLeftTop"
        app:layout_constraintRight_toLeftOf="@id/pointLeftTop"
        app:layout_constraintTop_toTopOf="@id/pointLeftTop" />

    <View
        android:id="@+id/lineLeftTop2Right"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointLeftTop"
        app:layout_constraintLeft_toRightOf="@id/pointLeftTop"
        app:layout_constraintTop_toTopOf="@id/pointLeftTop" />


    <!--  第二组  -->
    <View
        android:id="@+id/lineRightTop2Top"
        style="@style/keystone_line_vertical"
        app:layout_constraintBottom_toTopOf="@id/pointRightTop"
        app:layout_constraintLeft_toLeftOf="@id/pointRightTop"
        app:layout_constraintRight_toRightOf="@id/pointRightTop" />

    <View
        android:id="@+id/lineRightTop2Bottom"
        style="@style/keystone_line_vertical"
        app:layout_constraintLeft_toLeftOf="@id/pointRightTop"
        app:layout_constraintRight_toRightOf="@id/pointRightTop"
        app:layout_constraintTop_toBottomOf="@id/pointRightTop" />

    <View
        android:id="@+id/lineRightTop2Left"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointRightTop"
        app:layout_constraintRight_toLeftOf="@id/pointRightTop"
        app:layout_constraintTop_toTopOf="@id/pointRightTop" />

    <View
        android:id="@+id/lineRightTop2Right"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointRightTop"
        app:layout_constraintLeft_toRightOf="@id/pointRightTop"
        app:layout_constraintTop_toTopOf="@id/pointRightTop" />

    <!--  第三组  -->

    <View
        android:id="@+id/lineLeftBottom2Top"
        style="@style/keystone_line_vertical"
        app:layout_constraintBottom_toTopOf="@id/pointLeftBottom"
        app:layout_constraintLeft_toLeftOf="@id/pointLeftBottom"
        app:layout_constraintRight_toRightOf="@id/pointLeftBottom" />

    <View
        android:id="@+id/lineLeftBottom2Bottom"
        style="@style/keystone_line_vertical"
        app:layout_constraintLeft_toLeftOf="@id/pointLeftBottom"
        app:layout_constraintRight_toRightOf="@id/pointLeftBottom"
        app:layout_constraintTop_toBottomOf="@id/pointLeftBottom" />

    <View
        android:id="@+id/lineLeftBottom2Left"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointLeftBottom"
        app:layout_constraintRight_toLeftOf="@id/pointLeftBottom"
        app:layout_constraintTop_toTopOf="@id/pointLeftBottom" />

    <View
        android:id="@+id/lineLeftBottom2Right"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointLeftBottom"
        app:layout_constraintLeft_toRightOf="@id/pointLeftBottom"
        app:layout_constraintTop_toTopOf="@id/pointLeftBottom" />

    <!--  第四组  -->
    <View
        android:id="@+id/lineRightBottom2Top"
        style="@style/keystone_line_vertical"
        app:layout_constraintBottom_toTopOf="@id/pointRightBottom"
        app:layout_constraintLeft_toLeftOf="@id/pointRightBottom"
        app:layout_constraintRight_toRightOf="@id/pointRightBottom" />

    <View
        android:id="@+id/lineRightBottom2Bottom"
        style="@style/keystone_line_vertical"
        app:layout_constraintLeft_toLeftOf="@id/pointRightBottom"
        app:layout_constraintRight_toRightOf="@id/pointRightBottom"
        app:layout_constraintTop_toBottomOf="@id/pointRightBottom" />

    <View
        android:id="@+id/lineRightBottom2Left"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointRightBottom"
        app:layout_constraintRight_toLeftOf="@id/pointRightBottom"
        app:layout_constraintTop_toTopOf="@id/pointRightBottom" />

    <View
        android:id="@+id/lineRightBottom2Right"
        style="@style/keystone_line_horizontal"
        app:layout_constraintBottom_toBottomOf="@id/pointRightBottom"
        app:layout_constraintLeft_toRightOf="@id/pointRightBottom"
        app:layout_constraintTop_toTopOf="@id/pointRightBottom" />

    <!--  中间  -->

    <View
        android:id="@+id/lineCenter2Top"
        style="@style/keystone_line_vertical"
        android:layout_height="110px"
        app:layout_constraintBottom_toTopOf="@id/pointCenter"
        app:layout_constraintLeft_toLeftOf="@id/pointCenter"
        app:layout_constraintRight_toRightOf="@id/pointCenter" />

    <View
        android:id="@+id/lineCenter2Bottom"
        style="@style/keystone_line_vertical"
        android:layout_height="110px"
        app:layout_constraintLeft_toLeftOf="@id/pointCenter"
        app:layout_constraintRight_toRightOf="@id/pointCenter"
        app:layout_constraintTop_toBottomOf="@id/pointCenter" />

    <View
        android:id="@+id/lineCenter2Left"
        style="@style/keystone_line_horizontal"
        android:layout_width="110px"
        app:layout_constraintBottom_toBottomOf="@id/pointCenter"
        app:layout_constraintRight_toLeftOf="@id/pointCenter"
        app:layout_constraintTop_toTopOf="@id/pointCenter" />

    <View
        android:id="@+id/lineCenter2Right"
        style="@style/keystone_line_horizontal"
        android:layout_width="110px"
        app:layout_constraintBottom_toBottomOf="@id/pointCenter"
        app:layout_constraintLeft_toRightOf="@id/pointCenter"
        app:layout_constraintTop_toTopOf="@id/pointCenter" />

    <!--  8个方向按钮  -->

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftTop2TopIv"
        style="@style/keystone_arrow_iv"
        app:layout_constraintBottom_toTopOf="@id/lineLeftTop2Top"
        app:layout_constraintLeft_toLeftOf="@id/lineLeftTop2Top"
        app:layout_constraintRight_toRightOf="@id/lineLeftTop2Top" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftTop2BottomIv"
        style="@style/keystone_arrow_iv"
        android:rotation="180"
        app:layout_constraintLeft_toLeftOf="@id/lineLeftTop2Bottom"
        app:layout_constraintRight_toRightOf="@id/lineLeftTop2Bottom"
        app:layout_constraintTop_toBottomOf="@id/lineLeftTop2Bottom" />


    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftTop2LeftIv"
        style="@style/keystone_arrow_iv"
        android:rotation="-90"
        app:layout_constraintBottom_toBottomOf="@id/lineLeftTop2Left"
        app:layout_constraintRight_toLeftOf="@id/lineLeftTop2Left"
        app:layout_constraintTop_toTopOf="@id/lineLeftTop2Left" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftTop2RightIv"
        style="@style/keystone_arrow_iv"
        android:rotation="90"
        app:layout_constraintBottom_toBottomOf="@id/lineLeftTop2Right"
        app:layout_constraintLeft_toRightOf="@id/lineLeftTop2Right"
        app:layout_constraintTop_toTopOf="@id/lineLeftTop2Right" />

    <!--  第二组  -->

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightTop2TopIv"
        style="@style/keystone_arrow_iv"
        app:layout_constraintBottom_toTopOf="@id/lineRightTop2Top"
        app:layout_constraintLeft_toLeftOf="@id/lineRightTop2Top"
        app:layout_constraintRight_toRightOf="@id/lineRightTop2Top" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightTop2BottomIv"
        style="@style/keystone_arrow_iv"
        android:rotation="180"
        app:layout_constraintLeft_toLeftOf="@id/lineRightTop2Bottom"
        app:layout_constraintRight_toRightOf="@id/lineRightTop2Bottom"
        app:layout_constraintTop_toBottomOf="@id/lineRightTop2Bottom" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightTop2LeftIv"
        style="@style/keystone_arrow_iv"
        android:rotation="-90"
        app:layout_constraintBottom_toBottomOf="@id/lineRightTop2Left"
        app:layout_constraintRight_toLeftOf="@id/lineRightTop2Left"
        app:layout_constraintTop_toTopOf="@id/lineRightTop2Left" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightTop2RightIv"
        style="@style/keystone_arrow_iv"
        android:rotation="90"
        app:layout_constraintBottom_toBottomOf="@id/lineRightTop2Right"
        app:layout_constraintLeft_toRightOf="@id/lineRightTop2Right"
        app:layout_constraintTop_toTopOf="@id/lineRightTop2Right" />


    <!--  第三组  -->

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftBottom2TopIv"
        style="@style/keystone_arrow_iv"
        app:layout_constraintBottom_toTopOf="@id/lineLeftBottom2Top"
        app:layout_constraintLeft_toLeftOf="@id/lineLeftBottom2Top"
        app:layout_constraintRight_toRightOf="@id/lineLeftBottom2Top" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftBottom2BottomIv"
        style="@style/keystone_arrow_iv"
        android:rotation="180"
        app:layout_constraintLeft_toLeftOf="@id/lineLeftBottom2Bottom"
        app:layout_constraintRight_toRightOf="@id/lineLeftBottom2Bottom"
        app:layout_constraintTop_toBottomOf="@id/lineLeftBottom2Bottom" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftBottom2LeftIv"
        style="@style/keystone_arrow_iv"
        android:rotation="-90"
        app:layout_constraintBottom_toBottomOf="@id/lineLeftBottom2Left"
        app:layout_constraintRight_toLeftOf="@id/lineLeftBottom2Left"
        app:layout_constraintTop_toTopOf="@id/lineLeftBottom2Left" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/leftBottom2RightIv"
        style="@style/keystone_arrow_iv"
        android:rotation="90"
        app:layout_constraintBottom_toBottomOf="@id/lineLeftBottom2Right"
        app:layout_constraintLeft_toRightOf="@id/lineLeftBottom2Right"
        app:layout_constraintTop_toTopOf="@id/lineLeftBottom2Right" />


    <!--  第四组  -->

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightBottom2TopIv"
        style="@style/keystone_arrow_iv"
        app:layout_constraintBottom_toTopOf="@id/lineRightBottom2Top"
        app:layout_constraintLeft_toLeftOf="@id/lineRightBottom2Top"
        app:layout_constraintRight_toRightOf="@id/lineRightBottom2Top" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightBottom2BottomIv"
        style="@style/keystone_arrow_iv"
        android:rotation="180"
        app:layout_constraintLeft_toLeftOf="@id/lineRightBottom2Bottom"
        app:layout_constraintRight_toRightOf="@id/lineRightBottom2Bottom"
        app:layout_constraintTop_toBottomOf="@id/lineRightBottom2Bottom" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightBottom2LeftIv"
        style="@style/keystone_arrow_iv"
        android:rotation="-90"
        app:layout_constraintBottom_toBottomOf="@id/lineRightBottom2Left"
        app:layout_constraintRight_toLeftOf="@id/lineRightBottom2Left"
        app:layout_constraintTop_toTopOf="@id/lineRightBottom2Left" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/rightBottom2RightIv"
        style="@style/keystone_arrow_iv"
        android:rotation="90"
        app:layout_constraintBottom_toBottomOf="@id/lineRightBottom2Right"
        app:layout_constraintLeft_toRightOf="@id/lineRightBottom2Right"
        app:layout_constraintTop_toTopOf="@id/lineRightBottom2Right" />

    <!--  中心  -->
    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/center2TopIv"
        style="@style/keystone_arrow_iv"
        app:layout_constraintBottom_toTopOf="@id/lineCenter2Top"
        app:layout_constraintLeft_toLeftOf="@id/lineCenter2Top"
        app:layout_constraintRight_toRightOf="@id/lineCenter2Top" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/center2BottomIv"
        style="@style/keystone_arrow_iv"
        android:rotation="180"
        app:layout_constraintLeft_toLeftOf="@id/lineCenter2Bottom"
        app:layout_constraintRight_toRightOf="@id/lineCenter2Bottom"
        app:layout_constraintTop_toBottomOf="@id/lineCenter2Bottom" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/center2LeftIv"
        style="@style/keystone_arrow_iv"
        android:rotation="-90"
        app:layout_constraintBottom_toBottomOf="@id/lineCenter2Left"
        app:layout_constraintRight_toLeftOf="@id/lineCenter2Left"
        app:layout_constraintTop_toTopOf="@id/lineCenter2Left" />

    <com.czur.starry.device.settings.widget.HoldImageView
        android:id="@+id/center2RightIv"
        style="@style/keystone_arrow_iv"
        android:rotation="90"
        app:layout_constraintBottom_toBottomOf="@id/lineCenter2Right"
        app:layout_constraintLeft_toRightOf="@id/lineCenter2Right"
        app:layout_constraintTop_toTopOf="@id/lineCenter2Right" />

    <!--  让线和按钮成组  -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLT2T"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftTop2TopIv,lineLeftTop2Top" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLT2B"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftTop2BottomIv,lineLeftTop2Bottom" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLT2L"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftTop2LeftIv,lineLeftTop2Left" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLT2R"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftTop2RightIv,lineLeftTop2Right" />

    <!--  第二组  -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRT2T"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightTop2TopIv,lineRightTop2Top" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRT2B"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightTop2BottomIv,lineRightTop2Bottom" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRT2L"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightTop2LeftIv,lineRightTop2Left" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRT2R"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightTop2RightIv,lineRightTop2Right" />

    <!--  第三组  -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLB2T"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftBottom2TopIv,lineLeftBottom2Top" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLB2B"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftBottom2BottomIv,lineLeftBottom2Bottom" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLB2L"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftBottom2LeftIv,lineLeftBottom2Left" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLB2R"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="leftBottom2RightIv,lineLeftBottom2Right" />

    <!--  第四组  -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRB2T"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightBottom2TopIv,lineRightBottom2Top" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRB2B"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightBottom2BottomIv,lineRightBottom2Bottom" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRB2L"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightBottom2LeftIv,lineRightBottom2Left" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupRB2R"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="rightBottom2RightIv,lineRightBottom2Right" />

    <!--  中间组  -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupC2T"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="center2TopIv,lineCenter2Top" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupC2B"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="center2BottomIv,lineCenter2Bottom" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupC2L"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="center2LeftIv,lineCenter2Left" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupC2R"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="center2RightIv,lineCenter2Right" />

    <TextView
        style="@style/title_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="160px"
        android:text="@string/keystone_center_hint"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pointCenter" />
</androidx.constraintlayout.widget.ConstraintLayout>