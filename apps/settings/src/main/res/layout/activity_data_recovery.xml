<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_data_recovery"
    tools:ignore="PxUsage">


    <com.czur.uilib.btn.CZButton
        android:id="@+id/skipDataRecoveryBtn"
        android:layout_width="496px"
        android:layout_height="96px"
        android:layout_marginBottom="225px"
        android:text="@string/btn_skip_data_recovery"
        android:textSize="36px"
        android:textStyle="bold"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <LinearLayout
        android:id="@+id/startDataRecoveryBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="100px"
        android:layout_marginBottom="82px"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginRight="10px"
            android:layout_weight="1"
            android:text="@string/btn_start_data_recovery"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="30px"
            android:layout_gravity="center_vertical"
            android:src="@drawable/skip_arrow_right" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>