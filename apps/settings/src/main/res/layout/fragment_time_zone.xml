<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/searchBgView"
        android:layout_width="700px"
        android:layout_height="80px"
        android:layout_marginTop="80px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/cz_et_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/cz_et_bg" />

    <ImageView
        android:id="@+id/searchIconIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_search"
        app:tint="#5879FC"
        app:layout_constraintBottom_toBottomOf="@id/searchBgView"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/searchBgView"
        app:layout_constraintRight_toLeftOf="@id/searchHintTv"
        app:layout_constraintTop_toTopOf="@id/searchBgView" />

    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/searchLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="searchIconIv,searchHintTv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/searchTimeZoneGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="searchEt,closeSearchIv" />

    <TextView
        android:id="@+id/searchHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:text="@string/hint_search_time_zone"
        android:textColor="@color/base_bg_color"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/searchBgView"
        app:layout_constraintLeft_toRightOf="@id/searchIconIv"
        app:layout_constraintRight_toRightOf="@id/searchBgView"
        app:layout_constraintTop_toTopOf="@id/searchBgView" />

    <EditText
        android:id="@+id/searchEt"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:background="@null"
        android:gravity="center"
        android:nextFocusDown="@id/searchEt"
        android:singleLine="true"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        android:textCursorDrawable="@drawable/drawable_timezones_search_cursor"
        app:layout_constraintBottom_toBottomOf="@id/searchBgView"
        app:layout_constraintLeft_toLeftOf="@id/searchBgView"
        app:layout_constraintRight_toLeftOf="@id/closeSearchIv"
        app:layout_constraintTop_toTopOf="@id/searchBgView" />

    <View
        android:id="@+id/searchEtMark"
        android:layout_width="0px"
        android:layout_height="0px"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="@id/searchBgView"
        app:layout_constraintLeft_toLeftOf="@id/searchBgView"
        app:layout_constraintRight_toRightOf="@id/searchBgView"
        app:layout_constraintTop_toTopOf="@id/searchBgView" />

    <ImageView
        android:id="@+id/closeSearchIv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginRight="15px"
        android:src="@drawable/ic_close_timezone_search"
        app:layout_constraintBottom_toBottomOf="@id/searchBgView"
        app:layout_constraintRight_toRightOf="@id/searchBgView"
        app:layout_constraintTop_toTopOf="@id/searchBgView" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/timeZoneRv"
        android:layout_width="700px"
        android:layout_height="720px"
        android:layout_marginTop="10px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/searchTimeZoneEmptyTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:text="@string/str_no_time_zone"
        android:textColor="#A5B3FB"
        android:textSize="60px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/timeZoneRv"
        app:layout_constraintLeft_toLeftOf="@id/timeZoneRv"
        app:layout_constraintRight_toRightOf="@id/timeZoneRv"
        app:layout_constraintTop_toTopOf="@id/timeZoneRv" />

    <ProgressBar
        android:id="@+id/timeZonePB"
        android:layout_width="100px"
        android:layout_height="100px"
        android:indeterminateTint="@color/bg_main_blue"
        app:layout_constraintBottom_toBottomOf="@id/timeZoneRv"
        app:layout_constraintLeft_toLeftOf="@id/timeZoneRv"
        app:layout_constraintRight_toRightOf="@id/timeZoneRv"
        app:layout_constraintTop_toTopOf="@id/timeZoneRv" />

    <com.czur.starry.device.baselib.widget.WordIndexView
        android:id="@+id/timeZoneIndexBar"
        android:layout_width="wrap_content"
        android:layout_height="0px"
        android:layout_marginRight="20px"
        app:baselib_bg_color="@color/setting_index_bar_bg"
        app:baselib_tv_color="@color/setting_index_bar_text"
        app:layout_constraintBottom_toBottomOf="@id/timeZoneRv"
        app:layout_constraintRight_toLeftOf="@id/timeZoneRv"
        app:layout_constraintTop_toTopOf="@id/timeZoneRv" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/applyBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/apply"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>