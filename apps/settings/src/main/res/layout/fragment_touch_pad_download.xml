<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded">

    <TextView
        android:id="@+id/downloadNumTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="00"
        android:textColor="@color/cz_progress_bar_progress"
        android:textSize="100px"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/downloadProgress"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/subscriptTv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/subscriptTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5px"
        android:text="@string/suffix_percentage"
        android:textColor="@color/cz_progress_bar_progress"
        android:textSize="24px"
        app:layout_constraintBaseline_toBaselineOf="@id/downloadNumTv"
        app:layout_constraintLeft_toRightOf="@id/downloadNumTv"
        app:layout_constraintRight_toRightOf="parent" />

    <com.czur.uilib.CZProgressBar
        android:id="@+id/downloadProgress"
        android:layout_width="820px"
        android:layout_height="6px"
        android:layout_marginTop="50px"
        app:czPbMax="100"
        app:czPbProgress="0"
        app:layout_constraintBottom_toTopOf="@id/downloadSizeTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/downloadNumTv" />

    <TextView
        android:id="@+id/downloadSizeTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintBottom_toTopOf="@id/downloadHintTv1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/downloadProgress"
        tools:text="150.32MB/860.11MB" />

    <TextView
        android:id="@+id/downloadHintTv1"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50px"
        android:text="@string/str_touchpad_info_downloading"
        app:layout_constraintBottom_toTopOf="@id/downloadHintTv2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/downloadSizeTv" />

    <TextView
        android:id="@+id/downloadHintTv2"
        style="@style/TVSettingItemContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:text="@string/str_touchpad_info_warning"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/downloadHintTv1" />


    <com.czur.uilib.btn.CZButton
        android:id="@+id/cancelBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/str_cancel"
        android:textSize="30px"
        app:colorStyle="PaleBlueBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>