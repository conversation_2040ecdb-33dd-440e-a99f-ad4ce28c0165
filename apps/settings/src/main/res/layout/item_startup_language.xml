<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80px"
    tools:ignore="RtlHardcoded">

    <TextView
        android:id="@+id/itemLanguageTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        tools:text="简体中文"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/itemLanguageIv"
        android:layout_width="24px"
        android:layout_height="24px"
        android:layout_marginLeft="9px"
        android:src="@drawable/icon_selected"
        android:layout_toRightOf="@id/itemLanguageTv"
        android:layout_centerVertical="true"
        android:visibility="gone"/>
</RelativeLayout>