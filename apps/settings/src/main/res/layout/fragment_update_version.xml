<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/view_update_not"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/versionTitleNoUp"
            style="@style/TVSettingItemTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="当前版本: v9.7.30-cv"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/versionSubTvNoUp"
            style="@style/TVSettingItemContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16px"
            android:text="@string/str_latest_version"
            app:layout_constraintTop_toBottomOf="@+id/versionTitleTv" />
    </LinearLayout>

    <include
        android:id="@+id/view_update"
        layout="@layout/item_update_version"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>