<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="wallpaper_style_sort_pop_layout">
        <item name="android:layout_width">400px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:paddingLeft">28px</item>
        <item name="android:paddingRight">15px</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="join_btn_size_style">
        <item name="android:layout_width">360px</item>
        <item name="android:layout_height">80px</item>
    </style>

    <style name="add_image_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">20px</item>
    </style>

    <style name="add_text_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">18px</item>
    </style>

    <style name="dialog_qrcode_select_titleTv_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">22px</item>
        <item name="android:layout_marginStart">10dp</item>
        <item name="android:layout_marginEnd">10dp</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">20px</item>
    </style>

    <style name="memory_info_sum_tv_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">28px</item>
    </style>
</resources>