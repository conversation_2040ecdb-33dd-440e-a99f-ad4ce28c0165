<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="SettingItemBg">
        <attr name="position" format="enum">
            <enum name="top" value="0" />
            <enum name="middle" value="1" />
            <enum name="bottom" value="2" />
            <enum name="single" value="3" />
        </attr>
        <attr name="hoverWith" format="reference" />
        <attr name="ignoreHover" format="boolean" />
        <attr name="ignoreCorner" format="boolean" />
        <attr name="selected" format="boolean" />
        <attr name="syncSelectColorIDs" format="string" />
    </declare-styleable>

    <declare-styleable name="CleanRoomRemindItem">
        <attr name="cleanRoomItemText" format="string" />
        <attr name="itemPosition" format="enum" >
            <enum name="top" value="0" />
            <enum name="middle" value="1" />
            <enum name="bottom" value="2" />
            <enum name="single" value="3" />
        </attr>
    </declare-styleable>
</resources>