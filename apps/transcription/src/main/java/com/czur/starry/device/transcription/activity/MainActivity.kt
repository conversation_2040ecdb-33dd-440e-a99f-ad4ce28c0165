package com.czur.starry.device.transcription.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE


/**
 * Created by 陈丰尧 on 2022/8/5
 */
class MainActivity : Activity() {
    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        val open = intent?.getBooleanExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, false)
        val intent = Intent(this, TransSettingWindowActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.putExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, open)
        startActivity(intent)
        finish()
    }

}