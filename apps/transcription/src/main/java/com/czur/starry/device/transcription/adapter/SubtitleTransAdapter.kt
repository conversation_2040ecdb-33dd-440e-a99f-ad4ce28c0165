package com.czur.starry.device.transcription.adapter

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.transcription.model.AsrResult
import com.czur.starry.device.transcription.R

/**
 * Created by 陈丰尧 on 2024/10/22
 */
class SubtitleTransAdapter : BaseDifferAdapter<AsrResult>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: AsrResult) {
//        holder.setText(itemData.asrResult, R.id.subtitleTv)
    }

    override fun areContentsTheSame(oldItem: AsrResult, newItem: AsrResult): Boolean {
        return oldItem.audioAsrResult == newItem.audioAsrResult
                && oldItem.audioTranslateResult == newItem.audioTranslateResult
    }

    override fun areItemsTheSame(oldItem: AsrResult, newItem: AsrResult): Boolean {
        return oldItem.msgId == newItem.msgId
    }



    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_subtitle_trans, parent)
    }
}