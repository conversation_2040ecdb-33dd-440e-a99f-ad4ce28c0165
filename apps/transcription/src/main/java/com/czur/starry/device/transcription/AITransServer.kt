package com.czur.starry.device.transcription

import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpBody
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpHeader
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPath
import com.czur.starry.device.baselib.network.core.MiaoHttpPost

interface AITransServer {

    // 重命名 更新纪要名称
    @MiaoHttpPost("/api/starry/minute/rename/{sessionId}")
    fun postRenameRecord(
        @MiaoHttpPath("sessionId") id: String,
        @MiaoHttpParam("name") name: String,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}