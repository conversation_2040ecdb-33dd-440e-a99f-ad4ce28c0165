package com.czur.starry.device.transcription.widget

import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.WindowManager
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.transcription.databinding.CommonFloatDoubleBtnBinding

class DoubleBtnDialog(context: Context) : Dialog(context) {
    private val TAG = "DoubleBtnDialog"

    private lateinit var binding: CommonFloatDoubleBtnBinding
    private var negativeClick: (() -> Unit)? = null
    private var positiveClick: (() -> Unit)? = null
    private var dialogContent: String = ""
    private var positiveBtnText: String = ""
    private var negativeBtnText: String = ""


    private fun initDialog() {
        binding = CommonFloatDoubleBtnBinding.inflate(layoutInflater)
        setContentView(binding.root)

        window?.apply {
            setBackgroundDrawableResource(android.R.color.transparent)
            attributes = attributes.apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
            }
        }

        binding.apply {
            doubleBtnFloatCancelBtn.setOnClickListener {
                negativeClick?.invoke()
            }
            doubleBtnFloatConfirmBtn.setOnClickListener {
                positiveClick?.invoke()
            }
            doubleBtnFloatContentTv.text = dialogContent
            if (negativeBtnText.isNotEmpty()) {
                doubleBtnFloatCancelBtn.text = negativeBtnText
            }
            if (positiveBtnText.isNotEmpty()) {
                doubleBtnFloatConfirmBtn.text = positiveBtnText
            }
        }
    }

    override fun onStart() {
        super.onStart()
        setDialogCenter()
    }

    private fun setDialogCenter(){
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            logTagV(TAG, "适配StudioSPlus")
            this.window?.apply {
                val params = attributes
                params?.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    class Builder(private val context: Context) {
        private var onNegativeClick: (() -> Unit)? = null
        private var onPositiveClick: (() -> Unit)? = null
        private var cancelable: Boolean = false
        private var content: String = ""
        private var positiveText: String = ""
        private var negativeText: String = ""
        fun setNegativeBtnText(text: String): Builder {
            this.negativeText = text
            return this
        }

        fun setPositiveBtnText(text: String): Builder {
            this.positiveText = text
            return this
        }

        fun setContent(string: String): Builder {
            content = string
            return this
        }

        fun setPositiveListener(listener: () -> Unit): Builder {
            this.onPositiveClick = listener
            return this
        }

        fun setNegativeListener(listener: () -> Unit): Builder {
            this.onNegativeClick = listener
            return this
        }


        fun build(): DoubleBtnDialog {
            return DoubleBtnDialog(context).apply {
                positiveClick = onPositiveClick
                negativeClick = onNegativeClick
                positiveBtnText = positiveText
                negativeBtnText = negativeText
                dialogContent = content
                this.setCancelable(true)
                initDialog() // 在所有属性设置完成后初始化
            }
        }
    }
}