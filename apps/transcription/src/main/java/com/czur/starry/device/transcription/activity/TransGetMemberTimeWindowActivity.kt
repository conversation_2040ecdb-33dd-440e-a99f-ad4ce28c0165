package com.czur.starry.device.transcription.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.transcription.databinding.DialogGetTimeBinding


class TransGetMemberTimeWindowActivity : CZViewBindingAty<DialogGetTimeBinding>() {
    companion object {
        const val TAG = "FloatingActivity"
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logTagD(
            TAG,
            "免费领取会员窗口onNewIntent savedFileName${
                intent.getStringExtra("savedFileName").orEmpty()
            }"
        )
        handlePreIntent(intent)
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        logIntent(preIntent, TAG)

//        savedFileDocumentPath = preIntent.getStringExtra("savedFileDocumentPath").orEmpty()

    }


    override fun DialogGetTimeBinding.initBindingViews() {
        logTagD(TAG, "打开AI互译字幕-免费领取会员-窗口")
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {   //对于Android 8.0及以上
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        } else {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }

        getTimeTv.setOnClickListener {
            val intent = Intent(this@TransGetMemberTimeWindowActivity, TransSettingWindowActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            finish()
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
    }

    override fun finish() {
        super.finish()
        logTagD(TAG, "免费领取会员窗口finish")
    }

    override fun onDestroy() {
        super.onDestroy()
        logTagD(TAG, "免费领取会员窗口onDestroy")
    }
}