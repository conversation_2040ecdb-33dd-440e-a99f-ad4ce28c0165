package com.czur.starry.device.transcription.util

import android.os.Environment
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.getTimeStr
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File


/**
 * Created by 宋清君 on 2025/3/10
 */
private const val TAG = "AITransUtil"

/**
 * 创建AI互译假文件，用于显示红点
 * @return 创建的文件名
 */
suspend fun createAITransFakeFile(
    targetFileName: String? = null,
    clearDir: Boolean = true
): String = withContext(Dispatchers.IO) {
    try {
        logTagI(TAG, "创建AI互译假文件: $targetFileName - clearDir: $clearDir")
        // 获取本地文件根目录
        val sdCardPath = Environment.getExternalStorageDirectory().path
        val localDir = File(sdCardPath, "aiTransFakeFile").apply {
            if (!exists()) {
                mkdirs()
            } else if (clearDir) {
                // 清空目录
                listFiles()?.forEach {
                    it.delete()
                }
            }
        }

        // 生成文件名
        val fileName = targetFileName ?: getTimeStr("yyyyMMddHHmm")

        // 创建文件
        val file = File(localDir, fileName)
        logTagD(TAG, "创建文件${file.path}")
        val res = file.createNewFile()
        file.writeText(file.name)


        logTagD(TAG, "创建AI互译假文件完成: $fileName - $res")
        return@withContext fileName
    } catch (e: Exception) {
        logTagW(TAG, "创建AI互译假文件失败", tr = e)
        return@withContext ""
    }
}