package com.czur.starry.device.transcription.service

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.PixelFormat
import android.view.Gravity
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.annotation.LayoutRes
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelLazy
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.viewmodel.CreationExtras
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.orNull
import com.czur.starry.device.transcription.activity.TransSettingWindowActivity.Companion.TAG
import com.noober.background.BackgroundLibrary
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

abstract class AlertWindowService : LifecycleService() {
    var narrowTimeDownTime = System.currentTimeMillis()

    @get:LayoutRes
    abstract val layoutId: Int

    // window 的宽高
    abstract val windowWidth: Int
    abstract val windowHeight: Int

    private var windowWidthRevise = -1 // 允许重新设置宽度
    private var windowHeightRevise = -1 // 允许重新设置高度

    val currentWindowWidth: Int
        get() {
            return if (windowWidthRevise == -1) {
                windowWidth
            } else {
                windowWidthRevise
            }
        }

    val currentWindowHeight: Int
        get() {
            return if (windowHeightRevise == -1) {
                windowHeight
            } else {
                windowHeightRevise
            }
        }
    open val xOffSet: Int = 0
    open val yOffset: Int = 0
    open val windowType: Int? = null
    open val customWindowFlag: Int? = null
    open val careKeyEvent: Boolean = false  // 是否关心按键

    open val draggable = false //是否可以拖拽
    open val autoAdsorption = true //是否自动吸附
    private var isAutoAnimPlaying = false   // 自动吸附动画是否正在播放

    open val keepScreenOn = true // 是否需要屏幕保持常亮

    protected var rootView: View? = null
        private set
    protected val isViewAttached: Boolean
        get() = rootView != null

    var wmParams: WindowManager.LayoutParams? = null

    open val draggableIds = mutableListOf<Int>()
    protected val windowManager by lazy {
        getSystemService(WINDOW_SERVICE) as WindowManager
    }

    /**
     * 是否要阻止显示
     */
    private var blockDisplay: Boolean = false

    // 用来存储ViewModel
    val viewModelStore: ViewModelStore by lazy {
        ViewModelStore()
    }
    val defaultViewModelProviderFactory: ViewModelProvider.Factory
            by lazy {
                ViewModelProvider.AndroidViewModelFactory(application)
            }

    /**
     * 在AlertWindowService中使用ViewModel
     */
    protected inline fun <reified VM : ViewModel> viewModels(
        noinline extrasProducer: (() -> CreationExtras)? = null,
        noinline factoryProducer: (() -> ViewModelProvider.Factory)? = null
    ): Lazy<VM> {
        val factoryPromise = factoryProducer ?: {
            defaultViewModelProviderFactory
        }

        return ViewModelLazy(
            VM::class,
            { viewModelStore },
            factoryPromise,
            { extrasProducer?.invoke() ?: CreationExtras.Empty }
        )
    }

    override fun onCreate() {
        super.onCreate()
        if (keepScreenOn) {
            val serviceName = this::class.java.name
            logTagV(TAG, "唤醒屏幕: $serviceName")
            CZPowerManager.wakeUpScreen(serviceName)
        }


        launch {
            initTask()
            blockDisplay = needBlockDisplay()
            if (!blockDisplay) {
                createWindowView()
                initData()
            } else {
                dismiss()
            }
        }
    }

    open suspend fun initTask() {}

    open fun needBlockDisplay(): Boolean {
        return false
    }

    open fun dismiss() {
        stopSelf()
    }

    fun refreshParams(widthNew: Int, heightNew: Int, xOffSet: Int, yOffset: Int) {
        if (rootView == null){
            logTagD(TAG,"refreshParams-悬浮窗还未创建")
            return
        }
        wmParams?.apply {
            width = widthNew
            height = heightNew
            x = xOffSet
            y = yOffset
            windowManager.updateViewLayout(rootView, this)
        }
        windowWidthRevise = widthNew
        windowHeightRevise = heightNew
    }

    private fun createWindowView() {
        wmParams = WindowManager.LayoutParams().apply {
            // 设置大小
            width = windowWidth
            height = windowHeight
            type = if (windowType != null) {
                windowType!!
            } else {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            }

            gravity = Gravity.START or Gravity.TOP
            if (!careKeyEvent) {
                flags =
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            }

            if (keepScreenOn) {
                // 保持屏幕常亮
                flags = flags or WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            }

            customWindowFlag?.let {
                flags = flags or it
            }

            format = PixelFormat.RGBA_8888
            x = xOffSet
            y = yOffset
        }

        // 这里如果实装悬浮显示的化, 似乎应该改成inject2方法
        val inflater = BackgroundLibrary.inject(this)
        rootView = inflater.inflate(layoutId, null).apply {
            // 将悬浮窗控件添加到WindowManager
            windowManager.addView(this, wmParams)
        }
        draggableIds.forEach {
            rootView?.findViewById<View>(it)?.setOnTouchListener(FloatingListener())
        }

        rootView?.apply {
            if (draggable && draggableIds.isEmpty()) {
                setOnTouchListener(FloatingListener())
            }

            if (careKeyEvent) {
                rootView.isFocusableInTouchMode = true
                rootView.requestFocus()
                rootView.setOnKeyListener { _, keyCode, event ->
                    <EMAIL>(keyCode, event)
                }
            }

            initViews()
        }
    }

    abstract fun View.initViews()

    open fun initData() {}

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (!blockDisplay) {
            onDataRefresh(intent)
        }
        return super.onStartCommand(intent, flags, startId)
    }

    open fun onDataRefresh(intent: Intent?) {}

    open fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean = false

    protected fun releaseRootView() {
        logTagD(TAG,"releaseRootView-释放悬浮窗")
        rootView?.let {
            windowManager.removeViewImmediate(it)
        }
        rootView = null
    }

    protected fun updateLocation(offsetX: Int, offsetY: Int) {
        wmParams?.apply {
            x = offsetX
            y = offsetY
            windowManager.updateViewLayout(rootView, this)
        }
    }

    override fun onDestroy() {
        rootView?.setOnTouchListener(null)
        releaseRootView()
        ::viewModelStore.orNull?.clear()
        super.onDestroy()
    }

    var viewClickListener: OnAlertViewClickListener? = null

    // view的点击回调(解决点击事件被拖动事件拦截的问题)
    fun setOnViewClickListener(listener: OnAlertViewClickListener) {
        viewClickListener = listener
    }

    interface OnAlertViewClickListener {
        fun onViewClick(view: View)
    }

    protected inner class ViewFinder<T : View>(
        private val viewID: Int
    ) : ReadOnlyProperty<Any, T> {
        private var cacheView: T? = null
        override fun getValue(thisRef: Any, property: KProperty<*>): T {
            return cacheView ?: (rootView!!.findViewById<T>(viewID)).also {
                cacheView = it
            }
        }
    }

    private inner class FloatingListener : View.OnTouchListener {
        //开始触控的坐标，移动时的坐标（相对于屏幕左上角的坐标）
        private var mTouchStartX: Int = 0
        private var mTouchStartY: Int = 0
        private var mTouchCurrentX: Int = 0
        private var mTouchCurrentY: Int = 0

        //开始时的坐标和结束时的坐标（相对于自身控件的坐标）
        private var mStartX: Int = 0
        private var mStartY: Int = 0

        private var isMoving = false

        @SuppressLint("ClickableViewAccessibility")
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            if (isAutoAnimPlaying) {
                // 正在移动动画的时候, 禁止拖动
                return true
            }
            // 检查是否是鼠标滚轮事件
            if ((event.source and android.view.InputDevice.SOURCE_MOUSE) != 0 &&
                event.getAxisValue(MotionEvent.AXIS_VSCROLL) != 0f) {
                return false
            }

            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    mTouchStartX = event.rawX.toInt()
                    mTouchStartY = event.rawY.toInt()
                    mStartX = event.x.toInt()
                    mStartY = event.y.toInt()
                    isMoving = false
                }

                MotionEvent.ACTION_MOVE -> {
                    isMoving = true
                    mTouchCurrentX = event.rawX.toInt()
                    mTouchCurrentY = event.rawY.toInt()
                    val xNew = mTouchCurrentX - mTouchStartX + wmParams?.x!!
                    val yNew = mTouchCurrentY - mTouchStartY + wmParams?.y!!

                    wmParams?.apply {

                        x = if (xNew < 10) {
                            10
                        } else
                            if (xNew + currentWindowWidth > getScreenWidth() - 10) {
                                getScreenWidth() - currentWindowWidth - 10
                            } else {
                                xNew
                            }
                        y = if (yNew < 10) {
                            10
                        } else
                            if (yNew + currentWindowHeight > getScreenHeight() - 10) {
                                getScreenHeight() - currentWindowHeight - 10
                            } else {

                                yNew
                            }

                        windowManager.updateViewLayout(rootView, this)
                    }
                    mTouchStartX = mTouchCurrentX
                    mTouchStartY = mTouchCurrentY
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (autoAdsorption) {
                        autoAdsorptionToEdge()
                    }

                    if (!isMoving) {
                        //如果是点击事件
                        viewClickListener?.onViewClick(v)
                    }
                }

                else -> {}
            }
            //如果是移动事件不触发OnClick事件，防止移动的时候一放手形成点击事件
            return true
        }

        /**
         * 把悬浮窗自动吸附到边缘
         */
        private fun autoAdsorptionToEdge() {
            wmParams?.apply {
                val left = x
                val top = y
                val right = getScreenWidth() - x - currentWindowWidth
                val bottom = getScreenHeight() - y - currentWindowHeight

                val edge = when {
                    left <= top && left <= right && left <= bottom -> Edge.LEFT
                    top <= left && top <= right && top <= bottom -> Edge.TOP
                    right <= left && right <= top && right <= bottom -> Edge.RIGHT
                    else -> Edge.BOTTOM
                }

                val anim = when (edge) {
                    Edge.LEFT -> ValueAnimator.ofInt(x, 10)
                    Edge.TOP -> ValueAnimator.ofInt(y, 10)
                    Edge.RIGHT -> ValueAnimator.ofInt(x, getScreenWidth() - currentWindowWidth - 10)
                    Edge.BOTTOM -> ValueAnimator.ofInt(
                        y,
                        getScreenHeight() - currentWindowHeight - 10
                    )
                }
                anim.duration = Constants.ANIM_DURATION_SHORT
                anim.interpolator = AccelerateDecelerateInterpolator()
                // 为动画添加完成回调
                anim.doOnStart {
                    isAutoAnimPlaying = true
                }
                anim.doOnEnd {
                    isAutoAnimPlaying = false
                }
                anim.addUpdateListener {
                    when (edge) {
                        Edge.LEFT -> x = it.animatedValue as Int
                        Edge.TOP -> y = it.animatedValue as Int
                        Edge.RIGHT -> x = it.animatedValue as Int
                        Edge.BOTTOM -> y = it.animatedValue as Int
                    }
                    windowManager.updateViewLayout(rootView, this)
                }
                anim.start()
            }
        }
    }

    private enum class Edge {
        LEFT, TOP, RIGHT, BOTTOM
    }

}