package com.czur.starry.device.transcription.activity

import android.audioai.AudioAiServiceCallback
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.core.content.edit
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_PERSONAL
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_PERSONAL_RECHARGE
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.NormDialog
import com.czur.starry.device.transcription.App
import com.czur.starry.device.transcription.Config.DEFAULT_SHOW_CONTENT
import com.czur.starry.device.transcription.Config.ERROR_FAILED_CODE_LIST
import com.czur.starry.device.transcription.Config.ERROR_INIT_FAILED_CODE_LIST
import com.czur.starry.device.transcription.Config.ERROR_USING_NO_TIME_LIST
import com.czur.starry.device.transcription.Config.GENERATE_MEETING_MINUTES
import com.czur.starry.device.transcription.Config.NET_CALLBACK_ERROR_CODE
import com.czur.starry.device.transcription.Config.PREFERENCE_NAME
import com.czur.starry.device.transcription.Config.SHOW_CONTENT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TEXT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TRANS
import com.czur.starry.device.transcription.Config.SOURCE_LANG
import com.czur.starry.device.transcription.Config.TARGET_LANG
import com.czur.starry.device.transcription.MainViewModel
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.adapter.LanguageListAdapter
import com.czur.starry.device.transcription.databinding.DialogTranscriptionSettingBinding
import com.czur.starry.device.transcription.manager.AudioAiManager
import com.czur.starry.device.transcription.model.AITransMemberInfo
import com.czur.starry.device.transcription.model.LanguageBean
import com.czur.starry.device.transcription.service.SubtitleTransAlertWindowService
import com.czur.starry.device.transcription.util.createAITransFakeFile
import com.czur.starry.device.transcription.widget.DoubleBtnDialog
import com.czur.starry.device.transcription.widget.GetGiftDialog
import com.wanglu.lib.BasePopup
import com.wanglu.lib.WPopParams
import com.wanglu.lib.WPopupDirection
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext


class TransSettingWindowActivity : CZViewBindingAty<DialogTranscriptionSettingBinding>() {
    companion object {
        const val TAG = "TransSettingWindowActivity"
    }

    private val mainViewModel: MainViewModel by viewModels()

    private var dataList = mutableListOf<LanguageBean>()
    private var needRefreshMember = false // 是否需要再onresume的时候刷新
    private var memberOverDialog: DoubleBtnDialog? = null
    private var selectLangsPopup: BasePopup? = null
    private var languageDataLoaded = false // 标记语言数据是否已加载

    private var preIsMemberStatus = true // 上一个会员状态,记录一下,因为如果是false->true需要重新初始化sdk

    private var giftDialog: GetGiftDialog? = null

    private var audioAIHasInit = false

    var showErrorInitDialog: NormDialog? = null

    private val sharedPreferences by lazy {
        getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE)
    }

    private val sourceLanguage by lazy {
        sharedPreferences.getString(SOURCE_LANG, "CN")
    }

    private val targetLanguage by lazy {
        sharedPreferences.getString(TARGET_LANG, "EN")
    }

    private val showContent by lazy {
        sharedPreferences.getString(SHOW_CONTENT, DEFAULT_SHOW_CONTENT)
    }

    private val generateMeetingMinutes by lazy {
        sharedPreferences.getBoolean(
            GENERATE_MEETING_MINUTES, true
        )
    }

    // 内部回调类
    private val mAudioAiServiceCallback: AudioAiServiceCallback = object : AudioAiServiceCallback {
        override fun onAudioAsrResult(result: String, roleres: String) {
        }

        override fun onAudioTranslateResult(result: String, roleres: String) {
        }

        override fun onAudioAiError(errorCode: Int, errorMsg: String) {
            when (errorCode) {
                -500, -501, -502 -> {
                    //   java层返回的,不处理,直接和算法服务层的错误码对接
                }

                10000004, 10000005 -> {
                    // 这两个是即将不要的初始化失败码,对应会员没有时长的情况
                    // 现在会员没有时长不报错,但是需要重新获取到会员以后,重新初始化(不用stop release,直接init)
                }

                in ERROR_INIT_FAILED_CODE_LIST -> {//初始化错误
                    showErrorInitDialog()
                }

                in ERROR_FAILED_CODE_LIST -> {// 使用中错误
                    showErrorInitDialog()
                }
            }
            logTagI("song", "onAudioAiError errorCode:$errorCode, errorMsg:$errorMsg")
        }
    }

    override fun initWindow() {
        super.initWindow()
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            logTagD(TAG, "适配StudioSPlus")
            window.attributes = window.attributes.apply {
                y = getTopControlBarHeight() / 2
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logTagD(
            TAG,
            "AI互译设置窗口onNewIntent savedFileName${
                intent.getStringExtra("savedFileName").orEmpty()
            }"
        )
        handleIntent(intent)
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        logIntent(preIntent, TAG)
        handleIntent(preIntent)
    }

    private fun handleIntent(intent: Intent) {
        launch {
            intent.getBooleanExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, false).let {
                if (it) {
                    if (UserHandler.isLogin) {
                        delay(ONE_SECOND * 2)
                        binding.startBtn.performClick()
                    }
                }
            }
        }
    }


    override fun DialogTranscriptionSettingBinding.initBindingViews() {
        logTagD(TAG, "打开AI互译字幕-设置-窗口")
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出            if (showContent == SHOW_CONTENT_TEXT) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {   //对于Android 8.0及以上
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        } else {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }

        if (!sourceLanguage.isNullOrEmpty() && !targetLanguage.isNullOrEmpty()) {
            binding.selectLanguageArrowMiddleIv.show()
            binding.selectLanguageSourceTv.text = sourceLanguage
            binding.selectLanguageTargetTv.text = targetLanguage
        } else {
            binding.selectLanguageArrowMiddleIv.gone()
            binding.selectLanguageSourceTv.text = ""
            binding.selectLanguageTargetTv.text = ""
        }

        showContent.let {
            subtitlesOnlyCB.isChecked = it == SHOW_CONTENT_TEXT
            translationComparisonCB.isChecked = it == SHOW_CONTENT_TRANS
        }
        //创建一个弹窗
        selectLanguageCl.setOnDebounceClickListener {
            if (!UserHandler.isLogin) {
                logTagW(TAG, "没有登录,忽略点击")
                return@setOnDebounceClickListener
            }

            if (selectLangsPopup?.isShowing() == true)
            {
                return@setOnDebounceClickListener
            }
            // 显示弹窗
            selectLangsPopup = showLanguagesPopup()
            selectLangsPopup?.showAtDirectionByViewAlignLeft(
                binding.selectLanguageCl,
                WPopupDirection.BOTTOM
            )
        }

        closeIv.setOnClickListener {
            finish()
        }

        generateMeetingMinutesCB.isChecked =
            generateMeetingMinutes == true

        // 是否生成会议纪要
        generateMeetingMinutesCB.onCheckChangeListener = {
            sharedPreferences.edit {
                putBoolean(GENERATE_MEETING_MINUTES, it)
                commit()
                apply()
            }
        }

        subtitlesOnlyCB.onCheckChangeListener = {
            translationComparisonCB.isChecked = !it
            sharedPreferences.edit {
                putString(SHOW_CONTENT, (if (it) SHOW_CONTENT_TEXT else SHOW_CONTENT_TRANS))
                commit()
                apply()
            }
        }

        translationComparisonCB.onCheckChangeListener = {
            subtitlesOnlyCB.isChecked = !it
            sharedPreferences.edit {
                putString(SHOW_CONTENT, (if (it) SHOW_CONTENT_TRANS else SHOW_CONTENT_TEXT))
                commit()
                apply()
            }
        }

        startBtn.setOnClickListener {
            // 网络没有连接
            if (netUtil.getNetInfo().netStatus == NetStatusUtil.NetStatus.NO_NET_WORK) {
                toast(R.string.str_translation_net_error)
                return@setOnClickListener
            }

            if (!UserHandler.isLogin) {
                // 按钮显示登录,点击以后跳转登录页面
                bootAppByAction(ACTION_BOOT_PERSONAL)
                return@setOnClickListener
            }

            mainViewModel.refreshMemberInfo()   // 每次点击的时候都主动刷新一次

            // 会员信息没有刷新完成,点击无效
            if (!mainViewModel.memberInformationHasBeenRefreshed) {
                toast(R.string.dialog_translation_is_sync)
                return@setOnClickListener
            }

//            // 领取会员
//            if (!mainViewModel.memberInfo.isGifted) {
//                showGiftDialog(mainViewModel.memberInfo)
//                return@setOnClickListener
//            }

            // 会员到期 提示充值
            if (!mainViewModel.memberInfo.hasMember) {
                if (memberOverDialog?.isShowing == true) {
                    return@setOnClickListener
                }
                memberOverDialog = DoubleBtnDialog.Builder(this@TransSettingWindowActivity)
                    .setContent(getString(R.string.str_translation_member_over))
                    .setNegativeBtnText(getString(R.string.str_translation_member_wait))
                    .setPositiveBtnText(getString(R.string.str_translation_member_give_u_money))
                    .setNegativeListener {
                        memberOverDialog?.dismiss()
                    }
                    .setPositiveListener {
                        // 跳转充值页面
                        bootAppByAction(ACTION_BOOT_PERSONAL_RECHARGE)
                        needRefreshMember = true
                        memberOverDialog?.dismiss()
                    }.build()

                memberOverDialog?.show()
                return@setOnClickListener
            }

            val recordIntent =
                Intent(
                    this@TransSettingWindowActivity,
                    SubtitleTransAlertWindowService::class.java
                )
            if (TransHandler.isTranslating) {
                TransHandler.isTranslating = false
                stopService(recordIntent)
            } else {
                launch {
                    createAITransFakeFile()
                }
                AudioAiManager.unregisterCallback(mAudioAiServiceCallback)
                // 向service传递数据
                App.instance?.startService(recordIntent)
                TransHandler.isTranslating = true
            }
            finish()
        }
    }

    private val netUtil: NetStatusUtil by lazy {
        NetStatusUtil(CZURAtyManager.appContext)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        netUtil.startWatching()
        TransHandler.showSubtitles = true
        TransHandler.stopTrans = false

        TransHandler.isTranslatingLive.observe(this) {
            if (it) {
                logTagI(TAG, "正在翻译,退出该页面")
                finish()
            }
        }

        //网络监听
        netUtil.internetStatusLive.observe(this) {
            logTagD(TAG, "网络状态:$it")
            if (it == InternetStatus.CONNECT) {
                mainViewModel.refreshMemberInfo()   // 联网后重新刷新一次
            }
        }

        // 登录状态监听
        UserHandler.isLoginLive.observe(this) {
            logTagI(TAG, "isLoginLive $it")
            // 停止翻译,并刷新UI
            if (it) {
                // 刷新用户信息
                mainViewModel.refreshMemberInfo()
            } else {
                mainViewModel.memberInformationHasBeenRefreshed = false
                if (audioAIHasInit) {
                    unRegistAIService()
                }
            }
            refreshUI()
        }

        repeatCollectOnResume(mainViewModel.memberInfoFlow) {
            if (netUtil.getNetInfo().netStatus != NetStatusUtil.NetStatus.NO_NET_WORK
                && UserHandler.isLogin
            ) {
                refreshAudioAiServiceManager()
            }

            if (!preIsMemberStatus && it.hasMember) {
                // 会员状态从false->true,需要重新初始化sdk
                audioAIHasInit = false
                refreshAudioAiServiceManager()
            }

            if (it.accountNo != "0") {// 只登录了并且请求没有错误的时候,才是记录真正的会员变化
                preIsMemberStatus = it.hasMember
            }

            if (!TransHandler.isTranslating) {
                // 只在没有翻译的时候显示礼物弹窗
                showGiftDialog(it)
            }
            refreshUI()
        }
    }

    private fun showGiftDialog(aiTransMemberInfo: AITransMemberInfo) {
        if (!aiTransMemberInfo.isGifted && aiTransMemberInfo.giftTime.isNotEmpty()) {
            if (giftDialog == null) {
                giftDialog = GetGiftDialog.Builder()
                    .setGiftTimeStr(aiTransMemberInfo.giftTime)
                    .setConfirmClickListener {
                        giftDialog?.dismiss()
                    }
                    .setDismissListener {
                        launch {
                            if (netUtil.getNetInfo().netStatus == NetStatusUtil.NetStatus.NO_NET_WORK) {
                                toast(R.string.str_translation_net_error)
                                return@launch
                            }
                            val giftResult = mainViewModel.getGift()
                            if (giftResult) {
                                toast(R.string.toast_share_ai_get_gift_success)
                                mainViewModel.refreshMemberInfo()
                            } else {
                                toast(R.string.toast_share_ai_get_gift_failed)
                            }
                            giftDialog == null
                        }
                    }
                    .build()
                giftDialog?.show()
            }
        }
    }

    private fun unRegistAIService() {
        audioAIHasInit = false
        AudioAiManager.unregisterCallback(mAudioAiServiceCallback)
        AudioAiManager.stopAsr(null)
        AudioAiManager.release()
    }

    private fun refreshAudioAiServiceManager() {
        if (audioAIHasInit) {
            return
        }
        // 清空数据列表，准备重新加载
        dataList.clear()
        languageDataLoaded = false
        audioAIHasInit = true

        launch(Dispatchers.IO) {
            AudioAiManager.registerCallback(mAudioAiServiceCallback)
            AudioAiManager.init()
            logTagI(TAG, "刷新语音翻译服务")

            delay(600)
            AudioAiManager.getAsrLangs()?.forEach {
                logTagD(TAG, "getAsrLangs--$it")
            }
            var hasSaveLanguage = false
            var count = 0
            var select = false
            AudioAiManager.getTranslateOrigLangs()?.forEach { sourceIt ->
                AudioAiManager.getTranslateTargetLangs()?.forEach { targetIt ->
                    if (sourceIt != targetIt) {
                        if (sourceLanguage == sourceIt && targetLanguage == targetIt) {
                            hasSaveLanguage = true
                            withContext(Dispatchers.Main) {
                                binding.selectLanguageArrowMiddleIv.show()
                                binding.selectLanguageSourceTv.text = sourceIt
                                binding.selectLanguageTargetTv.text = targetIt
                            }
                            select = true
                        } else if (sourceLanguage == "" && targetLanguage == "") {
                            sharedPreferences.edit {
                                putString(SOURCE_LANG, sourceIt)
                                putString(TARGET_LANG, targetIt)
                                commit()
                                apply()
                            }
                            withContext(Dispatchers.Main) {
                                binding.selectLanguageArrowMiddleIv.show()
                                binding.selectLanguageSourceTv.text = sourceIt
                                binding.selectLanguageTargetTv.text = targetIt
                            }

                            select = true
                        }

                        logTagD(
                            TAG,
                            "translateOrigLangs--$sourceIt" +
                                    "translateTargetLangs--$targetIt"
                        )
                        dataList.add(
                            LanguageBean(
                                count,
                                sourceLanguage = sourceIt,
                                targetLanguage = targetIt,
                                isSelected = select
                            )
                        )
                        select = false
                        count++
                    }
                }
            }
            dataList.sortWith { a, b ->
                val res = a.sourceLanguage.compareTo(b.sourceLanguage)
                if (res != 0) {
                    return@sortWith res
                }
                a.targetLanguage.compareTo(b.targetLanguage)
            }

            // 数据加载完成，更新标志
            languageDataLoaded = true

            // 如果当前正在显示语言选择弹窗，则更新其内容
            withContext(Dispatchers.Main) {
                selectLangsPopup?.let { popup ->
                    val languageRv =
                        popup.getContentView().findViewById<RecyclerView>(R.id.language_rv)
                    val loadingContainer = popup.getContentView()
                        .findViewById<android.view.View>(R.id.loading_container)

                    // 隐藏加载指示器，显示列表
                    loadingContainer.gone()
                    languageRv.show()

                    // 更新适配器数据
                    (languageRv.adapter as? LanguageListAdapter)?.setData(dataList)
                }
            }
        }
    }

    private fun refreshUI() {
        logTagD(TAG, "刷新UI")
        // 刷新开始,暂停按钮状态
        if (!UserHandler.isLogin) {
            binding.startBtn.text = getString(R.string.str_translation_login)
            binding.membershipExpiry.gone()
            binding.fiveMinStopTips.gone()
            return
        }

        if (TransHandler.isTranslating) {
            binding.startBtn.text = getString(R.string.str_translation_stop)
        } else {
            binding.startBtn.text = getString(R.string.str_translation_start)
        }

        if (!mainViewModel.memberInfo.hasMember) {
            binding.membershipExpiry.gone()
            return
        }

        if (UserHandler.isLogin) {
            binding.fiveMinStopTips.show()

            if (mainViewModel.memberInfo.accountNo != "0") {
                // mainViewModel.memberInfo.expireTime 给的时间超过当前时间,说明过期了
                if (mainViewModel.memberInfo.type == 1) {
                    if (mainViewModel.memberInfo.expireTime < System.currentTimeMillis()) {
                        binding.membershipExpiry.clearContentText()
                        mainViewModel.memberInfo.expireTime = 0L

                        if (mainViewModel.memberInfo.remaining > 0) {
                            mainViewModel.memberInfo.type == 2
                        }
                    }
                }

                when (mainViewModel.memberInfo.type) {
                    1 -> {
                        // 包月会员
                        val format =
                            getTimeStr("yyyy-MM-dd", mainViewModel.memberInfo.expireTime)
                        binding.membershipExpiry.text =
                            resources.getString(R.string.str_translation_member_time, format)
                    }

                    2 -> {
                        // 预付费会员剩余时长
                        val remainingMillis = mainViewModel.memberInfo.remaining

                        // 计算小时、分钟、秒
                        val hours = remainingMillis / ONE_HOUR
                        val minutes = (remainingMillis / ONE_MIN) % 60
                        val seconds = (remainingMillis / ONE_SECOND) % 60

                        // 格式化时间，添加前导零
                        val hoursStr = hours.toString().padStart(2, '0')
                        val minutesStr = minutes.toString().padStart(2, '0')
                        val secondsStr = seconds.toString().padStart(2, '0')

                        // 根据不同情况格式化时间
                        val remainingTimeText = if (hours > 0) {
                            // 如果有小时，显示 HH:MM:SS
                            "$hoursStr:$minutesStr:$secondsStr"
                        } else if (minutes > 0) {
                            // 如果没有小时但有分钟，显示 MM:SS
                            "$minutesStr:$secondsStr"
                        } else {
                            // 如果只有秒，显示 00:SS
                            "00:$secondsStr"
                        }

                        // 显示剩余时长
                        binding.membershipExpiry.text = resources.getString(
                            R.string.str_translation_remaining_duration_time,
                            remainingTimeText
                        )
                    }

                    else -> {
                        binding.membershipExpiry.clearContentText()
                    }
                }

                binding.membershipExpiry.show()
            } else {

            }
        }

    }

    private fun showErrorInitDialog() {
        if (showErrorInitDialog != null) {
            return
        }
        showErrorInitDialog = NormDialog.Builder()
            .setTitle(getString(R.string.str_alert_dialog_title))
            .setInfo(getString(R.string.dialog_translation_error_init))
            .setConfirmText(getString(R.string.dialog_translation_error_init_ok))
            .setButtonStyle(NormDialog.ButtonStyle.SINGLE_BUTTON)
            .setCancelable(false)
            .setConfirmClickListener {
                finish()
            }
            .build()
        showErrorInitDialog?.show()
    }

    private fun showLanguagesPopup(): BasePopup {

        val customPopup = BasePopup(
            WPopParams(
                R.layout.popup_select_language,
                this@TransSettingWindowActivity,
                false,
                cancelable = true,
                width = 222,
                height = 308
            )
        )
        val adapter = LanguageListAdapter()
        adapter.setContext(this@TransSettingWindowActivity)
        val languageRv: RecyclerView =
            customPopup.getContentView().findViewById(R.id.language_rv)
        val loadingContainer =
            customPopup.getContentView().findViewById<android.view.View>(R.id.loading_container)

        languageRv.layoutManager = LinearLayoutManager(this)
        languageRv.adapter = adapter

        // 如果数据已加载，显示列表，否则显示加载指示器
        if (languageDataLoaded && dataList.isNotEmpty()) {
            loadingContainer.gone()
            languageRv.show()
            adapter.setData(dataList)
        } else {
            // 显示加载指示器，隐藏列表
            loadingContainer.show()
            languageRv.gone()
        }

        languageRv.doOnItemClick { vh, _ ->
            val pos = vh.bindingAdapterPosition
            val itemData = adapter.getData(pos)
            dataList.forEach { it.isSelected = false }
            val index = dataList.indexOfFirst { it.id == itemData.id }
            if (index != -1) {
                dataList[index].isSelected = true
            }
            binding.selectLanguageArrowMiddleIv.show()
            binding.selectLanguageSourceTv.text = itemData.sourceLanguage
            binding.selectLanguageTargetTv.text = itemData.targetLanguage
            sharedPreferences.edit() { putString(SOURCE_LANG, itemData.sourceLanguage) }
            sharedPreferences.edit() { putString(TARGET_LANG, itemData.targetLanguage) }
            customPopup.dismiss()
            true
        }
        return customPopup
    }

    override fun onResume() {
        super.onResume()
        launch(Dispatchers.IO) {
            if (needRefreshMember) {
                mainViewModel.refreshMemberInfo()
                needRefreshMember = false
            }
        }
        logTagD(TAG, "AI互译设置窗口onResume")
    }

    override fun onStop() {
        super.onStop()
        selectLangsPopup?.dismiss()
        logTagD(TAG, "AI互译设置窗口onStop")
    }

    override fun finish() {
        super.finish()

        logTagD(TAG, "AI互译设置窗口finish")
    }

    override fun onDestroy() {
        if (!TransHandler.isTranslating) {
            unRegistAIService()
        }
        super.onDestroy()

        logTagD(TAG, "AI互译设置窗口onDestroy")
    }

    private fun bootAppByAction(action: String, block: Intent.() -> Unit = {}) {
        val intent = Intent(action)
        intent.block()
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        try {
            startActivity(intent)
        } catch (e: Exception) {
            logTagE(TAG, "启动登录页面失败", tr = e)
        }
    }
}