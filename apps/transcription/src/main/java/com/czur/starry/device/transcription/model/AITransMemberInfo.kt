package com.czur.starry.device.transcription.model

data class AITransMemberInfo(
    val accountNo: String,// 账号
    var expireTime: Long,//到期时间
    var remaining: Long,//剩余时长，毫秒
    val type: Int,//会员类型，1-包月会员，2-预付会员
    val isGifted: Boolean,//是否赠送过会员
    var errorCode: Int = 0,
    val giftTime: String = ""
) {

    val hasMember: Boolean
        get() {
            return remaining > 0 || expireTime > 0
        }

}