<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:padding="16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="912px"
        android:layout_height="642px"
        android:background="@drawable/shape_blue_r_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/titleTopTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="110px"
            android:text="@string/str_translation_gift_title"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 显示方式 -->
        <TextView
            android:id="@+id/titleSecondTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8px"
            android:text="@string/str_translation_gift_title2"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleTopTv" />

        <TextView
            android:id="@+id/giftThankTv"
            android:layout_width="737px"
            android:layout_height="wrap_content"
            android:layout_marginStart="135px"
            android:layout_marginTop="88px"
            android:layout_marginEnd="135px"
            android:gravity="left"
            android:text="@string/str_translation_gift_thank"
            android:textColor="@color/white"
            android:textSize="30px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleSecondTv" />

        <TextView
            android:id="@+id/giftDaysTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16px"
            android:text="@string/str_translation_gift_days"
            android:textColor="@color/white"
            android:textSize="36px"
            app:layout_constraintStart_toStartOf="@+id/giftThankTv"
            app:layout_constraintTop_toBottomOf="@id/giftThankTv" />


        <!-- 开始按钮 -->
        <com.czur.uilib.btn.CZButton
            android:id="@+id/getTimeTv"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginBottom="32px"
            android:gravity="center"
            android:text="@string/str_translation_gift_get"
            android:textColor="@color/white"
            android:textSize="20px"
            app:colorStyle="SkyBlueWhite"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>