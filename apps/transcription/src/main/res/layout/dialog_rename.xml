<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/dialogBgView"
        android:layout_width="500px"
        android:layout_height="360px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#5879FC" />

    <TextView
        android:id="@+id/doubleBtnFloatTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:text="@string/str_save_file"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/dialogBgView"
        app:layout_constraintRight_toRightOf="@id/dialogBgView"
        app:layout_constraintTop_toTopOf="@id/dialogBgView" />

    <EditText
        android:id="@+id/inputDialogEt"
        android:layout_width="440px"
        android:layout_height="60px"
        android:layout_gravity="center"
        android:background="@drawable/bg_edittext"
        android:focusableInTouchMode="true"
        android:hint="@string/str_rename_inputet"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:nextFocusDown="@id/inputDialogEt"
        android:paddingStart="12px"
        android:paddingEnd="12px"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textColorHint="@color/colorWhiteInputHint"
        android:textCursorDrawable="@drawable/dialog_input_cursor"
        android:textSelectHandle="@android:color/transparent"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/cancelBtn"
        app:layout_constraintLeft_toLeftOf="@id/dialogBgView"
        app:layout_constraintRight_toRightOf="@id/dialogBgView"
        app:layout_constraintTop_toBottomOf="@id/doubleBtnFloatTitleTv">

        <requestFocus />
    </EditText>

    <com.czur.uilib.btn.CZButton
        android:id="@+id/cancelBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="30px"
        android:text="@string/dialog_normal_cancel"
        android:textSize="20px"
        android:textStyle="bold"
        app:colorStyle="NegativeInBlue"
        app:layout_constraintBottom_toBottomOf="@id/dialogBgView"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="@id/dialogBgView"
        app:layout_constraintRight_toLeftOf="@id/confirmBtn" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/confirmBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginLeft="30px"
        android:text="@string/dialog_normal_confirm"
        android:textSize="20px"
        android:textStyle="bold"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintLeft_toRightOf="@+id/cancelBtn"
        app:layout_constraintRight_toRightOf="@id/dialogBgView"
        app:layout_constraintTop_toTopOf="@id/cancelBtn" />

</androidx.constraintlayout.widget.ConstraintLayout>