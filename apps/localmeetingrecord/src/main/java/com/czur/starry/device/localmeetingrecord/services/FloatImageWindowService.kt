package com.czur.starry.device.localmeetingrecord.services

import android.animation.ValueAnimator
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.os.Binder
import android.os.IBinder
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_HEIGHT_CAMERA
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_WIDTH
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_X
import com.czur.starry.device.localmeetingrecord.Config.SCREEN_FLOAT_Y
import com.czur.starry.device.localmeetingrecord.R
import com.google.android.renderscript.Toolkit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext


private const val TAG = "FloatImageWindowService"

class FloatImageWindowService : AlertWindowService() {
    override val layoutId: Int = R.layout.float_screen_shot_layout
    override val windowWidthParam: Int
        get() = getScreenWidth()
    override val windowHeightParam: Int
        get() = getScreenHeight()

    override val draggable: Boolean
        get() = false

    override val careKeyEvent: Boolean
        get() = false
    override val customWindowFlag: Int =
        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE

    private var targetX = 0
    private var targetY = 0

    private val image: ImageView by ViewFinder(R.id.bgiv)

    var finishedListener: ImageSetListener? = null
    var imageSetFinished = false

    private val binder = MyBinder()

    override fun View.initViews() {}

    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        init(intent)
        return binder
    }


    override fun unbindService(conn: ServiceConnection) {
        super.unbindService(conn)
    }

    /**
     * 创建动画
     * 动画的过程是从当前的大小和位置,到目标的大小和位置
     */
    private fun createAnimation(): ValueAnimator = ValueAnimator.ofFloat(0F, 1F).apply {
        setDuration(400L)
        interpolator = AccelerateDecelerateInterpolator()
        addUpdateListener {
            val animatedValue = it.animatedValue as Float
            // 大小
            val targetWidth = SCREEN_FLOAT_WIDTH
            val targetHeight = SCREEN_FLOAT_HEIGHT_CAMERA
            val startWidth = windowWidthParam
            val startHeight = windowHeightParam
            val width = (startWidth + (targetWidth - startWidth) * animatedValue).toInt()
            val height = (startHeight + (targetHeight - startHeight) * animatedValue).toInt()
            // 位置
            val x = (targetX * animatedValue).toInt()
            val y = (targetY * animatedValue).toInt()

            image.let {
                it.layoutParams.width = width
                it.layoutParams.height = height
                it.requestLayout()

                it.x = x.toFloat()
                it.y = y.toFloat()
            }
        }
    }

    fun init(intent: Intent) {
        logTagV(TAG, "FloatImageWindowService-init")

        // 设置图片
        targetX = intent.getIntExtra("offsetX", SCREEN_FLOAT_X)
        targetY = intent.getIntExtra("offsetY", SCREEN_FLOAT_Y)

        val imageByte = intent.getByteArrayExtra("imageByte") ?: run {
            logTagW(TAG, "没有传入图片数据")
            // 没有传入图片数据,直接返回,并通知页面继续走流程
            finishedListener?.imageSetFinishedListener()
            return
        }

        // 计算小窗的目标位置和动画
        if (targetX == -1 || targetY == -1) {
            // 如果没有传入位置,则使用默认位置
            targetX = SCREEN_FLOAT_X
            targetY = SCREEN_FLOAT_Y
        }
        // 放到顶部的时候,有时会出现负坐标
        targetY = targetY.coerceAtLeast(0)
        targetX = targetX.coerceAtLeast(0)

        launch {
            val bmp = getBlurBmp(imageByte)
            // view重绘时回调
            image.viewTreeObserver.addOnDrawListener {
                logTagV(TAG, "image!!.viewTreeObserver imageSetFinished=$imageSetFinished")
                if (!imageSetFinished) {
                    imageSetFinished = true
                    launch {
                        delay(400)
                        createAnimation().start()
                    }
                }
            }
            image.setImageBitmap(bmp)
            logTagV(TAG, "finishedListener?.imageSetFinishedListener()")
            finishedListener?.imageSetFinishedListener()
        }
    }

    /**
     * 生成模糊图片
     * @param imageByte 图片的byte数组
     * @return Bitmap 模糊后的图片, 对比原始数据还会做一次翻转
     */
    private suspend fun getBlurBmp(imageByte: ByteArray): Bitmap {
        return withContext(Dispatchers.IO) {
            val option = BitmapFactory.Options().apply {
                inSampleSize = 8
            }
            val bitmap = BitmapFactory.decodeByteArray(imageByte, 0, imageByte.size, option)

            // 创建水平翻转矩阵
            val matrix = Matrix()
            matrix.setScale(-1f, 1f)
            matrix.postTranslate(bitmap.width.toFloat(), 0f)
            // 对 Bitmap 进行水平翻转操作
            var resultBmp = Bitmap.createBitmap(
                bitmap,
                0,
                0,
                bitmap.width,
                bitmap.height,
                matrix,
                true
            )
            bitmap.recycle()
            // 模糊图片
            repeat(4) {
                resultBmp = Toolkit.blur(resultBmp, 10)
            }

            resultBmp
        }
    }

    inner class MyBinder : Binder() {
        fun setListener(listener: ImageSetListener) {
            finishedListener = listener
        }

        fun getImageSetStatus(): Boolean {
            return imageSetFinished
        }
    }


    interface ImageSetListener {
        fun imageSetFinishedListener()
    }
}