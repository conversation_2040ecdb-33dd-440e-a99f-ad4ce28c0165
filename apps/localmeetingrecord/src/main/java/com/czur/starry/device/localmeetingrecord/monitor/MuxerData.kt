package com.czur.starry.device.localmeetingrecord.monitor

import android.media.MediaCodec
import java.nio.ByteBuffer

/**
 * 封装需要传输的数据类型
 */
class MuxerData {
    @JvmField
    var trackIndex = 0

    @JvmField
    var byteBuf: ByteBuffer? = null

    @JvmField
    var bufferInfo: MediaCodec.BufferInfo? = null

    @JvmField
    var bufferIndex: Int = -999

    @JvmField
    var codec: MediaCodec? = null

    constructor()
    constructor(
        trackIndex: Int,
        byteBuf: ByteBuffer?,
        bufferInfo: MediaCodec.BufferInfo?,
        bufferIndex: Int,
        codec: MediaCodec? = null
    ) {
        this.trackIndex = trackIndex
        this.byteBuf = byteBuf
        this.bufferInfo = bufferInfo
        this.bufferIndex = bufferIndex
        this.codec = codec
    }
}