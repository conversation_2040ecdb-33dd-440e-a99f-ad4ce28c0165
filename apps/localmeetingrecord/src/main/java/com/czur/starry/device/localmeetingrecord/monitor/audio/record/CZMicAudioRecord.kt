package com.czur.starry.device.localmeetingrecord.monitor.audio.record

import android.annotation.SuppressLint
import android.media.AudioRecord
import android.media.MediaRecorder
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.localmeetingrecord.Config
import java.io.File
import kotlin.concurrent.thread

/**
 * Created by 陈丰尧 on 2024/10/23
 *
 * https://www.jianshu.com/p/12d491bf286f
 *
 * idle -> init -> start -> pause -> idle
 */

private const val TAG = "CZMicAudioRecord"

@SuppressLint("MissingPermission")
class CZMicAudioRecord {
    private val audioBufferSize: Int by lazy {
        AudioRecord.getMinBufferSize(
            Config.AUDIO_SAMPLE_RATE,
            Config.AUDIO_CHANNEL_CONFIG,
            Config.AUDIO_FORMAT
        )
    }


    private var audioRecorder: AudioRecord? = null
    private var audioCodec: CZMicAudioCodec? = null

    private var outputFile: File? = null

    private var hasInit = false
    private var recording = false
    var onRecordDataReceive: ((ByteArray) -> Unit)? = null
    private val ptsManager = CZMicAudioPtsManager()

    var maxAmplitude: Int = 0
        private set


    fun startRecord() {
        logTagV(TAG, "startRecord")
        maxAmplitude = 0
        recording = true
    }

    fun pauseRecord() {
        logTagV(TAG, "pauseRecord")
        maxAmplitude = 0
        recording = false
        ptsManager.pause()
    }

    fun resumeRecord() {
        logTagV(TAG, "resumeRecord")
        maxAmplitude = 0
        ptsManager.resume()
        recording = true
    }

    fun prepare(outputFile: File) {
        logTagV(TAG, "prepare outputFile: $outputFile")
        maxAmplitude = 0
        this.outputFile = outputFile
        audioRecorder = AudioRecord(
            MediaRecorder.AudioSource.MIC,  // 音频源 固定位MIC
            Config.AUDIO_SAMPLE_RATE,   // 采样率
            Config.AUDIO_CHANNEL_CONFIG,  // 声道数
            Config.AUDIO_FORMAT,  // 采样位数
            audioBufferSize
        )
        audioRecorder?.startRecording() // 直接开始采集数据, 只不过不写入文件
        hasInit = true
        val outputArray = ByteArray(audioBufferSize)
        audioCodec = CZMicAudioCodec(ptsManager).apply {
            prepare(outputFile)
        }
        thread {
            while (hasInit) {
                val readCode = audioRecorder?.read(outputArray, 0, audioBufferSize) ?: 0
                if (readCode > 0 && recording) {
                    // 将数据传递出去
                    val copyArray = outputArray.copyOf(readCode)
                    calculateMaxAmplitude(copyArray)
                    onRecordDataReceive?.invoke(copyArray)

                    // 传递到Codec进行编码
                    audioCodec?.putData(copyArray)
                } else {
                    maxAmplitude = 0
                }
            }
        }
    }

    /**
     * 计算最大振幅
     */
    private fun calculateMaxAmplitude(pcmData: ByteArray) {
        var max = 0
        for (i in pcmData.indices step 2) {
            val value = (pcmData[i].toInt() and 0xFF) or (pcmData[i + 1].toInt() shl 8)
            max = max.coerceAtLeast(value)
        }
        maxAmplitude = max
    }

    fun stopAndReleaseRecord() {
        logTagV(TAG, "stopAndReleaseRecord")
        hasInit = false
        recording = false
        audioRecorder?.release()
        audioRecorder = null
        outputFile = null
        audioCodec?.stop()
        audioCodec = null
        ptsManager.reset()
    }
}