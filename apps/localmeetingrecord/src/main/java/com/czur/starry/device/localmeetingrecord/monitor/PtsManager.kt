package com.czur.starry.device.localmeetingrecord.monitor

import android.media.MediaCodec
import com.czur.czurutils.log.logTagI

/**
 * Created by 陈丰尧 on 2023/7/13
 * 用于管理pts, 单位为微秒
 */
private const val TAG = "PtsManager"

class PtsManager {
    private var videoStartTime = 0L// 开始时间
    private var audioStartTime = 0L// 开始时间

    var isVideoRunning = false
    var isAudioRunning = false

    private var pauseTime = 0L // 暂停时间
    private var pauseDuration = 0L // 暂停总时长
    var isRunning = false   // 是否已经开始

    private var firstVideo = true
    private var firstAudio = true

    fun setVideoStartTime(videoStartTime: Long) {
        this.videoStartTime = videoStartTime
        isVideoRunning = true
        logTagI(TAG, "startVideoStartTime")
    }

    fun setAudioStartTime(audioStartTime: Long) {
        this.audioStartTime = audioStartTime
        isAudioRunning = true
        logTagI(TAG, "startAudioStartTime")
    }

    fun startNewRecord() {
        reset()
        isRunning = true
    }

    fun pause() {
        logTagI(TAG, "暂停时间轴")
        pauseTime = currentTimeMicroseconds()
    }

    fun resume() {
        logTagI(TAG, "恢复时间轴")
        pauseDuration += currentTimeMicroseconds() - pauseTime
        pauseTime = 0L
    }

    fun reset() {
        logTagI(TAG, "重置时间轴各个属性")
        videoStartTime = 0L
        audioStartTime = 0L
        pauseTime = 0L
        pauseDuration = 0L
        isRunning = false
    }

    /**
     * trackIndex 1视频 2音频
     */
    fun resetBufferInfoPts(buffer: MediaCodec.BufferInfo, trackIndex: Int): MediaCodec.BufferInfo {
        val pts = getCurrentPts(trackIndex)
        return MediaCodec.BufferInfo().apply {
            set(
                buffer.offset, buffer.size,
                pts, buffer.flags
            )
        }
    }

    fun getCurrentPts(trackIndex: Int): Long {
        return when {
            trackIndex == 1 && firstVideo -> {
                firstVideo = false
                0L
            }

            trackIndex == 2 && firstAudio -> {
                firstAudio = false
                0L
            }

            trackIndex == 1 -> {
                currentTimeMicroseconds() - videoStartTime - pauseDuration
            }

            trackIndex == 2 -> {
                currentTimeMicroseconds() - audioStartTime - pauseDuration
            }

            else -> {
                0L
            }
        }
    }

    /**
     * 获取当前时间，单位为微秒
     */
    private fun currentTimeMicroseconds(): Long = System.nanoTime() / 1000L
}