package com.czur.starry.device.localmeetingrecord.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver

/**
 * Created by 陈丰尧 on 2023/7/7
 */
private const val TAG = "BroadcastManager"

private const val CZUR_SWITCH_LANGUAGE =
    "com.android.action.CZUR_SWITCH_LANGUAGE" /// 切换系统语言唤醒广播

fun addMainBroadcastReceiver(
    activity: CZBaseAty,
    onSwitchLanguage: () -> Unit,
    onPowerOff: () -> Unit,
) {
    val mainReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            when (intent.action ?: "") {
                CZUR_SWITCH_LANGUAGE ->
                    onSwitchLanguage()

                Intent.ACTION_REBOOT,
                Intent.ACTION_SHUTDOWN -> onPowerOff()
            }
        }
    }

    activity.lifecycle.addObserver(object : AutoRemoveLifecycleObserver {
        private var isRegistered = false
        override fun onCreate(owner: LifecycleOwner) {
            super.onCreate(owner)
            logTagI(TAG, "addMainBroadcastReceiver")
            val intentFilter = IntentFilter().apply {
                addAction(Intent.ACTION_REBOOT)     // Bug20855
                addAction(Intent.ACTION_SHUTDOWN)

                addAction(CZUR_SWITCH_LANGUAGE)     // Feature21992
            }
            activity.registerReceiver(mainReceiver, intentFilter)
            isRegistered = true
        }

        override fun onDestroy(owner: LifecycleOwner) {
            super.onDestroy(owner)
            logTagI(TAG, "removeMainBroadcastReceiver")
            if (isRegistered) {
                // 防止还没注册就被销毁
                activity.unregisterReceiver(mainReceiver)
            } else {
                logTagW(TAG, "removeMainBroadcastReceiver: isRegistered = false")
            }
        }
    })
}