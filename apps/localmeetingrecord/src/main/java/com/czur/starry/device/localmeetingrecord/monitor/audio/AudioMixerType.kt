package com.czur.starry.device.localmeetingrecord.monitor.audio

/**
 * Created by 陈丰尧 on 2023/7/18
 * 混音类型
 */
sealed class AudioMixerType {
    abstract val channelCount: Int  // 声道数
    abstract fun mixAudioData(micData: ByteArray?, submixData: ByteArray?): ByteArray?
}

/**
 * 多声道混音
 * 一个音源在左声道, 一个音源在右声道
 */
object MultiChannelMixer : AudioMixerType() {
    override val channelCount: Int
        get() = 2

    /**
     * 将两个音源一个在左声道混合, 一个在右声道混合
     * 如果只有一个音源, 则会将这个数据复制一份,
     */
    override fun mixAudioData(micData: ByteArray?, submixData: ByteArray?): ByteArray? {
        if (micData == null && submixData == null) {
            // 左右声道的数据全部为空
            return null
        }

        val pcmDataLeft = micData ?: submixData!!
        val pcmDataRight = submixData ?: micData!!
        val pcmData = ByteArray(pcmDataLeft.size + pcmDataRight.size)
        // 16Bit的数据, 每一个音频数据占用2个字节
        for (i in pcmDataLeft.indices step 2) {
            // 交错存储，形成双声道数据
            pcmData[i * 2] = pcmDataLeft[i]
            pcmData[i * 2 + 1] = pcmDataLeft[i + 1]
            pcmData[i * 2 + 2] = pcmDataRight[i]
            pcmData[i * 2 + 3] = pcmDataRight[i + 1]
        }
        return pcmData
    }
}