package com.czur.starry.device.localmeetingrecord.services

import android.content.Intent
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.localmeetingrecord.Config.TIME_FLOAT_HEIGHT
import com.czur.starry.device.localmeetingrecord.Config.TIME_FLOAT_WIDTH
import com.czur.starry.device.localmeetingrecord.Config.TIME_FLOAT_X
import com.czur.starry.device.localmeetingrecord.Config.TIME_FLOAT_Y
import com.czur.starry.device.localmeetingrecord.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class RecordFloatTimeWindowService : AlertWindowService() {
    override val layoutId: Int = R.layout.float_time_layou
    override val windowType: Int = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
    override val windowWidthParam: Int
        get() = TIME_FLOAT_WIDTH
    override val windowHeightParam: Int
        get() = TIME_FLOAT_HEIGHT
    override val xOffSet: Int
        get() = TIME_FLOAT_X
    override val yOffset: Int
        get() = TIME_FLOAT_Y + getTopControlBarHeight()
    override val draggable: Boolean
        get() = true
    private val recordTimeTv by ViewFinder<TextView>(R.id.float_time)

    private var job: Job? = null
    override fun View.initViews() {
        rootView.isClickable = false //设置为不可点击
        rootView.isFocusable = false //设置为不可获取焦点
        rootView.isFocusableInTouchMode = false //设置为不可在触摸模式下获取焦点
        val sdf = SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.CHINA)
        job = launch(Dispatchers.Main) {
            while (isActive) {
                val date = Date()
                val currentDateAndTime = sdf.format(date)
                recordTimeTv.text = currentDateAndTime
                delay(300)
            }
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_NOT_STICKY // 不需要被kill后重建Service
    }

    override fun onDestroy() {
        super.onDestroy()
        job?.cancel()
    }
}