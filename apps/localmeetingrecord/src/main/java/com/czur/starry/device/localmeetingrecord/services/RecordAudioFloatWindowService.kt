package com.czur.starry.device.localmeetingrecord.services

import android.app.ActivityManager
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.widget.CircleView
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_HEIGHT
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_WIDTH
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_X
import com.czur.starry.device.localmeetingrecord.Config.AUDIO_FLOAT_Y
import com.czur.starry.device.localmeetingrecord.MainActivity
import com.czur.starry.device.localmeetingrecord.MainActivity.Companion
import com.czur.starry.device.localmeetingrecord.R
import com.czur.starry.device.localmeetingrecord.RecordState
import com.czur.starry.device.localmeetingrecord.widget.RecordView
import com.czur.starry.device.hdmilib.HDMIIFStatus
import com.czur.starry.device.hdmilib.HDMIMonitor

private const val TAG = "RecordAudioFloatWindowService"
class RecordAudioFloatWindowService() : MeetingRecordAlertWindowService() {
    override var layoutId: Int = R.layout.float_record_audio_layout
    override var windowWidth: Int = AUDIO_FLOAT_WIDTH
    override var windowHeight: Int = AUDIO_FLOAT_HEIGHT

    override var channel_id = TAG
    override var channel_id_num = 11

    override val draggable: Boolean
        get() = true

    private lateinit var recordTimeTv: TextView
    private lateinit var fullScreenIv: ImageView
    private lateinit var controlIv: ImageView
    private lateinit var stopIv: RecordView
    private lateinit var redPointSimple: CircleView

    var floatWindowAudioServiceListener: FloatWindowAudioServiceListener? = null

    private val mBinder: IBinder = RecordBinder()

    // HDMI监听器，用于检测HDMI插入状态
    private val hdmiMonitor = HDMIMonitor()

    var activityManager: ActivityManager? = null

    var recordState: String = RecordState.REC.name

    var mClientCount = 0

    private var saveOffsetX = AUDIO_FLOAT_X
    private var saveOffsetY = AUDIO_FLOAT_Y

    override var xOffSet = saveOffsetX
    override var yOffset = saveOffsetY

    var currentRecordDuringTime = ""

    override fun onCreate() {
        super.onCreate()
        NoticeHandler.register(MsgType(MsgType.COMMON, MsgType.COMMON_BYOM_REQUEST), this) {
            if (byomIsPairing == true){
                logTagI(TAG, "收到宜享BYOM申请(Activity),但是正在配对中,不予相应")
                return@register
            }
            logTagI(TAG, "收到宜享BYOM申请(Service)")
            floatWindowAudioServiceListener?.floatStopRecord()
        }

        // 注册HDMI状态监听
        registerHDMIStatusListener()
    }
    override fun View.initViews() {
        initView()
    }

    private fun initView() {
        needNarrowToCircle = true

        recordTimeTv = rootView?.findViewById<TextView>(R.id.record_time_tv)!!
        fullScreenIv = rootView?.findViewById<ImageView>(R.id.full_screen_iv)!!
        controlIv = rootView?.findViewById<ImageView>(R.id.control_iv)!!
        stopIv = rootView?.findViewById<RecordView>(R.id.stop_iv)!!
        redPointSimple = rootView?.findViewById<CircleView>(R.id.redPointView)!!

        changeStopIvClickable(false)
        activityManager = application.getSystemService(ACTIVITY_SERVICE) as ActivityManager
        stopIv.setDebounceTouchClickListener {
            floatWindowAudioServiceListener?.floatStopRecord()
        }

        controlIv.setDebounceTouchClickListener {
            floatWindowAudioServiceListener?.floatControlBtnClick()
        }

        fullScreenIv.setDebounceTouchClickListener {
            moveFront()
        }
    }

    override fun onBind(intent: Intent): IBinder {
        mClientCount++
        val duringTimeStr = intent.getStringExtra("recordTime")
        setRecordDuringTime(duringTimeStr!!)
        val state = intent.getStringExtra("recordState") ?: ""

        saveOffsetX = intent.getIntExtra("offsetX", AUDIO_FLOAT_X)
        saveOffsetY = intent.getIntExtra("offsetY", AUDIO_FLOAT_Y)
        wmParams!!.x = saveOffsetX
        wmParams!!.y = saveOffsetY
        windowManager.updateViewLayout(rootView, wmParams)

        changeRecordState(state)
        updateRedPointStatus()
        super.onBind(intent)
        return mBinder
    }

    fun changeRecordState(state: String) {
        recordState = state
        when (recordState) {
            RecordState.STOP.name -> {
                controlIv.setImageResource(R.drawable.ic_play_white)
            }

            RecordState.PREPARE.name -> {
                stopIv.setImageResource(R.drawable.ic_recording)
                controlIv.setImageResource(R.drawable.ic_pause_white)
            }

            RecordState.REC.name -> {
                stopIv.post {
                    stopIv.setImageResource(R.drawable.ic_recording)
                    stopIv.recording = true
                }
                isPause = false
                controlIv.setImageResource(R.drawable.ic_pause_white)
            }

            RecordState.PAUSE.name -> {
                stopIv.setImageResource(R.drawable.ic_recording)
                controlIv.setImageResource(R.drawable.ic_play_white)
                isPause = true
                changeStopIvClickable(true)
            }

            else -> {
                controlIv.setImageResource(R.drawable.ic_play_white)
            }
        }
        updateRedPointStatus()
    }

    private fun moveFront(): Boolean {
        rootView?.visibility = View.GONE
        //获得当前运行的task(任务)
        val taskInfoList = activityManager!!.appTasks
        for (taskInfo in taskInfoList) {
            //找到本应用的 task，并将它切换到前台
            if (taskInfo.taskInfo.topActivity?.packageName?.contains(packageName) == true) {
                activityManager!!.moveTaskToFront(taskInfo.taskInfo.id, 0)
                break
            }
        }
        return true
    }

    private fun updateRedPointStatus() {
        redPointSimple.invisible(recordState != RecordState.REC.name)
    }


    override fun onUnbind(intent: Intent?): Boolean {
        mClientCount--
        return super.onUnbind(intent)
    }

    fun isServiceAlive(): Boolean {
        return mClientCount > 0
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun reCreateView() {//重新创建了悬浮窗
        initView()
        setRecordDuringTime(currentRecordDuringTime)
        changeRecordState(recordState)
        updateRedPointStatus()
    }

    fun setRecordDuringTime(time: String) {
        currentRecordDuringTime = time
        recordTimeTv.text = time
        if (time == "3" || time == "2" || getRecordTime(time) > 1) {
            changeStopIvClickable(true)
        }
    }

    // 把00:00:00格式的时间转换成秒
    private fun getRecordTime(time: String): Int {
        val timeArray = time.split(":")
        if (timeArray.size <= 1) {
            return 0
        }
        val hour = timeArray[0].toInt()
        val minute = timeArray[1].toInt()
        val second = timeArray[2].toInt()
        return hour * 3600 + minute * 60 + second
    }

    fun getFloatX(): Int {
        return wmParams!!.x
    }

    fun getFloatY(): Int {
        return wmParams!!.y
    }

    inner class RecordBinder : Binder() {
        val recordService: RecordAudioFloatWindowService
            get() = this@RecordAudioFloatWindowService
    }

    private fun changeStopIvClickable(clickable: Boolean) {
        stopIv.apply {
            isClickable = clickable
            isEnabled = clickable
            isFocusable = clickable
        }
    }


    /**
     * 注册HDMI状态监听
     */
    private fun registerHDMIStatusListener() {
        try {
            hdmiMonitor.registerHDMIReceiver(this) { hdmiStatus ->
                logTagI(TAG, "录音悬浮窗检测到HDMI状态变化: $hdmiStatus")

                if (hdmiStatus == HDMIIFStatus.IN) {
                    // HDMI插入，停止录制
                    logTagI(TAG, "录音悬浮窗检测到HDMI插入，停止录制")
                    floatWindowAudioServiceListener?.floatStopRecord()
                }
            }
        } catch (e: Exception) {
            logTagI(TAG, "录音悬浮窗注册HDMI监听失败: ${e.message}")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 取消注册HDMI监听
        try {
            hdmiMonitor.unRegisterHDMIReceiver(this)
        } catch (e: Exception) {
            logTagI(TAG, "录音悬浮窗取消注册HDMI监听失败: ${e.message}")
        }
    }

    interface FloatWindowAudioServiceListener {
        fun floatControlBtnClick()//暂停或继续
        fun floatStopRecord()//停止录制
        fun floatFullScreen()//最大化
    }
}