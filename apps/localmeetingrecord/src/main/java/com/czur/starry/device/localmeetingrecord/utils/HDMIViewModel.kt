package com.czur.starry.device.localmeetingrecord.utils

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.hdmilib.HDMIMonitor

/**
 * Created by 陈丰尧 on 2022/8/22
 */
class HDMIViewModel(application: Application) : AndroidViewModel(application) {
    private val hdmiMonitor = HDMIMonitor()
    val hdmiStatusLive = DifferentLiveData(hdmiMonitor.getHDMIIFStatus())
    var hdmiStatus by LiveDataDelegate(hdmiStatusLive)
        private set

    init {
        hdmiMonitor.registerHDMIReceiver(appContext) {
            hdmiStatus = it
        }
    }

    override fun onCleared() {
        hdmiMonitor.unRegisterHDMIReceiver(appContext)
        super.onCleared()
    }
}