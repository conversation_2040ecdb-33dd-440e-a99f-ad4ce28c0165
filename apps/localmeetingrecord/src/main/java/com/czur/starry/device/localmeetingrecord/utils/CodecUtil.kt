package com.czur.starry.device.localmeetingrecord.utils

import android.media.MediaCodec
import android.media.MediaFormat
import android.util.Log
import java.nio.ByteBuffer

/**
 * Created by 陈丰尧 on 2022/8/17
 */
enum class CodecType(val mimeType: String) {
    VIDEO(MediaFormat.MIMETYPE_VIDEO_AVC), // "video/avc" H.264 Advanced Video
    AUDIO(MediaFormat.MIMETYPE_AUDIO_AAC)   //"audio/mp4a-latm"
}

private const val TAG = "CodecUtil"


data class InputCodeCData(val id: Int, val inputBuffer: ByteBuffer?)


fun createCodec(
    type: CodecType,
    mediaFormat: MediaFormat,
): MediaCodec {

    Log.v(TAG, "create Codec mimeType:${type.mimeType}")
    return MediaCodec.createEncoderByType(type.mimeType).apply {
        configure(
            mediaFormat, null, null,
            MediaCodec.CONFIGURE_FLAG_ENCODE
        )
    }
}

fun MediaCodec.dequeueValidInputBuffer(timeOutUs: Long): InputCodeCData {
    val inputBufferId = dequeueInputBuffer(timeOutUs)
    if (inputBufferId >= 0) {
        return InputCodeCData(inputBufferId, getInputBuffer(inputBufferId))
    }
    return InputCodeCData(inputBufferId, null)
}


fun MediaCodec.handleOutputBuffer(
    bufferInfo: MediaCodec.BufferInfo, defTimeOut: Long,
    formatChanged: () -> Unit = {},
    render: (bufferId: Int) -> Unit,
    needEnd: Boolean = true
) {
    loopOut@ while (true) {
        try {
            //  获取可用的输出缓存队列
            val outputBufferId = dequeueOutputBuffer(bufferInfo, defTimeOut)
            if (outputBufferId == MediaCodec.INFO_TRY_AGAIN_LATER) {
                if (needEnd) {
                    break@loopOut
                }
            } else if (outputBufferId == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                formatChanged.invoke()
            } else if (outputBufferId >= 0) {
                render.invoke(outputBufferId)
                if (bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0) {
                    break@loopOut
                }
            }

        }catch (e:Exception){
            Log.e(TAG, "handleOutputBuffer: ${e.message}")
            break@loopOut
        }

    }
}
