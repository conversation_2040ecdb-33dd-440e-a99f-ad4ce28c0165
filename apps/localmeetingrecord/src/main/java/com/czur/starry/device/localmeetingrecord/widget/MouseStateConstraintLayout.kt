package com.czur.starry.device.localmeetingrecord.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.constraintlayout.widget.ConstraintLayout
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * Created by 陈丰尧 on 2023/2/13
 */

class MouseStateConstraintLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val hoverFlow = MutableStateFlow(false)
    private val touchDownFlow = MutableStateFlow(false)

    val mouseInFlow = hoverFlow.combine(touchDownFlow) { hoverEnter, touchDown ->
        hoverEnter || touchDown
    }.debounce(100).distinctUntilChanged()

    init {
        setOnHoverListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_HOVER_ENTER -> hoverFlow.value = true
                MotionEvent.ACTION_HOVER_EXIT -> hoverFlow.value = false
            }
            false
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        when (ev?.action) {
            MotionEvent.ACTION_DOWN -> touchDownFlow.value = true
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> touchDownFlow.value = false
        }
        return super.dispatchTouchEvent(ev)
    }
}