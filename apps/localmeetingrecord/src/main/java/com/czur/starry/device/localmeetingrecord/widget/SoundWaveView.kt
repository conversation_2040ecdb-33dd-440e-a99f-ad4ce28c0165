package com.czur.starry.device.localmeetingrecord.widget

import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import com.czur.czurutils.log.logTagD

/**
 * Created by 陈丰尧 on 2023/3/14
 */
class SoundWaveView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private companion object {
        private const val DEF_ITEM_WIDTH = 4
        private const val DEF_SPACE_WIDTH = 5
        private const val DEF_ITEM_MIN_HEIGHT = 5
        private const val DEF_ITEM_MAX_HEIGHT = 280
    }

    private val itemWidth = DEF_ITEM_WIDTH
    private val spaceWidth = DEF_SPACE_WIDTH
    private val itemMinHeight = DEF_ITEM_MIN_HEIGHT
    private val itemMaxHeight = DEF_ITEM_MAX_HEIGHT
    private val paint: Paint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
//        strokeCap = Paint.Cap.ROUND // 端点圆角
    }
    private var maxVolumeCount = 0

    // 音量列表
    private val volumeList = mutableListOf<Int>(0)

    private val recWidth = 10

    //    private val max = 32767f // 最大值
    private val max = 17767f // 最大值
    private val viewHeight = 300
    private val screenWeight = 1920
    private val during = 7000L

    private var soundWaveViewListener: SoundWaveViewListener? = null

    private var canvasOffset = 0F
        set(value) {
            if (field != value) {
                if (value < field) {
                    val volume = soundWaveViewListener?.getVolume()
//                    volumeList.add(volume!!)
                    if (volumeList.size>0){
                        volumeList.add(volumeList.size - 1, volume!!)
                        volumeList.removeFirstOrNull()  // 重新了, 销毁第一个
                    }

                }
                field = value
                invalidate() // 重绘
            }
        }

    fun pauseAnim() {
        anim?.pause()

    }

    fun resumeAnim() {
        anim?.resume()
    }

    fun cleanData() {
        volumeList.clear()
        maxVolumeCount = 0
        invalidate()
    }

    private var anim: ObjectAnimator? = null

    fun startAnim() {

        anim = ObjectAnimator.ofFloat(this, "canvasOffset", 0F, (itemWidth + spaceWidth) * 1F)
        anim?.interpolator = LinearInterpolator()
        anim?.repeatMode = ObjectAnimator.RESTART
        anim?.repeatCount = ObjectAnimator.INFINITE
        anim?.duration = 60
        anim?.start()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        paint.strokeWidth = itemWidth.toFloat()
        canvas.translate(-canvasOffset, 0F)

        // View
        val drawWidth = width + (itemWidth + spaceWidth)  // 多绘制1个
        val centerY = height / 2F
        var finishWidth = 0
        var index = 0
        var itemIndex = 0
        while (finishWidth < drawWidth) {
            if (index % 2 == 0) {
                val itemHeight = getItemHeight(itemIndex)
                val x = finishWidth.toFloat() + itemWidth / 2F
                canvas.drawLine(x, centerY - itemHeight / 2F, x, centerY + itemHeight / 2F, paint)
                finishWidth += itemWidth
                itemIndex++
                if (maxVolumeCount == 0) {
                    volumeList.add(0)   // 相当于初始化 应该可以计算一下
                }
            } else {
                finishWidth += spaceWidth
            }
            index++
        }



        maxVolumeCount = itemIndex  // 这里应该能计算出来
    }

    private fun getItemHeight(itemIndex: Int): Float {
        if (maxVolumeCount == 0) return DEF_ITEM_MIN_HEIGHT.toFloat()
        val volume = volumeList.getOrElse(itemIndex) { 0 }

        val height = volume.toFloat() / max * viewHeight
        return height + itemMinHeight
    }



    interface SoundWaveViewListener {
        fun getVolume(): Int
    }

    fun setVolumeListener(soundWaveViewListener: SoundWaveViewListener) {
        this.soundWaveViewListener = soundWaveViewListener
    }
}