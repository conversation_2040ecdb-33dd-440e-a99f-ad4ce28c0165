<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Lokales Meeting aufzeichnen</string>
    <string name="str_time_watermark">Uhrzeit als Wasserzeichen</string>
    <string name="toast_video_file_saved">Die Aufnahmedatei %s wurde gespeichert.</string>
    <string name="toast_video_file_save_failed">Die Aufnahmedatei konnte nicht gespeichert werden.</string>
    <string name="toast_storage_not_enough_start_record">Aufnahme nicht möglich. Der verbleibende Speicherplatz beträgt weniger als 500 MB.</string>
    <string name="toast_storage_not_enough_start_camera">Foto aufnehmen nicht möglich Der verbleibende Speicherplatz beträgt weniger als 500 MB.</string>
    <string name="toast_rec_storage_not_enough">Die Aufzeichnung wird in 5 Minuten beendet, weil der verbleibende Speicherplatz weniger als 500 MB ist.</string>
    <string name="toast_rec_storage_stop_recording">Nicht genügend Speicherplatz übrig Aufzeichnung ist beendet!</string>
    <string name="toast_rec_time_not_enough">Erinnerung: Die Aufzeichnung endet in %s Minuten. Bitte speichern Sie die Aufzeichnungsdatei sofort.</string>
    <string name="tips_camera_mode">Videoaufnahme</string>
    <string name="tips_screen_mode">Bildschirmaufnahme</string>
    <string name="tips_audio_mode">Audioaufnahme</string>
    <string name="toast_conflict_with_meeting">Sie können kein lokales Meeting starten, weil bereits ein Videomeeting läuft.</string>
    <string name="dialog_mic_occupy_hint">%s verwendet das Mikrofon. Möchten Sie diese App beenden und stattdessen mit der Aufnahme beginnen?</string>
    <string name="dialog_mic_occupy_hint_init">%s verwendet das Mikrofon. Möchten Sie diese App beenden und stattdessen mit dem lokalen Meeting beginnen?</string>
    <string name="dialog_stop_record_hint">Aufnahme beenden?</string>
    <string name="dialog_refuse_eshare">Sie können den Bildschirm nicht per Screencasting spiegeln, weil bereits ein Videomeeting läuft.</string>
    <string name="str_is_recording_audio">Audioaufzeichnung läuft</string>
    <string name="str_is_recording_screen">Bildschirmaufzeichnung läuft</string>
    <string name="str_resume_record_screen">Aufzeichnung fortführen.</string>
    <string name="str_pause_record_screen">Aufzeichnung anhalten.</string>
    <string name="str_stop_record_screen">Aufzeichnung beenden.</string>
    <string name="str_can_record_background">Hintergrundaufzeichnung</string>
    <string name="toast_recording_fail">Start der Aufzeichnung fehlgeschlagen. Wiederholen Sie den Vorgang.</string>
    <string name="toast_init_camera_fail">Kamera konnte nicht aktiviert werden. Starten Sie die App neu.</string>
    <string name="str_function_simultaneousRecording_pictureInPicture">Video aufnehmen (PiP)</string>
    <string name="str_function_simultaneousRecording">Audio aufnehmen</string>
    <string name="str_function_recorder">Audio aufnehmen</string>
    <string name="str_function_screen_recorder">Bildschirm aufnehmen</string>
    <string name="str_function_video">Video aufnehmen</string>
    <string name="str_function_photo">Foto aufnehmen</string>
    <string name="str_rename">Umbenennen</string>
    <string name="str_rename_inputet">Dateiname eingaben</string>
    <string name="toast_init_camera_fail_100">Fehler beim Initialisieren der Kamera Bitte starten Sie die App neu.</string>
    <string name="str_save_file">Datei umbenennen</string>
    <string name="str_alert_dialog_title">Benachrichtigung</string>
    <string name="str_dialog_sure">OK</string>
    <string name="str_dialog_cancel">Abbrechen</string>
    <string name="str_flip_lens">Video-Mirroring</string>
    <string name="toast_record_error">Video konnte nicht aufgenommen werden. Bitte versuchen Sie es noch einmal.</string>
</resources>
