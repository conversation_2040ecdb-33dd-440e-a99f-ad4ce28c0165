<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500px"
    android:layout_height="360px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    tools:background="#5879FC"
    tools:viewBindingIgnore="true">

    <TextView
        android:id="@+id/doubleBtnFloatTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:text="@string/dialog_normal_title_tips"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/doubleBtnFloatContentTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:layout_marginRight="40px"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/doubleBtnFloatBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/doubleBtnFloatTitleTv"
        tools:text="您的账号已在其他设备登录, 请确认账号安全。" />

    <LinearLayout
        android:id="@+id/doubleBtnFloatBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30px"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/doubleBtnFloatCancelBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            app:baselib_theme="dark"
            android:text="@string/dialog_normal_cancel"
            android:textSize="20px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/doubleBtnFloatConfirmBtn" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/doubleBtnFloatConfirmBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginLeft="30px"
            android:text="@string/dialog_normal_confirm"
            android:textSize="20px"
            android:textStyle="bold"
            app:baselib_theme="white2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/doubleBtnFloatCancelBtn"
            app:layout_constraintRight_toRightOf="parent" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>