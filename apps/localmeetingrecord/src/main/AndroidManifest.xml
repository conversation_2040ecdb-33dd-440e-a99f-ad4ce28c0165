<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.SYSTEM_CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />


    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.MANAGE_USB" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/LocalMeetingRecordAppTheme">

        <activity
            android:name=".MainActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service android:name=".services.RecordScreenFloatWindowService" />
        <service android:name=".services.RecordFloatTimeWindowService" />
        <service
            android:name=".services.RecordAudioFloatWindowService"
            android:foregroundServiceType="camera"
            tools:ignore="ForegroundServicePermission" />
        <service
            android:name=".services.RecordingFrontService"
            android:foregroundServiceType="camera"
            tools:ignore="ForegroundServicePermission" />
        <service android:name=".services.FloatImageWindowService" />

        <!--    只做重命名操作, 从主进程中独立出来,防止在多任务时影响主进程    -->
        <activity
            android:name=".widget.RenameDialogActivity"
            android:configChanges="${atyPlaceHolder}"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:process=":rename"
            android:taskAffinity="localmeetingrecord.rename"
            android:theme="@style/DialogActivity"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <provider
            android:name=".LocalMeetingProvider"
            android:authorities="com.czur.starry.device.localmeetingrecord.LocalMeetingProvider"
            android:exported="true"
            tools:ignore="ExportedContentProvider" />
    </application>

</manifest>