<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PxUsage">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:defaultFocusHighlightEnabled">false</item>
    </style>

    <style name="ActivityThemeBoot" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:animation">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="ActivityThemeMain" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:textColorHint">#80FFFFFF</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:textCursorDrawable">@drawable/drawable_base_cursor</item>

        <!--ContextMenu 的背景颜色-->
        <item name="android:dropDownListViewStyle">@style/listView</item>
        <item name="android:itemTextAppearance">@style/MyContextMenuText</item>

        <item name="android:defaultFocusHighlightEnabled">false</item>
    </style>

    <style name="short_cut_app_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_marginBottom">30px</item>
        <item name="android:layout_marginLeft">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="file_code_title_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">20px</item>
        <item name="android:layout_marginTop">30px</item>
        <item name="android:layout_marginRight">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="file_code_content_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">30px</item>
        <item name="android:layout_marginTop">5px</item>
        <item name="android:layout_marginRight">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="short_cut_app_content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_marginBottom">20px</item>
        <item name="android:layout_marginRight">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="contact_dialog_icon_iv">
        <item name="android:layout_width">60px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:layout_marginRight">20px</item>
    </style>

    <style name="dialog_company_label_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#80FFFFFF</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginTop">40px</item>
    </style>

    <style name="tv_float_record_info">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">24px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_no_permission_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#80FFFFFF</item>
        <item name="android:textSize">72px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="switch_meeting_config">
        <item name="android:layout_width">94px</item>
        <item name="android:layout_height">44px</item>
    </style>

    <style name="tv_meeting_config_label">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">24px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_meeting_config_switch_label" parent="tv_meeting_config_label">
        <item name="android:layout_marginRight">25px</item>
    </style>

    <style name="checkbox_meeting_config" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40px</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:button">@drawable/sel_check_box_meeting_config</item>
        <item name="android:paddingLeft">20px</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:background">@color/base_bg_color</item>
        <!--波纹的颜色-->
        <item name="android:colorControlHighlight">@color/transparent</item>
        <!--选中的颜色-->
        <item name="colorAccent">@color/transparent</item>
    </style>

    <style name="checkbox_meeting_config_party_building" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">40px</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:button">@drawable/sel_check_box_meeting_config_party</item>
        <item name="android:paddingLeft">20px</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:background">#EF4B4C</item>
        <!--波纹的颜色-->
        <item name="android:colorControlHighlight">@color/transparent</item>
        <!--选中的颜色-->
        <item name="colorAccent">@color/transparent</item>
    </style>

    <!--  党建课程 视频  -->
    <style name="party_video_item_img">
        <item name="android:layout_width">200px</item>
        <item name="android:layout_height">113px</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="round">10px</item>
    </style>

    <style name="party_video_item_play_icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:src">@drawable/ic_party_play</item>
    </style>

    <style name="tv_party_video_item_name">
        <item name="android:layout_width">0px</item>
        <item name="android:layout_height">0px</item>
        <item name="android:paddingLeft">60px</item>
        <item name="android:layout_marginRight">20px</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">24px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="tv_no_login_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">72px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_no_login_sub">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">36px</item>
    </style>

</resources>
