<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/partyVideoMoreRv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/white"
        android:indeterminateTintMode="src_atop" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noNetworkGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <ImageView
            android:id="@+id/noNetworkIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_party_video_net_error"
            app:layout_constraintBottom_toTopOf="@id/noNetworkTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/noNetworkTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20px"
            android:gravity="center"
            android:text="@string/str_party_video_no_network_hint"
            android:textColor="@color/white"
            android:textSize="30px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noNetworkIv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
