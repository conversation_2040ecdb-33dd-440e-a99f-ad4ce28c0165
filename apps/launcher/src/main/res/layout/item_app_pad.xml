<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="230px"
    android:paddingTop="12px"
    tools:ignore="PxUsage">

    <com.czur.starry.device.launcher.widget.ShakeIV
        android:id="@+id/itemAppIconIv"
        android:layout_width="140px"
        android:layout_height="140px"
        android:layout_marginTop="10px"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/uninstallIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="35px"
        android:src="@drawable/ic_launcher_uninstall"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/itemAppNameTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5px"
        android:gravity="center"
        android:maxLines="2"
        android:shadowColor="@color/black"
        android:shadowDx="1.0"
        android:shadowDy="1.0"
        android:shadowRadius="1"
        android:textColor="#ffffff"
        android:textSize="24px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/itemAppIconIv" />

</androidx.constraintlayout.widget.ConstraintLayout>