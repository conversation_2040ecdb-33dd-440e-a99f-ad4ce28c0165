<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1510px"
    android:layout_height="898px"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/topBgView"
        android:layout_width="match_parent"
        android:layout_height="90px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#CA0F2C"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#CA0F2C" />

    <View
        android:id="@+id/bottomBgView"
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="#DB0E2E"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBgView"
        tools:background="#DB0E2E" />

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_dialog_close"
        app:layout_constraintBottom_toBottomOf="@id/topBgView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/topBgView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="58px"
        android:includeFontPadding="false"
        android:text="@string/tab_party_building_course"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/topBgView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/topBgView" />

    <ImageView
        android:id="@+id/partyVideoPreIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginLeft="100px"
        android:src="@drawable/sel_party_video_pre"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/bottomBgView" />

    <ImageView
        android:id="@+id/partyVideoNextIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginRight="100px"
        android:src="@drawable/sel_party_video_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bottomBgView" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewMoreViewPager"
        android:layout_width="0px"
        android:layout_height="0px"
        app:layout_constraintBottom_toTopOf="@id/pageIndexSeparatorTv"
        app:layout_constraintLeft_toRightOf="@id/partyVideoPreIv"
        app:layout_constraintRight_toLeftOf="@id/partyVideoNextIv"
        app:layout_constraintTop_toTopOf="@id/bottomBgView" />

    <!--  分开布局可以让无论数据怎么变, 斜杠都在最中间  -->
    <TextView
        android:id="@+id/currentPageTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="5px"
        android:layout_marginBottom="24px"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/pageIndexSeparatorTv"
        app:layout_constraintRight_toLeftOf="@id/pageIndexSeparatorTv"
        tools:text="1" />

    <TextView
        android:id="@+id/pageIndexSeparatorTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24px"
        android:text="@string/str_party_video_more_page_separator"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/sumPageTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5px"
        android:layout_marginBottom="24px"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/pageIndexSeparatorTv"
        app:layout_constraintLeft_toRightOf="@id/pageIndexSeparatorTv"
        tools:text="1" />

</androidx.constraintlayout.widget.ConstraintLayout>