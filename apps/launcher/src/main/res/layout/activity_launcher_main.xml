<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">


    <com.czur.starry.device.launcher.widget.ViewPagerEventGroup
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_marginTop="78px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/mainPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.czur.starry.device.launcher.widget.ViewPagerEventGroup>


    <FrameLayout
        android:id="@+id/mainStatusBar"
        android:layout_width="match_parent"
        android:layout_height="140px"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.launcher.widget.IndicatorBar
        android:id="@+id/mainIndicatorBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="40px"
        android:paddingHorizontal="20px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="RtlHardcoded" />
</androidx.constraintlayout.widget.ConstraintLayout>