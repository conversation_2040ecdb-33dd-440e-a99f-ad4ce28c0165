<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="120px"
    tools:background="#5879FC"
    tools:ignore="PxUsage,RtlHardcoded">

    <LinearLayout
        android:id="@+id/selGroupLl"
        android:layout_width="wrap_content"
        android:layout_height="80px"
        android:baselineAligned="false"
        android:orientation="horizontal"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/white"
        tools:background="@color/white"
        android:layout_marginHorizontal="35px"
        android:layout_gravity="left|center_vertical">

        <Space
            android:layout_width="80px"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/itemRecordMemberNameSelTv"
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="#5879FC"
            android:textSize="36px"
            android:textStyle="bold"
            tools:text="AAAAAAAAA" />

        <ImageView
            android:id="@+id/itemRecordMemberTypeSelIv"
            android:layout_width="20px"
            android:layout_height="24px"
            android:layout_gravity="center"
            android:layout_marginLeft="19px"
            android:src="@drawable/ic_record_type_device_blue" />

        <Space
            android:layout_width="80px"
            android:layout_height="match_parent" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/normalGroupCl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/itemRecordMemberNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="35px"
            android:ellipsize="end"
            android:lines="1"
            android:maxWidth="255px"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="周康" />

        <ImageView
            android:id="@+id/itemRecordMemberTypeIv"
            android:layout_width="20px"
            android:layout_height="24px"
            android:layout_marginLeft="19px"
            android:src="@drawable/ic_record_type_device"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/itemRecordMemberNameTv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/itemRecordMemberCollectionIv"
            android:layout_width="27px"
            android:layout_height="27px"
            android:layout_marginLeft="19px"
            android:src="@drawable/ic_record_collection_on"
            app:float_tips="@string/float_tip_empty"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/itemRecordMemberTypeIv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/itemRecordMemberInBlacklistIv"
            android:layout_width="26px"
            android:layout_height="24px"
            android:layout_marginLeft="19px"
            android:src="@drawable/ic_record_blacklist_in"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/itemRecordMemberCollectionIv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/itemRecordMemberDelIv"
            android:layout_width="40px"
            android:layout_height="40px"
            android:layout_marginRight="20px"
            android:src="@drawable/ic_record_member_del"
            app:float_tips="@string/float_tip_del"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>