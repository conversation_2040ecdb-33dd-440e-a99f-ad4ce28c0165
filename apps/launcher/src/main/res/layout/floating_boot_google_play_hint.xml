<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500px"
    android:layout_height="360px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    tools:background="#5879FC"
    tools:ignore="PxUsage">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:text="@string/dialog_normal_title_tips"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/contentTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="20px"
        android:text="@string/str_boot_hint_google_play"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@id/neverRemindCb"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/neverRemindCb"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginTop="15px"
        app:checked="false"
        app:checkedImg="@drawable/file_icon_share_checked"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/neverRemindTv"
        app:layout_constraintTop_toBottomOf="@id/contentTv"
        app:unCheckedImg="@drawable/file_icon_share_unchecked" />

    <TextView
        android:id="@+id/neverRemindTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:ellipsize="end"
        android:lines="1"
        android:text="@string/str_boot_hint_google_play_never_remind"
        android:textColor="@color/white"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="@id/neverRemindCb"
        app:layout_constraintLeft_toRightOf="@id/neverRemindCb"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/neverRemindCb" />


    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/confirmBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="30px"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/str_boot_hint_google_ok"
        android:textSize="20px"
        android:textStyle="bold"
        app:autoSizeMaxTextSize="20px"
        app:autoSizeMinTextSize="12px"
        app:autoSizeTextType="uniform"
        app:baselib_theme="white2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>