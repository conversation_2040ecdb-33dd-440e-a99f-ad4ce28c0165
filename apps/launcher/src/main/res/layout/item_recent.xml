<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="600px"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        tools:ignore="PxUsage,RtlHardcoded">

        <View
            android:id="@+id/bgView"
            android:layout_width="540px"
            android:layout_height="370px"
            android:background="@drawable/recent_item_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/blackBgView"
            android:layout_width="0px"
            android:layout_height="0px"
            app:layout_constraintLeft_toLeftOf="@id/recentScreenshotIv"
            app:layout_constraintRight_toRightOf="@id/recentScreenshotIv"
            app:layout_constraintTop_toTopOf="@id/recentScreenshotIv"
            app:layout_constraintBottom_toBottomOf="@id/recentScreenshotIv"
            app:round="10px"
            android:layout_margin="1px"
            android:background="@color/black"/>

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/recentScreenshotIv"
            android:layout_width="500px"
            android:layout_height="281.25px"
            android:layout_margin="20px"
            android:layout_marginBottom="20px"
            app:layout_constraintBottom_toBottomOf="@id/bgView"
            app:layout_constraintLeft_toLeftOf="@id/bgView"
            app:layout_constraintRight_toRightOf="@id/bgView"
            app:round="10px" />

        <TextView
            android:id="@+id/appNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="20px"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/recentScreenshotIv"
            app:layout_constraintLeft_toLeftOf="@id/recentScreenshotIv"
            app:layout_constraintTop_toTopOf="@id/bgView" />

        <View
            android:id="@+id/appRecentClickView"
            android:layout_width="0px"
            android:layout_height="0px"
            app:layout_constraintBottom_toBottomOf="@id/bgView"
            app:layout_constraintLeft_toLeftOf="@id/bgView"
            app:layout_constraintRight_toRightOf="@id/bgView"
            app:layout_constraintTop_toTopOf="@id/bgView" />


        <ImageView
            android:id="@+id/appDeleteIv"
            android:layout_width="60px"
            android:layout_height="60px"
            android:layout_marginTop="-30px"
            android:layout_marginRight="-30px"
            android:src="@drawable/ic_recent_remove_item"
            app:layout_constraintRight_toRightOf="@id/bgView"
            app:layout_constraintTop_toTopOf="@id/bgView" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
