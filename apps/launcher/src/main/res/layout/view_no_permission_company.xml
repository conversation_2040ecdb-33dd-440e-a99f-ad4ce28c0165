<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="@dimen/index_bar_padding">

    <com.czur.starry.device.launcher.widget.BlurImageView
        android:id="@+id/blurImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="26px" />

    <View
        android:layout_width="0px"
        android:layout_height="0px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#33FFFFFF"
        app:layout_constraintBottom_toBottomOf="@id/blurImageView"
        app:layout_constraintLeft_toLeftOf="@id/blurImageView"
        app:layout_constraintRight_toRightOf="@id/blurImageView"
        app:layout_constraintTop_toTopOf="@id/blurImageView" />

    <ImageView
        android:id="@+id/buniessLogoIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:src="@drawable/ic_buniess"
        app:layout_constraintBottom_toTopOf="@id/noCompanyTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/blurImageView"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/noCompanyTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18px"
        android:gravity="center"
        android:lineSpacingMultiplier="1.1"
        android:text="@string/company_empty_hint"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/blurImageView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/buniessLogoIv" />
</androidx.constraintlayout.widget.ConstraintLayout>