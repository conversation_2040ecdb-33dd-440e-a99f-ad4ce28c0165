package com.czur.starry.device.launcher.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View

/**
 * Created by 陈丰尧 on 2022/8/25
 */
class OtherMeetingProcess @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    companion object {
        // 进度条颜色
        private const val COLOR_PROGRESS = 0xFFFFFFFF.toInt()
        private const val OUT_STOKE_WIDTH = 3F  // 外侧圆的线宽
    }

    private val paint: Paint = Paint().apply {
        isAntiAlias = true
        color = COLOR_PROGRESS
    }

    var progress = 0
        set(value) {
            if (field == value || field < 0 || field > 100) return
            field = value
            invalidate()
        }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制外侧圆
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = OUT_STOKE_WIDTH

        canvas.drawCircle(width / 2F, height / 2F, width / 2F - OUT_STOKE_WIDTH, paint)
        // 绘制内侧进度
        paint.style = Paint.Style.FILL
        val inSidePadding = OUT_STOKE_WIDTH * 2 + 5
        val startAngle = 360F / 100 * progress - 90   // 起始是以3点钟方向为0度
        canvas.drawArc(
            inSidePadding,
            inSidePadding,
            width - inSidePadding,
            height - inSidePadding,
            startAngle,
            270 - startAngle,
            true, paint
        )
    }
}