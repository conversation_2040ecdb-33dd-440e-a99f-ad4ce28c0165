package com.czur.starry.device.launcher.pages.view.launcher.custom

import android.view.MotionEvent
import android.view.View
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.view.BaseFragment
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.utils.injectionVScrollEvent

/**
 * Created by 陈丰尧 on 2023/5/19
 * 左侧定制页面
 */
private const val TAG = "LeftCustomFragment"

class LeftCustomFragment : BaseFragment() {
    override fun getLayoutId(): Int = R.layout.fragment_left_custom

    private val customViewPager by findView<ViewPager2>(R.id.customViewPager)

    private val leftCustomViewModel: LeftCustomViewModel by viewModels({ requireActivity() })
    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })


    override fun initView() {
        super.initView()

        customViewPager.isUserInputEnabled = false
        customViewPager.orientation = ViewPager2.ORIENTATION_VERTICAL
        for (i in 0 until customViewPager.childCount) {
            // 禁止掉 阴影效果
            (customViewPager.getChildAt(i) as? RecyclerView)?.overScrollMode =
                View.OVER_SCROLL_NEVER
        }
        customViewPager.offscreenPageLimit = 3

        // 滑动时, 需要刷新模糊View的背景
        customViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                keyCodeVM.scrollTime.value = System.currentTimeMillis()
            }
        })

        // 禁止响应鼠标滚轮事件
        customViewPager.children.forEach {
            if (it is RecyclerView) {
                it.setOnGenericMotionListener { _, event ->
                    if (event?.action == MotionEvent.ACTION_SCROLL) {
                        // 拦截鼠标滚轮事件, 将其转化为viewPager的翻页事件
                        val v = event.getAxisValue(MotionEvent.AXIS_VSCROLL)
                        launch {
                            injectionVScrollEvent(v)
                        }
                        return@setOnGenericMotionListener true
                    }
                    false
                }
            }
        }
    }

    override fun initData() {
        super.initData()
        initPageIndex()
    }

    private fun initPageIndex() {
        launch {

            val layoutMode = SettingUtil.PersonalizationSetting.getLauncherLayout()
            val initIndex =
                if (layoutMode == SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_MEETING) {
                    // 只显示会议Meeting
                    0
                } else {
                    1
                }
            val customAdapter = CustomAdapter()
            customViewPager.adapter = customAdapter
            customViewPager.setCurrentItem(initIndex, false)
            leftCustomViewModel.currentPage = initIndex

            leftCustomViewModel.currentPageLive.observe(viewLifecycleOwner) {
                if (it > (customViewPager.adapter?.itemCount ?: 0) - 1) {
                    logTagD(
                        TAG,
                        "currentPageLive: $it, itemCount: ${customViewPager.adapter?.itemCount}"
                    )
                    return@observe
                }
                if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
                    customViewPager.setCurrentItem(initIndex, true)
                } else {
                    customViewPager.setCurrentItem(it, true)
                }
            }
        }
    }


    private inner class CustomAdapter : FragmentStateAdapter(this) {

        private val leftTimeFragment by lazy {
            LeftTimeFragment()
        }

        override fun getItemCount(): Int = 1

        override fun getItemViewType(position: Int): Int {
            return position // 一共就2也, 每一页都是不同的
        }


        override fun createFragment(position: Int): Fragment {
            return leftTimeFragment
        }
    }
}