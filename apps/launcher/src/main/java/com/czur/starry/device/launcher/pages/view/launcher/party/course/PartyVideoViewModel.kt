package com.czur.starry.device.launcher.pages.view.launcher.party.course

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.data.NullableLiveDataDelegate
import com.czur.starry.device.launcher.data.bean.PartyVideoItemEntity
import com.czur.starry.device.launcher.net.IPartyVideoServer
import com.czur.starry.device.launcher.utils.bootAppByAction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/2/9
 */
private const val TAG = "PartyVideoViewModel"

private const val ACTION_EXTERNAL_MEDIA = "com.czur.starry.device.file.EXTERNAL_MEDIA"
private const val KEY_SOURCE_TYPE = "sourceType"
private const val VALUE_SOURCE_TYPE_EXTERNAL_VIDEO = "externalVideo"
private const val KEY_EXTERNAL_VIDEO_URL = "externalVideoURL"
private const val KEY_EXTERNAL_VIDEO_NAME = "externalVideoName"
private const val KEY_EXTERNAL_FINISH_WHEN_STOP = "finishWhenStop"  // 页面不可见时自动销毁

class PartyVideoViewModel(application: Application) : AndroidViewModel(application) {
    private val videoServer: IPartyVideoServer by lazy {
        HttpManager.getService()
    }

    val totalSizeLive = DifferentLiveData(0)
    var totalSize: Int by LiveDataDelegate(totalSizeLive)
        private set

    val launcherDisplayVideoItemsLive: LiveData<List<PartyVideoItemEntity>?> =
        DifferentLiveData(emptyList<PartyVideoItemEntity>())
    private var launcherDisplayVideoItems by NullableLiveDataDelegate(launcherDisplayVideoItemsLive)

    /**
     * 加载Launcher上的数据
     */
    suspend fun loadLauncherVideo() = withContext(Dispatchers.IO) {
        logTagV(TAG, "加载Launcher上的视频数据")
        val videoMiaoEntity = videoServer.getPartyFiles(1, 3)
        val videoData = if (videoMiaoEntity.isSuccess) {
            videoMiaoEntity.body
        } else {
            logTagW(TAG, "请求党建课程失败${videoMiaoEntity.code} - ${videoMiaoEntity.msg}")
            null
        }
        totalSize = videoData?.total ?: 0
        launcherDisplayVideoItems = if (videoData == null) {
            null
        } else {
            videoData.videoItems ?: emptyList()
        }
    }

    /**
     * 启动视频播放页面
     */
    fun bootFileManagerShowVideo(context: Context, itemEntity: PartyVideoItemEntity) {
        if (itemEntity.isEmpty) return  // 空数据不进行跳转
        val videoUrl = itemEntity.videoUrl
        val name = itemEntity.fileName
        logTagV(TAG, "播放视频:${name}: $videoUrl")
        bootAppByAction(ACTION_EXTERNAL_MEDIA, context) {
            putExtra(KEY_SOURCE_TYPE, VALUE_SOURCE_TYPE_EXTERNAL_VIDEO)
            putExtra(KEY_EXTERNAL_VIDEO_URL, videoUrl)
            putExtra(KEY_EXTERNAL_VIDEO_NAME, name)
            putExtra(KEY_EXTERNAL_FINISH_WHEN_STOP, true)
        }
    }
}