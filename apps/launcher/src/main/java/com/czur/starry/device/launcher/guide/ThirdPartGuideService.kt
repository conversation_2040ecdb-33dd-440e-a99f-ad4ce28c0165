package com.czur.starry.device.launcher.guide

import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.LifecycleService
import com.bumptech.glide.Glide
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.launcher.R
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2022/5/18
 */
class ThirdPartGuideService : LifecycleService() {
    companion object {
        private const val AUTO_CLOSE_TIME = 15 * ONE_SECOND

        fun startThirdPartGuide(content: Context) {
            content.startService(Intent(content, ThirdPartGuideService::class.java))
        }
    }


    private val windowManager by lazy {
        applicationContext.getSystemService(WINDOW_SERVICE) as WindowManager
    }
    private var autoCloseJob: Job? = null
    private var rootView: View? = null

    override fun onCreate() {
        super.onCreate()
        createView()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startAutoClose()
        return super.onStartCommand(intent, flags, startId)
    }

    private fun startAutoClose() {
        autoCloseJob?.cancel()
        autoCloseJob = launch {
            delay(AUTO_CLOSE_TIME)
            closeGuideFloat()
        }
    }

    /**
     * 创建悬浮窗View
     */
    private fun createView() {
        val layoutParam = WindowManager.LayoutParams().apply {
            //设置大小 自适应
            width = WRAP_CONTENT
            height = WRAP_CONTENT
            type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
            gravity = Gravity.LEFT or Gravity.TOP
            flags =
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            format = PixelFormat.RGBA_8888
            x = 1610
            y = 480
        }
        // 新建悬浮窗控件
        rootView =
            LayoutInflater.from(this).inflate(R.layout.service_third_part_guide, null).apply {
                val animIv = findViewById<ImageView>(R.id.animIv)
                val closeTv = findViewById<TextView>(R.id.closeBtn)
                Glide.with(animIv).load(R.drawable.anim_guide).into(animIv)
                closeTv.setOnClickListener {
                    closeGuideFloat()
                }
                // 将悬浮窗控件添加到WindowManager
                windowManager.addView(this, layoutParam)
            }


    }

    /**
     * 结束浮窗
     */
    private fun closeGuideFloat() {
        autoCloseJob?.cancel()
        stopSelf()
    }

    override fun onDestroy() {
        rootView?.let {
            windowManager.removeViewImmediate(it)
        }
        super.onDestroy()
    }
}