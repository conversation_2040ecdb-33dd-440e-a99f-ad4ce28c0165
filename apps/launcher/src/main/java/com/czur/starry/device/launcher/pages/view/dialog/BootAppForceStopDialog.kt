package com.czur.starry.device.launcher.pages.view.dialog

import android.app.Application
import android.content.Context
import androidx.lifecycle.ViewModelProvider
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.ProcessName
import com.czur.starry.device.baselib.utils.getAppNameByPkg
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.AppInfo
import com.czur.starry.device.launcher.utils.forceStopProcess
import com.czur.starry.device.sharescreen.esharelib.EshareByomRunningStatusCallBack
import com.czur.starry.device.sharescreen.esharelib.EshareByomStatusModel

/**
 * Created by 陈丰尧 on 2023/7/8
 */
private const val TAG = "BootAppForceStopDialog"
fun showForceStopDialog(
    context: Context,
    appInfo: AppInfo,
    processNames: MutableList<ProcessName>,
    onStopSuccess: () -> Unit = {}
) {
    if (processNames.isEmpty()) return
    val processName = processNames.removeAt(0)
    val appName = getAppNameByPkg(processName.processName)
    val hintStr =
        context.getString(
            R.string.dialog_mic_occupy_hint_other_app,
            appName,
            appInfo.appName
        )

    val viewModelProvider = ViewModelProvider.AndroidViewModelFactory.getInstance(Application())
    val shareByomModel = viewModelProvider.create(EshareByomStatusModel::class.java)
    DoubleBtnCommonFloat(content = hintStr) { commonFloat, position ->
        commonFloat.dismiss()
        when (position) {
            DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
            }

            DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                if (shareByomModel.isEshareByomRunning()) {
                    shareByomModel.setOnEshareRunningStatusChanged(object :
                        EshareByomRunningStatusCallBack {
                        override fun byomRunningCallBack() {
                            onStopSuccess()
                        }
                    })
                    shareByomModel.stopByom()
                } else {
                    forceStopProcess(processName.processName)
                    if (processNames.isEmpty()) {
                        logTagD(TAG, "所有占用进程全部被销毁")
                        onStopSuccess()
                    } else {
                        showForceStopDialog(context, appInfo, processNames)
                    }
                }
            }
        }
    }.show()
}

/**
 * 显示与USB外设模式冲突的Dialog
 */
fun showPeripheralUSBFloating(onConfirmClick: () -> Unit) {
    DoubleBtnCommonFloat(content = getString(R.string.dialog_byom_camera_mic_occupy_hint)) { commonFloat, position ->
        commonFloat.dismiss()
        when (position) {
            DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CANCEL -> {
            }

            DoubleBtnCommonFloat.DOUBLE_FLOAT_BTN_CONFIRM -> {
                onConfirmClick()
            }
        }
    }.show()
}