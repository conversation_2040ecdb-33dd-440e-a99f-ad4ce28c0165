package com.czur.starry.device.launcher.backdrop

import android.app.WallpaperManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapRegionDecoder
import android.graphics.Rect
import androidx.core.graphics.drawable.toBitmap
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.baselib.utils.screenX
import com.czur.starry.device.launcher.app.App
import com.czur.starry.device.launcher.utils.blur.BlurBitmapCache
import com.czur.starry.device.launcher.widget.BlurImageView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2024/7/24
 * 用来管理桌面壁纸的, 主要是用来监听壁纸的变化, 以便及时处理模糊图片
 */
object BackdropManager {
    private const val TAG = "BackdropManager"
    private const val BLUR_IMG_NAME = "blur_img.jpg"

    private val scope = MainScope()
    private val wpm: WallpaperManager by lazy {
        WallpaperManager.getInstance(App.context)
    }
    private val decoderOptions by lazy {
        BitmapFactory.Options().apply {
            inPreferredConfig = Bitmap.Config.RGB_565
        }
    }

    private var currentRegionDecoder: BitmapRegionDecoder? = null
    private val _eventFlow = MutableSharedFlow<BackdropEvent>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    // 事件Flow
    val eventFlow = _eventFlow.asSharedFlow()

    private val cache by lazy { BlurBitmapCache() }

    private val isPreparing: AtomicBoolean = AtomicBoolean(false)


    enum class BackdropEvent {
        CHANGE
    }

    /**
     * 当桌面背景发生变化时调用
     */
    fun onBackdropChange() {
        scope.launch {
            withContext(Dispatchers.IO) {
                prepareBackdropManager()
            }
        }
    }

    suspend fun prepareBackdropManager() = withContext(Dispatchers.IO) {
        try {
            isPreparing.set(true)
            currentRegionDecoder?.recycle()
            currentRegionDecoder = null
            val bmp = wpm.drawable?.toBitmap(getScreenWidth(), getScreenHeight())
                ?: return@withContext    // 这个bmp不能recycler哦
//            // 生成模糊图片
            val blurBmp = bmp.blur(20, 4)
//            // 保存到文件
            val file = File(App.context.cacheDir, BLUR_IMG_NAME)
            blurBmp.saveToFile(file)
            blurBmp.recycle()
            currentRegionDecoder = BitmapRegionDecoder.newInstance(file.absolutePath)
            cache.evictAll()    // 清理缓存
            _eventFlow.emit(BackdropEvent.CHANGE)
        } finally {
            isPreparing.set(false)
        }

    }

    private suspend fun getBmp(region: Rect): Bitmap? {
        val cacheKey = "${region.left}-${region.right}"
        if (region.left == region.right) {
            logTagW("BlurUtil", "没有宽度,ignore")
            return null
        }
        var bitmap = cache.get(cacheKey)
        if (bitmap == null) {
            bitmap = withContext(Dispatchers.IO) {
                try {
                    currentRegionDecoder?.decodeRegion(region, decoderOptions)
                } catch (exp: Exception) {
                    logTagE(
                        TAG, "处理模糊图片错误:${region}",
                        tr = exp
                    )
                    null
                }
            }
        }
        bitmap?.let {
            cache.put(cacheKey, it)
        }
        return bitmap
    }

    private suspend fun checkPrepare() {
        while (isPreparing.get()) {
            logTagW(TAG, "正在准备模糊图片, 请稍后")
            delay(150)
        }
        if (currentRegionDecoder == null) {
            logTagW(TAG, "currentRegionDecoder为空")
            prepareBackdropManager()
        }
    }

    /**
     * 获取模糊图片
     */
    suspend fun getBlurBitmap(region: Rect, isRetry: Boolean = false): Bitmap? {
        checkPrepare()
        val bitmap = getBmp(region)
        if (bitmap == null) {
            logTagW(TAG, "生成模糊图片失败, region:${region}, isRetry:${isRetry}")
            if (!isRetry) {
                delay(500)  // 通常失败的原因是View没有加载完成, 获取不到宽高
                logTagV(TAG, "重试1次")
                getBlurBitmap(region, true)
            }
        }
        return bitmap
    }

    /**
     * 设置模糊图片到BlurImageView
     */
    suspend fun setBlurBitmapToBlurIv(blurView: BlurImageView) {
        val x = blurView.screenX
        val width = blurView.width
        val region = Rect(x, 0, x + width, currentRegionDecoder?.height ?: getScreenHeight())
        val bitmap = getBlurBitmap(region)
        blurView.bitmap = bitmap
    }
}