package com.czur.starry.device.launcher.pages.view.launcher.custom

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.AppInfo

/**
 * Created by 陈丰尧 on 2023/8/15
 */
class ToolSheetAdapter : BaseDifferAdapter<AppInfo>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: AppInfo) {
        holder.setText(itemData.appName, R.id.itemNameTv)
        holder.setImgDrawable(itemData.appIcon, R.id.itemIconIv)
    }

    override fun areItemsTheSame(oldItem: AppInfo, newItem: AppInfo): Boolean {
        return oldItem.pkgName == newItem.pkgName
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_tool_sheet, parent)
    }
}