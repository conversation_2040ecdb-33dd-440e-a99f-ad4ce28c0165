package com.czur.starry.device.launcher.data.bean

import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 2022/8/20
 * 网络会议的App列表
 */
data class OtherQuickBootAppItem(
    val id: Long,                       // id
    val name: String,                   // 名称
    val iconUrl: String,                // 图标(没有该应用时使用)
    @SerializedName("appId")
    val packageName: String,            // 包名
    @SerializedName("cdnPackageUrl")
    val downloadUrl: String,            // 下载地址
    @SerializedName("versionNo")
    val versionCode: Int,
    @SerializedName("fileSize")
    val size: Long,
)