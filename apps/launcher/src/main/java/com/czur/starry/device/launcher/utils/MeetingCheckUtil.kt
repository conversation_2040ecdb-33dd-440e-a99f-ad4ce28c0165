package com.czur.starry.device.launcher.utils

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.AudioUtil
import com.czur.starry.device.baselib.utils.ProcessName
import com.czur.starry.device.baselib.utils.focusStop
import com.czur.starry.device.baselib.utils.getProcessNameByPid

/**
 * Created by 陈丰尧 on 2022/4/14
 */
/**
 * 获取使用Mic的进程名字
 */
private const val TAG = "MeetingCheckUtil"

private val audioUtil = AudioUtil()

/**
 * 获取使用Mic的进程名字
 */
fun getUseMicProcessName(): List<ProcessName> {
    try {
//        audioUtil.requestAudioFocus(AudioManager.STREAM_VOICE_CALL)//具体业务需要时候再进行内部的请求焦点
        if (!audioUtil.isMicInUse()) {
            logTagD(TAG, "麦克风没有被占用")
            return emptyList()
        }

        val pids = audioUtil.getUseMicPid()
        logTagW(TAG, "麦克风被占用,pid:${pids.joinToString()}")
        if (pids.isEmpty()) return emptyList()

        return pids.mapNotNull { pid ->
            getProcessNameByPid(pid)
        }
    } finally {
        audioUtil.abandonAudioFocus()
    }
}

fun forceStopProcess(pkgName: String) {
    focusStop(pkgName)
}