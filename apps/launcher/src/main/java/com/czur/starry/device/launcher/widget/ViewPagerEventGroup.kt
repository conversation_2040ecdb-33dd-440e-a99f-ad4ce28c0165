package com.czur.starry.device.launcher.widget

import android.content.Context
import android.util.AttributeSet
import android.view.InputDevice
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.core.view.forEach
import androidx.viewpager2.widget.ViewPager2
import com.czur.czurutils.log.logTagD

/**
 * Created by 陈丰尧 on 2023/8/28
 */
private const val TAG = "ViewPagerEventGroup"

class ViewPagerEventGroup @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private val viewPager: ViewPager2 by lazy {
        forEach {
            if (it is ViewPager2) {
                return@lazy it
            }
        }
        throw IllegalStateException("ViewPagerEventGroup must have a ViewPager2 child")
    }

    private var downX = 0F
    private var downY = 0F

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            downX = ev.x
            downY = ev.y
        } else if (ev.action == MotionEvent.ACTION_MOVE) {
            if (ev.x == downX && ev.source == InputDevice.SOURCE_TOUCHSCREEN) {
                // 竖向滑动
                val dy = ev.y - downY
                if (dy < 0) {
                    // 向上滑动
                    logTagD(TAG, "向上翻页")
                    viewPager.currentItem = viewPager.currentItem + 1
                } else {
                    // 向下滑动
                    logTagD(TAG, "向下翻页")
                    viewPager.currentItem = viewPager.currentItem - 1
                }
                return true
            }
        }
        return super.onInterceptTouchEvent(ev)
    }
}