package com.czur.starry.device.launcher.data.bean

import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 2023/2/9
 */
data class PartyVideoEntity(
    val total: Int,
    val pageNo: Int,
    val pageSize: Int,
    @SerializedName("data")
    val videoItems: List<PartyVideoItemEntity>?
) {
    val isEmpty: Boolean
        get() = total == -1

    companion object {
        fun mkEmptyData(): PartyVideoEntity = PartyVideoEntity(-1, 1, 0, emptyList())
    }
}

data class PartyVideoItemEntity(
    val id: Int,
    val fileName: String,
    @SerializedName("imageOssKey")
    val imageUrl: String,
    @SerializedName("effectOssKey")
    val videoUrl: String
) {
    // 是否为空数据
    val isEmpty: Boolean
        get() = id == EMPTY_ID

    companion object {
        const val EMPTY_ID = -1
        fun mkEmpty(): PartyVideoItemEntity {
            return PartyVideoItemEntity(EMPTY_ID, "", "", "")
        }
    }
}