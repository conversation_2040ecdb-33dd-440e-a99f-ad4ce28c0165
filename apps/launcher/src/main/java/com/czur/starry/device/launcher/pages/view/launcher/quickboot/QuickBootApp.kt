package com.czur.starry.device.launcher.pages.view.launcher.quickboot

import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_FILE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_SETTING
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_SCREEN_SHARE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_WRITE_PAD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeStatus
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileName
import com.czur.starry.device.launcher.R
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.starrypadlib.StarryPadPaintHandler
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2/22/21
 * 配置主页快速速启动的相关参数
 */
private const val TAG = "QuickBootApp"

open class QuickBootApp(
    val nameRes: Int,                               // 显示的名字
    val imgRes: Int,                                // 背景图片
    val needLogin: Boolean = false,                 // 是否需要登录后才能打开
    val badgeLive: LiveData<Boolean>? = null,       // 有新标记的LiveData
    val newFileLive: LiveData<String>? = null,      // 新生未读文件名
    val fileCodeLive: LiveData<String>? = null,      // 文件校验码
    val showShare: Boolean = false,
    val cannotOpenToast: Int? = null,
    val enable: suspend () -> Boolean = { true },
    private val intentBuilder: Intent.() -> Intent?    // 构建启动Intent
) {
    // 启动Intent
    val bootIntent: Intent?
        get() = Intent().intentBuilder()
}

// 占位
class PlaceHolderQuickBootApp : QuickBootApp(-1, -1, intentBuilder = { null })

val fileNameLiveData = MediatorLiveData<String>().apply {

    fun updateFileName() {
        logTagV(TAG, "新文件名字 = $unReadFileName")
        value = unReadFileName
    }

    addSource(FileHandlerLive.unReadFileNameLive) {
        updateFileName()
    }

}

val starryPadFileNameLiveData = MediatorLiveData<String>().apply {

    fun updateFileName(name: String) {
        logTagV(TAG, "StarryPad新文件名字 = $name")
        value = name
    }

    addSource(StarryPadPaintHandler.newPaintFileNameLive) {
        updateFileName(it)
    }

}

val fileCodeLiveData = MediatorLiveData<String>().apply {

    fun updateFileCode() {
        logTagV(TAG, "新校验码")
        value = fileShareCodeStatus ?: Random.nextInt(1000, 10000).toString()
    }

    addSource(FileHandlerLive.fileShareCodeLive) {
        updateFileCode()
    }
    addSource(FileHandlerLive.fileShareCodeEnableLive) {
        updateFileCode()
    }
    addSource(FileHandlerLive.fileShareEnableLive) {
        updateFileCode()
    }
}
val settingNewLiveData = MediatorLiveData<Boolean>().apply {
    fun updateVersion() {
        val ota = OTAHandler.newVersionStatus
        val touchPad = OTAHandler.newTouchPadVersion
        val camera =
            OTAHandler.newCameraVersion != "null" && OTAHandler.newCameraVersion != OTAHandler.currentCameraVersion
        logTagV(TAG, "ota新版本:$ota , 触控板新版本:${touchPad}")
        value = ota || touchPad || camera
    }

    addSource(OTAHandler.newTouchPadVersionLive) {
        updateVersion()
    }
    addSource(OTAHandler.newVersionStatusLive) {
        updateVersion()
    }
    addSource(OTAHandler.newCameraVersionStatusLive) {
        updateVersion()
    }

}

/**
 * 获取QuickBoot的信息
 */
val quickBootApps = listOf(
    PlaceHolderQuickBootApp(),
    QuickBootApp(
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) R.string.quick_boot_screen_share_army else R.string.quick_boot_screen_share,
        R.drawable.icon_quickboot_screen_share,
        showShare = true
    ) {
        // 无线投屏
        action = ACTION_BOOT_SCREEN_SHARE
        addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        this
    },
    PlaceHolderQuickBootApp(),
    QuickBootApp(
        R.string.quick_boot_write_pad,
        R.drawable.icon_quickboot_writepad,
        cannotOpenToast = R.string.func_coming_soon,
        enable = {
            SettingUtil.WritePadSetting.isEnable().also {
                logTagV(TAG, "手写板功能是否启用: $it")
            }
        },
        newFileLive = starryPadFileNameLiveData
    ) {
        // 手写板
        action = ACTION_BOOT_WRITE_PAD
        this
    },
    QuickBootApp(
        R.string.quick_boot_file, R.drawable.icon_quickboot_file,
        newFileLive = fileNameLiveData,
        fileCodeLive = fileCodeLiveData
    ) {
        // 文件
        action = ACTION_BOOT_FILE
        this
    },
    QuickBootApp(
        R.string.quick_boot_options,
        imgRes = R.drawable.icon_quickboot_option,
        badgeLive = settingNewLiveData
    ) {
        // 设置
        action = ACTION_BOOT_NOTICE_SETTING
        this
    }
)