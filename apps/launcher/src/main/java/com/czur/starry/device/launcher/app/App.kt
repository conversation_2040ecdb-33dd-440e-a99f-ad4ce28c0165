package com.czur.starry.device.launcher.app

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.listener.StarryApp
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.launcher.backdrop.BackdropChangeReceiver
import kotlin.properties.Delegates

class App : StarryApp() {

    companion object {
        var context: Context by Delegates.notNull()
    }

    override fun onCreate() {
        super.onCreate()
        context = this
        SPHandler.init(this)
        if (!getProcessName().contains(":")) {
            // 只有主进程才注册壁纸变化的广播
            registerReceiver(
                BackdropChangeReceiver(),
                IntentFilter(Intent.ACTION_WALLPAPER_CHANGED)
            )
        }
    }
}
