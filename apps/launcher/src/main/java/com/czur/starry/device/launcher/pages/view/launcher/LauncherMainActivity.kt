package com.czur.starry.device.launcher.pages.view.launcher

import android.app.WallpaperManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.whenResumed
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.ORIENTATION_VERTICAL
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_CODE_BTB_SEAT
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.KEY_HDMI_AUTO_OPEN
import com.czur.starry.device.baselib.common.KEY_HDMI_STATE_IN
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_BOOT_COMPLETE
import com.czur.starry.device.baselib.common.KEY_STARTUP_COMPLETE
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.tips.TipsPool
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.view.floating.FloatFragment
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.hdmilib.HDMIIFStatus
import com.czur.starry.device.hdmilib.sendBroadCastToMirror
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.databinding.ActivityLauncherMainBinding
import com.czur.starry.device.launcher.pages.view.launcher.custom.LeftCustomViewModel
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.ShareViewModel
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.v2.HDMIViewModel
import com.czur.starry.device.launcher.pages.view.launcher.status.StatusBarFragment
import com.czur.starry.device.launcher.utils.adapterMouse
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.utils.injectionVScrollEvent
import com.czur.starry.device.sharescreen.esharelib.EshareByomStatusModel
import com.czur.starry.device.sharescreen.esharelib.util.enableEShareVoice
import com.czur.starry.device.sharescreen.esharelib.util.isEShareActive
import com.czur.starry.device.sharescreen.esharelib.util.showDeviceNameAlertWindow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

class LauncherMainActivity : CZViewBindingAty<ActivityLauncherMainBinding>() {

    companion object {
        private const val TAG = "LauncherActivity"
        private const val SYSTEM_PROP_STOP_FORCE = "system.meeting.forceStop"

        const val KEY_BOOT_APP_PKG = "bootStartAppPkg"

        private const val ACTION_REFRESH_NAV_BAR_STATUS =
            "com.android.systemui.statusbar.NAVIGATION_BAR_VISIBLE"

        private const val ACTION_CZUR_APP_RESUME = "czur.intent.action.APP_RESUME"
        private const val CATEGORY_LAUNCHER = "czur.intent.category.LAUNCHER"
    }

    private val appsVM: AppInfoViewModel by viewModels()
    private val keyCodeVM: KeyCodeVM by viewModels()
    private val shareViewModel: ShareViewModel by viewModels()
    private val shareByomModel: EshareByomStatusModel by viewModels()
    private val leftCustomViewModel: LeftCustomViewModel by viewModels()
    private val mainViewModel: LauncherMainViewModel by viewModels()

    private var currentPageIndex: Int = 0
    private val pagerAdapter = ScreenSlidePagerAdapter(this)

    private var forceStopFloat: SingleBtnCommonFloat? = null

    // hdmi的状态
    private val hdmiVM: HDMIViewModel by viewModels()

    private val navBarChangeReceiver: NavBarChangeReceiver by lazy {
        NavBarChangeReceiver()
    }

    private val taskList = listOf(
        ::disableOverseaApp,
    )

    private val wallpaperManager: WallpaperManager by lazy(LazyThreadSafetyMode.NONE) {
        WallpaperManager.getInstance(globalAppCtx)
    }

    override fun AtyParams.initAtyParams() {
        onScreenOffListener = {
            TipsPool.clearPop()
        }
    }

    override fun initWindow() {
        super.initWindow()
        updateWallpaper()
    }

    override fun ActivityLauncherMainBinding.initBindingViews() {
        val statusBarFragment = StatusBarFragment().apply {
            // 注入滚轮事件
            onVScrollListener = {
                launch {
                    injectionVScrollEvent(it)
                }
            }
        }
        // 状态栏
        supportFragmentManager.commit {
            replace(R.id.mainStatusBar, statusBarFragment, "StatusBarFragment")
        }

        mainPager.orientation = ORIENTATION_VERTICAL
        mainPager.adapter = pagerAdapter

        for (i in 0 until mainPager.childCount) {
            // 禁止掉 阴影效果
            (mainPager.getChildAt(i) as? RecyclerView)?.overScrollMode = View.OVER_SCROLL_NEVER
        }
        // 适配鼠标事件
        mainPager.adapterMouse()

        // 不调大这个, EditExt的焦点会乱...
        mainPager.offscreenPageLimit = 10

        mainIndicatorBar.setOnCheckedChangeListener { checkedPos ->
            mainPager.currentItem = checkedPos
        }

        mainPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                mainIndicatorBar.checkedIndex = position

                if (position == 1 && currentPageIndex == 0) {
                    // 从第0页, 翻转到第一页
                    keyCodeVM.onLauncherSlideUp()
                }

                if (position != 0) {
                    // 页面如果划走了, 那么就需要回复默认
                    leftCustomViewModel.resetPageToDef()
                    // 只有在第一页, 切换按钮才是可见的
                    leftCustomViewModel.changeIconEnable = false
                } else {
                    leftCustomViewModel.changeIconEnable = true
                }

                currentPageIndex = position
                if (position == 0) {
                    // 如果当前是第一页, 那么就会切换到卸载模式
                    appsVM.changeToNormalMode()
                }
            }

            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                if (position == 0) {
                    keyCodeVM.scrollTime.value = System.currentTimeMillis()
                }
            }
        })

        // 页面
        appsVM.pageSize.observe(this@LauncherMainActivity) {
            mainIndicatorBar.pageCount = it
            pagerAdapter.pageCount = it
        }

        lifecycle.addObserver(object : DefaultLifecycleObserver {

            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                ContextCompat.registerReceiver(
                    this@LauncherMainActivity,
                    navBarChangeReceiver,
                    IntentFilter(ACTION_REFRESH_NAV_BAR_STATUS),
                    ContextCompat.RECEIVER_EXPORTED
                )
                keyCodeVM.refreshNavBarStats()
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                // Launcher画面看不见时, 也需要切换回默认页面
                leftCustomViewModel.resetPageToDef()
                unregisterReceiver(navBarChangeReceiver)
                launch {
                    showDeviceNameAlertWindow(this@LauncherMainActivity, false)
                }

                clearAllFloat()
            }
        })
    }

    /**
     * 清理全部的弹窗
     */
    private fun clearAllFloat() {
        supportFragmentManager.fragments.forEach {
            if (it is FloatFragment) {
                it.dismiss()
            } else if (it is CZVBFloatingFragment<*>) {
                it.dismiss()
            }
        }
    }

    /**
     * 检查是否需要拉起HDMIIn
     */
    private suspend fun checkHDMIIn() {
        if (getBooleanSystemProp(KEY_LAUNCHER_BOOT_COMPLETE, false)) {
            // 已经启动完成, 不需要再去检查了
            return
        }
        logTagV(TAG, "Launcher 开机第一次启动, 检查HDMI")
        logTagV(TAG, "isEShareActive ${isEShareActive(this)}")
        logTagV(TAG, "hdmi state ${hdmiVM.hdmiStatus == HDMIIFStatus.IN}")
        logTagV(TAG, "混投开关 ${getBooleanSystemProp(KEY_HDMI_AUTO_OPEN, false)}")
        delay(500)//等待用户看清界面
        if (hdmiVM.hdmiStatus == HDMIIFStatus.IN) {
            setBooleanSystemProp(KEY_HDMI_STATE_IN, true)
            logTagV(TAG, "由Launcher启动HDMI应用")
            if (getBooleanSystemProp(
                    KEY_HDMI_AIRPLAY_OPEN,
                    false
                )
            ) {
                sendBroadCastToMirror(this@LauncherMainActivity, true)
            } else if (getBooleanSystemProp(KEY_HDMI_AUTO_OPEN, false)) {
                bootAppByAction(BootParam.ACTION_BOOT_HDMI)
            }
        } else {
            setBooleanSystemProp(KEY_HDMI_STATE_IN, false)
        }

    }

    /**
     * 展示强制停止的弹窗
     */
    private fun showForceStopFloat() {
        if (forceStopFloat != null) {
            // 当前已显示ForceStop
            logTagD(TAG, "当前已显示forceStopFloat, 不需要重复显示")
            return
        }
        setBooleanSystemProp(SYSTEM_PROP_STOP_FORCE, false)
        logTagV(TAG, "显示强制停止提示")
        forceStopFloat =
            SingleBtnCommonFloat(
                content = getString(R.string.dialog_content_stop_by_server),
                outSideDismiss = true
            ) {
                it.dismiss()
            }.apply {
                setOnDismissListener {
                    forceStopFloat = null
                }
                show()
            }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatOnResume {
            // 扫描App数据
            appsVM.loadApps()
        }

        launch {
            if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
                if (UserHandler.isLogin) {
                    UserHandler.logout()
                }
                taskList.forEach {
                    try {
                        logTagV(TAG, "执行海外禁止启动app:${it.name}")
                        it.invoke()
                    } catch (tr: Throwable) {
                        logTagW(TAG, "执行海外禁止启动app失败:${it.name}", tr = tr)
                    }
                }
            }
        }

        // 监听是否被踢出
        UserHandler.beenKickOutLive.observe(this) { beenKickOutlive ->
            if (beenKickOutlive) {
                logTagI(TAG, "用户被顶掉")
                SingleBtnCommonFloat(content = getString(R.string.dialog_content_kick_out)) { commonFloat ->
                    launch {
                        UserHandler.clearKickOut()
                    }
                    commonFloat.dismiss()
                }.show()
            }
        }

        launch {
            whenResumed {
                doOnBootComplete()
            }
        }

        repeatOnResume {
            doRepeatResume()
        }

        repeatCollectOnCreate (BackdropManager.eventFlow) {
            if (it == BackdropManager.BackdropEvent.CHANGE) {
                logTagD(TAG, "壁纸发生变化了,更新壁纸")
                updateWallpaper()
            }
        }
    }

    private suspend fun doOnBootComplete() {
        if (mainViewModel.hasInit) {
            logTagD(TAG, "已经初始化过了,无需再次初始化")
            return
        }
        mainViewModel.hasInit = true
        // Launcher启动完成后执行
        logTagD(TAG, "Launcher启动完成")
        // 设置初期设定结束
        setStartUpFalse()

        // Launcher启动, 打开EShare
        shareViewModel.startShareService()

        // 发送启动消息
        delay(500)  // 防止在Launcher启动过程中,做的事情太多
        shareByomModel.startShareService()

        logTagD(TAG, "发送Launcher启动消息")
        doWithoutCatch(tag = TAG, msg = "发送启动信息失败") {
            // 发送消息可能导致OTA的dialog弹出, 会让改协程销毁
            logTagD(TAG, "发送Launcher启动消息")
            NoticeHandler.sendMessage(
                MsgType(
                    MsgType.MODULE_BOOT,
                    MsgType.MODULE_BOOT_LAUNCHER
                )
            )
        }

        // 开机启动应用
        val bootStartAppPkg = intent.getStringExtra(KEY_BOOT_APP_PKG) ?: ""
        try {
            if (bootStartAppPkg.isNotEmpty()) {
                bootStartApp(bootStartAppPkg)
            } else {
                checkHDMIIn()
            }
        } finally {
            // 即使协程被取消, 也能正确将值设置上
            setBooleanSystemProp(KEY_LAUNCHER_BOOT_COMPLETE, true)
        }
    }

    private suspend fun doRepeatResume() {
        // 处理每次执行到onResume生命周期都执行的任务
        logTagD(TAG, "Launcher doRepeatResume")

        sendLauncherResumeBroadcast()   // 发送Launcher的Resume广播

        showDeviceNameAlertWindow(this@LauncherMainActivity, true)
        // 非会议状态打开eShare的声音
        logTagD(TAG, "==enableEShareVoice===")
        enableEShareVoice(<EMAIL>, true)

        // 如果是强制停止, 则显示提示窗
        val forceStop = withContext(Dispatchers.IO) {
            getBooleanSystemProp(SYSTEM_PROP_STOP_FORCE, false)
        }
        if (forceStop) {
            showForceStopFloat()
        }

        // 防止KeystoneService被杀死
        bootCZKeyStone()
    }

    private suspend fun bootCZKeyStone() = withContext(Dispatchers.Default) {
        logTagV(TAG, "防止自动对焦服务挂掉")
        val intent = Intent().apply {
            `package` = "com.czur.keystone"
            action = "com.czur.keystone.ACTION.START"
        }
        startService(intent)
    }

    private suspend fun bootStartApp(bootStartAppPkg: String) {
        // 开机启动应用
        logTagD(TAG, "开机启动应用:${bootStartAppPkg}")
        delay(ONE_SECOND * 6)   // 等待Launcher完全显示, 这样效果不突兀
        bootApp(bootStartAppPkg)
    }

    override fun onPause() {
        super.onPause()
        // 任何弹窗挡住了, 都会切换到正常模式
        appsVM.changeToNormalMode()
    }

    /**
     * 标记初期设定结束
     */
    private suspend fun setStartUpFalse() = withContext(Dispatchers.IO) {
        setBooleanSystemProp(KEY_STARTUP_COMPLETE, true)
    }

    /**
     * 移动画面到应用列表页面
     */
    fun moveToAppPadPage() {
        if (currentPageIndex != 1) {
            binding.mainPager.setCurrentItem(1, true)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            // 输入源选择, 触控板放在设备上, 都要切换到正常模式
            KEY_CODE_BTB_SEAT -> appsVM.changeToNormalMode()
        }

        if (dispatchKeyDown2Fragment(keyCode, event)) {
            // 分发事件给Fragment
            return true
        }
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 拦截返回键
            if (binding.mainPager.currentItem != 0) {
                // 回到主页
                binding.mainPager.setCurrentItem(0, true)
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (dispatchKeyUp2Fragment(keyCode, event)) {
            return true
        }
        return super.onKeyUp(keyCode, event)
    }


    private class ScreenSlidePagerAdapter(fa: AppCompatActivity) : FragmentStateAdapter(fa) {
        companion object {
            private const val MAIN_SCREEN = 0
            private const val LAUNCH_SCREEN = 1
        }

        var pageCount = 0
            set(value) {
                if (field == value) {
                    return
                }
                field = value
                notifyDataSetChanged()
            }

        override fun getItemCount(): Int {
            return pageCount
        }

        override fun getItemViewType(position: Int): Int {
            return when (position) {
                0 -> MAIN_SCREEN
                else -> LAUNCH_SCREEN
            }
        }

        override fun createFragment(position: Int): Fragment {
            return when (getItemViewType(position)) {
                MAIN_SCREEN -> LauncherMainFragment()
                else -> LaunchPadFragment.getInstance(position - 1)
            }
        }
    }

    private fun sendLauncherResumeBroadcast() {
        logTagD(TAG, "发送LauncherResume广播")
        val intent = Intent(ACTION_CZUR_APP_RESUME)
        intent.addCategory(CATEGORY_LAUNCHER)
        sendBroadcast(intent)
    }


    override fun onDestroy() {
        super.onDestroy()
        NoticeHandler.clearAll()
    }

    private inner class NavBarChangeReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == ACTION_REFRESH_NAV_BAR_STATUS) {
                keyCodeVM.refreshNavBarStats()
            }
        }

    }

    /**
     * 禁用海外部分app
     */
    private suspend fun disableOverseaApp() = withContext(Dispatchers.IO) {
        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            logTagI(TAG, "禁用 登录模块")
            val disableComponens = listOf(
                ComponentName(
                    "com.czur.starry.device.personalcenter",
                    "com.czur.starry.device.personalcenter.MainActivity"
                ),
                ComponentName(
                    "com.czur.starry.device.personalcenter",
                    "com.czur.starry.device.personalcenter.web.WebActivity"
                ),
                ComponentName(
                    "com.czur.starry.device.personalcenter",
                    "com.czur.starry.device.personalcenter.begin.UserBeginNavActivity"
                )
            )
            for (component in disableComponens) {
                packageManager.setComponentEnabledSetting(
                    component,
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                    PackageManager.DONT_KILL_APP
                )
                // 检查是否已经启动了
                while (checkAtyEnable(component)) {
                    delay(100)
                }
                logTagI(TAG, "登录模块 已成功禁用")
            }
        }
    }

    private fun checkAtyEnable(componentName: ComponentName): Boolean {
        return packageManager.getComponentEnabledSetting(componentName) == PackageManager.COMPONENT_ENABLED_STATE_ENABLED
    }

    private fun updateWallpaper() {
        wallpaperManager.drawable?.let {
            logTagV(TAG, "更新壁纸")
            window.setBackgroundDrawable(it)    // 直击使用系统的壁纸会有比例问题
        }
    }
}