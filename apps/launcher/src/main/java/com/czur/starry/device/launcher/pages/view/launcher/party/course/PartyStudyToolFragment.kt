package com.czur.starry.device.launcher.pages.view.launcher.party.course

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Bitmap
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.databinding.FragmentPartyStudyToolBinding
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus
import com.czur.starry.device.launcher.meeting.OtherMeetingFloat
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv

/**
 * Created by 陈丰尧 on 2023/2/6
 */
class PartyStudyToolFragment : CZViewBindingFragment<FragmentPartyStudyToolBinding>() {
    companion object {
        private const val TAG = "PartyStudyToolFragment"
        private const val ANIM_DURATION = 100L
        private const val ANIM_SCALE_RATIO = 1.07f
    }

    private val appVM: AppInfoViewModel by viewModels({ requireActivity() })
    private var isOtherAppFloatShowing = false

    /**
     * iconView
     */
    private val iconViews by lazy {
        listOf(
            NetAppIconView(
                binding.netMeetingAppIcon1,
                binding.netMeetingAppName1,
                binding.netMeetingApp1
            ),
            NetAppIconView(
                binding.netMeetingAppIcon2,
                binding.netMeetingAppName2,
                binding.netMeetingApp2
            ),
            NetAppIconView(
                binding.netMeetingAppIcon3,
                binding.netMeetingAppName3,
                binding.netMeetingApp3
            ),
            NetAppIconView(
                binding.netMeetingAppIcon4,
                binding.netMeetingAppName4,
                binding.netMeetingApp4
            ),
        )
    }

    override fun FragmentPartyStudyToolBinding.initBindingViews() {
        studyToolBgView.setOnDebounceClickListener {
            // 展示所有的学习工具弹窗
            if (isOtherAppFloatShowing) {
                logTagV(TAG, "已经展示弹窗了, 忽略")
                return@setOnDebounceClickListener
            }
            isOtherAppFloatShowing = true
            // 弹出视频会议弹窗
            launch {
                val bgImg = takeScreenShot(Bitmap.Config.ARGB_8888)?.blur(samplingRate = 4)
                OtherMeetingFloat(
                    bgImg,
                    getString(R.string.str_quick_boot_title_party_building_study_tool),
                    appVM.studyToolAppsLive
                )
                    .apply {
                        setOnDismissListener {
                            isOtherAppFloatShowing = false
                        }
                    }
                    .show()
            }
        }

        iconViews.forEachIndexed { index, netAppIconView ->
            netAppIconView.iconView.setDebounceTouchClickListener {
                bootOrInstallApp(index)
            }
            netAppIconView.iconView.setOnHoverListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_HOVER_EXIT -> scareIcon(view, false)
                    MotionEvent.ACTION_HOVER_ENTER -> scareIcon(view, true)
                }
                false
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        appVM.studyToolAppsLive.observe(this) { studyToolsApps ->
            val showApps = studyToolsApps
                .filter { it.status == NetMeetingAppStatus.INSTALL }
                .ifEmpty { studyToolsApps }
                .take(iconViews.size)

            repeat(iconViews.size) { index ->
                val appView = iconViews[index]
                showApps.getOrNull(index)?.let {
                    appView.groupView.show()
                    appView.iconView.updateIcon(it)
                    appView.nameView.text = it.appName
                } ?: kotlin.run {
                    appView.groupView.invisible()
                }

            }

        }
    }

    private fun scareIcon(view: View, scaleUp: Boolean) {
        view.pivotX = view.width.toFloat() / 2F
        view.pivotY = view.height.toFloat() / 2F
        if (scaleUp) {
            scaleUp(view)
        } else {
            scaleDown(view)
        }
    }

    /**
     * 放大
     */
    private fun scaleUp(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(
            view, "scaleX",
            ANIM_SCALE_RATIO
        )
        val objectAnimatorY = ObjectAnimator.ofFloat(
            view, "scaleY",
            ANIM_SCALE_RATIO
        )
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * 缩小
     */
    private fun scaleDown(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    private fun bootOrInstallApp(index: Int) {
        val clickApp = appVM.studyToolApps.getOrNull(index) ?: return
        when (clickApp.status) {
            NetMeetingAppStatus.INSTALL -> {
                logTagV(TAG, "启动App:${clickApp.appName}")
                bootApp(clickApp.pkgName)
            }
            NetMeetingAppStatus.UNINSTALL -> {
                if (clickApp.downloadProcess >= 0) {
                    logTagD(TAG, "${clickApp.appName}正在下载, 不做任何操作")
                } else {
                    logTagD(TAG, "安装app:${clickApp.appName}")
                    appVM.downloadNetMeetingApp(clickApp)
                }
            }
        }
    }


    /**
     * IconView
     */
    private data class NetAppIconView(
        val iconView: OtherQuickBootAppIconIv,
        val nameView: TextView,
        val groupView: Group
    )
}