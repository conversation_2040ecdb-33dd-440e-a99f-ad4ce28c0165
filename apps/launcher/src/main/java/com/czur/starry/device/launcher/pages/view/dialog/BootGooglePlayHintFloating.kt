package com.czur.starry.device.launcher.pages.view.dialog

import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.launcher.databinding.FloatingBootGooglePlayHintBinding

/**
 * Created by 陈丰尧 on 2025/4/14
 */
class BootGooglePlayHintFloating(
    private val onConfirmListener: (neverRemind: Boolean, floating: BootGooglePlayHintFloating) -> Unit
) : CZVBFloatingFragment<FloatingBootGooglePlayHintBinding>() {
    override fun FloatingBootGooglePlayHintBinding.initBindingViews() {
        confirmBtn.setOnClickListener {
            val neverRemind = neverRemindCb.isChecked
            onConfirmListener(neverRemind, this@BootGooglePlayHintFloating)
        }
    }
}