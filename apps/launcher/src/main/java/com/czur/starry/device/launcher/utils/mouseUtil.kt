package com.czur.starry.device.launcher.utils

import android.app.Activity
import android.view.MotionEvent
import android.widget.EditText
import androidx.core.view.forEach
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.keyboard.hideKeyboard
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2021/7/31
 * 用于处理鼠标冲突的工具类
 */

private const val TAG = "MouseUtil"

/**
 * 适配ViewPager2的鼠标事件
 */
private val flow = MutableSharedFlow<Float>()
private val scope = MainScope()

/**
 * 额外注入鼠标管轮事件
 */
suspend fun injectionVScrollEvent(value: Float) {
    flow.emit(value)
}

fun ViewPager2.adapterMouse() {
    scope.launch {
        flow
            // 600ms内只会响应一次翻页, 这个时间是翻页动画执行完成后,还能停顿一小下
            .sample(600) // 解决触控版会一直翻页的问题
            .collect { v ->
                if (v < 0) {
                    // 只有第一页有软键盘, 所以翻到第二页时, 消去输入框
                    val aty = context as Activity
                    (aty.currentFocus as? EditText)?.let {
                        hideKeyboard(it.windowToken)
                        logTagD(TAG, "滚轮触发翻页-收起软键盘")
                        delay(200)  // 等待软键盘收起结束
                    }
                }

                currentItem = if (v > 0) {
                    // 翻到前一页
                    currentItem - 1
                } else {
                    // 翻到后一页
                    currentItem + 1
                }
            }
    }


    forEach {
        if (it is RecyclerView) {
            it.setOnGenericMotionListener { _, event ->
                if (event?.action == MotionEvent.ACTION_SCROLL) {
                    // 拦截鼠标滚轮事件, 将其转化为viewPager的翻页事件
                    val v = event.getAxisValue(MotionEvent.AXIS_VSCROLL)
                    scope.launch {
                        flow.emit(v)
                    }
                    return@setOnGenericMotionListener true
                }
                false
            }
        }
    }
}
