package com.czur.starry.device.launcher.guide

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

/**
 * Created by 陈丰尧 on 2022/5/19
 */
@Dao
abstract class GuideInfoDao {

    /**
     * 获取今天打开的次数
     */
    @Query("select count(1) from tab_guide_info where showGuideDay = :todayTime and pkgName = :pkgName")
    abstract fun getTodayShowGuideCount(pkgName: String, todayTime: String): Int

    @Insert
    abstract fun insertOpenData(guideInfoEntity: GuideInfoEntity): Long

    @Query("select count(1) from tab_guide_info")
    abstract fun getTotalOpenTimes(): Int
}