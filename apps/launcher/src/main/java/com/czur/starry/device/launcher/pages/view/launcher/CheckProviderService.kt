package com.czur.starry.device.launcher.pages.view.launcher

import android.content.ContentValues
import android.content.Intent
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logV
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.makeUri
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2023/1/12
 */
private const val TAG = "CheckProviderService"
internal const val KEY_CHECK_PROVIDER = "vendor.czur.CheckProvider"

class CheckProviderService : LifecycleService() { // 这里尽量不要跟其他任何类产生关联
    private val authorityList = listOf(
        "com.czur.starry.device.noticecenter.notice.NoticeProvider",
        "com.czur.starry.device.personalcenter.provider.PersonalProvider",
        "com.czur.starry.device.update.otaprovider",
        "com.czur.starry.device.wallpaperdisplay.initProvider",
        "com.czur.starry.device.sharescreen.initProvider",
        "com.czur.starry.device.file.initProvider",
        "com.czur.starry.device.transcription.provider.TranscriptionProvider",
    )

    override fun onCreate() {
        super.onCreate()
        logTagI(TAG, "CheckProviderService onCreate")
        launch {
            checkProvider()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_STICKY
    }

    private suspend fun checkProvider() {
        val checkDelayTime = ONE_SECOND
        while (!isProviderPrepare()) {
            logTagW(TAG, "等待${checkDelayTime}ms后重新检查")
            delay(checkDelayTime)
        }
        setBooleanSystemProp(KEY_CHECK_PROVIDER, true)
        stopSelf()
        logTagI(TAG, "各个Provider 已经启动完成")
    }

    private fun isProviderPrepare(): Boolean {
        return try {
            val contentValues = ContentValues()
            authorityList.fold(true) { acc, authority ->
                logV(TAG, "开始检查:${authority}")
                acc && contentResolver.update(
                    makeUri(authority),
                    contentValues,
                    null,
                    null
                ) > Int.MIN_VALUE
            }
        } catch (exp: Throwable) {
            logTagW(TAG, "NoticeCenterProvider 还没有准备好", tr = exp)
            false
        }
    }
}