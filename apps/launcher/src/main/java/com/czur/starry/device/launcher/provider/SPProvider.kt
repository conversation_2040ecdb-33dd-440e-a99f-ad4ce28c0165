package com.czur.starry.device.launcher.provider

import android.content.ContentValues
import android.content.Context
import android.content.SharedPreferences
import android.content.UriMatcher
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import com.czur.czurutils.base.CZURContentProvider
import com.czur.starry.device.baselib.data.sp.SPHandler

class SPProvider: CZURContentProvider() {

    private lateinit var sp: SharedPreferences
    private lateinit var uriMatcher: UriMatcher

    override fun onCreate(): Boolean {
        sp = context!!.getSharedPreferences(SPHandler.SP_NAME, Context.MODE_PRIVATE)
        uriMatcher = UriMatcher(UriMatcher.NO_MATCH)
        uriMatcher.addURI(SPHandler.AUTHORITY, SPHandler.PATH_GET_STRING, SPHandler.CODE_GET_STRING)
        uriMatcher.addURI(SPHandler.AUTHORITY, SPHandler.PATH_GET_INT, SPHandler.CODE_GET_INT)
        uriMatcher.addURI(SPHandler.AUTHORITY, SPHandler.PATH_GET_LONG, SPHandler.CODE_GET_LONG)
        uriMatcher.addURI(SPHandler.AUTHORITY, SPHandler.PATH_GET_BOOLEAN, SPHandler.CODE_GET_BOOLEAN)
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return try {
            val result: Any? = when (uriMatcher.match(uri)) {
                SPHandler.CODE_GET_STRING -> sp.getString(selection, sortOrder)
                SPHandler.CODE_GET_INT -> sp.getInt(selection, sortOrder?.toInt() ?: -65535)
                SPHandler.CODE_GET_LONG -> sp.getLong(selection, sortOrder?.toLong() ?: -65535L)
                SPHandler.CODE_GET_BOOLEAN -> sp.getBoolean(selection, sortOrder?.toBoolean() ?: false)
                else -> null
            }
            val matrixCursor = MatrixCursor(arrayOf(SPHandler.RESULT_ROW_NAME))
            matrixCursor.addRow(arrayOf(result))
            matrixCursor
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        TODO("Not yet implemented")
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<out String>?): Int {
        return try {
            if (values?.keySet().isNullOrEmpty()){
                return 0
            }
            val key = values?.keySet()?.first()
            val value = values?.get(key)
            when (uriMatcher.match(uri)) {
                SPHandler.CODE_GET_STRING -> sp.edit().putString(key, value.toString()).apply()
                SPHandler.CODE_GET_INT -> sp.edit().putInt(key, value as Int).apply()
                SPHandler.CODE_GET_LONG -> sp.edit().putLong(key, value as Long).apply()
                SPHandler.CODE_GET_BOOLEAN -> sp.edit().putBoolean(key, value as Boolean).apply()
            }
            1
        } catch (e: Throwable) {
            e.printStackTrace()
            0
        }
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        TODO("Not yet implemented")
    }

    override fun getType(uri: Uri): String? {
        TODO("Not yet implemented")
    }
}