package com.czur.starry.device.launcher.pages.view.launcher.party.course.more

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.widget.GridSpaceItemDecoration
import com.czur.starry.device.launcher.databinding.FragmentPartyVideoMoreItemBinding
import com.czur.starry.device.launcher.inter.RefreshAble
import com.czur.starry.device.launcher.pages.view.launcher.party.course.PartyVideoViewModel

/**
 * Created by 陈丰尧 on 2023/2/10
 */
private const val KEY_PAGE_INDEX = "pageIndex"
private const val TAG = "PartyVideoMoreItemFragment"

class PartyVideoMoreItemFragment : CZViewBindingFragment<FragmentPartyVideoMoreItemBinding>(), RefreshAble {
    companion object {
        fun getInstance(currentPage: Int): PartyVideoMoreItemFragment {
            return PartyVideoMoreItemFragment().apply {
                arguments = bundleOf(KEY_PAGE_INDEX to currentPage)
            }
        }
    }

    private val videoMoreVM: PartyVideoMoreViewModel by viewModels({ requireParentFragment() })
    private val videoViewModel: PartyVideoViewModel by viewModels({ requireActivity() })

    private val itemAdapter = PartyVideoMoreItemAdapter(QUANTITY_PER_PAGE)
    private var currentIndex: Int = -1

    private var hasData = false // 是否已经有数据


    override fun FragmentPartyVideoMoreItemBinding.initBindingViews() {
        partyVideoMoreRv.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = itemAdapter
            addItemDecoration(GridSpaceItemDecoration(2, 30, 40))
            doOnItemClick { vh, view ->
                val position = vh.bindingAdapterPosition
                val data = itemAdapter.getData(position)
                videoViewModel.bootFileManagerShowVideo(requireContext(), data)
                true
            }
        }
    }

    override fun onResume() {
        super.onResume()
        refresh()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        currentIndex = requireArguments().getInt(KEY_PAGE_INDEX)
    }

    override fun refresh() {
        if (hasData) return // 已经有数据了, 就不再请求数据了
        launch {
            binding.progressBar.show()
            binding.noNetworkGroup.gone()

            val itemData = videoMoreVM.loadPartyVideoWithPlaceHolder(currentIndex)
            binding.progressBar.gone()
            if (itemData.isEmpty) {
                logTagW(TAG, "currentPage:${currentIndex}, 请求党建课程失败")
                hasData = false
                binding.noNetworkGroup.show()
            } else {
                hasData = true
                binding.noNetworkGroup.gone()
            }
            itemAdapter.setData(itemData.videoItems ?: emptyList())
        }
    }
}