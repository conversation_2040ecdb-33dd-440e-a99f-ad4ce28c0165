package com.czur.starry.device.launcher.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.PointerIcon;
import android.view.View;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.core.content.res.ResourcesCompat;

import com.czur.starry.device.launcher.R;

import java.util.HashMap;

/**
 * Created by 陈丰尧 on 2/20/21
 */
public class IndicatorBar extends RadioGroup implements View.OnClickListener {
    private int pageCount = 2; //默认是2页面
    private int checkedIndex = 0; //默认选中第一页
    private OnCheckedChangeListener onCheckedChangeListener;

    private final HashMap<Integer, Integer> idMap = new HashMap<>();

    public IndicatorBar(Context context) {
        super(context);
        init();
    }

    public IndicatorBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        // 竖向排列
        setOrientation(VERTICAL);
        // 在中间设置分割线
        setShowDividers(SHOW_DIVIDER_MIDDLE);
        // 设置分割线样式
        Drawable divider = ResourcesCompat.getDrawable(getResources(), R.drawable.divider_indicator, null);
        setDividerDrawable(divider);

        updateIndexView();
    }

    private void updateIndexView() {
        //
        idMap.clear();
        removeAllViews();
        for (int i = 0; i < pageCount; i++) {
            View item = LayoutInflater.from(getContext()).inflate(R.layout.item_indicator_view, this, false);
            int id = View.generateViewId();
            item.setId(id);
            idMap.put(id, i);
            addView(item);
            item.setOnClickListener(this);
            item.setPointerIcon(PointerIcon.getSystemIcon(getContext(), PointerIcon.TYPE_ARROW));
        }
        setCheckedIndex(checkedIndex);
    }

    public void setOnCheckedChangeListener(OnCheckedChangeListener onCheckedChangeListener) {
        this.onCheckedChangeListener = onCheckedChangeListener;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void setPageCount(int pageCount) {
        if (this.pageCount == pageCount) {
            return;
        }
        this.pageCount = pageCount;
        updateIndexView();
    }

    public int getCheckedIndex() {
        return checkedIndex;
    }

    public void setCheckedIndex(int selectIndex) {
        if (selectIndex < getPageCount()) {
            this.checkedIndex = selectIndex;
            RadioButton rb = (RadioButton) getChildAt(selectIndex);
            if (rb != null) {
                rb.setChecked(true);
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (onCheckedChangeListener != null) {
            int index = idMap.getOrDefault(v.getId(), 0);
            onCheckedChangeListener.onCheckedChanged(index);
        }
    }

    /**
     * 选中状态改变的监听
     */
    public interface OnCheckedChangeListener {
        void onCheckedChanged(int checkedPos);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        // 修改点击识别范围
        MotionEvent me = MotionEvent.obtain(ev.getDownTime(), ev.getEventTime(), ev.getAction(), getWidth() / 2F, ev.getY(), ev.getMetaState());
        return super.dispatchTouchEvent(me);
    }

}
