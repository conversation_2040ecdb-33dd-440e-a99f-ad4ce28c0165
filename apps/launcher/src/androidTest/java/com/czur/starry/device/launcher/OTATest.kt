package com.czur.starry.device.launcher

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.czur.starry.device.otalib.OTAHandler

import org.junit.Test
import org.junit.runner.RunWith

import org.junit.Assert.*

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class OTATest {
    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.czur.starry.device.launcher", appContext.packageName)
    }

    @Test
    fun otaOnTestOn(){
        OTAHandler.newVersionStatus = true
        assertEquals(OTAHandler.newVersionStatus, true)
    }

    @Test
    fun otaOnTestOff(){
        OTAHandler.newVersionStatus = false
        assertEquals(OTAHandler.newVersionStatus, false)
    }
}