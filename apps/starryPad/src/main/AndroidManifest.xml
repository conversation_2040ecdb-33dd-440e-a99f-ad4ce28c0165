<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 蓝牙 -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <!--  悬浮窗  -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />

    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true"
        android:theme="@style/StarryPadTheme">
        <activity
            android:name=".ui.aty.MainActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="true">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.czur.starry.device.starrypad.BOOT_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!--    设置页面    -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:configChanges="${atyPlaceHolder}" />

        <!--    OTA页面    -->
        <activity
            android:name=".ui.ota.WPOtaActivity"
            android:configChanges="${atyPlaceHolder}" />

        <activity
            android:name=".ui.dialog.WPVersionIllegalDialog"
            android:configChanges="${atyPlaceHolder}"
            android:launchMode="singleInstance"
            android:theme="@style/DialogActivity" />

        <service
            android:name=".ui.window.GlobalMarkService"
            android:exported="true" />
        <service android:name=".ui.window.PaletteService" />
        <service
            android:name=".hardware.WritePadService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.starry.device.starrypad.BOOT_SERVICE" />
            </intent-filter>
        </service>

        <!--    分享悬浮窗    -->
        <service android:name=".ui.window.ShareAlertWindow" />

        <provider
            android:name=".provider.StarryPadPaintProvider"
            android:authorities="com.czur.starry.device.starrypad.provider.StarryPadPaintProvider"
            android:exported="true" />
    </application>

</manifest>