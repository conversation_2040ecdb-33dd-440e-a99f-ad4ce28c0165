package com.czur.starry.device.starrypad.ui.settings.detail

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.base.v2.fragment.startActivity
import com.czur.starry.device.baselib.utils.addItemDecoration
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.common.WPOTAConstant
import com.czur.starry.device.starrypad.databinding.FragmentWritePadMultiDeviceBinding
import com.czur.starry.device.starrypad.devinfo.WPDevInfo
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.ui.ota.WPOtaActivity
import com.czur.starry.device.starrypad.ui.settings.WritePadDevInfoViewModel

/**
 * Created by 陈丰尧 on 2023/12/6
 */
private const val TAG = "WritePadMultiDeviceFragment"

class WritePadMultiDeviceFragment : CZViewBindingFragment<FragmentWritePadMultiDeviceBinding>() {
    private val wpViewModel: WritePadDevInfoViewModel by activityViewModels()

    private val devAdapter = WPMultiDevAdapter()


    override fun FragmentWritePadMultiDeviceBinding.initBindingViews() {
        wpDeviceRv.closeDefChangeAnimations()
        wpDeviceRv.addItemDecoration(RecyclerView.VERTICAL, 15)
        wpDeviceRv.adapter = devAdapter

        wpDeviceRv.doOnItemClick { vh, view ->
            val info = devAdapter.getData(vh.bindingAdapterPosition)
            when (view.id) {
                R.id.wpDevRemoveIv -> {
                    logTagD(TAG, "点击了解绑按钮")
                    WPDevDetailFloatFragment().apply {
                        onBtnClick = { btn, devInfo ->
                            when (btn) {
                                WPDevDetailFloatFragment.BTN_DEL -> {
                                    logTagD(TAG, "点击了取消配对按钮: ${devInfo.name}")
                                    WPDeviceInfoManager.delDev(devInfo)
                                }
                            }
                        }
                    }.show(
                        args = bundleOf(
                            WPDevDetailFloatFragment.KEY_DEV_NAME to info.name
                        )
                    )
                    true
                }

                R.id.wpDevUpgradeIv -> {
                    logTagD(TAG, "点击了升级按钮")
                    onUpgradeClick(info)
                    true
                }

                else -> false
            }

        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnCreate(wpViewModel.writePadInfoListFlow) {
            logTagD(TAG, "手写板设备信息更新")
            devAdapter.setData(it)
        }
    }

    private fun onUpgradeClick(info: WPDevInfo) {
        WritePadReleaseNoteFloatFragment()
            .apply {
                onBtnClick = { devInfo ->
                    this.dismiss()
                    logTagD(TAG, "点击了升级按钮")
                    startUpgradeDev(devInfo)
                }
            }
            .show(
                args = bundleOf(
                    WritePadReleaseNoteFloatFragment.KEY_DEV_NAME to info.name
                )
            )
    }

    private fun startUpgradeDev(info: WPDevInfo) {
        logTagD(TAG, "startUpgradeDev")

        val md5 = info.otaInfo?.md5
        val downUrl = info.otaInfo?.downloadUrl
        val fileSize = info.otaInfo?.fileSize ?: 1L
        val batteryLevel = info.batteryLevel
        // 判断电量
        if (batteryLevel < WPOTAConstant.OTA_MINIMUM_BATTERY_CAPACITY) {
            logTagW(TAG, "电量低于${WPOTAConstant.OTA_MINIMUM_BATTERY_CAPACITY}%, 无法升级")
            toast(
                R.string.toast_write_pad_ota_low_power,
                WPOTAConstant.OTA_MINIMUM_BATTERY_CAPACITY
            )
            return
        }

        // 判断剩余空间是否可以下载
        val freeSpace =
            requireContext().cacheDir.freeSpace
        if (freeSpace < WPOTAConstant.OTA_MINIMUM_RESERVED_STORAGE_CAPACITY) { // 需要给系统留下冗余空间, 不能占满
            logTagW(TAG, "剩余空间不足, 无法升级, 剩余空间:${freeSpace}, 文件大小:${fileSize}")
            toast(R.string.toast_write_pad_ota_no_space)
            return
        } else {
            logTagD(TAG, "剩余空间充足, 可以升级, 剩余空间:${freeSpace}, 文件大小:${fileSize}")
        }


        if (md5.isNullOrEmpty() || downUrl.isNullOrEmpty() || info.devID.isNullOrEmpty()) {
            logTagW(
                TAG,
                "不满足升级条件: md5:${md5}, downUrl:${downUrl}, devID:${info.devID}"
            )
            return
        }

        startActivity<WPOtaActivity>(
            WPOtaActivity.KEY_MD5 to md5,
            WPOtaActivity.KEY_DOWN_URL to downUrl,
            WPOtaActivity.KEY_DEV_ID to info.devID,
            WPOtaActivity.KEY_FILE_SIZE to fileSize
        )
    }
}