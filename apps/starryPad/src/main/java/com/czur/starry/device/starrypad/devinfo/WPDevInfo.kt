package com.czur.starry.device.starrypad.devinfo

import android.os.Parcelable
import com.czur.starry.device.starrypad.net.WPOTAServerInfo
import kotlinx.parcelize.Parcelize

/**
 * Created by 陈丰尧 on 2023/12/4
 */
@Parcelize
data class WPDevInfo(
    val name: String,
    val version: String = "",
    val batteryLevel: Int = -1,      // 电量
    val screenOn: Boolean = false,      // 屏幕是否亮着
    val isClientOTARunning: Boolean = false, // 是否正在进行OTA
    val connected: Boolean = false,     // netty连接上了算连接, 因为OTA等功能必须依赖netty
    val devID: String? = null,         // 设备ID, 连接上了才能获取到
    val otaInfo: WPOTAServerInfo? = null
) : Parcelable {
    val canUpgrade: Boolean
        get() = connected && otaInfo?.canUpdate == true
}