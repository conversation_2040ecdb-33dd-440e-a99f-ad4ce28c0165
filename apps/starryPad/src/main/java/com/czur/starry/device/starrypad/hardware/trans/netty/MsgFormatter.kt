package com.czur.starry.device.starrypad.hardware.trans.netty

// contributors: l<PERSON><PERSON><PERSON>: proposed special treatment of array parameter values
// <PERSON><PERSON>: pointed out double[] omission, suggested deep array copy
/**
 * Formats messages according to very simple substitution rules. Substitutions
 * can be made 1, 2 or more arguments.
 *
 *
 *
 *
 * For example,
 *
 *
 * <pre>
 * MessageFormatter.format(&quot;Hi {}.&quot;, &quot;there&quot;)
</pre> *
 *
 *
 * will return the string "Hi there.".
 *
 *
 * The {} pair is called the *formatting anchor*. It serves to designate
 * the location where arguments need to be substituted within the message
 * pattern.
 *
 *
 * In case your message contains the '{' or the '}' character, you do not have
 * to do anything special unless the '}' character immediately follows '{'. For
 * example,
 *
 *
 * <pre>
 * MessageFormatter.format(&quot;Set {1,2,3} is not equal to {}.&quot;, &quot;1,2&quot;);
</pre> *
 *
 *
 * will return the string "Set {1,2,3} is not equal to 1,2.".
 *
 *
 *
 *
 * If for whatever reason you need to place the string "{}" in the message
 * without its *formatting anchor* meaning, then you need to escape the
 * '{' character with '\', that is the backslash character. Only the '{'
 * character should be escaped. There is no need to escape the '}' character.
 * For example,
 *
 *
 * <pre>
 * MessageFormatter.format(&quot;Set \\{} is not equal to {}.&quot;, &quot;1,2&quot;);
</pre> *
 *
 *
 * will return the string "Set {} is not equal to 1,2.".
 *
 *
 *
 *
 * The escaping behavior just described can be overridden by escaping the escape
 * character '\'. Calling
 *
 *
 * <pre>
 * MessageFormatter.format(&quot;File name is C:\\\\{}.&quot;, &quot;file.zip&quot;);
</pre> *
 *
 *
 * will return the string "File name is C:\file.zip".
 *
 *
 *
 *
 * The formatting conventions are different than those of [MsgFormatter]
 * which ships with the Java platform. This is justified by the fact that
 * SLF4J's implementation is 10 times faster than that of [MsgFormatter].
 * This local performance difference is both measurable and significant in the
 * larger context of the complete logging processing chain.
 *
 *
 *
 *
 * See also [.format],
 * [.format] and
 * [.arrayFormat] methods for more details.
 */
internal object MsgFormatter {
    private const val DELIM_STR = "{}"
    private const val ESCAPE_CHAR = '\\'

    /**
     * Performs single argument substitution for the 'messagePattern' passed as
     * parameter.
     *
     *
     * For example,
     *
     *
     * <pre>
     * MessageFormatter.format(&quot;Hi {}.&quot;, &quot;there&quot;);
    </pre> *
     *
     *
     * will return the string "Hi there.".
     *
     *
     *
     * @param messagePattern The message pattern which will be parsed and formatted
     * @param arg            The argument to be substituted in place of the formatting anchor
     * @return The formatted message
     */
    fun format(messagePattern: String?, arg: Any?): FormattingTuple {
        return arrayFormat(messagePattern, if (arg == null) null else arrayOf(arg))
    }

    /**
     * Performs a two argument substitution for the 'messagePattern' passed as
     * parameter.
     *
     *
     * For example,
     *
     *
     * <pre>
     * MessageFormatter.format(&quot;Hi {}. My name is {}.&quot;, &quot;Alice&quot;, &quot;Bob&quot;);
    </pre> *
     *
     *
     * will return the string "Hi Alice. My name is Bob.".
     *
     * @param messagePattern The message pattern which will be parsed and formatted
     * @param argA           The argument to be substituted in place of the first formatting
     * anchor
     * @param argB           The argument to be substituted in place of the second formatting
     * anchor
     * @return The formatted message
     */
    fun format(
        messagePattern: String?,
        argA: Any?, argB: Any?
    ): FormattingTuple {
        return arrayFormat(messagePattern, arrayOf(argA, argB))
    }

    /**
     * Same principle as the [.format] and
     * [.format] methods except that any number of
     * arguments can be passed in an array.
     *
     * @param messagePattern The message pattern which will be parsed and formatted
     * @param argArray       An array of arguments to be substituted in place of formatting
     * anchors
     * @return The formatted message
     */
    fun arrayFormat(
        messagePattern: String?,
        argArray: Array<out Any?>?
    ): FormattingTuple {
        if (argArray.isNullOrEmpty()) {
            return FormattingTuple(messagePattern, null)
        }
        val lastArrIdx = argArray.size - 1
        val lastEntry = argArray[lastArrIdx]
        val throwable = if (lastEntry is Throwable) lastEntry else null
        if (messagePattern == null) {
            return FormattingTuple(null, throwable)
        }
        var j = messagePattern.indexOf(DELIM_STR)
        if (j == -1) {
            // this is a simple string
            return FormattingTuple(messagePattern, throwable)
        }
        val sbuf = StringBuilder(messagePattern.length + 50)
        var i = 0
        var l = 0
        do {
            var notEscaped = j == 0 || messagePattern[j - 1] != ESCAPE_CHAR
            if (notEscaped) {
                // normal case
                sbuf.append(messagePattern, i, j)
            } else {
                sbuf.append(messagePattern, i, j - 1)
                // check that escape char is not is escaped: "abc x:\\{}"
                notEscaped = j >= 2 && messagePattern[j - 2] == ESCAPE_CHAR
            }
            i = j + 2
            if (notEscaped) {
                deeplyAppendParameter(sbuf, argArray[l], null)
                l++
                if (l > lastArrIdx) {
                    break
                }
            } else {
                sbuf.append(DELIM_STR)
            }
            j = messagePattern.indexOf(DELIM_STR, i)
        } while (j != -1)

        // append the characters following the last {} pair.
        sbuf.append(messagePattern, i, messagePattern.length)
        return FormattingTuple(sbuf.toString(), if (l <= lastArrIdx) throwable else null)
    }

    // special treatment of array values was suggested by 'lizongbo'
    private fun deeplyAppendParameter(
        sbuf: StringBuilder, o: Any?,
        seenSet: MutableSet<Array<*>>?
    ) {
        if (o == null) {
            sbuf.append("null")
            return
        }
        val objClass: Class<*> = o.javaClass
        if (!objClass.isArray) {
            if (Number::class.java.isAssignableFrom(objClass)) {
                // Prevent String instantiation for some number types
                when (objClass) {
                    Long::class.java -> {
                        sbuf.append(o as Long?)
                    }
                    Int::class.java, Short::class.java, Byte::class.java -> {
                        sbuf.append((o as Number).toInt())
                    }
                    Double::class.java -> {
                        sbuf.append(o as Double?)
                    }
                    Float::class.java -> {
                        sbuf.append(o as Float?)
                    }
                    else -> {
                        safeObjectAppend(sbuf, o)
                    }
                }
            } else {
                safeObjectAppend(sbuf, o)
            }
        } else {
            // check for primitive array types because they
            // unfortunately cannot be cast to Object[]
            sbuf.append('[')
            when (objClass) {
                BooleanArray::class.java -> {
                    booleanArrayAppend(sbuf, o as BooleanArray)
                }
                ByteArray::class.java -> {
                    byteArrayAppend(sbuf, o as ByteArray)
                }
                CharArray::class.java -> {
                    charArrayAppend(sbuf, o as CharArray)
                }
                ShortArray::class.java -> {
                    shortArrayAppend(sbuf, o as ShortArray)
                }
                IntArray::class.java -> {
                    intArrayAppend(sbuf, o as IntArray)
                }
                LongArray::class.java -> {
                    longArrayAppend(sbuf, o as LongArray)
                }
                FloatArray::class.java -> {
                    floatArrayAppend(sbuf, o as FloatArray)
                }
                DoubleArray::class.java -> {
                    doubleArrayAppend(sbuf, o as DoubleArray)
                }
                else -> {
                    (o as? Array<*>)?.let {
                        objectArrayAppend(sbuf, it, seenSet)
                    }
                }
            }
            sbuf.append(']')
        }
    }

    private fun safeObjectAppend(sbuf: StringBuilder, o: Any) {
        try {
            val oAsString = o.toString()
            sbuf.append(oAsString)
        } catch (t: Throwable) {
            System.err
                .println(
                    "SLF4J: Failed toString() invocation on an object of type ["
                            + o.javaClass.name + ']'
                )
            t.printStackTrace()
            sbuf.append("[FAILED toString()]")
        }
    }

    private fun objectArrayAppend(
        sbuf: StringBuilder,
        a: Array<*>,
        seenSet: MutableSet<Array<*>>?
    ) {
        if (a.isEmpty()) {
            return
        }
        val set = seenSet ?: HashSet(a.size)

        if (set.add(a)) {
            deeplyAppendParameter(sbuf, a[0], set)
            for (i in 1 until a.size) {
                sbuf.append(", ")
                deeplyAppendParameter(sbuf, a[i], set)
            }
            // allow repeats in siblings
            set.remove(a)
        } else {
            sbuf.append("...")
        }
    }

    private fun booleanArrayAppend(sbuf: StringBuilder, a: BooleanArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0])
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i])
        }
    }

    private fun byteArrayAppend(sbuf: StringBuilder, a: ByteArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0].toInt())
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i].toInt())
        }
    }

    private fun charArrayAppend(sbuf: StringBuilder, a: CharArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0])
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i])
        }
    }

    private fun shortArrayAppend(sbuf: StringBuilder, a: ShortArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0].toInt())
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i].toInt())
        }
    }

    private fun intArrayAppend(sbuf: StringBuilder, a: IntArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0])
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i])
        }
    }

    private fun longArrayAppend(sbuf: StringBuilder, a: LongArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0])
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i])
        }
    }

    private fun floatArrayAppend(sbuf: StringBuilder, a: FloatArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0])
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i])
        }
    }

    private fun doubleArrayAppend(sbuf: StringBuilder, a: DoubleArray) {
        if (a.isEmpty()) {
            return
        }
        sbuf.append(a[0])
        for (i in 1 until a.size) {
            sbuf.append(", ")
            sbuf.append(a[i])
        }
    }
}

internal class FormattingTuple(val message: String?, val throwable: Throwable?)
