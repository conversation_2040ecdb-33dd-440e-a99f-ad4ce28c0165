package com.czur.starry.device.starrypad.common

import com.czur.starry.writepadlib.proto.WPTransferData

/**
 * Created by 陈丰尧 on 2023/11/7
 * 画板基础信息
 */
data class BoardBasicInfo(
    val zoomFloor: Int,     // 最小缩放值
    val zoomCeil: Int,      // 最大缩放值
    val boardWidth: Int,    // 画板宽度
    val boardHeight: Int,   // 画板高度
) {
    var boardID: String = ""
        private set

    companion object {
        private val markInfo by lazy(LazyThreadSafetyMode.NONE) {
            BoardBasicInfo(
                zoomFloor = WPDrawConstant.ZOOM_FLOOR_MARK,
                zoomCeil = WPDrawConstant.ZOOM_CEIL_MARK,
                boardWidth = WPDrawConstant.MARK_100_WIDTH,
                boardHeight = WPDrawConstant.MARK_100_HEIGHT,
            )
        }

        private val paletteInfo by lazy {
            BoardBasicInfo(
                zoomFloor = WPDrawConstant.ZOOM_FLOOR_PALETTE,
                zoomCeil = WPDrawConstant.ZOOM_CEIL_PALETTE,
                boardWidth = WPDrawConstant.PALETTE_100_WIDTH * WPDrawConstant.ZOOM_BASIC / WPDrawConstant.ZOOM_FLOOR_PALETTE,
                boardHeight = WPDrawConstant.PALETTE_100_HEIGHT * WPDrawConstant.ZOOM_BASIC / WPDrawConstant.ZOOM_FLOOR_PALETTE,
            )
        }

        fun boardInfo(mode: WPTransferData.Mode, boardID: String): BoardBasicInfo {
            return when (mode) {
                WPTransferData.Mode.MODE_MARK -> markInfo.apply {
                    this.boardID = boardID
                }
                WPTransferData.Mode.MODE_PALETTE -> paletteInfo.apply {
                    this.boardID = boardID
                }
                else -> throw IllegalArgumentException("不支持的模式")
            }
        }
    }
}