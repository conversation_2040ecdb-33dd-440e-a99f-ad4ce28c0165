package com.czur.starry.device.starrypad.util

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Rect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import kotlin.math.max

/**
 * Created by 陈丰尧 on 2023/11/6
 */
object BitmapUtil {
    private const val TAG = "BitmapUtil"
    private const val CONTENT_EDGE_PADDING = 10
    private const val CONTENT_WIDTH_MIN = 1920
    private const val CONTENT_HEIGHT_MIN = 1080

    /**
     * 合并图片
     */
    suspend fun margeBitmap(
        drawImg: Bitmap,
        bgBitmap: Bitmap?,
    ): Bitmap {
        return withContext(Dispatchers.IO) {
            val completeImg =
                Bitmap.createBitmap(drawImg.width, drawImg.height, Bitmap.Config.RGB_565)
            val canvas = Canvas(completeImg)
            canvas.drawColor(Color.WHITE)
            bgBitmap?.let {
                canvas.drawBitmap(it, 0F, 0F, null)
            }
            canvas.drawBitmap(drawImg, 0F, 0F, null)
            completeImg
        }
    }

    suspend fun getContentRect(bitmap: Bitmap, rawWidth: Int, rawHeight: Int): Rect =
        withContext(Dispatchers.IO) {
            val sampleSize = rawWidth / bitmap.width
            // 获取有内容的范围
            var left = 0
            var top = 0
            var right = bitmap.width - 1
            var bottom = bitmap.height - 1

            // 左
            for (i in 0 until bitmap.width) {
                for (j in 0 until bitmap.height) {
                    if (bitmap.getPixel(i, j) != 0) {
                        left = i
                        break
                    }
                }
                if (left != 0) {
                    break
                }
            }

            // 右
            for (i in bitmap.width - 1 downTo left) {
                for (j in 0 until bitmap.height) {
                    if (bitmap.getPixel(i, j) != 0) {
                        right = i
                        break
                    }
                }
                if (right != bitmap.width - 1) {
                    break
                }
            }

            // 上
            for (j in 0 until bitmap.height) {
                for (i in left..right) {
                    if (bitmap.getPixel(i, j) != 0) {
                        top = j
                        break
                    }
                }
                if (top != 0) {
                    break
                }
            }

            // 下
            for (j in bitmap.height - 1 downTo top) {
                for (i in left..right) {
                    if (bitmap.getPixel(i, j) != 0) {
                        bottom = j
                        break
                    }
                }
                if (bottom != bitmap.height - 1) {
                    break
                }
            }

            left = max(0, left - CONTENT_EDGE_PADDING) * sampleSize
            top = max(0, top - CONTENT_EDGE_PADDING) * sampleSize
            right = max(0, right + CONTENT_EDGE_PADDING) * sampleSize
            bottom = max(0, bottom + CONTENT_EDGE_PADDING) * sampleSize

            val dW = right - left
            if (dW < CONTENT_WIDTH_MIN) {
                val d = (CONTENT_WIDTH_MIN - dW) / 2
                left -= d
                right += d
                if (left < 0) {
                    right -= left
                    left = 0
                }
                if (right > rawWidth) {
                    left -= right - rawWidth
                    right = rawWidth
                }
            }

            val dH = bottom - top
            if (dH < CONTENT_HEIGHT_MIN) {
                val d = (CONTENT_HEIGHT_MIN - dH) / 2
                top -= d
                bottom += d
                if (top < 0) {
                    bottom -= top
                    top = 0
                }
                if (bottom > rawHeight) {
                    top -= bottom - rawHeight
                    bottom = rawHeight
                }
            }
            Rect(left, top, right, bottom)
        }
}

/**
 * 将bitmap转换为字节数组
 */
suspend fun Bitmap.toBytes(
    format: Bitmap.CompressFormat = Bitmap.CompressFormat.WEBP_LOSSLESS,
    quality: Int = 100
): ByteArray = withContext(Dispatchers.Default) {
    ByteArrayOutputStream().use {
        compress(format, quality, it)
        it.toByteArray()
    }
}