package com.czur.starry.device.starrypad.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.global.globalAppCtx
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import kotlinx.coroutines.flow.first

/**
 * Created by 陈丰尧 on 2024/11/15
 */
object SettingRepository {
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
        "StarryPadSettings",
        corruptionHandler = createDefCorruptionHandler("StarryPadSettings")
    )

    private val dataStore: DataStore<Preferences> by lazy {
        globalAppCtx.dataStore
    }

    private val DS_KEY_SHARE_WHEN_EXIT = booleanPreferencesKey("shareWhenExit")

    suspend fun shareWhenExit(): Boolean {
        return dataStore.data.first()[DS_KEY_SHARE_WHEN_EXIT] ?: false
    }

    suspend fun setShareWhenExit(value: Boolean) {
        dataStore.edit {
            it[DS_KEY_SHARE_WHEN_EXIT] = value
        }
    }

}