package com.czur.starry.device.starrypad.hardware.trans.netty

import com.czur.czurutils.log.logTagV
import com.czur.starry.device.starrypad.BuildConfig
import com.czur.starry.writepadlib.proto.WPTransferData.NettyMessage
import com.czur.starry.writepadlib.util.toDebugStr
import com.czur.starry.writepadlib.util.toReleaseStr
import io.netty.channel.ChannelHandlerContext
import io.netty.handler.codec.MessageToMessageCodec

/**
 * Created by 陈丰尧 on 2023/12/26
 */
private const val TAG = "WPNettyLog"

class NettyDataLogChannel : MessageToMessageCodec<NettyMessage, NettyMessage>() {
    override fun encode(ctx: ChannelHandlerContext?, msg: NettyMessage?, out: MutableList<Any>?) {
        msg?.let {
            logTagV(TAG, "发送消息: ${getLogStr(it, BuildConfig.DEBUG)}")
            out?.add(it)
        }
    }

    override fun decode(ctx: ChannelHandlerContext?, msg: NettyMessage?, out: MutableList<Any>?) {
        msg?.let {
            logTagV(TAG, "接收消息: ${getLogStr(it, BuildConfig.DEBUG)}")
            out?.add(it)
        }
    }

    private fun getLogStr(message: NettyMessage, isDebug: Boolean): String {
        return if (isDebug) {
            message.toDebugStr()
        } else {
            message.toReleaseStr()
        }
    }
}