package com.czur.starry.device.starrypad.hardware

import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.ui.dialog.WPVersionIllegalDialog
import com.czur.starry.writepadlib.proto.WPTransferData.Mode
import com.czur.starry.writepadlib.proto.WPTransferData.ModeChangeReq
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2022/10/13
 * 手写板模式管理类
 */
object WritePadDeviceModeManager : IRemoteMsgHelper by RemoteMsgHelper() {
    private const val TAG = "WritePadDeviceManager"

    // 当前模式 默认是鼠标模式
    private val _serverModeFlow = MutableStateFlow(Mode.MODE_MOUSE)
    val serverModeFlow = _serverModeFlow.asStateFlow()
    val serverMode: Mode
        get() = serverModeFlow.value

    private val _serverModeRequestFlow = MutableStateFlow<Mode?>(null)
    val serverModeReqFlow = _serverModeRequestFlow.asStateFlow()
    val isBootingDrawService: Boolean
        get() = serverMode == Mode.MODE_MARK || serverMode == Mode.MODE_PALETTE

    /**
     * 准备中的模式
     */
    private var isModeChangePreparing: Boolean = false

    fun startModeChangePreparing() {
        isModeChangePreparing = true
    }

    fun resetServerMode() {
        logTagV(TAG, "将模式重置为鼠标模式")
        updateServerMode(Mode.MODE_MOUSE)
    }

    fun updateServerMode(mode: Mode) {
        logTagV(TAG, ":将模式设置为:${mode}")
        isModeChangePreparing = false
        _serverModeFlow.value = mode
        _serverModeRequestFlow.value = null // 清空请求信息
    }

    fun updateClientModeReq(clientModeReq: ModeChangeReq) {
        if (clientModeReq.mode == Mode.MODE_PALETTE || clientModeReq.mode == Mode.MODE_MARK) {
            logTagV(TAG, "updateClientCMD: $clientModeReq.mode")
            if (!CZPowerManager.isScreenOn()) {
                logTagI(TAG, "会议星在休眠, 仅做屏幕唤醒")
                CZPowerManager.wakeUpScreen("WPChangeMode")
                return
            }
            clientRequestChangeMode(clientModeReq)
        } else {
            logTagW(TAG, "updateClientCMD: 客户端不应该直接发送mode: ${clientModeReq.mode}")
        }
    }

    private fun clientRequestChangeMode(clientModeReq: ModeChangeReq) {
        if (WPDeviceInfoManager.hasWPConnecting()) {
            logTagW(TAG, "手写板正在连接中, 不允许切换模式")
            return
        }

        val requestMode = clientModeReq.mode

        val devID = clientModeReq.devID
        val versionLegal = WPDeviceInfoManager.clientVersionLegal(devID)
        if (!versionLegal) {
            logTagW(TAG, "客户端版本不合法, 不允许切换模式")
            if (serverMode == Mode.MODE_MOUSE) {
                WPVersionIllegalDialog.show()
                return
            } else {
                globalAppCtx.toast(R.string.str_wp_dev_version_illegal)
            }
            return
        }


        if (canChangeMode(requestMode)) {
            logTagV(TAG, "切换模式到${requestMode}")
            isModeChangePreparing = false
            _serverModeRequestFlow.value = requestMode

        } else {
            logTagV(
                TAG,
                "当前模式为${serverModeFlow.value}, 无法切换到${requestMode}, preparing:${isModeChangePreparing}"
            )
        }
    }

    private fun canChangeMode(requestMode: Mode): Boolean {
        val currentMode = serverMode
        if (currentMode == requestMode) return false
        if (isModeChangePreparing) return false
        if (requestMode == Mode.MODE_MARK || requestMode == Mode.MODE_PALETTE) {
            // 只有当前是鼠标模式时, 才会将模式切换到其他模式
            return currentMode == Mode.MODE_MOUSE
        }
        if (isBootingDrawService) return false  // 正在切换中, 不允许再次切换
        return true // 如果是鼠标模式, 则可以切换到其他模式
    }
}