package com.czur.starry.device.starrypad.common

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.ONE_GB_SI
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth

/**
 * Created by 陈丰尧 on 2022/5/10
 */
val OSS_BUCKET_NAME = Constants.TMP_BUCKET_NAME
val OSS_END_POINT = Constants.TMP_END_POINT
const val OSS_PREFIX = "starry-share-image"

const val NETTY_PORT = 18752    // Netty服务端口

const val WP_NAME_PREFIX = "WritePad"
const val SERVER_DEV_ID = "StarryHub"   // 服务端的设备ID

object WPOTAConstant {
    const val OTA_MINIMUM_BATTERY_CAPACITY = 30 // OTA最低电量
    const val OTA_MINIMUM_RESERVED_STORAGE_CAPACITY = ONE_GB_SI // OTA最低剩余存储空间
}

/**
 * 绘制相关的常量
 */
object WPDrawConstant {
    const val PALETTE_100_WIDTH = 1920
    const val PALETTE_100_HEIGHT = 1080

    val MARK_100_WIDTH
        get() = getScreenWidth()
    val MARK_100_HEIGHT
        get() = getScreenHeight()

    const val ZOOM_BASIC = 100 // 默认缩放等级

    const val ZOOM_FLOOR_PALETTE = 100
    const val ZOOM_CEIL_PALETTE = 100

    const val ZOOM_FLOOR_MARK = 100
    const val ZOOM_CEIL_MARK = 100
}

/**
 * 功能是否启用
 */
object WPFuncOption {
    const val FUNC_HISTORY_EDIT_ENABLE = true  // 是否开启历史编辑功能
    const val FUNC_MULTIPLE_DEVICE_COUNT = 2   // 多设备数量

    const val MINIMUM_ALLOWED_WP_DEV_VERSION = 60000 // 最低允许的版本号(包括该版本)

    const val WHITE_LIST_VERSION = 2

    // 是否开启同传会控功能
    val mirrorEnable: Boolean by lazy {
        when (val s = Constants.starryHWInfo.series) {
            Q1Series -> when (s.model) {
                StarryModel.Q1Model.Q1,
                StarryModel.Q1Model.Q1Pro -> false

                else -> true
            }

            Q2Series -> when (s.model) {
                StarryModel.Q2Model.Q2,
                StarryModel.Q2Model.Q2Pro -> false

                else -> true
            }

            StudioSeries -> when (s.model) {
                StarryModel.StudioModel.Studio,
                StarryModel.StudioModel.StudioPro
                    -> false

                else -> true
            }
        }
    }
}
