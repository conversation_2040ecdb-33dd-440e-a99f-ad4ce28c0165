package com.czur.starry.device.starrypad.ui.adapter

import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.writepadlib.proto.WPTransferData

/**
 * Created by 陈丰尧 on 2022/4/27
 */

const val ALBUM_VO_TYPE_RECENT = 0          // 最近使用
const val ALBUM_VO_TYPE_TITLE = 1           // 标题
const val ALBUM_VO_TYPE_DATA = 2     // 数据

const val CREATE_TIME_BY_DAY_RECENT = "#*RECENT*#"

data class AlbumDataVO(
    val type: Int,
    val createTime: String,
    val createTimeByDay: String,
    val albumWithPaintList: AlbumWithPaintList? = null
) {
    companion object {

        fun mkTitle(createTime: String) =
            AlbumDataVO(
                ALBUM_VO_TYPE_TITLE,
                createTime,
                createTime,
            )

        fun mkRecentTitle() =
            AlbumDataVO(
                ALBUM_VO_TYPE_RECENT,
                getString(R.string.str_recent_title),
                CREATE_TIME_BY_DAY_RECENT,
            )
    }
}

/**
 * 日期选择数据
 */
data class PaintTimePickerVO(
    val time: String,
    val paintCount: Int,
    val paintIndex: Int,
    val isSelect: Boolean,
) {
    // 画作数字
    val countStr: String
        get() = if (paintCount <= 99) paintCount.toString() else "99+"
}