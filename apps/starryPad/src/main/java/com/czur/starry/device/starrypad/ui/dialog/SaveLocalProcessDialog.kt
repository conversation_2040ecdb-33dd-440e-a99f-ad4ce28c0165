package com.czur.starry.device.starrypad.ui.dialog

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.common.Constants.ANIM_DURATION_SHORT
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.DialogTransmissionProcessBinding
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.vm.SaveLocalViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * Created by 陈丰尧 on 2022/5/10
 */
class SaveLocalProcessDialog(
    private val sharePaintEntities: List<PaintEntity>,
    private val onUploadFinish: (dialog: SaveLocalProcessDialog, error: Throwable?) -> Unit
) : CZVBFloatingFragment<DialogTransmissionProcessBinding>() {
    companion object {
        private const val TAG = "UploadProcessDialog"
    }

    private val paintCount = sharePaintEntities.size

    private val saveLocalViewModel: SaveLocalViewModel by viewModels()
    private var saveLocalJob: Job? = null

    override fun FloatingFragmentParams.initFloatingParams() {
        outSideDismiss = false
    }

    override fun DialogTransmissionProcessBinding.initBindingViews() {
        cancelBtn.setOnClickListener {
            logTagV(TAG, "取消上传")
            dismiss()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(saveLocalViewModel.sumSizeFlow) {
            binding.transmissionProcessBar.setMax(it)
        }

        repeatCollectOnResume(saveLocalViewModel.finishSizeFlow) {
            binding.transmissionProcessBar.setProgress(it)
        }

        repeatCollectOnResume(saveLocalViewModel.savedIndexFlow) {
            binding.transmissionTitleTv.text =
                getString(R.string.str_save_local_title, it, paintCount)
        }
    }

    override fun onShow() {
        super.onShow()
        saveLocalJob = launch {
            val error = try {
                saveLocalViewModel.startSaveLocal(sharePaintEntities)
                null
            } catch (tr: Throwable) {
                logTagE(TAG, "复制失败", tr = tr)
                tr
            }
            if (isActive) {
                onUploadFinish(this@SaveLocalProcessDialog, error)
            }
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        doWithoutCatch {
            saveLocalJob?.cancel()
        }
    }
}