package com.czur.starry.device.starrypad.vm

import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Environment
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File
import java.nio.file.Files
import java.nio.file.StandardCopyOption

/**
 * Created by 陈丰尧 on 2022/5/10
 */
class SaveLocalViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "SaveLocalViewModel"
        private const val DATE_PATTERN = "yyyy_MM_dd_HH_mm_ss"
    }

    // 保存到本地的位置
    private val saveLocalDir: File by lazy {
        File(Environment.getExternalStorageDirectory(), "CZURShare")
    }


    private val _sumSizeFlow = MutableStateFlow(1L)
    val sumSizeFlow = _sumSizeFlow.asStateFlow()

    private val _finishSizeFlow = MutableStateFlow(0L)
    val finishSizeFlow = _finishSizeFlow.asStateFlow()

    private val _savedIndexFlow = MutableStateFlow(1)
    val savedIndexFlow = _savedIndexFlow.asStateFlow()


    /**
     * 开始保存到本地
     */
    suspend fun startSaveLocal(uploadPaintEntities: List<PaintEntity>) =
        withContext(Dispatchers.IO) {
            // 计算总大小
            val sumSize = uploadPaintEntities.sumOf {
                File(it.contentImgPath).length()
            }
            logTagD(TAG, "开始保存到本地:sumSize:$sumSize")
            _sumSizeFlow.value = sumSize


            uploadPaintEntities.forEachIndexed { index, paintEntity ->
                if (isActive.not()) {
                    return@withContext  // 取消了
                }
                val srcFile = File(paintEntity.contentImgPath)
                val fileName =
                    getTimeStr(DATE_PATTERN, paintEntity.createTime) + "." + srcFile.extension
                val targetName = getTargetName(fileName, saveLocalDir)
                val targetFile = File(saveLocalDir, targetName)
                // 图片不大, 复制起来很快,所以不用详细的进度
                Files.copy(
                    srcFile.toPath(),
                    targetFile.toPath(),
                    StandardCopyOption.REPLACE_EXISTING
                )
                _finishSizeFlow.value += srcFile.length()
                _savedIndexFlow.value = index + 1
                manualUpdate(appContext, targetName)
                logTagV(TAG, "复制完成:$targetName")
            }
        }

    /**
     * 通知文件显示小红点
     * @param fileName 文件名称
     */
    private fun manualUpdate(context: Context, fileName: String) {
        val intent = Intent(BootParam.CZService.ACTION_SERVICE_FILE_WATCH_UPDATE)
        intent.`package` = "com.czur.starry.device.file"
        intent.putExtra("updateFileType", "TYPE_SHARE_FILE")
        intent.putExtra("fileName", fileName)
        context.startService(intent)
    }

    private suspend fun getTargetName(fileName: String, targetDir: File): String {
        return withContext(Dispatchers.IO) {
            val targetFile = File(targetDir, fileName)
            if (targetFile.exists()) {
                val name = fileName.substringBeforeLast(".")
                val suffix = fileName.substringAfterLast(".")
                var index = 1
                while (true) {
                    val newFileName = "${name}($index).$suffix"
                    val newFile = File(targetDir, newFileName)
                    if (!newFile.exists()) {
                        return@withContext newFileName
                    }
                    index++
                }
            }
            fileName
        }
    }
}