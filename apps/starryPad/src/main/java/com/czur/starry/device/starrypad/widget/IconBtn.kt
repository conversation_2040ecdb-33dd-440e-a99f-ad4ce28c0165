package com.czur.starry.device.starrypad.widget

import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.WidgetIconBtnBinding

/**
 * Created by 陈丰尧 on 2022/5/9
 */
class IconBtn @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val iconText: String    // 按钮文字
    private val iconTextColor: Int  // 文字颜色
    private val iconDrawable: Drawable? // 图标
    private val iconBgColor: Int     // 背景颜色

    private var binding: WidgetIconBtnBinding

    /**
     * 显示文字
     */
    var text: String
        get() = binding.iconTextTv.text.toString()
        set(value) {
            binding.iconTextTv.text = value
        }

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.IconBtn)
        iconText = ta.getString(R.styleable.IconBtn_iconText) ?: ""
        iconTextColor = ta.getColor(R.styleable.IconBtn_iconTextColor, 0xFF3D3D3D.toInt())
        iconDrawable = ta.getDrawable(R.styleable.IconBtn_iconImg)
        iconBgColor = ta.getColor(R.styleable.IconBtn_iconColor, 0xFFE7E7E7.toInt())
        ta.recycle()

        binding = WidgetIconBtnBinding.inflate(LayoutInflater.from(context), this)

        iconDrawable?.let {
            binding.iconIv.setImageDrawable(it)
        }

        binding.iconTextTv.setTextColor(iconTextColor)
        binding.iconTextTv.text = iconText

        val gradientDrawable = GradientDrawable()
        gradientDrawable.cornerRadius = 10F
        gradientDrawable.setColor(iconBgColor)
        background = gradientDrawable
    }

    override fun setEnabled(enabled: Boolean) {
        alpha = if (enabled) {
            1F
        } else {
            0.5F
        }
        super.setEnabled(enabled)
    }
}