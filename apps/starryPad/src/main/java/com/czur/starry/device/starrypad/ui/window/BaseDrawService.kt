package com.czur.starry.device.starrypad.ui.window

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.os.SystemClock
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.ViewHolderDrawControlBarBinding
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.device.starrypad.util.PaintUIHelper
import com.czur.starry.device.starrypad.util.PaletteState
import com.czur.starry.device.starrypad.vm.PaletteViewModel
import com.czur.starry.device.starrypad.widget.UserInputView
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.BizBtnType
import com.czur.starry.writepadlib.proto.WPTransferData.DrawEventType
import com.czur.starry.writepadlib.proto.WPTransferData.DrawEventType.DRAW_EVENT_TYPE_ERASER
import com.czur.starry.writepadlib.proto.WPTransferData.Mode
import com.czur.starry.writepadlib.widget.drawing.LocalRenderView
import com.czur.starry.writepadlib.widget.drawing.palette.CZPaletteView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlin.time.Duration.Companion.seconds

/**
 * Created by 陈丰尧 on 2024/7/29
 */
private const val TAG = "GlobalMarkService"

abstract class BaseDrawService : AlertWindowService(), IControlBarCb {

    companion object {
        protected const val KEY_HISTORY_ALBUM = "key_history_album"
        protected const val KEY_HISTORY_CURRENT_INDEX = "key_history_current_index"
        protected const val KEY_FROM_HISTORY = "key_from_history" // 点编辑进来的
    }

    override val layoutId: Int = R.layout.window_draw
    override val windowWidthParam: Int = getScreenWidth()
    override val windowHeightParam: Int = getScreenHeight() + getTopControlBarHeight()
    override val careKeyEvent: Boolean = true
    override val keepScreenOn: Boolean = true


    // 在StudioSPlus上需要盖住导航栏, 其他机型中, 不盖住导航栏
    override val windowType: Int = when (Constants.starryHWInfo.model) {
        StarryModel.StudioModel.StudioSPlus -> WindowManager.LayoutParams.FIRST_SYSTEM_WINDOW + 24
        else -> WindowManager.LayoutParams.TYPE_PHONE
    }

    private val needDrawLocal: Boolean by lazy(LazyThreadSafetyMode.NONE) {
        Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus
    }

    private val paletteViewMode: PaletteViewModel by viewModels()

    private val paletteView: CZPaletteView by ViewFinder(R.id.globalMarkPaletteView)
    private val loadingGroup: FrameLayout by ViewFinder(R.id.loadingGroup)
    private val markGroup: View by ViewFinder(R.id.markGroup)
    private val pageNumGroup: View by ViewFinder(R.id.pageNumGroup)
    private val whiteView: View by ViewFinder(R.id.whiteView)   // 白板模式的白色背景
    private val controlBarFrameLayout: View by ViewFinder(R.id.controlBarFrameLayout)
    private val toastTv: TextView by ViewFinder(R.id.fakeToastView)

    private val pageNumCurrentTv: TextView by ViewFinder(R.id.pageNumCurrentTv)
    private val pageNumTotalTv: TextView by ViewFinder(R.id.pageNumTotalTv)

    // 用于接收用户输入
    private val userInputView: UserInputView by ViewFinder(R.id.userInputView)
    private val localRenderView: LocalRenderView by ViewFinder(R.id.localRenderView)

    private var initByScreenOFF = false

    private var isWaitingFinish = false // 是否等待退出

    private var lastToastJob: Job? = null

    private val screenLockReceiver: CZPowerManager.LockScreenReceiver by lazy(LazyThreadSafetyMode.NONE) {
        CZPowerManager.createOneLockScreenReceiver()
    }

    private val uiHelper: PaintUIHelper by lazy {
        PaintUIHelper(lifecycleScope, paletteViewMode, paletteView)
    }

    private lateinit var bootIntent: Intent
    private var needBlockDisplay = false

    private var fromHistory = false

    abstract val mode: Mode

    private val drawControlBarViewHolder by lazy {
        val controlBarBinding = ViewHolderDrawControlBarBinding.bind(controlBarFrameLayout)
        DrawControlBarViewHolder(controlBarBinding, lifecycleScope, this, paletteViewMode, mode)
    }

    override fun onCreate() {
        initByScreenOFF = !CZPowerManager.isScreenOn()
        screenLockReceiver.register(this) { lock ->
            if (lock) {
                logTagV(TAG, "锁屏")
                exit(saveToFile = false, autoShare = paletteViewMode.autoShare)
            }
        }
        super.onCreate()
    }

    override suspend fun initTask() {
        super.initTask()
        logTagD(TAG, "初始化开始,mode:${mode}")
        val result = waitForIntent(3 * ONE_SECOND)
        bootIntent = result.getOrElse {
            logTagE(TAG, "获取Intent失败")
            needBlockDisplay = true
            stopSelf()
            return
        }

        fromHistory = bootIntent.getBooleanExtra(KEY_FROM_HISTORY, false)
        if (fromHistory) {
            val albumID = bootIntent.getLongExtra(KEY_HISTORY_ALBUM, -1L)
            val currentIndex = bootIntent.getIntExtra(KEY_HISTORY_CURRENT_INDEX, -1)
            // 历史记录
            WritePadDeviceModeManager.startModeChangePreparing()
            logTagI(TAG, "展示历史记录: $albumID:$currentIndex")
            paletteViewMode.initByHistory(albumID, currentIndex)
        } else {
            // 新建
            if (mode == Mode.MODE_MARK) {
                val screenShot = takeScreenShot() ?: kotlin.run {
                    stopSelf()
                    return
                }
                paletteViewMode.initByDefMark(screenShot)
                logTagD(
                    TAG, "截图完成, screenShot:${screenShot.byteCount}"
                )
            } else {
                paletteViewMode.initByDefPalette()
            }
        }
    }

    override fun needBlockDisplay(): Boolean {
        return needBlockDisplay
    }

    private suspend fun waitForIntent(timeout: Long): Result<Intent> {
        return withContext(Dispatchers.Default) {
            // 每10ms 检测一次lastIntent是否为空
            // 如果超过timeout时间, 则返回失败, 不为空,则返回成功
            val startTime = SystemClock.elapsedRealtime()
            while (SystemClock.elapsedRealtime() - startTime < timeout) {
                val lastIntent = lastIntent
                if (lastIntent != null) {
                    return@withContext Result.success(lastIntent)
                }
                delay(10L)
            }
            return@withContext Result.failure(Exception("waitForIntent timeout"))
        }
    }

    /**
     * 截图
     */
    private suspend fun takeScreenShot(repeatTime: Int = 5): Bitmap? {
        repeat(repeatTime) {
            val screenShot = takeScreenShot(Bitmap.Config.RGB_565)
            if (screenShot != null) {
                return screenShot
            }
            delay(1.seconds)
            logTagW(TAG, "截图失败, 重试(${it + 1}/$repeatTime)")
        }
        logTagE(TAG, "截图失败")
        return null
    }

    override fun View.initViews() {
        // 更新画板背景
        if (mode == Mode.MODE_MARK) {
            startMarkAnim()
            pageNumGroup.gone()
            whiteView.gone()
        } else {
            markGroup.gone()
            if (needDrawLocal) {
                pageNumGroup.gone() // StudioSPlus有控制栏, 也不显示页码信息
            }
        }

        if (needDrawLocal) {
            // StudioSPlus, 可以直接在Starry上进行绘制
            drawControlBarViewHolder.initViews()
            userInputView.show()
            localRenderView.show()
        } else {
            controlBarFrameLayout.gone()
            userInputView.gone()
            localRenderView.gone()
        }
    }

    override fun initData() {
        super.initData()

        logTagD(TAG, "发送模式切换到标注模式广播")
        paletteViewMode.sendMode()

        repeatCollectOnStart(paletteViewMode.boardInfoFlow) {
            logTagV(TAG, "画板初始化:${it}")
            paletteView.initBoardInfo(
                it.zoomFloor, it.zoomCeil, paletteViewMode.zoom, it.boardWidth, it.boardHeight
            )
            if (needDrawLocal) {
                userInputView.initBoardInfo(
                    paletteViewMode.zoom,
                    it.zoomFloor,
                    0F,
                    0F,
                    it.boardWidth,
                    it.boardHeight
                )
                localRenderView.initBoardInfo(
                    it.zoomFloor,
                    it.zoomCeil,
                    paletteViewMode.zoom,
                    it.boardWidth,
                    it.boardHeight
                )
            }
        }

        // 页码信息
        if (mode != Mode.MODE_MARK) {
            if (!needDrawLocal) {
                @SuppressLint("SetTextI18n")
                repeatCollectOnStart(paletteViewMode.pageInfoFlow) { (current, total) ->
                    logTagI(TAG, "页码信息: $current/$total")
                    pageNumCurrentTv.text = current.toString()
                    pageNumTotalTv.text = total.toString()
                }
            }

        }
        // 翻页后的数据
        repeatCollectOnStart(paletteViewMode.savedPageDrawBitmapFlow) {
            // 当前页面的数据, 之间更新到历史记录上
            logTagV(TAG, "更新历史数据 绘制图:${it == null}")
            paletteView.updateEternalBitmap(it)
        }

        repeatCollectOnStart(paletteViewMode.pageBgBitmapFlow) {
            // 当前页面的数据, 之间更新到历史记录上
            logTagV(TAG, "更新历史数据 背景图 null:${it == null}")
            if (it != null) {
                paletteView.updateShaderBmp(it)
            } else {
                paletteView.clearShaderBmp()
            }
        }

        // 远端数据
        repeatCollectOnStart(paletteViewMode.remoteBizDataFlow) {
            when (it.bizDataBodyCase) {
                WPTransferData.BizData.BizDataBodyCase.BIZ_SYNC -> {
                    logTagV(TAG, "客户端:${it.devID} 请求同步数据")
                    when (it.bizSync.syncContentType) {
                        WPTransferData.SyncContentType.SYNC_DRAW_STEP -> {
                            logTagI(TAG, "客户端:${it.devID} 请求同步内容图")
                            paletteViewMode.broadcastPaintContent(
                                paletteView.getDrawBitmap(),
                                true,
                                it.devID
                            )
                        }

                        WPTransferData.SyncContentType.SYNC_BOARD_INFO -> {
                            logTagI(TAG, "客户端:${it.devID} 请求同步画板信息")
                            paletteViewMode.broadBoardInfo(it.devID)
                        }

                        else -> {
                            logTagW(TAG, "未识别的同步内容:${it.bizSync.syncContentType}")
                        }
                    }
                }

                WPTransferData.BizData.BizDataBodyCase.BIZ_SYNC_ZOOM -> {
                    logTagV(TAG, "客户端:${it.devID} 发送缩放指令")
                }

                WPTransferData.BizData.BizDataBodyCase.BIZ_SYNC_USER_DRAG -> {
                    logTagV(
                        TAG, "客户端:${it.devID} 发送用户拖拽指令"
                    )
                }

                WPTransferData.BizData.BizDataBodyCase.BIZ_SYNC_BTN_CLICK -> {
                    // 处理按钮事件
                    processRemoteClick(it.bizSyncBtnClick, it.devID)
                }

                else -> {}
            }
        }

        // 绘制步骤
        repeatCollectOnStart(paletteViewMode.drawStepFlow) {
            paletteView.addDrawStep(it)
        }

        if (needDrawLocal) {
            drawControlBarViewHolder.initData(this)
            repeatCollectOnStart(userInputView.userPointFlow) {
                val currentColor = drawControlBarViewHolder.currentColor
                paletteViewMode.mkUserInput(
                    it,
                    currentColor,
                    if (it.isErase) {
                        // 橡皮擦模式是使用笔的尾部
                        DRAW_EVENT_TYPE_ERASER
                    } else {
                        drawControlBarViewHolder.penState
                    }
                )
            }

            repeatCollectOnStart(paletteViewMode.userInputFlow) {
                // 只需要线删的时候画预览图,其他的直接画就行, 速度可以的
                if (it.type == DrawEventType.DRAW_EVENT_TYPE_LINE_ERASER) {
                    localRenderView.onUserInput(it)
                }
            }
        }

    }

    private fun processRemoteClick(btnType: BizBtnType, devID: String) {
        when (btnType) {
            BizBtnType.BIZ_BTN_EXIT -> {
                logTagD(TAG, "退出")
                // 历史记录的编辑,再退出时,是不需要考虑自动保存并分享的(于洋 24.11.19)
                exit(saveToFile = false, autoShare = !fromHistory && paletteViewMode.autoShare)
            }

            BizBtnType.BIZ_BTN_SAVE -> {
                logTagD(TAG, "保存")
                launch {
                    paletteViewMode.doWithPaletteState(PaletteState.CONTROLLING) {
                        saveToFile()
                    }
                }
            }

            BizBtnType.BIZ_BTN_SAVE_AND_EXIT -> {
                logTagD(TAG, "保存并退出")
                exit(saveToFile = true, paletteViewMode.autoShare)
            }

            BizBtnType.BIZ_BTN_UNDO -> {
                // 撤销
                uiHelper.undo()
            }

            BizBtnType.BIZ_BTN_REDO -> {
                // 重做
                uiHelper.redo()
            }

            BizBtnType.BIZ_BTN_PAGE_PRE -> {
                logTagD(TAG, "上一页")
                if (!paletteViewMode.canChangePage(true)) {
                    logTagI(TAG, "不能翻页")
                    paletteViewMode.sendPageChangeIllegal(devID)
                    return
                }
                launch {
                    paletteViewMode.doWithPaletteState(PaletteState.CONTROLLING) {
                        paletteViewMode.changePreviousPage(paletteView.getDrawBitmap())
                    }
                }
            }

            BizBtnType.BIZ_BTN_PAGE_NEXT -> {
                logTagD(TAG, "下一页")
                if (!paletteViewMode.canChangePage(false)) {
                    logTagI(TAG, "不能翻页")
                    paletteViewMode.sendPageChangeIllegal(devID)
                    return
                }
                launch {
                    paletteViewMode.doWithPaletteState(PaletteState.CONTROLLING) {
                        paletteViewMode.changeNextPage(paletteView.getDrawBitmap())
                    }
                }
            }

            BizBtnType.BIZ_BTN_NEW -> {
                uiHelper.createNewPage(devID)   // 新建页面
            }

            else -> {
                logTagD(TAG, "未实装: $btnType")
            }
        }
    }

    /**
     * 退出
     */
    private fun exit(saveToFile: Boolean, autoShare: Boolean) {
        if (isWaitingFinish) {
            return
        }
        isWaitingFinish = true
        launch {

            logTagV(TAG, "退出: saveToFile:$saveToFile, autoShare:$autoShare")
            val needSave = saveToFile || autoShare
            logTagV(TAG, "是否需要保存:$needSave")

            var needShare = autoShare
            if (needSave) {
                val saveRes = saveToFile()
                needShare = autoShare && saveRes
            }
            val savedPaintList = paletteViewMode.savedPaintList
            needShare = needShare && savedPaintList.isNotEmpty()
            if (needShare) {
                logTagV(TAG, "开始分享:${savedPaintList.size} 张图片")
                ShareAlertWindow.sharePaint(this@BaseDrawService, savedPaintList)
            }
            stopSelf()
        }
    }

    private suspend fun saveToFile(): Boolean {
        loadingGroup.show()
        val result = paletteViewMode.saveOrUpdateToFile(paletteView.getDrawBitmap())
        logTagV(TAG, "保存结果:${result}")
        if (result) {
            showSelfToast(R.string.save_success)
        } else {
            isWaitingFinish = false
            showSelfToast(R.string.save_error)
        }
        loadingGroup.gone()
        return result
    }

    /**
     * 进入页面时的提示动画
     */
    private fun View.startMarkAnim() {
        logTagV(TAG, "开始标注动画")
        val markWhiteView = findViewById<View>(R.id.markWhiteView)
        val anim = markWhiteView.animate().alpha(0F)
        anim.interpolator = DecelerateInterpolator()
        anim.duration = 500L
        anim.withEndAction {
            markWhiteView.gone()
        }
        anim.start()
    }

    override fun onDestroy() {
        isWaitingFinish = false
        doWithoutCatch {
            screenLockReceiver.unregister()
        }
        paletteViewMode.resetServerModeMode()    // 退出标注模式
        super.onDestroy()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            exit(false, autoShare = paletteViewMode.autoShare)
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun sendLocalEvent(event: BizBtnType) {
        processRemoteClick(event, "Starry")
    }

    private fun showSelfToast(msgRes: Int) {
        if (Constants.starryHWInfo.model != StarryModel.StudioModel.StudioSPlus) {
            toast(msgRes)
        } else {
            lastToastJob?.cancel()
            lastToastJob = launch {
                toastTv.setText(msgRes)
                toastTv.show()
                delay(2.seconds)
                toastTv.gone()
                toastTv.clearContentText()
            }
        }
    }

}