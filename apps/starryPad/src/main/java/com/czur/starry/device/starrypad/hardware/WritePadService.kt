package com.czur.starry.device.starrypad.hardware

import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_WP_SCREEN_ON
import com.czur.starry.device.baselib.common.VALUE_WP_SCREEN_OFF
import com.czur.starry.device.baselib.common.VALUE_WP_SCREEN_ON
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.createNotificationChannel
import com.czur.starry.device.baselib.utils.keyboard.injectKeyAPPSwitch
import com.czur.starry.device.baselib.utils.keyboard.injectKeyBack
import com.czur.starry.device.baselib.utils.keyboard.injectKeyHome
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.noticelib.hwconn.HwConnHelper
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.hardware.trans.bt.BTServer
import com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer
import com.czur.starry.device.starrypad.receiver.ShutdownReceiver
import com.czur.starry.device.starrypad.ui.window.GlobalMarkService
import com.czur.starry.device.starrypad.ui.window.PaletteService
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.ProcessControl.ControlEventCase.KEYEVENT
import com.czur.starry.writepadlib.proto.WPTransferData.ProcessControl.ControlEventCase.PROCESSEVENT
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.debounce

/**
 * Created by 陈丰尧 on 2023/8/31
 * 手写板服务, 主要负责
 * 1. 蓝牙Service: 每当有蓝牙客户端接入时, 都会向其发送设备的AP信息
 * 2. 启动NettyService
 */
private const val TAG = "WritePadService"

class WritePadService : LifecycleService() {
    companion object {
        private const val CHANNEL_ID = "WritePadService"
    }

    private var btServer: BTServer = BTServer()
    private val nettyServer by lazy {
        WritePadNettyServer(WritePadDeviceModeManager)
    }
    private val devInfoManager = WPDeviceInfoManager

    private val shutDownReceiver = ShutdownReceiver()   // 关机广播
    private val touchBarEventReceiver by lazy(LazyThreadSafetyMode.NONE) {
        TouchBarEventReceiver()
    }

    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "WritePadService onCreate")
        launch {
            devInfoManager.loadWhiteListFromDisk()
        }
        showNotification()  // 不添加通知栏, 批注模式会很慢

        startBtServer()

        launch {
            nettyServer.startServer()
            WritePadDeviceModeManager.nettySender = nettyServer
        }

        shutDownReceiver.register(this)
        // StudioSPlus才有控制栏
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            touchBarEventReceiver.register(this)
        }

        repeatCollectOnStart(WritePadDeviceModeManager.serverModeReqFlow) {
            logTagD(TAG, "收到模式切换请求: $it")
            when (it) {
                WPTransferData.Mode.MODE_MARK -> {
                    if (WritePadDeviceModeManager.isBootingDrawService) {
                        logTagD(TAG, "正在启动, 忽略")
                        return@repeatCollectOnStart
                    }
                    CZPowerManager.wakeUpScreen("Boot GlobalMarkService")
                    // 亮屏后3秒内不能截图, 防止截图失败
                    val delayTime = (3 * ONE_SECOND) - CZPowerManager.screenOnDuration
                    if (delayTime > 0) {
                        logTagD(TAG, "等待屏幕唤醒: $delayTime")
                        delay(delayTime)
                    }
                    logTagD(TAG, "启动GlobalMarkService")
                    GlobalMarkService.start(this@WritePadService)
                }

                WPTransferData.Mode.MODE_PALETTE -> {
                    if (WritePadDeviceModeManager.isBootingDrawService) {
                        logTagD(TAG, "正在启动, 忽略")
                        return@repeatCollectOnStart
                    }
                    logTagD(TAG, "启动画板")
                    PaletteService.start(this@WritePadService)
                }

                else -> {}
            }
        }

        repeatCollectOnStart(WritePadDeviceModeManager.serverModeFlow) {
            logTagD(TAG, "广播serverMode: $it")
            WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withServerMode {
                mode = it
            })
        }

        /**
         * 设备信息更新
         */
        repeatCollectOnStart(WritePadDeviceModeManager.clientInfoFlow) {

            if (devInfoManager.isNewConnDev(it.name)) {
                logTagI(TAG, "新连接设备: ${it.name}")
                HwConnHelper.showHwConnAlert(
                    this@WritePadService,
                    HwConnHelper.HwConnType.WRITE_PAD
                )
            }
            devInfoManager.updateDevInfoFromNetty(it)

            launch {
                val devOtaInfo = devInfoManager.getOATVersion(it)
                if (!devOtaInfo.downloadUrl.isNullOrBlank() && !devOtaInfo.md5.isNullOrBlank()) {
                    // 有OTA信息
                    val withOTAInfo = it.copy(otaInfo = devOtaInfo)
                    devInfoManager.updateDevInfoFromNetty(withOTAInfo)
                }
            }

        }

        repeatCollectOnStart(WPDeviceInfoManager.hasDevicesScreenOnFlow.conflate().debounce(20)) {
            logTagD(TAG, "hasDevicesScreenOnFlow:${it}")
            setSystemProp(KEY_WP_SCREEN_ON, if (it) VALUE_WP_SCREEN_ON else VALUE_WP_SCREEN_OFF)
        }

        repeatCollectOnStart(WritePadDeviceModeManager.remoteModeChangeReqFlow) {
            logTagD(TAG, "收到远端模式切换请求: ${it.mode}")
            WritePadDeviceModeManager.updateClientModeReq(it)
        }

        repeatCollectOnStart(WritePadDeviceModeManager.remoteProcessControlFlow) {
            logTagD(TAG, "收到远端流程控制请求: $it")
            when (it.controlEventCase) {
                PROCESSEVENT -> {
                    val processEvent = it.processEvent
                    when (processEvent) {
                        WPTransferData.ProcessControlEvent.STARRY_SCREEN_ON -> {
                            CZPowerManager.wakeUpScreen("WPClientReq")
                        }

                        WPTransferData.ProcessControlEvent.REQ_SYNC_SERVER_MODE -> {
                            val devId = it.devID
                            logTagD(TAG, "客户端请求同步serverMode:${it.devID}")
                            WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withServerMode {
                                mode = WritePadDeviceModeManager.serverMode
                            }, devId)
                        }

                        else -> {}
                    }
                }

                KEYEVENT -> {
                    val keyEvent = it.keyEvent
                    when (keyEvent) {
                        WPTransferData.StarryKeyEvent.KEY_BACK -> {
                            logTagV(TAG, "注入返回键(手写板发送)")
                            injectKeyBack()
                        }

                        WPTransferData.StarryKeyEvent.KEY_HOME -> {
                            logTagV(TAG, "注入Home键(手写板发送)")
                            injectKeyHome()
                        }

                        WPTransferData.StarryKeyEvent.KEY_APP_SWITCH -> {
                            logTagV(TAG, "注入多任务键(手写板发送)")
                            injectKeyAPPSwitch()
                        }

                        else -> {}
                    }
                }

                else -> {}
            }
        }
    }

    /**
     * 启动蓝牙服务端, 像客户端发送设备的AP信息
     */
    private fun startBtServer() {
        btServer.startListener()
        btServer.registerBTBondChangeReceiver(this)
        btServer.onUserDeUnbindListener = { devName ->
            logTagD(TAG, "用户解绑设备: $devName")
            launch {
                WritePadDeviceModeManager.sendMsg(
                    ServerProtoBuilder.withProcessControl(
                        WPTransferData.ProcessControlEvent.CONN_REJECT
                    ), devName
                )
                WPDeviceInfoManager.delDev(devName)
            }
        }
    }

    private fun showNotification() {
        createNotificationChannel(CHANNEL_ID, CHANNEL_ID)
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(TAG)
            .setContentText(TAG)
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()
        startForeground(4, notification)
    }

    override fun onDestroy() {
        logTagW(TAG, "WritePadService onDestroy")
        shutDownReceiver.unregister(this)
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            touchBarEventReceiver.unregister(this)
        }

        try {
            btServer.stop()
        } catch (e: Exception) {
            logTagW(TAG, "Exception when stopping btServer = $e")
        }
        btServer.unregisterBTBondChangeReceiver(this)
        val expHandler = CoroutineExceptionHandler { _, throwable ->
            logTagW(TAG, "WritePadService 释放资源失败", tr = throwable)
        }
        launch(expHandler) {
            WritePadDeviceModeManager.nettySender = null
            nettyServer.stopServer()
        }

        super.onDestroy()
    }
}