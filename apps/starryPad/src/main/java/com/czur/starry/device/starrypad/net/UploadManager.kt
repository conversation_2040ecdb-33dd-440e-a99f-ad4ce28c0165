package com.czur.starry.device.starrypad.net

import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.model.MultipartUploadRequest
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.czur.starry.device.baselib.network.core.NoNetworkException
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.starrypad.common.OSS_BUCKET_NAME
import com.czur.starry.device.starrypad.common.OSS_PREFIX
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import kotlinx.coroutines.*
import java.io.File

/**
 * Created by 陈丰尧 on 2022/5/10
 */
class UploadManager {
    companion object {
        private const val TAG = "UploadManager"
        private const val PART_COUNT = 100 * 1024L
    }

    private val ossInstance by lazy {
        OSSInstance()
    }

    private val fileOss: OSS?
        get() = ossInstance.getOSS()

    private val asyncRequestManager by lazy { OSSRequestManager(fileOss!!) }


    /**
     * 上传
     * @return 对应的文件夹名, 被取消了就是空串
     */
    suspend fun upload(
        src: List<PaintEntity>,
        listener: (finishSize: Long, uploadIndex: Int) -> Unit
    ): String {
        return withContext(Dispatchers.IO) {
            // 首先创建文件夹
            val folderName = randomStr()

            if (!isActive) {
                return@withContext ""
            }

            val folderPath = "$OSS_PREFIX/$folderName/"
            val res = createNewFolderOrFile(folderPath)
            if (!res) {
                logTagE(TAG, "创建文件失败")
                throw NoNetworkException()
            }


            val finishSize = mutableMapOf<String, Long>()

            /**
             * 更新总完成进度
             */
            fun updateFinishSize(taskId: String, size: Long, index: Int) {
                finishSize[taskId] = size
                val sumSize = finishSize.values.sum()
                launch {
                    listener(sumSize, index)
                }
            }

            // 将src展开成单独的文件
            val fileList = src.map {
                File(it.contentImgPath)
            }
            fileList.forEachIndexed { index, file ->
                // 每次循环之前都进行检查
                if (!isActive) {
                    logTagI(TAG, "上传取消")
                    // 上传取消, 跳出循环
                    return@forEachIndexed
                }

                // 拼接文件名
                val ossKey = folderPath + getUploadFileName(file, index, fileList.size)   //
                logTagD(TAG, "上传:ossKey:$ossKey")
                when {
                    else -> {
                        logTagD(TAG, "上传文件:${ossKey}")
                        val rq: MultipartUploadRequest<MultipartUploadRequest<*>> =
                            MultipartUploadRequest(OSS_BUCKET_NAME, ossKey, file.absolutePath)
                        rq.partSize = PART_COUNT
                        // 添加Task
                        val taskId = asyncRequestManager.addUploadRequest(rq, file.length())
                        logTagV(TAG, "taskID:${taskId} (${ossKey})")

                        // 循环等待 上传完成
                        doUpload(ossKey, taskId) { taskID, finishSize ->
                            updateFinishSize(taskID, finishSize, index)
                        }
                    }
                }
                logTagV(TAG, "上传完成:${ossKey}")
            }

            folderName
        }
    }

    /**
     * 获取上传文件名: 时间戳+文件索引, 如果只有一个文件就不加索引
     * @param file 文件
     * @param index 当前文件的索引
     * @param sum 文件总数
     * @return 上传文件名
     */
    private fun getUploadFileName(file: File, index: Int, sum: Int): String {
        val createTime = file.lastModified()    // 在Android中这个时间就是创建时间
        val ext = file.extension
        val timeStr = getTimeStr("yyyy-MM-dd_HH-mm-ss", createTime)
        val fileName = if (sum == 1) {
            timeStr
        } else {
            "${timeStr}(${index + 1})"
        }
        return "${fileName}.${ext}"
    }

    /**
     * 执行上传操作
     */
    private suspend fun CoroutineScope.doUpload(
        ossKey: String,
        taskId: String,
        updateFinishSize: (taskID: String, finishSize: Long) -> Unit
    ) {
        while (true) {
            if (!isActive) {
                logTagD(TAG, "上传已取消:${ossKey}")
                asyncRequestManager.clearTask(taskId)
                break
            }
            try {
                // 每 200ms检查一次进度
                delay(200)
            } catch (exp: Exception) {
                logTagD(TAG, "上传已取消:${ossKey}")
                asyncRequestManager.clearTask(taskId)
                break
            }
            when (asyncRequestManager.getTaskStatus(taskId)) {
                OssTaskState.RUNNING -> {
                    // Task正在运行, 获取进度
                    val finishSize = asyncRequestManager.getFinishSize(taskId)
                    updateFinishSize(taskId, finishSize)
                }

                OssTaskState.SUCCESS -> {
                    val finishSize = asyncRequestManager.getTotalSize(taskId)
                    updateFinishSize(taskId, finishSize)
                    asyncRequestManager.clearTask(taskId)
                    break
                }

                OssTaskState.FAIL -> {
                    val exp = asyncRequestManager.getException(taskId)
                    logTagW(TAG, "上传发生异常!", tr = exp)
                    asyncRequestManager.clearTask(taskId)
                    // 上传失败, 抛出异常
                    throw NoNetworkException()
                }

                null -> {
                    logTagW(TAG, "根据TaskID:${taskId},没有找到对应Task")
                    break
                }
            }
        }
    }

    /**
     * 创建文件夹或空文件
     */
    private suspend fun createNewFolderOrFile(ossKey: String): Boolean =
        withContext(Dispatchers.IO) {
            logTagV(TAG, "创建文件夹:${ossKey}")
            val put =
                PutObjectRequest(
                    OSS_BUCKET_NAME, ossKey,
                    byteArrayOf()
                )
            try {
                fileOss?.putObject(put) ?: throw NoNetworkException()
                return@withContext true
            } catch (e: ClientException) {
                // 本地异常，如网络异常等。
                e.printStackTrace()
            } catch (e: ServiceException) {
                // 服务异常。
                logTagE(TAG, "RequestId " + e.requestId)
                logTagE(TAG, "ErrorCode " + e.errorCode)
                logTagE(TAG, "HostId " + e.hostId)
                logTagE(TAG, "RawMessage " + e.rawMessage)
            }
            return@withContext false
        }
}