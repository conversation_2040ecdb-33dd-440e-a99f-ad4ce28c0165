package com.czur.starry.device.starrypad.repository

import android.graphics.Bitmap
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2023/11/18
 * 用来存储翻页时的画作信息
 */
private const val TAG = "PageCacheRepository"
private const val CACHE_FILE_EXTENSION = "webp"
private const val CACHE_FILE_NAME_DRAW = "draw"
private const val CACHE_FILE_NAME_BG = "bg"

class PageCacheRepository {
    // 存储翻页时的画作信息
    private val cacheDir by lazy {
        File(globalAppCtx.cacheDir, "page_cache").apply {
            if (exists()) {
                deleteRecursively()
            }
            mkdirs()
        }
    }

    /**
     * 是否有缓存文件
     */
    var hasCacheFile = false
        private set

    suspend fun clearCache() = withContext(Dispatchers.IO) {
        logTagD(TAG, "clearCache: ")
        cacheDir.deleteRecursively()
        hasCacheFile = false
    }

    /**
     * 将相册信息拷贝到缓存中,方便后续翻页
     * @param albumWithPaint 相册信息
     */
    suspend fun copyAlbumToCache(albumWithPaint: AlbumWithPaintList) = withContext(Dispatchers.IO) {
        logTagI(TAG, "copyAlbumToCache")
        albumWithPaint.paintList.forEachIndexed { index, paintEntity ->
            // 缓存是从0开始的
            val cacheIndexDir = getCacheIndexDir(index, true)
            val drawFile = File(cacheIndexDir, "${CACHE_FILE_NAME_DRAW}.${CACHE_FILE_EXTENSION}")
            val srcDrawFile = File(paintEntity.drawImgPath)
            logTagV(
                TAG,
                "copyAlbumToCache: ${srcDrawFile.absolutePath} -> ${drawFile.absolutePath}"
            )
            srcDrawFile.copyTo(drawFile, true)

            paintEntity.bgImgPath?.let { bgImgPath ->
                val bgFile = File(cacheIndexDir, "${CACHE_FILE_NAME_BG}.${CACHE_FILE_EXTENSION}")
                val srcBgFile = File(bgImgPath)
                logTagV(
                    TAG,
                    "copyAlbumToCache: ${srcBgFile.absolutePath} -> ${bgFile.absolutePath}"
                )
                srcBgFile.copyTo(bgFile, true)
            }
        }
        hasCacheFile = true
    }

    suspend fun savePageCache(pageIndex: Int, drawBitmap: Bitmap, bgBitmap: Bitmap?) {
        val cacheIndexDir = getCacheIndexDir(pageIndex, true)
        val file = File(cacheIndexDir, "${CACHE_FILE_NAME_DRAW}.${CACHE_FILE_EXTENSION}")
        logTagD(TAG, "savePageCache: ${file.absolutePath}")
        drawBitmap.saveToFile(file)

        bgBitmap?.let {
            val bgFile = File(cacheIndexDir, "${CACHE_FILE_NAME_BG}.${CACHE_FILE_EXTENSION}")
            logTagD(TAG, "有背景图, 保存背景图")
            bgBitmap.saveToFile(bgFile)
        }

        hasCacheFile = true
    }

    suspend fun getAllCachedIndexDir(): List<File> {
        return withContext(Dispatchers.IO) {
            cacheDir.listFiles()?.toList() ?: emptyList()
        }.sortedBy { it.name }
    }

    fun getSavedImgBytes(targetIndex: Int): ByteArray? {
        val cacheIndexDir = getCacheIndexDir(targetIndex, false)
        val file = File(cacheIndexDir, "${CACHE_FILE_NAME_DRAW}.${CACHE_FILE_EXTENSION}")
        logTagV(TAG, "getSavedImgBytes: ${file.absolutePath}")
        return if (file.exists()) {
            file.readBytes()
        } else {
            null
        }
    }

    /**
     * 获取缓存的路径
     * @param targetIndex 目标页码
     * @param autoReSet 是否自动重置
     */
    private fun getCacheIndexDir(targetIndex: Int, autoReSet: Boolean = false): File {
        return File(cacheDir, targetIndex.toString().padStart(2,'0')).apply {
            if (autoReSet) {
                if (exists()) {
                    deleteRecursively()
                }
                mkdirs()
            }
        }
    }

    companion object {
        fun getDrawFilePair(targetDir: File): Pair<File, File?> {
            val drawFile = File(targetDir, "${CACHE_FILE_NAME_DRAW}.${CACHE_FILE_EXTENSION}")
            val bgFile = File(targetDir, "${CACHE_FILE_NAME_BG}.${CACHE_FILE_EXTENSION}")
            return if (!bgFile.exists()) {
                drawFile to null
            } else {
                drawFile to bgFile
            }
        }
    }
}