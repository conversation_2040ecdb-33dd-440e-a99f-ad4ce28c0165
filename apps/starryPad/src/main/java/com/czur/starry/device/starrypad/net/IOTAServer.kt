package com.czur.starry.device.starrypad.net

import android.os.Build
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.utils.SettingHandler

/**
 * Created by 陈丰尧 on 2022/5/10
 */
interface IOTAServer {
    @MiaoHttpGet("/api/ota/starry/writepad/versionCheck")
    fun versionCheck(
        @MiaoHttpParam("version") version: String,
        @MiaoHttpParam("frameworkVersion") fwVersion: String = Constants.FIRMWARE_NAME,
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<WPOTAServerInfo> = WPOTAServerInfo::class.java
    ): MiaoHttpEntity<WPOTAServerInfo?>

    @MiaoHttpGet("/api/ota/writepadFwVersion/log")
    fun getReleaseNote(
        @MiaoHttpParam("version") targetVersion: String,
        @MiaoHttpParam("languageCode") languageCode: String = SettingHandler.czurLang.serverCode,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String?>

}