package com.czur.starry.device.starrypad.ui.dialog

import android.content.Context
import android.content.Intent
import android.view.Gravity
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.starrypad.databinding.ActivityDialogWpVersionIllegalBinding

/**
 * Created by 陈丰尧 on 2024/4/2
 */
private const val TAG = "WPVersionIllegalDialog"

class WPVersionIllegalDialog : CZViewBindingAty<ActivityDialogWpVersionIllegalBinding>() {
    companion object {
        fun show(context: Context = globalAppCtx) {
            val intent = Intent(context, WPVersionIllegalDialog::class.java).newTask()
            context.startActivity(intent)
        }
    }

    override fun initWindow() {
        super.initWindow()
        if (Constants.starryHWInfo.hasTouchScreen) {
            logTagV(TAG, "触控屏,添加偏移量")
            window.apply {
                val params = attributes
                params.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun ActivityDialogWpVersionIllegalBinding.initBindingViews() {
        knowItBtn.setOnDebounceClickListener {
            finish()
        }
    }

}