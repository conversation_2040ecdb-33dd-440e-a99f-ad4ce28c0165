package com.czur.starry.device.starrypad.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.starrypad.db.dao.AlbumDao
import com.czur.starry.device.starrypad.db.dao.PairedWPDao
import com.czur.starry.device.starrypad.db.entity.AlbumEntity
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.db.entity.PairedWPEntity

/**
 * Created by 陈丰尧 on 2022/4/26
 */
private const val TAG = "StarryPadDataBase"

@Database(
    entities = [AlbumEntity::class, PaintEntity::class, PairedWPEntity::class],
    version = 4
)
abstract class StarryPadDataBase : RoomDatabase() {
    abstract fun albumDao(): AlbumDao
    abstract fun pairedWPDao(): PairedWPDao

    companion object {
        val instance = Single.sin
    }

    private object Single {
        val sin: StarryPadDataBase = Room.databaseBuilder(
            globalAppCtx,
            StarryPadDataBase::class.java,
            "StarryPad.db"
        )
            .addMigrations(MIGRATION_3_4)
            .fallbackToDestructiveMigration()   // 之前应用没有启用, 所以直接删除历史数据
            .build()
    }

}

private val MIGRATION_3_4 = object : Migration(3, 4) {
    override fun migrate(db: SupportSQLiteDatabase) {
        logTagV(TAG, "MIGRATION_3_4->添加数据表tab_paired_wp")
        db.execSQL("CREATE TABLE IF NOT EXISTS `tab_paired_wp` (`wpId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `wpName` TEXT NOT NULL)")
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_tab_paired_wp_wpName` ON `tab_paired_wp` (`wpName`)")
    }

}