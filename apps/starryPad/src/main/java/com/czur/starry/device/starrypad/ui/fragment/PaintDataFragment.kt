package com.czur.starry.device.starrypad.ui.fragment

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZFloatingAnimParam
import com.czur.starry.device.baselib.utils.addItemDecorationDrawable
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.baselib.utils.toastSuccess
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.FragmentPaintDataBinding
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.ui.adapter.ALBUM_VO_TYPE_DATA
import com.czur.starry.device.starrypad.ui.adapter.AllAlbumAdapter
import com.czur.starry.device.starrypad.ui.dialog.DelWarnDialog
import com.czur.starry.device.starrypad.ui.dialog.SaveLocalProcessDialog
import com.czur.starry.device.starrypad.ui.fragment.history.HistoryAlbumFloatFragment
import com.czur.starry.device.starrypad.ui.fragment.side.PaintTimePickerSideFragment
import com.czur.starry.device.starrypad.ui.window.ShareAlertWindow
import com.czur.starry.device.starrypad.vm.PaintDataViewModel

/**
 * Created by 陈丰尧 on 2022/4/27
 */
private const val TAG = "PaintDataFragment"

class PaintDataFragment : CZViewBindingFragment<FragmentPaintDataBinding>() {
    private val paintDataVM: PaintDataViewModel by viewModels({ requireActivity() })
    private val paintAdapter = AllAlbumAdapter()
    private val layoutManager by lazy {
        GridLayoutManager(requireContext(), 6).apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    if (paintAdapter.getItemViewType(position) != ALBUM_VO_TYPE_DATA) {
                        return 6
                    }
                    return 1
                }
            }
        }
    }

    override fun FragmentPaintDataBinding.initBindingViews() {
        // 显示日期数据
        datePickerIv.setOnClickListener {
            PaintTimePickerSideFragment { fragment, paintTimePickVO ->
                fragment.dismiss()
                val targetPos = paintTimePickVO.paintIndex
                layoutManager.scrollToPositionWithOffset(targetPos, 0)
            }.show(
                CZFloatingAnimParam.SCREEN_LEFT // 从屏幕左侧弹出
            )
        }

        // 按钮默认不可用
        selDelIcon.isEnabled = false
        selShareIcon.isEnabled = false
        selSaveLocalIcon.isEnabled = false  // 成者妙传


        paintDataRv.layoutManager = layoutManager
        // 纵向间距
        paintDataRv.addItemDecorationDrawable(
            RecyclerView.VERTICAL,
            R.drawable.shape_paint_divider_y
        )
        paintDataRv.closeDefChangeAnimations()
        paintDataRv.adapter = paintAdapter

        paintDataRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (paintDataVM.displayMode == PaintDataViewModel.DisplayMode.BROWSE) {
                val albumWithPaintList =
                    paintAdapter.getData(pos).albumWithPaintList ?: return@doOnItemClick true
                HistoryAlbumFloatFragment(albumWithPaintList).show()
                true
            } else {
                paintAdapter.switchSelStatus(
                    pos,
                    paintDataVM.displayMode == PaintDataViewModel.DisplayMode.SHARE // 分享状态只能单选
                )
                true
            }
        }

        paintDataRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val pos = layoutManager.findFirstCompletelyVisibleItemPosition()
                val data = paintAdapter.getDataOrNull(pos)
                data?.let {
                    paintDataVM.updateFirstVisibleItemPaintData(it)
                }
            }
        })

        // 删除按钮
        mainDelIv.setOnClickListener {
            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.DEL
        }

        // 分享按钮
        mainShareIv.setOnClickListener {
            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.SHARE
        }

        mainSaveLocalIv.setOnClickListener {
            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.SAVE_LOCAL
        }

        // 全选按钮
        selectAllIcon.setOnClickListener {
            if (paintDataVM.isSelectAll) {
                // 全选了就取消全选
                paintAdapter.selectNone()
            } else {
                paintAdapter.selectAll()
            }
        }

        // 取消按钮
        selCancelIcon.setOnClickListener {
            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.BROWSE
        }

        // 删除选中
        selDelIcon.setOnClickListener {
            DelWarnDialog {
                it.dismiss()
                delSelect()
            }.show()

        }

        // 分享按钮
        selShareIcon.setOnClickListener {
            logTagV(TAG, "开始分享")
            val paints = getPaints()
            if (paints.isEmpty()) return@setOnClickListener

            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.BROWSE

            ShareAlertWindow.sharePaint(requireContext(), paints)
        }

        // 保存到本地按钮
        selSaveLocalIcon.setOnClickListener {
            logTagV(TAG, "开始保存到本地")
            val paints = getPaints()
            if (paints.isEmpty()) return@setOnClickListener

            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.BROWSE

            SaveLocalProcessDialog(paints) { dialog, error ->
                dialog.dismiss()
                if (error != null) {
                    // 保存失败
                    toastFail()
                } else {
                    toastSuccess()
                }
            }.show()
        }

        paintAdapter.selCountChangeListener = { selCount ->
            paintDataVM.selCount = selCount
        }
    }

    private fun getPaints(): List<PaintEntity> {
        val albumWithPaintLists = paintAdapter.getSelAlbumWithPaintLists()
        return albumWithPaintLists.map {
            it.paintList
        }.fold(mutableListOf<PaintEntity>()) { acc, paintEntities ->
            acc.addAll(paintEntities)
            acc
        }
    }

    private fun delSelect() {
        launch {
            val selAlbums = paintAdapter.getSelAlbum()
            logTagV(TAG, "delSelect size:: ${selAlbums.size}")
            paintDataVM.displayMode = PaintDataViewModel.DisplayMode.BROWSE
            paintDataVM.delAlbum(selAlbums)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        paintDataVM.albumDataVOLive.observe(this) {
            paintAdapter.setData(it)
        }

        paintDataVM.selCountLive.observe(this) { count ->
            binding.selCountTv.text = getString(R.string.hint_sel_count, count)
            binding.selDelIcon.isEnabled = count > 0
            binding.selShareIcon.isEnabled = count > 0
            binding.selSaveLocalIcon.isEnabled = count > 0
        }

        /**
         * 全选按钮的文字
         */
        paintDataVM.isSelectAllLive.observe(this) { isSelAll ->
            binding.selectAllIcon.text = if (isSelAll) {
                getString(R.string.icon_sel_none)
            } else {
                getString(R.string.icon_sel_all)
            }

        }

        repeatCollectOnResume(paintDataVM.displayModeFlow) {
            // 显示模式改变
            binding.modeChangeGroup.gone(it != PaintDataViewModel.DisplayMode.BROWSE)
            binding.selCancelIcon.gone(it == PaintDataViewModel.DisplayMode.BROWSE)
            binding.selectAllIcon.gone(it == PaintDataViewModel.DisplayMode.BROWSE)
            binding.selCountTv.gone(it == PaintDataViewModel.DisplayMode.BROWSE)

            binding.selShareIcon.gone(it != PaintDataViewModel.DisplayMode.SHARE)   // 分享
            binding.selDelIcon.gone(it != PaintDataViewModel.DisplayMode.DEL)       // 删除
            binding.selSaveLocalIcon.gone(it != PaintDataViewModel.DisplayMode.SAVE_LOCAL) // 保存到本地

            paintAdapter.updateSelMode(it != PaintDataViewModel.DisplayMode.BROWSE)
        }
    }
}