package com.czur.starry.device.starrypad.net

import com.czur.czurutils.log.logTagD
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.common.auth.OSSFederationCredentialProvider
import com.alibaba.sdk.android.oss.common.auth.OSSFederationToken
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.starrypad.common.OSS_END_POINT

/**
 * Created by 陈丰尧 on 2021/11/15
 */
class OSSInstance {
    companion object {
        private const val TAG = "OSSInstance"
    }

    private val ossService: IUploadServer by lazy { HttpManager.getService() }

    private var lastOSS: OSS? = null

    fun getOSS(): OSS? {
        if (lastOSS == null) {
            logTagD(TAG, "需要重新初始化OSS")
            lastOSS = initFileOSS()
        }
        return lastOSS
    }

    private fun initFileOSS(): OSS {
        val provider = object : OSSFederationCredentialProvider() {
            override fun getFederationToken(): OSSFederationToken {
                logTagD(TAG, "刷新Token")
                val entity = makeOSSFederationToken() ?: throw ClientException("token获取失败")
                val ak = entity.accessKeyId
                val sk = entity.accessKeySecret
                val token = entity.securityToken
                val expiration = entity.expiration
                return OSSFederationToken(ak, sk, token, expiration)
            }

        }

        return OSSClient(
            CZURAtyManager.appContext,
            OSS_END_POINT,
            provider,
            makeOSSConfig()
        )
    }

    /**
     * 生成OSSConfig信息
     */
    private fun makeOSSConfig(): ClientConfiguration {
        val conf = ClientConfiguration()
        conf.connectionTimeout = 30 * 1000 // 连接超时，默认30秒。
        conf.socketTimeout = 30 * 1000 // socket超时，默认30秒。
        conf.maxConcurrentRequest = 5 // 最大并发请求数，默认5个。
        conf.maxErrorRetry = 3 // 失败后最大重试次数，默认3次。
        return conf
    }

    private fun makeOSSFederationToken(): OSSTokenEntity? {
        val ossTokenEntity = ossService.getToken()
        if (!ossTokenEntity.isSuccess) {
            return null
        }
        return ossTokenEntity.body
    }
}