package com.czur.starry.device.starrypad.ui.ota

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.network.download.startDownload
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.clearDir
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.hardware.ServerProtoBuilder
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.device.starrypad.hardware.trans.netty.WPUpgradeHelper
import com.czur.starry.writepadlib.proto.WPTransferData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2023/12/19
 */
private const val TAG = "WPOtaViewModel"

private const val OTA_FILE_EXT = "zip"

class WPOtaViewModel(application: Application) : AndroidViewModel(application) {
    private var md5 = ""
    private var downURL = ""
    private var devID = ""

    enum class OTAPageEvent {
        START_DOWNLOAD, // 开始下载
        START_TRANS,    // 开始传输
    }

    enum class OTAErrorType {
        CLIENT_OFF_LINE, // 客户端断开了
    }

    private val _pageEventFlow = MutableSharedFlow<OTAPageEvent>(replay = 1)
    val pageEventFlow = _pageEventFlow.asSharedFlow()

    private val _downloadTotalSizeFlow = MutableStateFlow(1L)
    val downloadTotalSizeFlow = _downloadTotalSizeFlow.asStateFlow()
    val downloadTotalSize: Long
        get() = downloadTotalSizeFlow.value

    private val _downloadCurrentSizeFlow = MutableStateFlow(0L)
    val downloadCurrentSizeFlow = _downloadCurrentSizeFlow.asStateFlow()

    val downloadCurrentStrFlow = downloadCurrentSizeFlow
        .map {
            it.toSizeStr()
        }
    val downloadTotalStrFlow = downloadTotalSizeFlow.map {
        it.toSizeStr()
    }

    val downloadProgressStrFlow =
        combine(_downloadCurrentSizeFlow, _downloadTotalSizeFlow) { current, total ->
            (current * 100F / total).toInt().toString().padStart(2, '0')
        }

    // 发送进度
    private val _transPercentFlow = MutableStateFlow(0)
    val transPercentFlow = _transPercentFlow.asStateFlow()


    fun initParams(md5: String, downURL: String, devID: String, fileSize: Long) {
        this.md5 = md5
        this.downURL = downURL
        this.devID = devID
        _downloadTotalSizeFlow.value = fileSize
    }

    // 存放OTA包的文件
    private val otaDir: File by lazy {
        File(application.cacheDir, "ota").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    private var transFile: File? = null

    val remoteUpgradeInfoFlow = WritePadDeviceModeManager.remoteUpgradeInfoFlow
    private val _otaErrorFlow = MutableSharedFlow<OTAErrorType>()
    val otaErrorFlow = _otaErrorFlow.asSharedFlow()

    private val wpUpgradeHelper = WPUpgradeHelper()

    var transToClientJob: Job? = null

    // 连接的设备数量
    private val connectDevCountFlow = WPDeviceInfoManager.connectingClientCountFlow

    // 是否有连接的设备
    private val hasConnectDevFlow = connectDevCountFlow.map { it > 0 }.distinctUntilChanged()
    var hasConnectDev: Boolean = true
        private set

    init {
        launch {
            hasConnectDevFlow.collect {
                hasConnectDev = it
                if (!it) {
                    _otaErrorFlow.emit(OTAErrorType.CLIENT_OFF_LINE)
                }
            }
        }
    }


    /**
     * 切换到下载页面
     */
    fun changeDownloadPage() {
        launch {
            _pageEventFlow.emit(OTAPageEvent.START_DOWNLOAD)
        }
    }

    /**
     * 切换到传输页面
     * @param file 要传输的文件
     */
    fun changeTransPage(file: File) {
        _transPercentFlow.value = 0

        transFile = file
        launch {
            _pageEventFlow.emit(OTAPageEvent.START_TRANS)
        }
    }

    /**
     * 检查是否有可用的OTA包
     */
    suspend fun getAvailableOTAFile(): File? = withContext(Dispatchers.IO) {
        val targetFile = File(otaDir, "${md5}.$OTA_FILE_EXT")
        if (!targetFile.exists()) {
            return@withContext null
        }
        val fileMD5 = targetFile.md5()
        if (fileMD5 != md5) {
            logTagD(TAG, "文件MD5不匹配:${fileMD5} != $md5")
            targetFile.delete()
            return@withContext null
        }
        targetFile
    }


    /**
     * 下载OTA包
     */
    suspend fun downloadOtaPkg(): File? {
        otaDir.clearDir()
        val downloadFile = File(otaDir, "tmp.${OTA_FILE_EXT}")
        _downloadCurrentSizeFlow.value = 0L
        val downloadRes = startDownload(url = downURL, downloadFile) { _, downloadSize ->
            _downloadCurrentSizeFlow.value = downloadSize
        }
        logTagV(TAG, "downloadRes:$downloadRes")
        if (!downloadRes) {
            downloadFile.delete()
            return null
        }
        return downloadFile
    }

    suspend fun checkMd5(downloadFile: File): Boolean {
        val fileMd5 = try {
            downloadFile.md5()
        } catch (e: Exception) {
            logTagD(TAG, "文件MD5计算失败:${e.message}")
            return false
        }
        logTagV(TAG, "fileMd5:${fileMd5}")
        logTagV(TAG, "targetMd5:${md5}")
        return fileMd5 == md5
    }

    suspend fun renameOTAFile(otaFile: File): File? = withContext(Dispatchers.IO) {
        logTagV(TAG, "renameOTAFile:${otaFile.name} -> ${md5}.${OTA_FILE_EXT}")
        val targetFile = File(otaDir, "${md5}.${OTA_FILE_EXT}")
        otaFile.renameTo(targetFile).yes {
            targetFile
        }.otherwise {
            null
        }

    }


    /**
     * 推送updateRequest
     */
    suspend fun pushUpgradeRequest() {
        val msg = ServerProtoBuilder.withUpgradeInfo {
            upgradePushInfo = WPTransferData.UpgradePushInfo.newBuilder()
                .setFilePath(transFile!!.absolutePath)
                .setMd5(md5)
                .setSize(transFile!!.length().toInt())
                .build()
        }
        logTagD(TAG, "pushUpgradeRequest finish:${transFile!!.absolutePath}")
        WritePadDeviceModeManager.sendMsgSync(msg, devID)
    }

    fun startTransOta() {
        transToClientJob = launch {
            val transRes = wpUpgradeHelper.transUpgradeFile(devID, transFile!!.absolutePath) {
                logTagV(TAG, "上传进度: $it")
                _transPercentFlow.value = it
            }
            logTagD(TAG, "上传完成:transRes:$transRes")
            if (!transRes) {
                logTagD(TAG, "上传失败")
                _otaErrorFlow.emit(OTAErrorType.CLIENT_OFF_LINE)
            }
            transToClientJob = null
        }
    }
}