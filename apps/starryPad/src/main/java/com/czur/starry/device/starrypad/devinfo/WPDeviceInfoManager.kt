package com.czur.starry.device.starrypad.devinfo

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.os.SystemClock
import android.text.Html
import android.text.Spanned
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.bluetoothlib.util.unpair
import com.czur.starry.device.starrypad.common.WPFuncOption
import com.czur.starry.device.starrypad.common.WP_NAME_PREFIX
import com.czur.starry.device.starrypad.db.StarryPadDataBase
import com.czur.starry.device.starrypad.db.entity.PairedWPEntity
import com.czur.starry.device.starrypad.hardware.ServerProtoBuilder
import com.czur.starry.device.starrypad.net.IOTAServer
import com.czur.starry.device.starrypad.net.WPOTAServerInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/12/4
 * 设备信息管理类
 */
@SuppressLint("MissingPermission")
object WPDeviceInfoManager {
    private const val TAG = "WPDeviceInfoManager"

    private val scope = CoroutineScope(Dispatchers.Main)
    private val Context.whiteListDs by preferencesDataStore(
        name = "wpWhiteList",
        corruptionHandler = createDefCorruptionHandler("wpWhiteList")
    )
    private val whiteListVersionKey = intPreferencesKey("whiteListVersion")
    private val whiteListKey = stringSetPreferencesKey("whiteList")

    private const val OTA_REQUEST_INTERVAL = ONE_MIN    // OTA请求间隔
    const val MAX_CONNECTING_TIME = 60 * ONE_SECOND // 最大连接时间

    // 从Netty来的设备信息
    private val _nettyWPMapFlow = MutableStateFlow(mapOf<String, WPDevInfo>())
    private val nettyWPMap
        get() = _nettyWPMapFlow.value

    // 已经记录的配对设备
    private val _pairedWPFlow =
        StarryPadDataBase.instance.pairedWPDao().getPairedWPListFlow().stateIn(
            CoroutineScope(Dispatchers.Default),
            SharingStarted.Eagerly,
            emptyList()
        )

    // 已经记录的配对设备
    private val pairedWPCacheList
        get() = _pairedWPFlow.value

    private var connectingDevInfo: ConnectingInfo? = null   // 正在连接的设备

    val devInfoListFlow = combine(
        _pairedWPFlow,      // 从数据库中获取的设备信息
        _nettyWPMapFlow     // 从netty中获取的设备信息
    ) { pairedWPList, nettyWPList ->
        pairedWPList.map {
            if (it.wpName == connectingDevInfo?.devName) {
                logTagV(TAG, "连接完成,清除连接设备: ${it.wpName}")
                connectingDevInfo = null
            }
            // 如果从netty中获取了设备信息, 则使用netty中的信息, 否则使用数据库中的信息
            val devInfo = nettyWPList[it.wpName] ?: WPDevInfo(it.wpName)
            devInfo
        }
    }
        .flowOn(Dispatchers.Default)
        .shareIn(scope, SharingStarted.Eagerly, 1)  // 防止多次订阅导致逻辑重复执行


    /**
     * 客户端版本号
     */
    private val clientVersionMap = mutableMapOf<String, Long>()

    /**
     * 是否有需要更新的设备
     */
    val hasNeedUpdateDevFlow = devInfoListFlow.map {
        it.any { dev -> dev.connected && (dev.otaInfo?.canUpdate ?: false) }
    }.distinctUntilChanged().flowOn(Dispatchers.Default)

    /**
     * 是否有设备屏幕是点亮的
     */
    val hasDevicesScreenOnFlow = devInfoListFlow
        .map {
            it.filter { dev -> dev.connected && dev.screenOn }
        }.map { infoList -> infoList.map { it.name } }
        .map { it.isNotEmpty() }.flowOn(Dispatchers.Default)


    private val btManager: BluetoothManager by lazy {
        globalAppCtx.getSystemService(BluetoothManager::class.java)
    }
    private val btAdapter: BluetoothAdapter by lazy {
        btManager.adapter
    }

    private val otaServer: IOTAServer by lazy { HttpManager.getService(Constants.OTA_BASE_URL) }

    private val otaReqSuccessTimeMap = mutableMapOf<String, Long>()

    // 版本号对应的更新日志
    private val releaseNoteCacheMap = mutableMapOf<String, Spanned>()

    // 远端连接到的客户端数量
    val connectingClientCountFlow = devInfoListFlow.map {
        it.count { dev -> dev.connected }
    }.stateIn(scope, SharingStarted.Eagerly, 0)

    /**
     * 是否有旧版本的设备
     */
    val hasOldVersionDevice: Boolean
        get() {
            return clientVersionMap.any { it.value < WPFuncOption.MINIMUM_ALLOWED_WP_DEV_VERSION }
        }


    private var clearConnectingInfoJob: Job? = null

    /**
     * 从硬盘加载白名单列表
     */
    suspend fun loadWhiteListFromDisk() {
        logTagV(TAG, "loadWhiteListFromDisk")
        val localWhiteListVersion = globalAppCtx.whiteListDs.data.first()[whiteListVersionKey] ?: 0
        if (localWhiteListVersion < WPFuncOption.WHITE_LIST_VERSION) {
            logTagI(TAG, "需要初始化配对数据库")

            if (localWhiteListVersion < 1) {
                logTagI(TAG, "之前没有白名单, 初始化白名单")
                initPairedWPDBFromBT()
            } else {
                logTagI(TAG, "加载本地白名单")
                initPairedWPDBFromWhiteList()
            }

            // 更新白名单版本号
            globalAppCtx.whiteListDs.edit {
                it[whiteListKey] = emptySet()
                it[whiteListVersionKey] = WPFuncOption.WHITE_LIST_VERSION
            }
        } else {
            logTagV(TAG, "不需要初始化配对设备数据库")
        }
    }

    /**
     * 从白名单初始化绑定数据库
     */
    private suspend fun initPairedWPDBFromWhiteList() {
        logTagI(TAG, "从白名单初始化绑定数据库")
        val whiteList = globalAppCtx.whiteListDs.data.first()[whiteListKey] ?: emptySet()
        if (whiteList.isNotEmpty()) {
            val pairedList = whiteList.map {
                PairedWPEntity(it)
            }
            logTagV(TAG, "插入数据库: ${pairedList.joinToString()}")
            StarryPadDataBase.instance.pairedWPDao().insertPairedWPList(pairedList)
        }
    }

    /**
     * 从蓝牙初始化绑定数据库
     */
    private suspend fun initPairedWPDBFromBT() {
        logTagI(TAG, "从蓝牙初始化绑定数据库")
        val boundedWritePad = btAdapter.bondedDevices.filter {
            it.name.startsWith(WP_NAME_PREFIX)
        }.map {
            it.name
        }.toMutableSet()
        if (boundedWritePad.size > WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT) {
            logTagW(
                TAG,
                "已配对的设备数量大于允许设备数量${WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT}, 不添加白名单"
            )
            boundedWritePad.clear()
        }
        logTagV(TAG, "初始化白名单: ${boundedWritePad.joinToString()}")

        if (boundedWritePad.isNotEmpty()) {
            val pairedList = boundedWritePad.map {
                PairedWPEntity(it)
            }
            logTagV(TAG, "插入数据库: ${pairedList.joinToString()}")
            StarryPadDataBase.instance.pairedWPDao().insertPairedWPList(pairedList)
        }
    }

    /**
     * 是否有设备正在连接
     */
    fun hasWPConnecting() = connectingDevInfo?.isTimeOut() == false


    /**
     * 更新客户端版本号
     */
    fun updateClientVersion(devID: String, version: Long) {
        clientVersionMap[devID] = version
    }

    /**
     * 客户端版本是否合法
     */
    fun clientVersionLegal(devID: String): Boolean {
        if (devID == ServerProtoBuilder.devID) {
            logTagI(TAG, "Starry自己,无需检查")
            return true
        }
        return getClientVersion(devID) >= WPFuncOption.MINIMUM_ALLOWED_WP_DEV_VERSION
    }

    /**
     * 获取客户端版本号
     */
    fun getClientVersion(devID: String): Long {
        return clientVersionMap.getOrDefault(devID, 0L)
    }

    /**
     * 判断设备是否是第一次连接上来的
     * @param devName 设备名
     */
    fun isNewConnDev(devName: String): Boolean {
        return connectingDevInfo?.devName == devName
    }

    /**
     * 清除未记录在数据库的设备
     */
    suspend fun clearUnSavedBTDevice() = withContext(Dispatchers.Default) {
        connectingDevInfo = null   // 清除连接中的设备
        val savedWPNameSet = _pairedWPFlow.value.map { it.wpName }.toSet()
        btAdapter.bondedDevices.filter {
            it.name.startsWith(WP_NAME_PREFIX) && !savedWPNameSet.contains(it.name)
        }.forEach {
            logTagI(TAG, "删除未保存的设备: ${it.name}")
            it.unpair()
        }
    }

    /**
     * 删除设备信息
     * @param wpInfo 设备信息
     */
    suspend fun delDev(wpInfo: WPDevInfo) = delDev(wpInfo.name)
    suspend fun delDev(wpName: String) {
        logTagD(TAG, "删除设备: $wpName")

        if (connectingDevInfo?.devName == wpName) {
            logTagV(TAG, "删除连接中的设备名称: $wpName")
            connectingDevInfo = null
        }

        // 删除数据库
        logTagV(TAG, "删除数据库中的设备: $wpName")
        StarryPadDataBase.instance.pairedWPDao().delPairedWPByName(wpName)

        val nettyMap = nettyWPMap.toMutableMap()
        nettyMap.remove(wpName)?.also {
            logTagV(TAG, "删除Netty中的设备: $wpName")
            _nettyWPMapFlow.value = nettyMap
        }

        // 删除蓝牙配对信息
        withContext(Dispatchers.Default) {
            logTagV(TAG, "寻找并删除蓝牙配对信息: $wpName")
            btAdapter.bondedDevices.firstOrNull {
                it.name == wpName
            }?.unpair()
        }
    }

    /**
     * 更新设备信息
     */
    suspend fun updateDevInfoFromNetty(devInfo: WPDevInfo) {
        logTagI(TAG, "updateDevInfo: $devInfo")

        if (devInfo.name == connectingDevInfo?.devName) {
            logTagV(TAG, "连接完成,将${devInfo.name}添加到白名单")
            StarryPadDataBase.instance.pairedWPDao().insertPairedWP(PairedWPEntity(devInfo.name))
        }

        val nettyWPMap = nettyWPMap.toMutableMap()
        if (nettyWPMap.containsKey(devInfo.name)) {
            nettyWPMap[devInfo.name] = devInfo
        } else {
            nettyWPMap[devInfo.name] = devInfo
        }
        _nettyWPMapFlow.value = nettyWPMap

    }

    suspend fun checkAndMarkConnecting(name: String): Boolean {
        if (StarryPadDataBase.instance.pairedWPDao()
                .count() >= WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT
        ) {
            logTagW(
                TAG,
                "已经配对的设备数量大于允许设备数量${WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT}, 不能连接设备"
            )
            return false
        }
        if (!name.startsWith(WP_NAME_PREFIX, true)) {
            logTagW(TAG, "设备名称不合法: $name")
            return false
        }
        connectingDevInfo = ConnectingInfo(name)
        clearConnectingInfoJob?.cancel()
        clearConnectingInfoJob = scope.launch {
            delay(MAX_CONNECTING_TIME)
            clearUnSavedBTDevice()
        }
        return true
    }

    /**
     * 检查设备合法性
     * 1. 正在连接中的设备
     * 2. 已经记录配对的设备
     */
    suspend fun isLegalDevice(name: String): Boolean {
        return connectingDevInfo?.devName == name
                || StarryPadDataBase.instance.pairedWPDao().hasThisName(name)
    }

    fun isLegalDeviceFromCache(name: String): Boolean {
        return connectingDevInfo?.devName == name
                || pairedWPCacheList.any { it.wpName == name }
    }

    suspend fun refreshAllDeviceInfo() {
        logTagI(TAG, "刷新所有的手写板信息")
        val refreshMap = nettyWPMap.values
            .filter { it.connected }
            .map {
                logTagV(TAG, "刷新设备信息: $it")
                val otaInfo = getOATVersion(it)
                it.copy(otaInfo = otaInfo)
            }.associateBy {
                it.name
            }
        _nettyWPMapFlow.value = refreshMap
    }

    suspend fun getOATVersion(devInfo: WPDevInfo): WPOTAServerInfo {
        val lastReqTime = otaReqSuccessTimeMap[devInfo.name] ?: 0
        if (System.currentTimeMillis() - lastReqTime < OTA_REQUEST_INTERVAL) {
            val lastOtaInfo = nettyWPMap[devInfo.name]?.otaInfo
            logTagD(TAG, "OTA请求间隔太短${devInfo.name}, 不请求,使用之前数据")
            if (lastOtaInfo != null) {
                return lastOtaInfo
            }
        }
        return withContext(Dispatchers.IO) {
            logTagI(TAG, "请求OTA信息: ${devInfo.name}")
            val result = otaServer.versionCheck(devInfo.version)
            if (result.isSuccess) {
                otaReqSuccessTimeMap[devInfo.name] = System.currentTimeMillis()
                result.body ?: WPOTAServerInfo(null, null, 1L, null)
            } else {
                WPOTAServerInfo(null, null, 1L, null)
            }
        }.also {
            if (!it.versionName.isNullOrEmpty()) {
                scope.launch {
                    getReleaseNote(it.versionName)
                }
            }
        }
    }

    suspend fun getReleaseNote(targetVersion: String): Spanned {
        logTagV(TAG, "获取更新日志: $targetVersion")
        if (releaseNoteCacheMap.containsKey(targetVersion)) {
            return releaseNoteCacheMap[targetVersion]!!
        }
        return withContext(Dispatchers.IO) {
            val res = otaServer.getReleaseNote(targetVersion)
            val rnStr = if (res.isSuccess) res.body ?: "" else ""
            return@withContext Html.fromHtml(rnStr, Html.FROM_HTML_MODE_LEGACY)
        }.also {
            releaseNoteCacheMap[targetVersion] = it
        }
    }

    private data class ConnectingInfo(
        val devName: String,
        val startTime: Long = SystemClock.elapsedRealtime()
    ) {
        fun isTimeOut(): Boolean {
            return SystemClock.elapsedRealtime() - startTime > MAX_CONNECTING_TIME
        }
    }
}