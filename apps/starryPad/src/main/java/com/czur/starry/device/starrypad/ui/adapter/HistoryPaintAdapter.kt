package com.czur.starry.device.starrypad.ui.adapter

import android.view.ViewGroup
import com.bumptech.glide.signature.ObjectKey
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.db.entity.PaintEntity

/**
 * Created by 陈丰尧 on 2023/1/12
 */

data class HistoryPaintEntityVO(
    val paintEntity: PaintEntity,
    val modifyTime: Long,
    val isSelect: Boolean
)

class HistoryPaintAdapter : BaseDifferAdapter<HistoryPaintEntityVO>() {

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: HistoryPaintEntityVO) {
        holder.setImageFileWithCorners(
            6,
            itemData.paintEntity.contentImgPath,
            ObjectKey("${itemData.paintEntity.contentImgPath}${itemData.modifyTime}"),
            R.id.itemHistoryIv
        )
        if (itemData.isSelect) {
            holder.setImgResource(R.drawable.ic_history_border_sel, R.id.itemSelMarkIv)
        } else {
            holder.setImgResource(R.drawable.ic_history_border_unsel, R.id.itemSelMarkIv)
        }

        holder.setText((position + 1).toString(), R.id.itemHistoryIndexTv)
    }

    override fun areItemsTheSame(
        oldItem: HistoryPaintEntityVO,
        newItem: HistoryPaintEntityVO
    ): Boolean {
        return oldItem.paintEntity.paintId == newItem.paintEntity.paintId
    }

    override fun areContentsTheSame(
        oldItem: HistoryPaintEntityVO,
        newItem: HistoryPaintEntityVO
    ): Boolean {
        return oldItem == newItem && oldItem.paintEntity == newItem.paintEntity
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_history_paint, parent)
    }
}