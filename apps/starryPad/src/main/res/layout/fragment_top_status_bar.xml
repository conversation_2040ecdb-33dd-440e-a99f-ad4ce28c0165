<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/topBarRootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription"
    tools:layout_height="130px">

    <ImageView
        android:id="@+id/useHintIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_top_use_hint_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/useHintTv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/useHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20px"
        android:text="@string/str_wp_use_hint"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/useHintIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/backIv"
        android:layout_width="80px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:scaleType="center"
        android:src="@drawable/ic_wp_top_status_bar_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/statusAndSettingLayout"
        android:layout_width="wrap_content"
        android:layout_height="80px"
        android:layout_marginRight="30px"
        android:paddingHorizontal="35px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/connStatusIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_status_conn_none" />

        <com.czur.starry.device.baselib.widget.CircleView
            android:id="@+id/newVersionCircleView"
            android:layout_width="12px"
            android:layout_height="12px"
            android:layout_marginTop="-7px"
            app:circleColor="#F34949"
            app:layout_constraintRight_toRightOf="@id/connStatusIv"
            app:layout_constraintTop_toTopOf="@id/connStatusIv" />

        <TextView
            android:id="@+id/connStatusTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20px"
            android:includeFontPadding="false"
            android:text="@string/str_write_pad_connected"
            android:textSize="24px"
            android:textStyle="bold"
            android:layout_marginTop="-5px"
            app:layout_constraintLeft_toRightOf="@id/connStatusIv"
            app:layout_constraintTop_toTopOf="@id/connStatusIv" />

        <TextView
            android:id="@+id/connCountTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5px"
            android:textColor="@color/color_top_status_bar_conn_action"
            android:textSize="18px"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@id/connStatusTv"
            app:layout_constraintLeft_toRightOf="@id/connStatusTv" />

        <TextView
            android:id="@+id/goSettingTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="@string/str_go_setting"
            android:textSize="18px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/connStatusIv"
            app:layout_constraintLeft_toLeftOf="@id/connStatusTv" />

        <ImageView
            android:id="@+id/goSettingIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_go_setting"
            app:layout_constraintBottom_toBottomOf="@id/goSettingTv"
            app:layout_constraintLeft_toRightOf="@id/goSettingTv"
            app:layout_constraintTop_toTopOf="@id/goSettingTv" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>