<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#F4F4F4"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/selFlow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal"
        app:constraint_referenced_ids="selCancelIcon,selectAllIcon,selShareIcon,selSaveLocalIcon,selDelIcon"
        app:flow_horizontalGap="30px"
        app:flow_horizontalStyle="packed"
        app:layout_constraintBottom_toTopOf="@id/paintDataRv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <com.czur.starry.device.starrypad.widget.IconBtn
        android:id="@+id/selCancelIcon"
        android:layout_width="200px"
        android:layout_height="70px"
        android:visibility="gone"
        app:iconImg="@drawable/ic_sel_cancel"
        app:iconText="@string/icon_sel_cancel" />

    <com.czur.starry.device.starrypad.widget.IconBtn
        android:id="@+id/selectAllIcon"
        style="@style/iconBtn_style"
        android:visibility="gone"
        app:iconImg="@drawable/ic_sel_all"
        app:iconText="@string/icon_sel_all"
        app:iconTextColor="#036CFE" />

    <com.czur.starry.device.starrypad.widget.IconBtn
        android:id="@+id/selShareIcon"
        android:layout_width="200px"
        android:layout_height="70px"
        android:visibility="gone"
        app:iconColor="#036CFE"
        app:iconImg="@drawable/ic_sel_share"
        app:iconText="@string/icon_sel_share"
        app:iconTextColor="@color/white" />

    <com.czur.starry.device.starrypad.widget.IconBtn
        android:id="@+id/selSaveLocalIcon"
        android:layout_width="200px"
        android:layout_height="70px"
        android:visibility="gone"
        app:iconColor="#036CFE"
        app:iconImg="@drawable/ic_sel_save_local"
        app:iconText="@string/icon_sel_save_local"
        app:iconTextColor="@color/white" />

    <com.czur.starry.device.starrypad.widget.IconBtn
        android:id="@+id/selDelIcon"
        style="@style/iconBtn_style"
        android:visibility="gone"
        app:iconColor="#F34949"
        app:iconImg="@drawable/ic_sel_del"
        app:iconText="@string/icon_sel_del"
        app:iconTextColor="@color/white" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/paintDataRv"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_marginTop="185px"
        android:layout_marginRight="80px"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingLeft="80px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/datePickerIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_date_picker"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/selCountTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="80px"
        android:textColor="#ABABAB"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/selFlow"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="已选 1" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/mainDelIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:src="@drawable/ic_main_del"
        app:bl_focused_hovered="#E7E7E7"
        app:bl_unFocused_hovered="#00000000"
        android:layout_marginTop="145px"
        android:layout_marginRight="60px"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:roundPercent="1" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/mainShareIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginRight="25px"
        android:src="@drawable/ic_main_share"
        app:bl_focused_hovered="#E7E7E7"
        app:bl_unFocused_hovered="#00000000"
        app:layout_constraintTop_toTopOf="@id/mainDelIv"
        app:layout_constraintRight_toLeftOf="@id/mainDelIv"
        app:roundPercent="1" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/mainSaveLocalIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginRight="25px"
        android:src="@drawable/ic_main_save_local"
        app:bl_focused_hovered="#E7E7E7"
        app:bl_unFocused_hovered="#00000000"
        app:layout_constraintTop_toTopOf="@id/mainShareIv"
        app:layout_constraintRight_toLeftOf="@id/mainShareIv"
        app:roundPercent="1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/modeChangeGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="mainDelIv,mainShareIv,mainSaveLocalIv" />


</androidx.constraintlayout.widget.ConstraintLayout>