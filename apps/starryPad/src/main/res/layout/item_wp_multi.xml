<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription">

    <androidx.constraintlayout.widget.Group
        android:id="@+id/wpConnectGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        android:visibility="visible"
        app:constraint_referenced_ids="wpItemConnectedBg,wpConnectTagIv,wpConnectTagTv" />

    <com.noober.background.view.BLView
        android:id="@+id/wpItemConnectedBg"
        android:layout_width="match_parent"
        android:layout_height="125px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintTop_toTopOf="parent" />

    <com.noober.background.view.BLView
        android:id="@+id/wpItemDisConnectedBg"
        android:layout_width="match_parent"
        android:layout_height="80px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#B6B9C3"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/wpConnectTagIv"
        android:layout_width="24px"
        android:layout_height="15px"
        android:layout_marginRight="11px"
        android:src="@drawable/ic_wp_connect_tag"
        app:layout_constraintBottom_toTopOf="@id/wpIconIv"
        app:layout_constraintLeft_toLeftOf="@id/wpIconIv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/wpConnectTagTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:layout_marginRight="30px"
        android:text="@string/str_write_pad_connected"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/wpConnectTagIv"
        app:layout_constraintLeft_toRightOf="@id/wpConnectTagIv"
        app:layout_constraintTop_toTopOf="@id/wpConnectTagIv" />

    <ImageView
        android:id="@+id/wpIconIv"
        android:layout_width="50px"
        android:layout_height="36px"
        android:layout_marginLeft="30px"
        android:src="@drawable/ic_wp_device_connected"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wpConnectTagIv" />

    <TextView
        android:id="@+id/wpDevNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/wpIconIv"
        app:layout_constraintLeft_toRightOf="@id/wpIconIv"
        app:layout_constraintTop_toTopOf="@id/wpIconIv"
        tools:text="WritePad-JJJJ" />

    <ImageView
        android:id="@+id/wpDevRemoveIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_wp_dev_remove"
        app:layout_constraintBottom_toTopOf="@id/wpDevVersionNameTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/wpDevUpgradeIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="15px"
        android:src="@drawable/ic_wp_dev_upgrade"
        app:layout_constraintRight_toLeftOf="@id/wpDevRemoveIv"
        app:layout_constraintTop_toTopOf="@id/wpDevRemoveIv" />

    <TextView
        android:id="@+id/wpDevVersionNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#80FFFFFF"
        android:textSize="20px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="@id/wpDevRemoveIv"
        app:layout_constraintTop_toBottomOf="@id/wpDevRemoveIv"
        tools:text="v1.0.0-user.zh-20240315" />

</androidx.constraintlayout.widget.ConstraintLayout>