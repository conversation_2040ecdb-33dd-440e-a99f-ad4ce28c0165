<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80px"
    android:layout_gravity="center"
    tools:ignore="PxUsage">

    <TextView
        android:id="@+id/normalTimeTv"
        android:layout_width="match_parent"
        android:layout_height="50px"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#036CFE"
        tools:background="#036CFE" />
</FrameLayout>