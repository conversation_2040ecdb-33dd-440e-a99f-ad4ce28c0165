<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F4F4F4"
    tools:ignore="PxUsage,RtlHardcoded,RtlSymmetry">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_color="@color/black"
        app:baselib_titlebar_title=""
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/wpDeviceContainer"
        android:layout_width="900px"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/wpHintBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="PxUsage" />


    <View
        android:id="@+id/wpHintBg"
        android:layout_width="900px"
        android:layout_height="0px"
        android:layout_marginTop="15px"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/wpDeviceContainer" />

    <TextView
        android:id="@+id/wpHintWarmTipsTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24px"
        android:layout_marginTop="25px"
        android:text="@string/title_write_pad_warm_tips"
        android:textColor="@color/black"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/wpHintTitleTv"
        app:layout_constraintLeft_toLeftOf="@id/wpHintBg"
        app:layout_constraintTop_toTopOf="@id/wpHintBg" />

    <TextView
        android:id="@+id/wpHintTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:text="@string/title_write_pad_hint"
        android:textColor="@color/black"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/wpHintContentTv"
        app:layout_constraintLeft_toLeftOf="@id/wpHintWarmTipsTv"
        app:layout_constraintTop_toBottomOf="@id/wpHintWarmTipsTv" />

    <TextView
        android:id="@+id/wpHintContentTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:layout_marginRight="30px"
        android:layout_marginBottom="25px"
        android:text="@string/str_write_pad_hint"
        android:textColor="@color/black"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/wpHintBg"
        app:layout_constraintLeft_toLeftOf="@id/wpHintTitleTv"
        app:layout_constraintRight_toRightOf="@id/wpHintBg"
        app:layout_constraintTop_toBottomOf="@id/wpHintTitleTv" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:layout_marginRight="60px"
        android:orientation="vertical"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.noober.background.view.BLLinearLayout
            android:id="@+id/addWpDevBtn"
            android:layout_width="match_parent"
            android:layout_height="50px"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="50px"
            app:bl_corners_radius="25px"
            app:bl_stroke_color="#5879FC"
            app:bl_stroke_width="1px">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_add_wp_dev"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="8px"
                android:text="@string/str_add_wp_dev"
                android:textColor="#5879FC"
                android:textSize="24px"
                android:textStyle="bold" />
        </com.noober.background.view.BLLinearLayout>

        <com.noober.background.view.BLLinearLayout
            android:id="@+id/optWpDevBtn"
            android:layout_width="match_parent"
            android:layout_height="50px"
            android:layout_marginTop="16px"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="50px"
            app:bl_corners_radius="25px"
            app:bl_stroke_color="#5879FC"
            app:bl_stroke_width="1px">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_opt_wp_dev"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="8px"
                android:text="@string/str_opt_wp_dev"
                android:textColor="#5879FC"
                android:textSize="24px"
                android:textStyle="bold" />
        </com.noober.background.view.BLLinearLayout>
    </LinearLayout>


    <!--  扫码购买  -->
    <View
        android:id="@+id/buyWpHintBg"
        android:layout_width="200px"
        android:layout_height="250px"
        android:layout_marginRight="95px"
        android:layout_marginBottom="50px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#E7E7E7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:background="#E7E7E7" />

    <ImageView
        android:id="@+id/buyQrCodeIv"
        android:layout_width="150px"
        android:layout_height="150px"
        android:layout_marginTop="20px"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="@id/buyWpHintBg"
        app:layout_constraintRight_toRightOf="@id/buyWpHintBg"
        app:layout_constraintTop_toTopOf="@id/buyWpHintBg" />

    <TextView
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:text="@string/str_buy_wp_hint"
        android:textAlignment="center"
        android:textColor="@color/black"
        android:textSize="20px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/buyQrCodeIv"
        app:layout_constraintRight_toRightOf="@id/buyQrCodeIv"
        app:layout_constraintTop_toBottomOf="@id/buyQrCodeIv" />

</androidx.constraintlayout.widget.ConstraintLayout>