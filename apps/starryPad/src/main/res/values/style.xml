<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PxUsage">

    <!--  历史相册页面, 当前展示的图片  -->
    <style name="HistoryCurrentImg">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">10px</item>
        <item name="cornerSizeTopRight">10px</item>
    </style>

    <style name="HistoryItemImg">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10px</item>
    </style>

    <style name="writePadUpdateBtnStyle">
        <item name="android:layout_width">100px</item>
        <item name="android:layout_height">50px</item>
        <item name="android:layout_marginRight">195px</item>
    </style>>

    <style name="writePadUpdateTextStyle">
        <item name="android:layout_width">0px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="layout_goneMarginRight">195px</item>
    </style>>

    <style name="user_default_dialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowFrame">@android:color/transparent</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:background">@drawable/bg_global_mark_dialog_corner</item>
        <!-- 背景色 -->
        <item name="android:windowBackground">@color/white</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
                <item name="android:windowFullscreen">false</item>
        <!-- 全屏 -->
    </style>
    <!--    从中间弹出:-->
    <style name="AnimCenter" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_center_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_center_out</item>
    </style>

    <style name="no_network_text_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">60px</item>
        <item name="android:textSize">24px</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="iconBtn_style">
        <item name="android:layout_width">200px</item>
        <item name="android:layout_height">70px</item>
    </style>

    <style name="upgrade_dev_btn_style">
        <item name="android:layout_width">100px</item>
        <item name="android:layout_height">50px</item>
    </style>

    <style name="iv_control_bar_icon">
        <item name="android:layout_width">68px</item>
        <item name="android:layout_height">68px</item>
        <item name="android:background">@drawable/bg_control_bar_icon</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="iv_control_bar_icon_color" parent="iv_control_bar_icon">
        <item name="android:padding">15px</item>
    </style>

</resources>