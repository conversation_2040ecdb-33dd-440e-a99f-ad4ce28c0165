package com.czur.starry.device.wallpaperdisplay.view.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.data.NullableLiveDataDelegate
import com.czur.starry.device.wallpaperdisplay.bean.CustomImageEntity
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.`interface`.InnerServices
import com.czur.starry.device.wallpaperdisplay.util.CUSTOM_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.RECENT_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.copyFileToLocal
import com.czur.starry.device.wallpaperdisplay.util.getNormalCustomData
import com.czur.starry.device.wallpaperdisplay.util.getNormalRecentData
import com.czur.starry.device.wallpaperdisplay.util.getRecentData
import com.czur.starry.device.wallpaperdisplay.util.setValueToString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean

/**
 * created by wangh 22.0930
 */


class PhotoVM(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "PhotoVM"
        //扫码上传二维码url拼接
        private const val WELCOME_SN = "welcomeImage?sn="
    }

    val isShowRecent = DifferentLiveData(false)
    private var isShow by LiveDataDelegate(isShowRecent)

    val unReceivedImageLive: LiveData<MutableList<CustomImageEntity>> = MutableLiveData()
    private var unReceivedImage by NullableLiveDataDelegate(unReceivedImageLive, null)

    var isNetworking = AtomicBoolean(false)

    //最近使用数据
    suspend fun refreshIsShow() = withContext(Dispatchers.IO) {
        isShow = getRecentData().size > 0
    }

    //更新名称
    suspend fun updateDataName(tag: String, entity: FileEntity, newName: String) =
        withContext(Dispatchers.IO) {
            when (tag) {
                RECENT_ASSETS -> {
                    val recentList = getNormalRecentData()
                    recentList.forEach {
                        if (it == entity) {
                            logTagD(TAG, "======it.name:${it.name}")
                            it.name = newName
                        }
                    }
                    recentList.reverse()
                    setValueToString(RECENT_ASSETS, recentList)
                    true
                }

                CUSTOM_ASSETS -> {
                    val customList = getNormalCustomData()
                    customList.forEach {
                        if (it == entity) {
                            logTagD(TAG, "======it.name:${it.name}")
                            it.name = newName
                        }
                    }
                    setValueToString(CUSTOM_ASSETS, customList)
                    true
                }

                else -> {
                    false
                }
            }
        }


    //本地复制
    suspend fun copyToLocal(it: FileEntity) = copyFileToLocal(it)

    fun mkRootDir() {
        if (!DisplayVM.rootDir.exists()) {
            DisplayVM.rootDir.mkdirs()
        }
    }

    /**
     * 查询未接收图片
     */
    suspend fun queryUnReceived() = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                    .getUnreceivedImage(Constants.SERIAL)
            logTagD(TAG, "entity==${entity.code}")
            if (entity.isSuccess) {
                if (entity.bodyList.size > 0) {
                    unReceivedImage = entity.withCheck().bodyList
                } else {
                    unReceivedImage?.clear()
                }
                logTagD(TAG, "entity.withCheck().body==${entity.withCheck().bodyList}")
            } else {
                logTagD(TAG, "responseRecived失败")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * 响应收到图片URL
     */
    suspend fun responseReceived(id: String) = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                    .responseImageReceived(id)
            logTagD(TAG, "entity==" + entity.code)
            if (entity.isSuccess) {
                logTagD(TAG, "responseRecived成功")
            } else {
                logTagD(TAG, "responseRecived失败")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * 获取扫码上传url
     */
    suspend fun getUploadUrl() = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.BASE_URL)
                    .getImageUploadUrl()
            logTagD(TAG, "entity==" + entity.code)
            if (entity.isSuccess) {
                logTagD(TAG, "getUploadUrl成功")
                //过滤末尾#符号后拼接地址
                val url = entity.body.toString().trimEnd('#') + WELCOME_SN
                url
            } else {
                logTagD(TAG, "getUploadUrl失败")
                null
            }

        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}