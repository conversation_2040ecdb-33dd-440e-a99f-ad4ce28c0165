
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.util.NULL_LAYOUT
import com.czur.starry.device.wallpaperdisplay.util.WallPaperListItem

private const val TAG = "FlexboxAdapter"
private const val ASSETS_PATH = "file:///android_asset/"

class FlexboxAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var mContext: Context? = null
    private var mData: MutableList<WallPaperListItem>? = null


    private val diffCallback = object : DiffUtil.ItemCallback<WallPaperListItem>() {
        override fun areItemsTheSame(oldItem: WallPaperListItem, newItem: WallPaperListItem): Boolean {
            return oldItem.wallPaperEntity?.absPath == newItem.wallPaperEntity?.absPath
        }

        override fun areContentsTheSame(oldItem: WallPaperListItem, newItem: WallPaperListItem): Boolean {
            return oldItem == newItem
        }
    }
    private val mDiffer: AsyncListDiffer<WallPaperListItem> = AsyncListDiffer(this, diffCallback)


    fun getData(position: Int): WallPaperListItem {
        return mDiffer.currentList[position]
    }

    fun getAllData(): MutableList<WallPaperListItem> {
        return mDiffer.currentList
    }

    fun setData(context: Context, data: MutableList<WallPaperListItem>) {
        mContext = context
        mData = data
        mDiffer.submitList(mData)
    }

    fun refreshData(data: MutableList<WallPaperListItem>) {
        mData = data
        mDiffer.submitList(mData)
    }

    fun updateItem(position: Int) {
        notifyItemChanged(position)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView: View =
            LayoutInflater.from(mContext).inflate(R.layout.custom_item_layout, parent, false)
        return NormalHolder(itemView)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val normalHolder = holder as NormalHolder

        if (position > 0) {
            normalHolder.progressBar.show()
            normalHolder.selectLayout.gone()
            normalHolder.mtvName.invisible(false)
            normalHolder.mImage.show()

            val data = mDiffer.currentList[position]
            var path = ASSETS_PATH + data.wallPaperEntity?.absPath
            if (data.wallPaperEntity?.textMode == NULL_LAYOUT){
                path = data.wallPaperEntity.absPath
            }
            logTagD(TAG, "===path=${path}")
            normalHolder.mtvName.text = data.wallPaperEntity!!.name.substringBeforeLast(".")
            Glide.with(holder.itemView.context)
                .load(path)
                .signature(ObjectKey(System.currentTimeMillis()))
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        normalHolder.progressBar.gone()
                        return false
                    }

                })
                .into(normalHolder.mImage)
        } else if (position == 0) {
            normalHolder.progressBar.gone()
            normalHolder.selectLayout.show()
            normalHolder.mtvName.invisible()
            normalHolder.mImage.gone()
        }

    }


    override fun getItemCount(): Int {
        return mDiffer.currentList.size
    }

    class NormalHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var mImage: ImageFilterView
        var mtvName: TextView
        var selectLayout: ConstraintLayout
        var layoutLocal: LinearLayout
        var layoutQrcode: LinearLayout
        var progressBar: ProgressBar

        init {
            mtvName = itemView.findViewById(R.id.tv_name) as TextView
            mImage = itemView.findViewById(R.id.im_custom) as ImageFilterView
            selectLayout = itemView.findViewById(R.id.select_id) as ConstraintLayout
            layoutLocal = itemView.findViewById(R.id.layout_local) as LinearLayout
            layoutQrcode = itemView.findViewById(R.id.layout_qrcode) as LinearLayout
            progressBar = itemView.findViewById(R.id.progress_bar) as ProgressBar
        }
    }
}