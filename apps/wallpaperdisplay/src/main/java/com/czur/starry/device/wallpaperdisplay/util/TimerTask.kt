package com.czur.starry.device.wallpaperdisplay.util

import com.czur.czurutils.log.logTagD
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Calendar
import java.util.TimeZone

/**
 * created by wangh 22.0914
 */
private const val TAG = "WallpaperTimerTask"


//设置屏保结束时间
suspend fun setTimeOver(setTime: Int) = withContext(Dispatchers.IO) {
    val timeOut = getCalendarMilliTime(setTime)
    logTagD(TAG, "timeOut=${timeOut}=")
    setTimeSystemProp(timeOut)
}



suspend fun getCalendarMilliTime(setTime: Int): Long = withContext(Dispatchers.IO) {
    val calendar = Calendar.getInstance()
    calendar.timeInMillis = System.currentTimeMillis()
    calendar.timeZone = TimeZone.getDefault()
    //设置时间
    calendar.set(Calendar.MILLISECOND, 0)
    calendar.set(Calendar.SECOND, 0)
    val min = calendar.get(Calendar.MINUTE)
    calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE))
    val hour = calendar.get(Calendar.HOUR_OF_DAY)
    logTagD(TAG, "===min==${min}")
    logTagD(TAG, "===currentHour==${hour}")
    val lastHour = hour + setTime
    if (lastHour >= 24) {
        val time = (lastHour - 24)
        calendar.set(Calendar.HOUR_OF_DAY, time)
        calendar.add(Calendar.DAY_OF_MONTH, 1)
    } else {
        calendar.set(Calendar.HOUR_OF_DAY, lastHour)
    }

    calendar.timeInMillis
}