package com.czur.starry.device.wallpaperdisplay.view.dialog

import android.text.InputFilter
import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.doOnTextChanged
import com.czur.starry.device.baselib.utils.addEmojiFilter
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getShowLength
import com.czur.starry.device.baselib.utils.keyboard.SoftKeyboardStateHelper
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.view.floating.KeyBackFloatFragment
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.wallpaperdisplay.R

/**
 *  author : copy from fileManager
 *  time   :2023/08/24
 */


class RenameDialog(

    val inputText: String = "",
    private val lastSelection: Boolean = true,
    private val allowEmptyInput: Boolean = false,
    private val isConstraintLength: Boolean = false,
    private val limitLength: Int = 30,
    private val onInputConfirmListener: ((inputText: String) -> Unit)? = null
) : KeyBackFloatFragment(
    bgDark = true,
    inputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
) {
    override fun getLayoutId(): Int = R.layout.dialog_rename

    private val inputDialogEt by findView<EditText>(R.id.inputDialogEt)
    private val doubleBtnFloatTitleTv by findView<TextView>(R.id.doubleBtnFloatTitleTv)
    private val confirmBtn by findView<CommonButton>(R.id.confirmBtn)
    private val cancelBtn by findView<CommonButton>(R.id.cancelBtn)


    private var defY = 0F // float 的初始位置

    private val screenHeight by lazy {
        getScreenHeight()
    }

    private val softKeyboardStateHelper by lazy {
        SoftKeyboardStateHelper(this)
    }

    override fun initView() {
        softKeyboardStateHelper.doOnImeOpen { keyboardHeightInPx ->
            defY = subView.y
            val offset = -50
            val dy = defY + subView.height - (screenHeight - keyboardHeightInPx) + offset
            // 只有当需要向上移动时（dy > 0）才进行移动
            if (dy > 0) {
                subView.animate().apply {
                    duration = 100
                    yBy(-dy)
                }
            }
        }

        softKeyboardStateHelper.doOnImeClose {
            if (!isDismissing) {
                subView.animate().apply {
                    duration = 100
                    y(defY)
                }
            }
        }


        inputDialogEt.setText(inputText)
        inputDialogEt.addEmojiFilter()

        if (lastSelection) {
            inputDialogEt.setSelection(inputText.length)
        }
        // 字符长度为50, 中文2个字符
        inputDialogEt.showTextLength = 50
        // 没有输入内容时, 禁止点击确定按钮
        if (!allowEmptyInput) {
            // 不允许空输入时, 判断确定按钮的状态
            inputDialogEt.doOnTextChanged { _, _, _, _ ->
                refreshConfirmEnable()
            }
            refreshConfirmEnable()
        }
        if (isConstraintLength) {
            inputDialogEt.filters = arrayOf(InputFilter.LengthFilter(limitLength))
        }

        inputDialogEt.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
            ) {
                if (confirmBtn.isEnabled && confirmBtn.isVisible) {
                    confirmBtn.performClick()
                }
                true
            } else {
                true
            }
        }


        confirmBtn.setOnClickListener {
            dismiss()
            onInputConfirmListener?.let { listener ->
                val text = inputDialogEt.text.toString()
                listener(text)
            }
        }
        cancelBtn.setOnClickListener {
            dismiss()
        }

        // 显示输入法
        inputDialogEt.requestFocus()
        inputDialogEt.keyboardShow()

    }

    /**
     * 刷新确定按钮 是否可点击
     * 如果 输入框中没有输入内容,则确定按钮不可点击
     */
    private fun refreshConfirmEnable() {
        confirmBtn.isEnabled = !inputDialogEt.text.isNullOrBlank() && inputDialogEt.text.toString()
            .getShowLength() <= 50
    }
}