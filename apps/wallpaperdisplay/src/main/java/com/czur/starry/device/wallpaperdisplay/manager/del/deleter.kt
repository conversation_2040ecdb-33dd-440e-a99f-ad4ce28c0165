package  com.czur.starry.device.wallpaperdisplay.manager.del

import com.czur.starry.device.wallpaperdisplay.bean.FileEntity

/**
 * Created by 陈丰尧 on 2021/9/8
 */

/**
 * 删除器
 */
interface IDeleter {
    /**
     * 执行删除操作
     */
    suspend fun del(src: FileEntity): <PERSON><PERSON>an
}

/**
 * 本地文件删除器
 */
class LocalDeleter : IDeleter {
    override suspend fun del(src: FileEntity): Boolean {
//        val fileAccess = FileManager.createFileAccess(AccessType.LOCAL)
        return  false//fileAccess.delFiles(listOf(src)) == FileAccess.Result.SUCCESS
    }
}

