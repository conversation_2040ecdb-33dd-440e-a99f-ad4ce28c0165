package com.czur.starry.device.wallpaperdisplay.adapter

import android.util.Log
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.file.filelib.ShareFile
import com.czur.starry.device.file.filelib.getIconRes
import com.czur.starry.device.wallpaperdisplay.R

/**
 * base from  陈丰尧   0812
 */
class FileChooseAdapter : RecyclerView.Adapter<BaseVH>() {
    private val TAG = "FileChooseAdapter"
    var currentShareFile: ShareFile? = null

    var dataList: List<ShareFile> = emptyList()
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var selPos = -1
        set(value) {
            val preSel = selPos
            field = value
            notifyItemChanged(value)
            if (preSel >= 0) {
                notifyItemChanged(preSel)
            }
        }

    fun clearSel() {
        val preSel = selPos
        if (selPos >= 0) {
            selPos = -1
            notifyItemChanged(preSel)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_share_file, parent)
    }

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val data = dataList[position]

        holder.setAlpha(1F, R.id.itemShareFileContainer)

        holder.setImgResource(
            data.getIconRes(false),
            R.id.itemShareFileIcon
        )
        holder.setTextAndTip(data.name, R.id.itemShareFileTv)

        // 选中
        holder.visible(position == selPos, R.id.itemShareFileSelIv)
    }

    override fun getItemCount() = dataList.size
    fun getData(pos: Int): ShareFile = dataList[pos]
}