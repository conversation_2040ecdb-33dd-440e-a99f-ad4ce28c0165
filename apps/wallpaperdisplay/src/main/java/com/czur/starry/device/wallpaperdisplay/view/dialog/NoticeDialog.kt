package com.czur.starry.device.wallpaperdisplay.view.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.PointerIcon
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.wallpaperdisplay.R

/**
 * created by wangh 22.0818
 */

private const val TAG = "NoticeDialog"

class NoticeDialog : BaseDialog(), View.OnClickListener {
    var confirmClickListener: ((Int) -> Unit)? = null
    var cancelClickListener: (() -> Unit)? = null
    private var timeout = 1
    private var mLastCheckedRadioBtn: RadioButton? = null

    private val cbSel1: RadioButton by findView(R.id.cb_sel1)
    private val cbSel2: RadioButton by findView(R.id.cb_sel2)
    private val cbSel3: RadioButton by findView(R.id.cb_sel3)
    private val cbSel4: RadioButton by findView(R.id.cb_sel4)
    private val cbSel5: RadioButton by findView(R.id.cb_sel5)
    private val cbSel6: RadioButton by findView(R.id.cb_sel6)
    private val normalDialogConfirmBtn: View by findView(R.id.normalDialogConfirmBtn)
    private val closeIv: View by findView(R.id.closeIv)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_remind, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setClickListenerAndPointer(cbSel1, cbSel2, cbSel3, cbSel4, cbSel5, cbSel6)
        mLastCheckedRadioBtn = cbSel1

        normalDialogConfirmBtn.setOnClickListener {
            logTagD(TAG, "====timeout=${timeout}===")
            confirmClickListener?.invoke(timeout)
            dismiss()
        }

        closeIv.setOnClickListener {
            cancelClickListener?.invoke()
            dismiss()
        }

    }

    class Builder {
        private var confirmClickListener: ((Int) -> Unit)? = null
        private var cancelClickListener: (() -> Unit)? = null


        fun setConfirmClickListener(listener: (Int) -> Unit): Builder {
            confirmClickListener = listener
            return this
        }


        fun build(): NoticeDialog {
            val dialog = NoticeDialog()
            dialog.confirmClickListener = confirmClickListener
            dialog.cancelClickListener = cancelClickListener
            dialog.isBottom = false
            return dialog
        }
    }

    private fun setClickListenerAndPointer(vararg radioButtons: RadioButton) {
        radioButtons.forEach {
            it.setOnClickListener(this)
            it.pointerIcon = PointerIcon.getSystemIcon(requireContext(), PointerIcon.TYPE_ARROW)
        }
    }

    override fun onClick(v: View?) {
        val checkedRb = v as RadioButton
        when (v?.id) {
            R.id.cb_sel1 -> {
                timeout = 1
            }

            R.id.cb_sel2 -> {
                timeout = 3
            }

            R.id.cb_sel3 -> {
                timeout = 6
            }

            R.id.cb_sel4 -> {
                timeout = 12
            }

            R.id.cb_sel5 -> {
                timeout = 16
            }

            R.id.cb_sel6 -> {
                timeout = 24
            }
        }
        if (mLastCheckedRadioBtn != null) {
            mLastCheckedRadioBtn?.isChecked = false
        }
        mLastCheckedRadioBtn = checkedRb
        mLastCheckedRadioBtn?.isChecked = true
    }
}