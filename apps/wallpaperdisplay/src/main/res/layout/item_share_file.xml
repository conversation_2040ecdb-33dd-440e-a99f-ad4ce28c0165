<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="100px"
    android:id="@+id/itemShareFileContainer">

    <ImageView
        android:id="@+id/itemShareFileIcon"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/itemShareFileTv"
        android:layout_width="0px"
        android:layout_height="match_parent"
        android:layout_marginLeft="19px"
        android:layout_marginRight="70px"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="normal"
        app:float_tips="@string/float_tip_empty"
        app:layout_constraintLeft_toRightOf="@id/itemShareFileIcon"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/itemShareFileSelIv"
        android:layout_width="22px"
        android:layout_height="22px"
        android:layout_marginRight="29px"
        android:src="@drawable/ic_file_choose_sel"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>