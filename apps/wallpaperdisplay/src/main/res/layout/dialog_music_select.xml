<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="600px"
        android:layout_height="600px"
        android:layout_gravity="center"
        android:paddingLeft="30px"
        android:paddingRight="30px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/dialog_bg_color">


        <LinearLayout
            android:id="@+id/layout_img"
            android:layout_width="140px"
            android:layout_height="140px"
            android:layout_marginTop="180px"
            android:gravity="center"
            app:bl_corners_radius="12px"
            app:bl_solid_color="@color/im_background"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/im_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_local_select"
                android:textStyle="bold"
                app:round="12px" />
        </LinearLayout>

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20px"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="24px"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_img"
            tools:text="@string/tv_local_music_content" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/normalDialogCancelBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginBottom="30px"
            android:text="@string/btn_tv_cancel"
            android:textSize="20px"
            app:baselib_theme="dark"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/normalDialogConfirmBtn" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/normalDialogConfirmBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginLeft="30px"
            android:text="@string/btn_tv_ok"
            android:textSize="20px"
            app:baselib_theme="white2"
            app:layout_constraintLeft_toRightOf="@id/normalDialogCancelBtn"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/normalDialogCancelBtn" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>