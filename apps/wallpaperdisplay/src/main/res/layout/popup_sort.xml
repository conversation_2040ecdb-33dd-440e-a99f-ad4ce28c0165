<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:bl_corners_radius="10px"
    app:bl_solid_color="@color/white"
    tools:background="@color/white">

    <LinearLayout
        android:id="@+id/sortItemTimeAsc"
        style="@style/style_sort_pop_layout"
        app:layout_constraintBottom_toTopOf="@id/sortItemTimeDesc"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/timeAscTv"
            style="@style/style_sort_pop_text"
            android:text="@string/str_sort_time_asc" />

        <ImageView
            android:id="@+id/timeDescIv"
            style="@style/style_sort_pop_img" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sortItemTimeDesc"
        style="@style/style_sort_pop_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sortItemTimeAsc">

        <TextView
            android:id="@+id/timeDescTv"
            style="@style/style_sort_pop_text"
            android:text="@string/str_sort_time_desc" />

        <ImageView
            android:id="@+id/timeAscIv"
            style="@style/style_sort_pop_img" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sortItemNameAsc"
        style="@style/style_sort_pop_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sortItemTimeDesc">

        <TextView
            android:id="@+id/nameAscTv"
            style="@style/style_sort_pop_text"
            android:text="@string/str_sort_name_asc" />

        <ImageView
            android:id="@+id/nameDescIv"
            style="@style/style_sort_pop_img" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/sortItemNameDesc"
        style="@style/style_sort_pop_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sortItemNameAsc">

        <TextView
            android:id="@+id/nameDescTv"
            style="@style/style_sort_pop_text"
            android:text="@string/str_sort_name_desc" />

        <ImageView
            android:id="@+id/nameAscIv"
            style="@style/style_sort_pop_img" />
    </LinearLayout>


    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/nameBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:barrierDirection="right"
        app:constraint_referenced_ids="sortItemTimeAsc,sortItemTimeDesc,sortItemNameAsc,sortItemNameDesc" />


</androidx.constraintlayout.widget.ConstraintLayout>