package com.czur.starry.device.memoryservice

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.memoryservice.utils.cancelAlarmTask
import com.czur.starry.device.memoryservice.utils.compareTheFreeSize
import com.czur.starry.device.memoryservice.utils.startAlarmTask
import com.czur.starry.device.otalib.OTAHandler

/**
 * created by wangh 22.0531
 */


class MemoryService : LifecycleService() {
    companion object {
        const val TAG = "MemoryService"

        //会议，录像后需要检测标记
        var needCheckMemorySize = false
    }

    override fun onCreate() {
        super.onCreate()

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_DEVICE_STORAGE_LOW)
            addAction(Intent.ACTION_DATE_CHANGED)
        }
        this.registerReceiver(storageLowAction, filter)


        launch {
            if (cancelAlarmTask()) {
                startAlarmTask()
            }
        }

        //监听会议和本地录像
        OTAHandler.allMeetingStateLive.observe(this) {
            if (needCheckMemorySize && !it) {
                compareTheFreeSize()
            }
        }
    }


    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {

        val command = intent?.getIntExtra("command", 0)
        logTagD(TAG, "====command====$command=")
        if (command == 1) {
            compareTheFreeSize()
        }
        return super.onStartCommand(intent, flags, startId)
    }


    override fun onDestroy() {
        super.onDestroy()
        this.unregisterReceiver(storageLowAction)
    }


    private val storageLowAction = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            when (intent.action) {
                //低内存 500M以下
                Intent.ACTION_DEVICE_STORAGE_LOW,
                    //内存满 1M
                "android.intent.action.DEVICE_STORAGE_FULL" -> {
                    logTagD(TAG, "========ACTION=" + intent.action)
                }

                Intent.ACTION_DATE_CHANGED -> {
                    logTagD(TAG, "========ACTION=" + intent.action)
                }
            }

        }

    }

}