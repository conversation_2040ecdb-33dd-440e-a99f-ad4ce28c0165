<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    tools:context="com.czur.keystone.MainActivity">


    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:weightSum="3"
        android:layout_marginTop="10px"
        android:layout_marginBottom="10px"
        android:layout_marginLeft="10px"
        android:layout_marginRight="10px">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:id="@+id/line_top"
            android:gravity="center_vertical|center_horizontal">

            <TextView
                android:text="@string/top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/textView3"
                android:textSize="24px"
                android:gravity="center" />

            <Button
                android:text="@string/horizontal_expand_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/button_top_expand"
                android:textSize="36px" />

            <Button
                android:text="@string/horizontal_shrink_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/button_top_shrink"
                android:textSize="36px" />
        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:id="@+id/line_left_right"
            android:weightSum="3"
            android:gravity="center_vertical|center_horizontal">

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_gravity="center">

                <TextView
                    android:text="@string/left"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/textView5"
                    android:textSize="24px"
                    android:gravity="center"
                    android:layout_gravity="center_horizontal" />

                <Button
                    android:text="@string/horizontal_expand_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/button_left_expand"
                    android:textSize="36px"
                    android:layout_gravity="center_horizontal" />

                <Button
                    android:text="@string/horizontal_shrink_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/button_left_shrink"
                    android:textSize="36px"
                    android:layout_gravity="center_horizontal" />

            </LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"></LinearLayout>

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_gravity="center">

                <TextView
                    android:text="@string/right"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/textView6"
                    android:textSize="24px"
                    android:gravity="center" />

                <Button
                    android:text="@string/horizontal_expand_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/button_right_expand"
                    android:textSize="36px"
                    android:layout_gravity="center_horizontal" />

                <Button
                    android:text="@string/horizontal_shrink_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/button_right_shrink"
                    android:textSize="36px"
                    android:layout_gravity="center_horizontal" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:id="@+id/line_bottom"
            android:gravity="center_vertical|center_horizontal">

            <TextView
                android:text="@string/bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/textView4"
                android:textSize="24px"
                android:gravity="center" />

            <Button
                android:text="@string/horizontal_expand_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/button_bottom_expand"
                android:textSize="36px" />

            <Button
                android:text="@string/horizontal_shrink_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/button_bottom_shrink"
                android:textSize="36px" />
        </LinearLayout>

    </LinearLayout>

    <TextView
        android:text="@string/vertex_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="false"
        android:layout_alignParentRight="false"
        android:id="@+id/textView_lt"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:textSize="18px" />

    <TextView
        android:text="@string/vertex_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="false"
        android:layout_alignParentLeft="false"
        android:id="@+id/textView_rt"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:textSize="18px" />

    <TextView
        android:text="@string/vertex_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="false"
        android:layout_alignParentRight="false"
        android:id="@+id/textView_lb"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:textSize="18px" />

    <TextView
        android:text="@string/vertex_default"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="false"
        android:layout_alignParentLeft="false"
        android:id="@+id/textView_rb"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:textSize="18px" />
</RelativeLayout>
