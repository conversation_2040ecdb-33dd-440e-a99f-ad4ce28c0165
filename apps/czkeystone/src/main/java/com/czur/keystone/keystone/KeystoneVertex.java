package com.czur.keystone.keystone;

import com.czur.keystone.LogUtil;
import com.czur.starry.device.baselib.utils.prop.SystemPropUtilKt;

public class KeystoneVertex {

    private static final String PROP_KEYSTONE_TOP_LEFT = "persist.sys.keystone.lt";
    private static final String PROP_KEYSTONE_TOP_RIGHT = "persist.sys.keystone.rt";
    private static final String PROP_KEYSTONE_BOTTOM_LEFT = "persist.sys.keystone.lb";
    private static final String PROP_KEYSTONE_BOTTOM_RIGHT = "persist.sys.keystone.rb";
    private static final String PROP_KEYSTONE_UPDATE = "persist.sys.keystone.update";

    public Vertex vTopLeft;
    public Vertex vTopRight;
    public Vertex vBottomLeft;
    public Vertex vBottomRight;

    public void getAllKeystoneVertex() {
            String sTopLeft = SystemPropUtilKt.getStringSystemProp(PROP_KEYSTONE_TOP_LEFT, "0,0");
            String sTopRight = SystemPropUtilKt.getStringSystemProp(PROP_KEYSTONE_TOP_RIGHT, "0,0");
            String sBottomLeft = SystemPropUtilKt.getStringSystemProp(PROP_KEYSTONE_BOTTOM_LEFT, "0,0");
            String sBottomRight = SystemPropUtilKt.getStringSystemProp(PROP_KEYSTONE_BOTTOM_RIGHT, "0,0");
			LogUtil.d("getAllKeystoneVertex:  " + sTopLeft + " " + sTopRight + " " + sBottomLeft + " " + sBottomRight);
            vTopLeft = new Vertex(sTopLeft);
            vTopRight = new Vertex(sTopRight);
            vBottomLeft = new Vertex(sBottomLeft);
            vBottomRight = new Vertex(sBottomRight);
    }

    public void updateAllKeystoneVertex() {
        LogUtil.d("Update vertex to: "+ vTopLeft + " " + vTopRight + " " + vBottomLeft + " " + vBottomRight);
        SystemPropUtilKt.setStringSystemProp(PROP_KEYSTONE_TOP_LEFT, vTopLeft.toString());
        SystemPropUtilKt.setStringSystemProp(PROP_KEYSTONE_TOP_RIGHT, vTopRight.toString());
        SystemPropUtilKt.setStringSystemProp(PROP_KEYSTONE_BOTTOM_LEFT, vBottomLeft.toString());
        SystemPropUtilKt.setStringSystemProp(PROP_KEYSTONE_BOTTOM_RIGHT, vBottomRight.toString());
        SystemPropUtilKt.setStringSystemProp(PROP_KEYSTONE_UPDATE, "1");
    }
}


