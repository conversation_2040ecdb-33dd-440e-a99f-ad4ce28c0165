package com.czur.keystone.keystone;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.Intent;
import android.database.ContentObserver;
import android.database.Cursor;
import android.hardware.SensorEvent;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.os.PowerManager;
import android.provider.Settings;

import androidx.annotation.NonNull;

import com.czur.keystone.App;
import com.czur.keystone.CZKeystoneNative;
import com.czur.keystone.LogUtil;
import com.czur.starry.device.baselib.utils.prop.SystemPropUtilKt;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class AutoCorrectionCZUR {
    // TODO  adding method for changing size
    private static final int FRAME_WIDTH = 1920;
    private static final int FRAME_HEIGHT = 1080;

    private static final String SYSTEM_KEY_PIC_SIZE = "persist.vendor.overscan.main";
    private static final int DEF_PIC_SIZE = 100;

    private static final String KEY_AUTO_FOCUS = "settingAutoFocus";
    private static final int AUTO_FOCUS_ON = 1;

    private static final String KEY_AUTO_KEYSTONE = "settingAutoKeystone";
    private static final int AUTO_KEYSTONE_ON = 1;

    private static final String FOCUS_ACTION = "com.czur.starry.device.settings.startFocus";

    private static final Uri FOCUS_URI = Uri.parse("content://com.czur.starry.device.settings.czurprovider/focus");
    public static final String COLUMN_FOCUS_STATUS = "focusStatus";
    public static final int FOCUS_STATUS_IDLE = 0; //对焦空闲
    public static final int FOCUS_STATUS_WORKING = 1; //正在对焦中

    // 补充对焦的
    private static final int ADDED_FOCUS_THRESHOLD = 200;

    private int currentAutoFocusStatus = FOCUS_STATUS_IDLE;

    // 类加载的时间, 就认为是开机时间
    private static long bootTime = System.currentTimeMillis();

    private static final long NO_AF_TIME = 3000L; // 启动的3s内, 屏蔽自动对焦事件


    CZKeystoneNative alg = null;
    private final ContentResolver cr;

    private PowerManager pm;

    private PowerManager getPowerManager() {
        if (pm == null) {
            pm = App.getContext().getSystemService(PowerManager.class);
        }
        return pm;
    }

    @SuppressLint("HandlerLeak")
    private final Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == MSG_CHECK_AND_UPDATE) {
                if (currentAutoFocusStatus != FOCUS_STATUS_WORKING) {
                    LogUtil.d("补充对焦");
                    // 当前没有对焦, 则直接对焦
                    doAutoFocus();
                }
            }
        }
    };

    private static final int MSG_CHECK_AND_UPDATE = 1;

    public AutoCorrectionCZUR() {
        alg = new CZKeystoneNative();
        cr = App.getContext().getContentResolver();

        updateCurrentFocusStatus();

        cr.registerContentObserver(FOCUS_URI, true, new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange) {
                super.onChange(selfChange);
                // 更新对焦的状态
                updateCurrentFocusStatus();
            }
        });
    }

    /**
     * 检查自动对焦的状态
     */
    private void updateCurrentFocusStatus() {
        try {
            Cursor cursor = cr.query(FOCUS_URI, null, null, null, null);
            cursor.moveToFirst();
            currentAutoFocusStatus = cursor.getInt(cursor.getColumnIndex(COLUMN_FOCUS_STATUS));
            LogUtil.d("更新自动对焦状态:" + currentAutoFocusStatus);
            cursor.close();
        } catch (Exception e) {
            LogUtil.d("更新自动对焦状态异常:" + e);
            e.printStackTrace();
        }
    }

    public void onAccelerometerSensorChanged(SensorEvent event) {
        float xValue = event.values[0];
        float yValue = event.values[1];
        float zValue = event.values[2];
        long timestamp = event.timestamp; //NS
        //LogUtil.d("time: "+event.timestamp);
        if (alg.NeedUpdate(xValue, yValue, zValue, timestamp)) {
            LogUtil.d("NEED UPDATE...");

            // 1. 执行梯形矫正
            doAutoCorrection();

            // 2. 自动对焦对焦
            checkAndFocus();
        }
    }

    private void checkAndFocus() {
        if (System.currentTimeMillis() - bootTime < NO_AF_TIME) {
            bootTime = 0L;  // 通过修改这个时间, 来实现只屏蔽一次的效果
            LogUtil.d("开机屏蔽一次自动对焦");
            return;
        }

        // 自动对焦
        int focusStatus = Settings.Global.getInt(App.getContext().getContentResolver(),
                KEY_AUTO_FOCUS, AUTO_FOCUS_ON);
        if (focusStatus == AUTO_FOCUS_ON) {
            LogUtil.d("自动对焦 开启");
            if (currentAutoFocusStatus == FOCUS_STATUS_WORKING) {
                // 正在对焦, 则200ms后检测一次
                handler.sendEmptyMessageDelayed(MSG_CHECK_AND_UPDATE, ADDED_FOCUS_THRESHOLD);
                LogUtil.d("自动对焦正在进行中 " + ADDED_FOCUS_THRESHOLD + "ms 后检查");
            } else {
                // 当前没有对焦, 则直接对焦
                doAutoFocus();
            }
        } else {
            LogUtil.d("自动对焦 关闭");
        }
    }


    /**
     * 开始自动梯形矫正
     */
    public void doAutoCorrection() {
        // 自动梯形矫正
        int autoKeystone = Settings.Global.getInt(cr, KEY_AUTO_KEYSTONE, AUTO_KEYSTONE_ON);
        if (autoKeystone != AUTO_KEYSTONE_ON) {
            LogUtil.d("自动梯形矫正 关闭,跳过");
            return;
        }
        LogUtil.d("开始自动梯形矫正");

        float[] angle = alg.GetAngleDataForUpdate();
        float anglex = angle[0];
        float angley = angle[1];

        int picSize = getPicSize();
        LogUtil.d("当前画面尺寸是:" + picSize);
        int currentWidth = FRAME_WIDTH * picSize / 100;
        int currentHeight = FRAME_HEIGHT * picSize / 100;

        float[] ptss = alg.AngleToVeteRaw(anglex, angley, 0.0f,
                0.0f, 0, 0.0f, currentWidth, currentHeight);

        KeystoneVertex kv = new KeystoneVertex();
        //kv.vTopLeft       = vetexes.get(0);

        kv.vTopLeft = new Vertex(Math.round(ptss[0]), Math.round(ptss[1]));
        kv.vTopLeft.y = -kv.vTopLeft.y;

        //kv.vTopRight      = vetexes.get(1);
        kv.vTopRight = new Vertex(Math.round(ptss[2]), Math.round(ptss[3]));
        kv.vTopRight.x = kv.vTopRight.x - currentWidth;
        kv.vTopRight.y = -kv.vTopRight.y;

        //kv.vBottomLeft    = vetexes.get(2);
        kv.vBottomLeft = new Vertex(Math.round(ptss[4]), Math.round(ptss[5]));
        kv.vBottomLeft.y = kv.vBottomLeft.y - currentHeight;
        kv.vBottomLeft.y = -kv.vBottomLeft.y;

        //kv.vBottomRight   = vetexes.get(3);
        kv.vBottomRight = new Vertex(Math.round(ptss[6]), Math.round(ptss[7]));
        kv.vBottomRight.x = kv.vBottomRight.x - currentWidth;
        kv.vBottomRight.y = currentHeight - kv.vBottomRight.y;
        //kv.vBottomLeft.y  = -kv.vBottomLeft.y;
        kv.updateAllKeystoneVertex();
    }

    /**
     * 开始自动对焦
     */
    private void doAutoFocus() {
        boolean screenOn = getPowerManager().isInteractive();
        LogUtil.d("屏幕状态:" + screenOn);
        if (screenOn) {
            // 开始自动对焦, 进入自动对焦页面
            LogUtil.d("开始对焦");
            Intent intent = new Intent(FOCUS_ACTION);
            intent.setPackage("com.czur.starry.device.settings");
            App.getContext().startService(intent);
        } else {
            LogUtil.d("屏幕熄灭状态, 不做自动对焦");
        }
    }

    /**
     * 获取画面尺寸
     *
     * @return 画面尺寸百分比
     */
    int getPicSize() {
        String picSizeValue = SystemPropUtilKt.getStringSystemProp(SYSTEM_KEY_PIC_SIZE,"");
        return parsePisSizeStr(picSizeValue);
    }

    /**
     * 解析系统画面尺寸的值
     * 格式为:overscan 80,80,80,80
     */
    private int parsePisSizeStr(String picSizeStr) {
        String pattern = "([1-9][0-9]*)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(picSizeStr);
        if (m.find()) {
            try {
                return Integer.valueOf(m.group(0));
            } catch (Exception exp) {
                return DEF_PIC_SIZE;
            }
        } else {
            return DEF_PIC_SIZE;
        }
    }
}

