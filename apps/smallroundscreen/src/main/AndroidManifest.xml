<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system">

    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="true" />

    <application
        android:name=".app.App"
        android:allowBackup="true"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:persistent="true"
        android:theme="@style/BaseAppTheme">
        <activity
            android:name=".AnimationActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:configChanges="${startUpatyPlaceHolder}"
            android:theme="@style/TransparentTheme"
            android:launchMode="singleInstance" />

        <activity
            android:name=".MainActivity"
            android:configChanges="${atyPlaceHolder}"
            android:exported="true"/>

    </application>
</manifest>