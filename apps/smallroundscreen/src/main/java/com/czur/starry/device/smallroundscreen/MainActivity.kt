package com.czur.starry.device.smallroundscreen

import android.app.ActivityOptions
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logTagD


class MainActivity : AppCompatActivity() {

    private val TAG = "MainActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        supportActionBar?.hide()
        // 让屏幕保持常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        bootSmallRoundScreen()
    }

    private fun bootSmallRoundScreen() {
        logTagD(TAG, "启动小圆屏")
        val intent = Intent().apply {
            component = ComponentName("com.czur.starry.device.smallroundscreen", "com.czur.starry.device.smallroundscreen.AnimationActivity")
        }.newTask()

        val displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val displays = displayManager.displays
        var targetDisplayId = -1
        for (display in displays) {
            val metrics = DisplayMetrics()
            display.getRealMetrics(metrics)
            if (metrics.widthPixels == 360 && metrics.heightPixels == 360) {
                targetDisplayId = display.displayId
                break
            }
        }

        if (targetDisplayId != -1) {
            val options = ActivityOptions.makeBasic().apply {
                launchDisplayId = targetDisplayId
            }
            logTagD(TAG, "找到了尺寸为 360x360 的屏幕")
            startActivity(intent, options.toBundle())
        } else {
            logTagD(TAG, "没有找到尺寸为 360x360 的屏幕")
        }
    }
}