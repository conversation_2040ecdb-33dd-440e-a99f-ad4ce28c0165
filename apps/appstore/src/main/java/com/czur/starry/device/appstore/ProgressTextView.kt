package com.czur.starry.device.appstore

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView

/**
 * Created by 陈丰尧 on 2021/10/26
 */
class ProgressTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : AppCompatTextView(context, attrs, defStyleAttr) {

    companion object {
        private const val DEF_RADIUS = 10F
        private const val DEF_BG_COLOR = 0xFFDADFFD.toInt()
        private const val DEF_PROGRESS_COLOR = 0xFF5879FC.toInt()
        private const val DEF_PROGRESS_HEIGHT = 2F
    }

    private val paint = Paint().apply {
        isAntiAlias = true
    }

    private var radius: Float = DEF_RADIUS
    private var bgColor: Int = DEF_BG_COLOR
    private var progressColor = DEF_PROGRESS_COLOR
    private var progressHeight = DEF_PROGRESS_HEIGHT

    init {
        attrs?.let {
            obtainStyleAttrs(it)
        }
    }

    var progress: Int = 0
        set(value) {
            field = value
            invalidate()
        }

    /**
     * 解析各种属性
     */
    private fun obtainStyleAttrs(attrs: AttributeSet) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ProgressTextView)
        radius = typedArray.getDimension(R.styleable.ProgressTextView_radius, DEF_RADIUS)
        bgColor = typedArray.getColor(R.styleable.ProgressTextView_bgColor, DEF_BG_COLOR)
        progressColor = typedArray.getColor(
            R.styleable.ProgressTextView_progressColor,
            DEF_PROGRESS_COLOR
        )
        progressHeight = typedArray.getDimension(
            R.styleable.ProgressTextView_progressHeight,
            DEF_PROGRESS_HEIGHT
        )

        typedArray.recycle()
    }


    override fun onDraw(canvas: Canvas) {

        // 绘制圆角背景
        paint.color = bgColor
        paint.style = Paint.Style.FILL
        canvas.drawRoundRect(0F, 0F, width.toFloat(), height.toFloat(), radius, radius, paint)

        // 绘制进度条, 左右两侧圆角部分不绘制
        paint.color = progressColor
        val progressWidth = (width.toFloat() - radius * 2) * progress / 100F
        canvas.drawRect(
            radius,
            height - progressHeight,
            radius + progressWidth,
            height.toFloat(),
            paint
        )

        // 绘制文字
        super.onDraw(canvas)
    }

}