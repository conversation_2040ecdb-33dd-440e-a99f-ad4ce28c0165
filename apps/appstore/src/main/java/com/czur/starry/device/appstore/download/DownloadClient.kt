package com.czur.starry.device.appstore.download

import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.appstore.App
import com.drake.net.Get
import com.drake.net.Net
import com.drake.net.component.Progress
import com.drake.net.interfaces.ProgressListener
import com.drake.net.scope.NetCoroutineScope
import kotlinx.coroutines.Dispatchers
import java.io.File

class DownloadClient(
    private val lifecycleOwner: LifecycleOwner
) {
    companion object {
        private const val TAG = "DownloadClient"

        private const val DOWNLOAD_MAX_CONCURRENCY = 4  // 最大并发量
    }

    private val fileDir: File = App.instance.filesDir

    private val downloadingIds = mutableSetOf<String>()

    private val downloadQueue = mutableListOf<DownloadParams>()

    /**
     * 下载完成的回调, 有可能是下载完成, 有可能是下载出错
     */
    var downloadDoneListener: ((taskId: Int) -> Unit)? = null

    private val scope = NetCoroutineScope(
        lifecycleOwner = lifecycleOwner,
        dispatcher = Dispatchers.Default
    )

    /**
     * 是否有正在下载的任务
     */
    fun hasDowningTask(): Boolean = downloadingIds.isNotEmpty()

    private fun isMaxConcurrency(): Boolean = downloadingIds.size >= DOWNLOAD_MAX_CONCURRENCY

    fun addDownload(
        downloadUrl: String,
        downloadId: String,
        saveFileName: String,
        estimatedSize: Long = -1L,
        isXapk: Boolean = false,
        downloadCallback: (max: Long, saved: Long, finish: Boolean, filePath: String) -> Unit
    ) {
        if (isMaxConcurrency()) {
            logTagD(TAG, "下载任务已满, 添加到队列: $downloadId")
            downloadQueue.add(
                DownloadParams(
                    downloadUrl,
                    downloadId,
                    saveFileName,
                    estimatedSize,
                    isXapk,
                    downloadCallback
                )
            )
            return
        }

        val estimated = if (estimatedSize <= 0) {
            1024 * 1024L
        } else {
            estimatedSize
        }

        var isSaveFileName = saveFileName
        val directoryPath = File(fileDir, saveFileName)
        var isCallBackAbsPath = directoryPath.absolutePath

        if (isXapk) {
            if (!directoryPath.exists()) {
                directoryPath.mkdirs()
            }
            isSaveFileName += ".xapk"
            val isCallBackFile = File(directoryPath, "$saveFileName.xapk")
            isCallBackAbsPath = isCallBackFile.absolutePath
        }

        scope.launch {
            try {
                Get<File>(downloadUrl) {
                    logTagV(
                        TAG,
                        "开始下载:downloadId:$downloadId, isSaveFileName:$isSaveFileName, downloadUrl:$downloadUrl"
                    )
                    setId(downloadId)
                    setDownloadFileName(isSaveFileName)
                    setDownloadDir(directoryPath)
                    setDownloadFileNameConflict(true)

                    addDownloadListener(object : ProgressListener() {
                        override fun onProgress(p: Progress) {
                            val totalSize = p.totalByteCount
                            val currentSize = p.currentByteCount
                            downloadCallback(totalSize, currentSize, false, isCallBackAbsPath)
                        }

                    })
                }.await()
                logTagD(TAG, "下载完成: $isCallBackAbsPath - $downloadUrl")
                downloadCallback(estimated, estimated, true, isCallBackAbsPath)
            } catch (tr: Throwable) {
                if (downloadId !in downloadingIds) {
                    logTagD(TAG, "下载取消", tr = tr)
                } else {
                    logTagD(TAG, "下载失败", tr = tr)
                    downloadCallback(-2, -2, true, isCallBackAbsPath)
                }
                deleteDirectory(directoryPath.path)
            } finally {
                downloadingIds.remove(downloadId)
                checkQueue()
            }
        }
        downloadingIds.add(downloadId)
    }

    private fun deleteDirectory(path: String): Boolean {
        val directory = File(path)
        if(directory.exists()) {
            val files = directory.listFiles()
            if(null!=files) {
                for (file in files) {
                    if(file.isDirectory) {
                        //递归删除子文件夹
                        deleteDirectory(file.path)
                    } else {
                        //删除子文件
                        file.delete()
                    }
                }
            }
        }
        //删除主文件夹
        return directory.delete()
    }

    private fun checkQueue() {
        logTagV(TAG, "checkQueue: ${downloadQueue.size}")
        if (downloadQueue.isEmpty()) {
            return
        }
        val params = downloadQueue.removeAt(0)
        addDownload(
            params.downloadUrl,
            params.downloadId,
            params.saveFileName,
            params.estimatedSize,
            params.isXapk,
            params.downloadCallback
        )
    }

    fun cancelAllDownload(downloadId: String) {
        logTagD(TAG, "cancelAllDownload: $downloadId")

        downloadQueue.removeIf {
            it.downloadId == downloadId
        }

        downloadingIds.remove(downloadId)
        Net.cancelId(downloadId).also {
            logTagD(TAG, "$downloadId 取消结果: $it")
        }
    }

    class DownloadParams(
        val downloadUrl: String,
        val downloadId: String,
        val saveFileName: String,
        val estimatedSize: Long = -1L,
        val isXapk: Boolean,
        val downloadCallback: (max: Long, saved: Long, finish: Boolean, filePath: String) -> Unit
    )
}