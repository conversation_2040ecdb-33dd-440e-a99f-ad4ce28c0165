package com.czur.starry.device.appstore.download

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagV
import kotlinx.coroutines.runBlocking

class DownloadUtil(
    private val context: Context,
) {
    companion object {
        private const val APK_FILE_SUFFIX = ".apk"
        private const val TAG = "DownloadUtil"

        fun createFileName(pkgName: String): String {
            return runBlocking { pkgName.md5() } + System.currentTimeMillis() + APK_FILE_SUFFIX
        }

        fun createXApkFolderName(pkgName: String): String {
            return runBlocking { pkgName.md5() } + System.currentTimeMillis()
        }
    }

    private var initFinishListener: (() -> Unit)? = null

    private var downloadBinder: DownloadService.DownloadBinder? = null
    private val conn = object : ServiceConnection {

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            downloadBinder = service as? DownloadService.DownloadBinder
            initFinishListener?.invoke()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            downloadBinder = null
        }

    }


    fun init(initFinish: () -> Unit) {
        initFinishListener = initFinish
        bindService()
    }


    private fun bindService() {
        logTagV(TAG,"绑定DownloadService")
        val intent = Intent(context, DownloadService::class.java).apply {
            type = System.currentTimeMillis().toString()
        }
        context.startService(intent)
        context.bindService(intent, conn, Context.BIND_AUTO_CREATE)
    }

    private fun unbindService() {
        logTagV(TAG,"解绑DownloadService")
        context.unbindService(conn)
    }

    fun addDownloadReq(
        downloadUrl: String,
        pkgName: String,
        versionCode:Int,
        estimatedSize: Long = -1L,
        isXapk:Boolean = false
    ): String {
        val newSaveFile = if (isXapk) {
            createXApkFolderName(pkgName)
        } else {
            createFileName(pkgName)
        }
        val request = DownloadRequest(
            downloadUrl = downloadUrl,
            pkgName = pkgName,
            saveFileName = newSaveFile,
            versionCode = versionCode,
            estimatedSize = estimatedSize,
            isXapk = isXapk
        )
        return downloadBinder?.let {
            val downloadId = it.addDownloadReq(request)
            downloadId
        } ?: ""
    }

    fun getDownloadProgressLive() = downloadBinder?.downloadInfoUpdateTimeLive


    fun recycler() {
        unbindService()
    }

    fun isDownloading(packageName: String): Boolean {
        return downloadBinder?.isDownloading(packageName) ?: false
    }

    fun getDownloadPercent(packageName: String): Int {
        return downloadBinder?.getDownloadPercent(packageName) ?: 0
    }

    fun removeDownloadInfo(downloadId: String) {
        downloadBinder?.removeDownloadInfo(downloadId)
    }

    fun cancelAllDownload(pkgName: String) {
        downloadBinder?.cancelAllDownload(pkgName)
    }

}