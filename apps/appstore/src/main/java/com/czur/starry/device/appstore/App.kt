package com.czur.starry.device.appstore

import com.czur.starry.device.baselib.base.listener.StarryApp
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 2021/10/20
 */
class App : StarryApp() {
    override val commonLogTag: String
        get() = "AppStore"

    companion object {
        var instance: App by Delegates.notNull()
    }

    override fun onCreate() {
        super.onCreate()

        instance = this
    }
}