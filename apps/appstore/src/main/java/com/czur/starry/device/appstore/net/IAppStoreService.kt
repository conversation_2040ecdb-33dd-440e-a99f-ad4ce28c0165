package com.czur.starry.device.appstore.net

import android.os.Build
import com.czur.starry.device.appstore.entity.AppTag
import com.czur.starry.device.appstore.entity.NetAppItem
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.baselib.utils.SettingHandler
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

/**
 * Created by 陈丰尧 on 2021/10/26
 */
interface IAppStoreService {
    /**
     * 获取应用市场的应用列表
     */
    fun getCallRecords(
        keywords: String? = null,
        sdkVersion: String = Build.VERSION.SDK_INT.toString(),
        page: Int = 0,
        size: Int = Int.MAX_VALUE,
        tagCode: String? = null,
        language:String = SettingHandler.czurLang.serverCode,
        frameworkVersion: String = Constants.FIRMWARE_NAME,
        type: Type = object :
            TypeToken<List<NetAppItem>>() {}.type
    ): MiaoHttpEntity<NetAppItem>

    fun installCallBack(
        version: String,
        pkgName: String,
        udid: String = Constants.SERIAL,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>

    fun getTagList(
        language: String = SettingHandler.czurLang.serverCode,
        token: Type = object : TypeToken<List<AppTag>>() {}.type
    ): MiaoHttpEntity<AppTag>
}