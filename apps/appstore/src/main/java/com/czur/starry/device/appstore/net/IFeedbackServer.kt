package com.czur.starry.device.appstore.net

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost

/**
 * Created by 陈丰尧 on 2023/5/25
 */
interface IFeedbackServer {
    companion object {
        const val FEED_TYPE_APP_NAME = 1    // 1为需适配的应用反馈
        const val FEED_TYPE_OTHER = 2       // 2为通用反馈
    }

    @MiaoHttpPost("/api/app/store/app/addFeedback")
    fun addAppFeedback(
        @MiaoHttpParam("title") title: String,
        @MiaoHttpParam("type") feedType: Int = FEED_TYPE_APP_NAME,
        @MiaoHttpParam("fwVersion") fwVersion: String = Constants.FIRMWARE_NAME,
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}