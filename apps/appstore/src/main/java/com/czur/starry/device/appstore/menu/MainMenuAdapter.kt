package com.czur.starry.device.appstore.menu

import android.view.MotionEvent
import android.view.ViewGroup
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.entity.AppTag
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.uilib.CZMenuItem

/**
 * Created by 陈丰尧 on 2023/8/11
 */
data class ShowMenuTag(
    val appTag: AppTag,
    val isSelected: Boolean = false,
)

class MainMenuAdapter : BaseDifferAdapter<ShowMenuTag>() {
    override fun areItemsTheSame(oldItem: ShowMenuTag, newItem: ShowMenuTag): Boolean {
        return oldItem.appTag == newItem.appTag
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_menu, parent)
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: ShowMenuTag) {
        val menuItem = holder.getView<CZMenuItem>(R.id.menuItem)
        val appTag = itemData.appTag
        if (appTag.tagIconRes != null) {
            menuItem.setIconRes(appTag.tagIconRes)
        } else {
            menuItem.setIconUrl(appTag.imageUrl, R.drawable.ic_menu_icon_all)
        }
        if (appTag.tagNameRes != null) {
            menuItem.setTitleResAndTip(appTag.tagNameRes)
        } else {
            menuItem.setTitleAndTip(appTag.tagName)
        }
        if (appTag.id == MENU_ID_UNINSTALL) {
            menuItem.dividerType = CZMenuItem.DividerType.TOP
        } else {
            menuItem.dividerType = CZMenuItem.DividerType.NONE
        }

        holder.visible(itemData.isSelected,R.id.menuSelView)
        holder.visible(false,R.id.menuSelViewCursor)

        holder.itemView.setOnHoverListener { v, event ->
            if (event == null || itemData.isSelected || holder.bindingAdapterPosition < 0) {
                return@setOnHoverListener false
            }
            if (event.action == MotionEvent.ACTION_HOVER_ENTER) {
                holder.visible(true, R.id.menuSelViewCursor)
            } else if (event.action == MotionEvent.ACTION_HOVER_EXIT) {
                holder.visible(false, R.id.menuSelViewCursor)
            }
            true
        }
    }
}