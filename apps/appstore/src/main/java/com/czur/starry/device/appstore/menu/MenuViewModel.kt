package com.czur.starry.device.appstore.menu

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.appstore.R
import com.czur.starry.device.appstore.entity.AppTag
import com.czur.starry.device.appstore.manager.AppStoreManager
import com.czur.starry.device.appstore.ui.NetAppFragment.Companion.NET_TAG_ALL
import com.czur.starry.device.baselib.utils.ONE_SECOND
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.retry

/**
 * Created by 陈丰尧 on 2023/8/11
 */
private const val TAG = "MenuViewModel"

const val MENU_ID_ALL = -1
const val MENU_ID_UNINSTALL = -2
const val MENU_TAG_ALL = "all"
const val MENU_TAG_UNINSTALL = "uninstall"

class MenuViewModel(application: Application) : AndroidViewModel(application) {
     val allAppTag: AppTag = AppTag(
        MENU_ID_ALL, MENU_TAG_ALL, NET_TAG_ALL, null,
        tagIconRes = R.drawable.ic_menu_icon_all,
        tagNameRes = R.string.tab_all_app
    )
     val uninstallAppTag: AppTag = AppTag(
        MENU_ID_UNINSTALL, MENU_TAG_UNINSTALL, "uninstall", null,
        tagIconRes = R.drawable.ic_menu_icon_del,
        tagNameRes = R.string.tab_uninstall_app
    )
    private val menuTagsFlow = flow {
        emit(loadTags())
    }
        .onStart {
            emit(listOf(allAppTag, uninstallAppTag))
        }
        .retry {
            logTagW(TAG, "loadTags error: $it")
            delay(ONE_SECOND)
            true
        }

    val selectTagFlow = MutableStateFlow(allAppTag)

    val showMenuFlow = combine(menuTagsFlow, selectTagFlow) { menuTags, selectTag ->
        menuTags.map {
            ShowMenuTag(it, it.id == selectTag.id)
        }
    }.flowOn(Dispatchers.Default)

    private suspend fun loadTags(): List<AppTag> {
        val netApps = AppStoreManager.getTagList().withCheck().bodyList
        netApps.add(0, allAppTag)
        netApps.add(uninstallAppTag)
        return netApps
    }

    /**
     * 选择标签
     */
    fun selectTag(tag: AppTag) {
        logTagD(TAG, "选择标签: $tag")
        selectTagFlow.value = tag
    }
}