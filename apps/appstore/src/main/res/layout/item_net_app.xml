<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="38px"
    android:paddingVertical="25px"
    tools:background="@color/base_bg_color"
    tools:ignore="PxUsage"
    tools:viewBindingIgnore="true">

    <ImageView
        android:id="@+id/itemNetAppIconIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_def_icon" />

    <TextView
        android:id="@+id/itemNetAppNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:layout_marginTop="15px"
        android:includeFontPadding="false"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/itemNetAppIconIv"
        app:layout_constraintTop_toTopOf="@id/itemNetAppIconIv"
        tools:text="微信" />

    <TextView
        android:id="@+id/itemNetAppVersionTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15px"
        android:includeFontPadding="false"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@id/itemNetAppIconIv"
        app:layout_constraintLeft_toLeftOf="@id/itemNetAppNameTv"
        tools:text="v1.36" />

    <TextView
        android:id="@+id/itemNetAppSizeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:includeFontPadding="false"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBaseline_toBaselineOf="@id/itemNetAppVersionTv"
        app:layout_constraintLeft_toRightOf="@id/itemNetAppVersionTv"
        tools:text="40MB" />

    <androidx.constraintlayout.widget.Placeholder
        android:id="@+id/itemPlaceholder"
        android:layout_width="140px"
        android:layout_height="60px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/itemInstallBtn"
        style="@style/ActionBtn.Light"
        android:text="@string/install"
        android:visibility="gone"
        android:textSize="@dimen/itemInstallBtnSize"
        app:layout_constraintBottom_toBottomOf="@id/itemPlaceholder"
        app:layout_constraintLeft_toLeftOf="@id/itemPlaceholder"
        app:layout_constraintRight_toRightOf="@id/itemPlaceholder"
        app:layout_constraintTop_toTopOf="@id/itemPlaceholder" />

    <com.czur.starry.device.appstore.ProgressTextView
        android:id="@+id/itemCancelDownBtn"
        android:layout_width="0px"
        android:layout_height="0px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/cancel_install"
        android:textColor="@color/bg_main_blue"
        android:textSize="@dimen/itemInstallBtnSize"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/itemPlaceholder"
        app:layout_constraintLeft_toLeftOf="@id/itemPlaceholder"
        app:layout_constraintRight_toRightOf="@id/itemPlaceholder"
        app:layout_constraintTop_toTopOf="@id/itemPlaceholder" />

    <ProgressBar
        android:id="@+id/itemInstallProgress"
        android:layout_width="50px"
        android:layout_height="50px"
        android:indeterminateTint="@color/bg_main_blue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/itemPlaceholder"
        app:layout_constraintLeft_toLeftOf="@id/itemPlaceholder"
        app:layout_constraintRight_toRightOf="@id/itemPlaceholder"
        app:layout_constraintTop_toTopOf="@id/itemPlaceholder" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/itemOpenBtn"
        style="@style/ActionBtn.Light"
        android:text="@string/open"
        app:layout_constraintBottom_toBottomOf="@id/itemPlaceholder"
        app:layout_constraintLeft_toLeftOf="@id/itemPlaceholder"
        app:layout_constraintRight_toRightOf="@id/itemPlaceholder"
        app:layout_constraintTop_toTopOf="@id/itemPlaceholder" />


    <com.czur.uilib.btn.CZButton
        android:id="@+id/itemUpdateBtn"
        style="@style/ActionBtn.Dark"
        android:layout_width="140px"
        android:layout_height="60px"
        android:layout_marginRight="10px"
        android:text="@string/update"
        app:baselib_theme="dark"
        app:layout_constraintBottom_toBottomOf="@id/itemPlaceholder"
        app:layout_constraintRight_toLeftOf="@id/itemOpenBtn"
        app:layout_constraintTop_toTopOf="@id/itemPlaceholder" />


</androidx.constraintlayout.widget.ConstraintLayout>