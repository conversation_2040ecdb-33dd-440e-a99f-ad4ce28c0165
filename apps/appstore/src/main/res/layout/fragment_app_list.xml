<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/appListRv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="22px"
        android:clipToPadding="false"
        android:overScrollMode="never" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/allAppGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="appListRv" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main_blue"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/localEmptyIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_local_empty"
        app:layout_constraintBottom_toTopOf="@id/localEmptyTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/localEmptyTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:layout_marginBottom="170px"
        android:text="@string/str_local_empty"
        android:textColor="@color/text_common"
        android:alpha="0.7"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/localEmptyIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/emptyGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="localEmptyIv,localEmptyTv" />


</androidx.constraintlayout.widget.ConstraintLayout>