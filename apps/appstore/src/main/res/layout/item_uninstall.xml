<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="38px"
    android:paddingVertical="25px"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded"
    tools:viewBindingIgnore="true">

    <ImageView
        android:id="@+id/itemUninstallIconIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@mipmap/icon" />

    <TextView
        android:id="@+id/itemUninstallNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:layout_marginTop="15px"
        android:includeFontPadding="false"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/itemUninstallIconIv"
        app:layout_constraintTop_toTopOf="@id/itemUninstallIconIv"
        tools:text="微信" />

    <TextView
        android:id="@+id/itemUninstallVersionTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15px"
        android:includeFontPadding="false"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@id/itemUninstallIconIv"
        app:layout_constraintLeft_toLeftOf="@id/itemUninstallNameTv"
        tools:text="v1.36" />

    <TextView
        android:id="@+id/itemUninstallSizeTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:includeFontPadding="false"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBaseline_toBaselineOf="@id/itemUninstallVersionTv"
        app:layout_constraintLeft_toRightOf="@id/itemUninstallVersionTv"
        tools:text="40MB" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/itemUninstallBtn"
        android:text="@string/uninstall"
        style="@style/ActionBtn.Dark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/itemUninstallProgress"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginRight="45px"
        android:indeterminateTint="@color/bg_main_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>