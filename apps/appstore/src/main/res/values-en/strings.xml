<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">App Store</string>
    <string name="tab_all_app">All apps</string>
    <string name="tab_uninstall_app">Uninstall apps</string>
    <string name="uninstall">Uninstall</string>
    <string name="uninstall_dialog">Yes</string>
    <string name="install">Install</string>
    <string name="open">Open</string>
    <string name="update">Update</string>
    <string name="cancel_install">Cancel</string>
    <string name="dialog_title_uninstall">Notification</string>
    <string name="dialog_msg_uninstall">Uninstall \"%s\" ?</string>
    <string name="toast_net_error">No Internet connection</string>
    <string name="str_local_empty">No application</string>

    <string name="str_load_net_error">Fail to load</string>
    <string name="btn_load_error_try_again">Reload</string>
    <string name="toast_install_app_fail">Fail to install the application</string>
    <string name="dialog_btn_submit">Submit</string>

    <string name="tab_feedback">Feedback</string>
    <string name="dialog_msg_install_video_entertainment">You can install the apps on your smart phone, then cast your smartphone screen to StarryHub.</string>
    <string name="dialog_msg_install_other">StarryHub has limited storage space, please reconsider before installing.</string>
    <string name="dialog_btn_install">Still install</string>
    <string name="dialog_msg_feedback_app">Please let us know the name of  the app you would like to use on StarryHub.</string>
    <string name="et_hint_feedback_app_name">Enter the name of the app.</string>
    <string name="toast_submit_success">Successfully Submitted</string>
    <string name="dialog_normal_cancel1">Later</string>
    <string name="dialog_normal_cancel2">No</string>
    <string name="str_do_not_uninstall">Not yet</string>
    <string name="str_never_remind">Don\'t remind me again</string>
    <string name="str_hint_install_app">StarryHub is made for conference use only. This App might not be fully compatible with StarryHub. Would you like to uninstall this App?</string>
</resources>
