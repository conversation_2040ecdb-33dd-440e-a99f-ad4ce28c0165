<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:clickable="false"
    tools:viewBindingIgnore="true">

    <com.czur.starry.device.baselib.widget.NetStatusIcon
        android:id="@+id/simpleStatusBarWifi"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_gravity="right"
        android:layout_marginTop="30px"
        android:layout_marginRight="49px"
        android:clickable="false"
        android:focusable="false"/>
</FrameLayout>