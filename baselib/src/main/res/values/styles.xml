<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="BaseLib_Dialog.FullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">#00000000</item>
        <item name="android:windowIsFloating">false</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="BaseLib.CommonDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">#00000000</item>
    </style>

    <style name="CustomDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowMinWidthMinor">100%</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>