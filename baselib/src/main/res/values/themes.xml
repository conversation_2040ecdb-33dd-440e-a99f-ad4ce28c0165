<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="BaseAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowBackground">@color/dialog_bg_color</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColorHint">#80FFFFFF</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:textCursorDrawable">@drawable/drawable_base_cursor</item>

        <!--ContextMenu 的背景颜色-->
        <item name="android:dropDownListViewStyle">@style/listView</item>
        <item name="android:itemTextAppearance">@style/MyContextMenuText</item>

        <item name="android:defaultFocusHighlightEnabled">false</item>
    </style>


    <style name="listView" parent="Widget.AppCompat.ListView.Menu">
        <!--ContextMenu 的背景颜色-->
        <item name="android:background">@color/menu_bg_color</item>
        <item name="android:divider">@null</item>

    </style>

    <style name="MyContextMenuText" parent="@android:style/TextAppearance.Widget.IconMenu.Item">
        <!--ContextMenu 的文字颜色-->
        <item name="android:textColor">@color/sel_menu_text</item>
        <item name="android:textSize">18px</item>
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.Dialog">
        <!-- Customize your theme here. -->
        <item name="windowNoTitle">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>
    <style name="AppTheme.NoDisplay">
        <item name="android:windowBackground">@null</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowNoDisplay">true</item>
    </style>
</resources>