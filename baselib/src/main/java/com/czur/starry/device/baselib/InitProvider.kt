package com.czur.starry.device.baselib

import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import com.czur.czurutils.base.CZURContentProvider
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpManager

private const val TAG = "InitProvider"
class InitProvider : CZURContentProvider() {


    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        TODO("Implement this to handle requests to delete one or more rows")
    }

    override fun getType(uri: Uri): String? {
        TODO(
            "Implement this to handle requests for the MIME type of the data" +
                    "at the given URI"
        )
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        TODO("Implement this to handle requests to insert a new row.")
    }

    override fun onCreate(): Boolean {
        CZURAtyManager.appContext = context!!.applicationContext
        logTagD(TAG,"initProvider")
        SPHandler.init(context!!)
        MiaoHttpManager.getInstance().init()
        return true
    }

    override fun query(
        uri: Uri, projection: Array<String>?, selection: String?,
        selectionArgs: Array<String>?, sortOrder: String?
    ): Cursor? {
        TODO("Implement this to handle query requests from clients.")
    }

    override fun update(
        uri: Uri, values: ContentValues?, selection: String?,
        selectionArgs: Array<String>?
    ): Int {
        return 0
    }
}