package com.czur.starry.device.baselib.base

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.base.v2.aty.IAtyTag
import com.czur.starry.device.baselib.tips.MergeFactoryUtil
import com.czur.starry.device.baselib.utils.has
import com.noober.background.BackgroundLibrary
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter

/**
 * Created by 陈丰尧 on 12/29/20
 */
@SuppressLint("StaticFieldLeak")
object CZURAtyManager : Application.ActivityLifecycleCallbacks {
    private const val TAG = "CZURAtyManager"
    private const val SETTING_PKG = "com.czur.starry.device.settings"
    private const val APP_STATE_DEBOUNCE_TIME = 200L // 200ms的去重时间

    private val atys = mutableListOf<CZBaseAty>()

    /**
     * 应用的可见性状态
     */
    enum class CZURAppState {
        INIT,       // 初始化
        VISIBLE,    // 可见
        INVISIBLE,  // 不可见
        DESTROY,    // 已销毁
    }


    /**
     * 处于可见的Activity名称
     */
    private val visibleAtyNameSet = mutableSetOf<String>()
    private val _czurAppStateFlow = MutableStateFlow(CZURAppState.INIT)
    val czurAppStateFlow: Flow<CZURAppState> = _czurAppStateFlow
        .debounce(APP_STATE_DEBOUNCE_TIME)
    val czurAppState: CZURAppState
        get() = _czurAppStateFlow.value

    @Volatile
    private var contextAtomic: Context? = null

    private val _backgroundFlow = MutableStateFlow(-1L)
    val backgroundFlow: Flow<Long> = _backgroundFlow.filter { it > 0 }

    var appContext: Context
        set(value) {
            globalAppCtx = value
            contextAtomic = value
        }
        get() {
            // 如果cache有, 则直接返回, 就不需要做原子操作了
            contextAtomic?.let {
                // 如果是主线程, 那么这里一定不会为空
                return it
            }
            // 一共尝试50次
            for (i in 0..50) {
                Thread.yield()      // 放弃当前线程的优先级
                Thread.sleep(20) // 一共是1秒 还没获取到, 则肯定是空的

                contextAtomic?.let {
                    return it
                }
            }
            throw RuntimeException("获取不到Context对象:${Thread.currentThread().name}")
        }

    private val am by lazy {
        appContext.getSystemService(android.app.ActivityManager::class.java)
    }

    /**
     * 是否有正在运行的Activity
     */
    val hasRunningAty: Boolean
        get() = atys.isNotEmpty()


    private val packageName by lazy {
        appContext.packageName
    }

    fun hasActivity(simpleName: String): Boolean {
        return atys.has {
            it::class.java.simpleName == simpleName
        }
    }

    fun getActivity(simpleName: String): CZBaseAty? {
        return atys.find { it::class.java.simpleName == simpleName }
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        // 注入LayoutFactory
        val inflater = activity.layoutInflater
        if (inflater.factory2 == null) {
            val factory = MergeFactoryUtil.setDelegateFactory(activity)
            inflater.factory2 = factory
        } else {
            MergeFactoryUtil.forceSetFactory2(inflater)
        }

        BackgroundLibrary.inject(activity);


        if (activity is CZBaseAty) {
            logTagV(TAG, "添加:${activity::class.java.simpleName}(onCreate)")
            atys.add(activity)
        } else {
            logTagD(TAG, "${activity.javaClass.name}没有继承BaseActivity,不进行管理")
        }

    }

    override fun onActivityStarted(activity: Activity) {
        val simpleName = activity.javaClass.name
        visibleAtyNameSet.add(simpleName)
        checkCZURAppState()
    }

    override fun onActivityResumed(activity: Activity) {
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
        if (!isAppOnForeground()) {
            // ActivityManager 中的最后一个Activity是自己,表示回到了Home
            logTagV(TAG, "App:${packageName} 处于后台, 清理禁止后台显示的Activity")
            clearAllBackground()
        }

        val simpleName = activity.javaClass.name
        visibleAtyNameSet.remove(simpleName)
        checkCZURAppState()
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
        logTagV(TAG, "remove:${activity::class.java.simpleName}(onDestroy)")
        atys.remove(activity)

        val simpleName = activity.javaClass.name
        visibleAtyNameSet.remove(simpleName)
        checkCZURAppState()
    }

    /**
     * 检查App的可见性状态
     */
    private fun checkCZURAppState() {
        if (atys.isEmpty()) {
            logTagD(TAG, "${packageName}已经没有可见页面")
            _czurAppStateFlow.value = CZURAppState.DESTROY
            return
        }
        _czurAppStateFlow.value =
            if (visibleAtyNameSet.isEmpty()) CZURAppState.INVISIBLE else CZURAppState.VISIBLE
    }

    /**
     * 获取当前Activity
     */
    fun currentActivity(): CZBaseAty {
        return atys.last()
    }

    /**
     * 获取当前Activity, 如果没有, 则返回Null
     */
    fun currentActivityOrNull(): CZBaseAty? {
        if (atys.isEmpty()) return null
        return atys.last()
    }

    /**
     * 将Activity栈中, 所有标记为不能后台显示的Activity都finish
     * 直到找到 标记为可以后台显示的Activity
     */
    private fun clearAllBackground() {
        atys.asReversed().forEach {
            // 新的CZCZBusinessAty 自己管理后台Activity
            if (it is BaseActivity && it.banBackGround) {
                if (!it.isFinishing) {
                    it.finish()
                }
            } else {
                return
            }
        }
    }

    fun finishAllAty() {
        atys.asReversed().forEach {
            if (!it.isFinishing) {
                it.finish()
            }
        }
    }

    /**
     * 获取Context, 如果没有Activity,就使用Application的Context
     */
    fun getContext() = if (atys.isNotEmpty()) atys.last() else appContext


    private fun isAppOnForeground(): Boolean {
        val runningAppProcesses = am.runningAppProcesses
        runningAppProcesses.forEach { appProcess ->
            // 有可能是自动对焦中,自动对焦时是不算后台的
            if (isPkgForeground(packageName, appProcess)) {
                return true
            }
        }
        _backgroundFlow.value = SystemClock.elapsedRealtime()   // 更新处于后台的事件
        return false
    }

    private fun isPkgForeground(
        packageName: String,
        appProcess: android.app.ActivityManager.RunningAppProcessInfo,
    ): Boolean {
        return appProcess.processName == packageName
                && appProcess.importance == android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND

    }


    /**
     * 根据AtyTag来移除Activity
     */
    fun removeActivityByAtyTag(atyTag: String) {
        val removeActivities = atys.filter {
            it is IAtyTag && it.atyTag == atyTag
        }
        removeActivities.forEach {
            if (!it.isFinishing) {
                logTagD(TAG, "根据TAG:${atyTag},结束Activity")
                it.finish()
            }
        }
    }

}