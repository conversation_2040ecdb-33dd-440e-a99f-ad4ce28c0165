package com.czur.starry.device.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.databinding.BaselibWidgetNotifyBarBinding
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.requireLifecycleOwner

/**
 * 状态栏右侧的标签
 */
class NotifyBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var itemClick: ((clickItem: ClickItem) -> Unit)? = null

    // 未读消息指示
    var hasUnReadMsg: Boolean = false
        set(value) {
            field = value
            updateMsgPoint()
        }

    private val binding: BaselibWidgetNotifyBarBinding =
        BaselibWidgetNotifyBarBinding.inflate(LayoutInflater.from(context), this, true)

    init {

        // 登录状态的判断
        UserHandler.isLoginLive.observe(requireLifecycleOwner()) { isLogin ->
            updateUserName(isLogin)
        }

        initClick()
    }

    /**
     * 更新消息通知小红点的显隐
     */
    fun updateMsgPoint(){
        binding.notifyBarUnReadPoint.visibility = if (hasUnReadMsg && UserHandler.isLogin) View.VISIBLE else View.GONE
    }


    private fun initClick() {
        // 点击启动个人中心
        findViewById<View>(R.id.notifyBarUserLayer).setOnClickListener {
            itemClick?.invoke(ClickItemUserInfo)
        }

        // 消息中心
        findViewById<View>(R.id.notifyBarMsgIv).setOnClickListener {
            itemClick?.invoke(ClickItemNotice)
        }

        // 网络状态点击事件
        findViewById<NetStatusIcon>(R.id.notifyBarWifiIv).onNetViewClick = {
            itemClick?.invoke(ClickItemNetStatus(it))
        }
    }

    private fun updateUserName(isLogin: Boolean) {
        if (isLogin) {
            binding.notifyBarUserTv.text = ""
        } else {
            binding.notifyBarUserTv.setText(R.string.str_notify_bar_not_login)
        }
    }


}

/**
 * 通知栏点击事件的类型
 */
sealed class ClickItem
data object ClickItemUserInfo : ClickItem()  // 点击用户信息
data object ClickItemNotice : ClickItem()      // 点击消息中

// 点击网络图标
class ClickItemNetStatus(val netStatus: NetStatusUtil.NetStatus) : ClickItem()