package com.czur.starry.device.baselib.network.core.util

import com.czur.starry.device.baselib.network.core.SHA1
import java.util.*

object MiaoHttpUtils {

    fun mkTimeStamp():String = System.currentTimeMillis().toString()

    @JvmStatic
    fun makeRandomStr16(): String {
        val str = StringBuilder()
        val random = Random(10)
        for (i in 0 until 16) {
            str.append(random.nextInt(10))
        }
        return str.toString()
    }

    /**
     * 生成签名信息
     */
    @JvmStatic
    fun generateSignature(paramMap: HashMap<String, String>?): String {
        if (paramMap == null || paramMap.isEmpty())
            return ""
        val params = ArrayList<String>()
        for ((key, value) in paramMap) {
            params.add("$key=$value")
        }
        params.sortWith(Comparator { obj, anotherString -> obj.compareTo(anotherString) })
        val temp = StringBuilder()
        for (i in params.indices) {
            temp.append(params[i])
            if (i != params.size - 1) {
                temp.append("&")
            }
        }
        return SHA1.encode(temp.toString())
    }
}