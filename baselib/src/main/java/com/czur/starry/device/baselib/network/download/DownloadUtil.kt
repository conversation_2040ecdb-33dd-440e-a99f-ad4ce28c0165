package com.czur.starry.device.baselib.network.download

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.internal.headersContentLength
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * Created by 陈丰尧 on 2021/9/26
 */
private const val TAG = "DownloadUtil"
private val okHttpClient: OkHttpClient by lazy {
    OkHttpClient.Builder()
        .connectTimeout(8, TimeUnit.SECONDS)
        .readTimeout(8, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()
}

private fun String.toRequest(): Request {
    return Request.Builder().url(this).build()
}

/**
 * 下载文件
 * @param url 下载地址
 * @param destFile 下载文件
 * @param callbackInterval 进度回调间隔, 默认是100ms
 * @param progress 下载进度回调
 */
suspend fun startDownload(
    url: String,
    destFile: File,
    callbackInterval: Long = 100,
    progress: ((contentLength: Long, downloadSize: Long) -> Unit)? = null
): Boolean {
    return withContext(Dispatchers.IO) {
        try {
            if (destFile.exists()) {
                // 删除原有文件
                destFile.delete()
            }
            var contentLength = -1L
            val response = okHttpClient.newCall(url.toRequest()).execute()
            contentLength = response.headersContentLength()
            val buffer = ByteArray(DEFAULT_BUFFER_SIZE * 2) // buffer 设置为16K
            var bytesCopied: Long = 0
            var lastCallbackTime = 0L
            response.body?.let { body ->
                body.byteStream().buffered().use { input ->
                    destFile.outputStream().use { out ->
                        var bytes = input.read(buffer)
                        while (bytes >= 0) {
                            if (!isActive) {
                                logTagD(TAG, "下载被取消:${url}")
                                return@withContext false
                            }
                            out.write(buffer, 0, bytes)
                            bytesCopied += bytes
                            if (System.currentTimeMillis() - lastCallbackTime > callbackInterval) {
                                progress?.invoke(contentLength, bytesCopied)
                                lastCallbackTime = System.currentTimeMillis()
                            }
                            bytes = input.read(buffer)
                        }
                    }
                }
            }
            progress?.invoke(contentLength, contentLength)
            true
        } catch (exp: Exception) {
            logTagW(TAG, "下载失败:${url}", tr = exp)
            false
        }
    }
}
