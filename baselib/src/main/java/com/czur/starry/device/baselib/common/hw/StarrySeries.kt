package com.czur.starry.device.baselib.common.hw

import androidx.annotation.StringRes
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.common.Constants

/**
 * Created by 陈丰尧 on 2024/12/2
 */
private const val TAG = "StarrySeries"

/**
 * 硬件系列
 */
sealed class StarrySeries<T : StarryModel> {
    abstract val model: T
    protected val sn: String = Constants.SERIAL
}

/**
 * 细分机型
 * 硬件信息在这里配置
 */
sealed class StarryModel(@StringRes val modelName: Int) {
    abstract val cameraInfo: StarryCameraInfo

    /**
     * Q1机型
     */
    sealed class Q1Model(modelName: Int) : StarryModel(modelName) {
        override val cameraInfo: StarryCameraInfo =
            StarryCameraInfo(CameraType.CAMERA_1080P, false)

        data object Q1 : Q1Model(R.string.str_device_name_q1)
        data object Q1S : Q1Model(R.string.str_device_name_q1s)
        data object Q1SPlus : Q1Model(R.string.str_device_name_q1s_plus) {
            override val cameraInfo: StarryCameraInfo =
                StarryCameraInfo(CameraType.CAMERA_4K, true)
        }

        data object Q1Pro : Q1Model(R.string.str_device_name_q1s_pro)
        data object Q1SPro : Q1Model(R.string.str_device_name_q1s_pro) {
            override val cameraInfo: StarryCameraInfo =
                StarryCameraInfo(CameraType.CAMERA_4K, true)
        }
    }

    /**
     * Studio机型
     */
    sealed class StudioModel(modelName: Int) : StarryModel(modelName) {
        override val cameraInfo: StarryCameraInfo =
            StarryCameraInfo(CameraType.CAMERA_4K, false)

        data object Studio : StudioModel(R.string.str_device_name_studio)
        data object StudioS : StudioModel(R.string.str_device_name_studio_s)
        data object StudioSPlus : StudioModel(R.string.str_device_name_studio_s_plus)
        data object StudioPro : StudioModel(R.string.str_device_name_studio_pro)
        data object StudioSPro : StudioModel(R.string.str_device_name_studio_s_pro)
        data object StudioSProMax : StudioModel(R.string.str_device_name_studio_s_pro_max)
    }

    /**
     * Q2机型
     */
    sealed class Q2Model(modelName: Int) : StarryModel(modelName) {
        override val cameraInfo: StarryCameraInfo =
            StarryCameraInfo(CameraType.CAMERA_4K, false)

        data object Q2 : Q2Model(R.string.str_device_name_q2)
        data object Q2S : Q2Model(R.string.str_device_name_q2_s)
        data object Q2SPlus : Q2Model(R.string.str_device_name_q2_s_plus)
        data object Q2Pro : Q2Model(R.string.str_device_name_q2_pro)
        data object Q2SPro : Q2Model(R.string.str_device_name_q2_s_pro)
        data object Q2SProMax : Q2Model(R.string.str_device_name_q2_s_pro_max)
    }
}


// Q1系列 例如:CPH22A2109000001
data object Q1Series : StarrySeries<StarryModel.Q1Model>() {

    override val model: StarryModel.Q1Model by lazy(LazyThreadSafetyMode.NONE) {
        when (sn.substring(1, 6).uppercase()) {
            "PH22A" -> StarryModel.Q1Model.Q1
            "PH24A" -> StarryModel.Q1Model.Q1S
            "PH24B" -> StarryModel.Q1Model.Q1SPlus
            "PA22A" -> StarryModel.Q1Model.Q1Pro
            "PA24A" -> StarryModel.Q1Model.Q1SPro
            else -> {
                logTagW(TAG, "没有找到对应的设备名称, 序列号:${sn}, 默认返回Q1")
                StarryModel.Q1Model.Q1
            }
        }
    }
}

// Q2系列 例如:CTH66A2412000001
data object Q2Series : StarrySeries<StarryModel.Q2Model>() {
    override val model: StarryModel.Q2Model by lazy(LazyThreadSafetyMode.NONE) {
        when (sn.substring(1, 6).uppercase()) {
            "TH66A" -> StarryModel.Q2Model.Q2
            "TH66B" -> StarryModel.Q2Model.Q2S
            "TH66C" -> StarryModel.Q2Model.Q2SPlus
            "TA66A" -> StarryModel.Q2Model.Q2Pro
            "TA66B" -> StarryModel.Q2Model.Q2SPro
            "TA66C" -> StarryModel.Q2Model.Q2SProMax
            else -> {
                logTagW(TAG, "没有找到对应的设备名称, 序列号:${sn}, 默认返回Q2")
                StarryModel.Q2Model.Q2
            }
        }
    }
}

// Studio系列 例如:CBP02A2411000001
data object StudioSeries : StarrySeries<StarryModel.StudioModel>() {
    override val model: StarryModel.StudioModel by lazy(LazyThreadSafetyMode.NONE) {
        when (sn.substring(1, 6).uppercase()) {
            "BP02A" -> StarryModel.StudioModel.Studio
            "BP02B" -> StarryModel.StudioModel.StudioS
            "BP02C" -> StarryModel.StudioModel.StudioSPlus
            "BA02A" -> StarryModel.StudioModel.StudioPro
            "BA02B" -> StarryModel.StudioModel.StudioSPro
            "BA02C" -> StarryModel.StudioModel.StudioSProMax
            else -> {
                logTagW(TAG, "没有找到对应的设备名称, 序列号:${sn}, 默认返回Studio")
                StarryModel.StudioModel.Studio
            }
        }
    }
}
