package com.czur.starry.device.baselib.common.hw

import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants.SERIAL
import com.czur.starry.device.baselib.common.StarryDevLocale

/**
 * Created by 陈丰尧 on 2024/12/2
 */
private const val TAG = "StarrySeries"

// 设备渠道
private const val DEVICE_CHANNEL_MAINLAND_ONLINE = "H"      // 国内线上
private const val DEVICE_CHANNEL_MAINLAND_OFFLINE = "P"     // 国内线下
private const val DEVICE_CHANNEL_OVERSEA = "A"              // 海外

class StarryHWInfo(val sn: String) {
    // 硬件系列
    val series: StarrySeries<*> = when (sn.substring(1, 2).uppercase()) {
        "P" -> Q1Series
        "T" -> Q2Series
        "B" -> StudioSeries
        else -> {
            logTagW(TAG, "没有找到对应的设备名称, 序列号:$sn, 默认返回Q1")
            Q1Series
        }
    }

    // 硬件型号
    val model: StarryModel
        get() = series.model

    /**
     * 摄像头信息
     */
    val cameraInfo: StarryCameraInfo
        get() = model.cameraInfo

    /**
     * 设备地域
     */
    val salesLocale: StarryDevLocale by lazy(LazyThreadSafetyMode.NONE) {
        when (val deviceChannelFlag = SERIAL[2].toString()) {
            DEVICE_CHANNEL_MAINLAND_ONLINE, DEVICE_CHANNEL_MAINLAND_OFFLINE -> StarryDevLocale.Mainland
            DEVICE_CHANNEL_OVERSEA -> StarryDevLocale.Overseas
            else -> {
                logTagW(
                    TAG,
                    "deviceChannelFlag不符合要求:$deviceChannelFlag($SERIAL), 按照国内处理"
                )
                StarryDevLocale.Mainland
            }
        }
    }

    /**
     * 是否有光机
     */
    val hasOpticalEngine: Boolean
        get() = when (series) {
            Q1Series -> true
            Q2Series -> true
            StudioSeries -> false
        }

    /**
     * 是否有触摸屏
     */
    val hasTouchScreen: Boolean
        get() = when (model) {
            StarryModel.StudioModel.StudioSPlus -> true
            StarryModel.StudioModel.StudioSProMax -> true
            else -> false
        }
}