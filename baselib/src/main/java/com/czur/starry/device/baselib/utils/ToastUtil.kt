package com.czur.starry.device.baselib.utils

import android.app.Activity
import android.app.Application
import android.app.Service
import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.view.toast.SimpleToast

/**
 * Created by 陈丰尧 on 2021/9/3
 */
private const val TAG = "ToastUtil"

fun Context.toast(msg: String, isWarnings: Boolean = false) {
    when (this) {
        is Activity -> SimpleToast.makeText(this, msg, isWarnings).show()
        is Service, is Application -> SimpleToast.makeText(this, msg, isWarnings).show()
        else -> logTagW(TAG, "context无法显示对应Toast:${this.javaClass.simpleName}")
    }
}

fun Context.toast(resId: Int, vararg formatArgs: Any = emptyArray()) {
    if (formatArgs.isNotEmpty()) {
        val toastText = getString(resId, *formatArgs)
        toast(toastText)
    } else {
        when (this) {
            is Activity -> SimpleToast.makeText(this, resId).show()
            is Service, is Application -> SimpleToast.makeText(this, resId).show()
            else -> logTagW(TAG, "context无法显示对应Toast:${this.javaClass.simpleName}")
        }
    }
}

/**
 * 从Service中弹出Toast, 因为没有主题, 所以不能使用SimpleToast
 */
private fun toastWithoutTheme(context: Context, msg: String) {
    Toast.makeText(context, msg, Toast.LENGTH_LONG).show()
}

private fun toastWithoutTheme(context: Context, resId: Int) {
    Toast.makeText(context, resId, Toast.LENGTH_LONG).show()
}

fun Context.toastSuccess() = SimpleToast.makeToast(this, true).show()


fun Context.toastFail() = SimpleToast.makeToast(this, false).show()


fun Fragment.toast(msg: String) {
    requireContext().toast(msg)
}

fun Fragment.toast(resId: Int, vararg formatArgs: Any = emptyArray()) {
    context?.toast(resId, *formatArgs)
}

/**
 * 白底红字
 */
fun Fragment.toast(msg: String, isWarnings: Boolean) {
    requireContext().toast(msg, isWarnings = isWarnings)
}

fun Fragment.toastSuccess() = requireContext().toastSuccess()
fun Fragment.toastFail() = requireContext().toastFail()

fun Fragment.toastFailCheckNet(showNormalFail: Boolean = true) {
    if (isNetworkConnected()) {
        if (showNormalFail) {
            toastFail()
        }
    } else {
        toast(R.string.toast_operation_failure_by_net)
    }
}

fun View.toast(msg: String) {
    context.toast(msg)
}


fun View.toast(resId: Int) {
    context.toast(resId)
}

fun View.toastSuccess() = context.toastSuccess()
fun View.toastFail() = context.toastFail()

fun AndroidViewModel.toast(msg: String) = appContext.toast(msg)
fun AndroidViewModel.toast(resId: Int) = appContext.toast(resId)
fun AndroidViewModel.toastSuccess() = appContext.toastSuccess()
fun AndroidViewModel.toastFail() = appContext.toastFail()


