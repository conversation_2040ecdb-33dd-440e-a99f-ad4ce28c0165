package com.czur.starry.device.baselib.utils

import android.content.Context
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.BuildConfig
import com.czur.starry.device.baselib.common.KEY_DEVICE_APP_VERSION_CODE
import com.czur.starry.device.baselib.utils.prop.getIntSystemProp
import com.czur.starry.device.baselib.utils.prop.setIntSystemProp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/8/19
 */
class VersionUtil(
    val appContext: Context,
    private val updateTask: List<Pair<Int, suspend () -> Unit>> = emptyList()
) {
    companion object {
        private const val TAG = "VersionUtil"
        private const val DEF_VERSION_CODE = 0
    }

    private val currentVersionCode = BuildConfig.CZUR_APP_VERSION_CODE

    /**
     * 检查并执行升级程序
     */
    suspend fun checkAndUpdate() {
        logTagD(TAG, "检查App版本")
        val lastVersion = getLastVersionCode()
        if (currentVersionCode == lastVersion) {
            logTagV(TAG, "版本没有改变")
            return
        }
        logTagD(TAG, "检查升级任务")
        updateTask.forEach { (fromVersion, updateTask) ->
            if (fromVersion > lastVersion) {
                doWithoutCatch {
                    logTagD(TAG, "执行升级任务:${fromVersion}")
                    updateTask()    // 执行对应的升级任务
                }
            }
        }
        // 升级完成后记录当前版本号
        saveCurrentVersionCode()
    }

    /**
     * 记录当前的版本号
     */
    suspend fun saveCurrentVersionCode() {
        withContext(Dispatchers.IO) {
            logTagV(TAG, "记录当前版本号")
            setIntSystemProp(KEY_DEVICE_APP_VERSION_CODE, currentVersionCode)
        }
    }

    /**
     * 获取之前的版本号
     */
    private suspend fun getLastVersionCode(): Int {
        return withContext(Dispatchers.IO) {
            getIntSystemProp(KEY_DEVICE_APP_VERSION_CODE, DEF_VERSION_CODE)
        }
    }
}