package com.czur.starry.device.baselib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.ONE_SECOND
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2023/2/13
 * 用来识别用户操作的
 * 分为 空闲和忙碌
 */
class UserInterruptConstraintLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr), CoroutineScope by MainScope() {
    companion object {
        private const val USER_ACTION_IDLE_THRESHOLD = 3 * ONE_SECOND // 3s没有操作后,恢复空闲
    }

    enum class UserInterruptState {
        IDLE,   // 空闲
        ACTIVE, // 忙碌
    }

    private val _interruptFlow = MutableStateFlow(UserInterruptState.IDLE)
    val interruptFlow: StateFlow<UserInterruptState>
        get() = _interruptFlow

    var clickEvent = false

    private var lastOperationTime: Long = 0L // 用户的最后操作时间

    var listenerSwitch = true
    init {

        launch {
            while (listenerSwitch) {
                delay(500)
                System.currentTimeMillis().let {
                    if (lastOperationTime != 0L && it - lastOperationTime > USER_ACTION_IDLE_THRESHOLD) {
                        lastOperationTime = 0
                        _interruptFlow.value = UserInterruptState.IDLE
                    }
                }
            }
        }

        setOnHoverListener { _, _ ->
            mkActive()
            false
        }
    }

    fun mkActive(){
        lastOperationTime = System.currentTimeMillis()
        _interruptFlow.value = UserInterruptState.ACTIVE
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        when (ev?.action) {
            MotionEvent.ACTION_DOWN -> {
                clickEvent = true
            }
            MotionEvent.ACTION_MOVE -> {
                clickEvent = false
            }
            MotionEvent.ACTION_UP -> {
                if (clickEvent) {
                    lastOperationTime = System.currentTimeMillis()
                    _interruptFlow.value = UserInterruptState.ACTIVE
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    //页面结束的监听
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        listenerSwitch = false
    }
}