package com.czur.starry.device.baselib.utils.data

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

/**
 * Created by 陈丰尧 on 2021/9/23
 */
class ReadOnlyLiveDataDelegate<T>(private val liveData: LiveData<T>, private val def: T? = null) :
    ReadOnlyProperty<Any, T> {
    override fun getValue(thisRef: Any, property: KProperty<*>): T {
        val result = liveData.value
        return if (result != null || def == null) {
            result!!
        } else {
            def
        }
    }
}