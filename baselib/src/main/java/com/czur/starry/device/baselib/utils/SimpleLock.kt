package com.czur.starry.device.baselib.utils

import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.withLock

/**
 * Created by 陈丰尧 on 2021/11/9
 */
class SimpleLock {
    private val lock = ReentrantReadWriteLock()
    private val rLock = lock.readLock()
    private val wLock = lock.writeLock()

    fun <T> read(action: () -> T): T = rLock.withLock(action)


    fun <T> write(action: () -> T): T = wLock.withLock(action)
}