package com.czur.starry.device.baselib.utils.fw.proxy

import com.czur.czurutils.log.logTagD
import java.lang.reflect.Method

/**
 * Created by 陈丰尧 on 2023/4/12
 * RkDisplayOutputManager 代理类
 */
class RkDisplayOutputManagerProxy {
    private companion object {
        private const val TAG = "RkDisplayOutputManagerProxy"
        private const val MAIN_SCREEN = 0
    }

    private val rkDisplayOutputManager: Any
    private val rkDisplayOutputManagerClazz by lazy {
        Class.forName("android.os.RkDisplayOutputManager")

    }

    init {
        val rkDisplayOutputManagerClazz = rkDisplayOutputManagerClazz
        val constructor = rkDisplayOutputManagerClazz.constructors[0]
        rkDisplayOutputManager = constructor.newInstance()
    }

    private val setBrightnessMethod: Method by lazy {
        rkDisplayOutputManagerClazz.getMethod("setBrightness", Int::class.java, Int::class.java)
    }

    private val setContrastMethod: Method by lazy {
        rkDisplayOutputManagerClazz.getMethod("setContrast", Int::class.java, Int::class.java)
    }

    private val setSaturationMethod: Method by lazy {
        rkDisplayOutputManagerClazz.getMethod("setSaturation", Int::class.java, Int::class.java)
    }

    private val setHueMethod: Method by lazy {
        rkDisplayOutputManagerClazz.getMethod("setHue", Int::class.java, Int::class.java)
    }

    private val saveConfigMethod: Method by lazy {
        rkDisplayOutputManagerClazz.getMethod("saveConfig")
    }

    private val getBrightnessMethod: Method by lazy {
        rkDisplayOutputManagerClazz.getMethod("getBrightness", Int::class.java)
    }

    /**
     * 获取屏幕亮度
     */
    fun getBrightness(): Int {
        return getBrightnessMethod.invoke(rkDisplayOutputManager, MAIN_SCREEN) as? Int ?: -1
    }

    /**
     * 设置屏幕亮度
     */
    fun setBrightness(brightPercent: Int) {
        val result =
            setBrightnessMethod.invoke(rkDisplayOutputManager, MAIN_SCREEN, brightPercent) as? Int
                ?: -1
        logTagD(TAG, "setBrightness result: $result")
        if (result == 0) {
            logTagD(TAG, "setBrightness success")
            saveConfigMethod.invoke(rkDisplayOutputManager)
        } else {
            logTagD(TAG, "setBrightness failed")
        }
    }

    /**
     * 设置屏幕饱和度
     */
    fun setContrast(contrastPercent: Int) {
        val result =
            setContrastMethod.invoke(rkDisplayOutputManager, MAIN_SCREEN, contrastPercent) as? Int
                ?: -1
        logTagD(TAG, "setContrast result: $result")
        if (result == 0) {
            logTagD(TAG, "setContrast success")
            saveConfigMethod.invoke(rkDisplayOutputManager)
        } else {
            logTagD(TAG, "setContrast failed")
        }
    }

    /**
     * 设置屏幕色调
     */
    fun setSaturation(saturationPercent: Int) {
        val result =
            setSaturationMethod.invoke(rkDisplayOutputManager, MAIN_SCREEN, saturationPercent) as? Int
                ?: -1
        logTagD(TAG, "setSaturation result: $result")
        if (result == 0) {
            logTagD(TAG, "setSaturation success")
            saveConfigMethod.invoke(rkDisplayOutputManager)
        } else {
            logTagD(TAG, "setSaturation failed")
        }
    }

    /**
     * 设置屏幕色调
     */
    fun setHue(huePercent: Int) {
        val result =
            setHueMethod.invoke(rkDisplayOutputManager, MAIN_SCREEN, huePercent) as? Int
                ?: -1
        logTagD(TAG, "setHue result: $result")
        if (result == 0) {
            logTagD(TAG, "setHue success")
            saveConfigMethod.invoke(rkDisplayOutputManager)
        } else {
            logTagD(TAG, "setHue failed")
        }
    }

}