package com.czur.starry.device.baselib.utils;

import androidx.lifecycle.MutableLiveData;

import java.util.List;

/**
 * Created by 陈丰尧 on 3/2/21
 */
public class DifferentLiveData<T> extends MutableLiveData<T> {
    public DifferentLiveData(T value) {
        super(value);
    }

    public DifferentLiveData() {
        super();
    }

    @Override
    public void setValue(T value) {
        T last = getValue();
        if (!isEqual(value, last)) {
            super.setValue(value);
        }
    }

    @Override
    public void postValue(T value) {
        T last = getValue();
        if (!isEqual(value, last)) {
            super.postValue(value);
        }
    }

    public void upgrade() {
        if (Thread.currentThread().getName().toLowerCase().contains("main")) {
            super.setValue(getValue());
        } else {
            super.postValue(getValue());
        }
    }

    public void upgrade(T t) {
        if (Thread.currentThread().getName().toLowerCase().contains("main")) {
            super.setValue(t);
        } else {
            super.postValue(t);
        }
    }

    private boolean isEqual(T value, T lastValue) {
        // 一个为空一个不为空, 则不相等
        if (value == null && lastValue != null) {
            return false;
        }
        if (lastValue == null && value != null) {
            return false;
        }

        if (value == null) {
            // 都为空
            return true;
        }

        if (value instanceof List) {
            List valueList = (List) value;
            List lastList = (List) lastValue;

            if (valueList.size() != lastList.size()) {
                // 数量不等 ,直接返回false
                return false;
            }

            for (int i = 0; i < valueList.size(); i++) {
                Object item = valueList.get(i);
                Object lastItem = lastList.get(i);
                if (!lastItem.equals(item)) {
                    return false;
                }
            }
            return true;
        }

        return value.equals(lastValue);
    }
}
