package com.czur.starry.device.baselib.base

import android.util.TypedValue
import android.view.Gravity
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commitNow
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.getTopControlBarHeight

/**
 * Created by 陈丰尧 on 12/31/20
 */
private const val TAG = "BaseDialog"
open class BaseDialog : AppCompatDialogFragment() {
    var isBottom = false


    override fun onStart() {
        super.onStart()
        val dialog = dialog
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)

        if (isBottom) {
            setDialogBottom()
        } else {
            setDialogCenter()
        }
    }


    open fun show(tag: String = "baseDialog") {

        val currentAty = CZURAtyManager.currentActivity()
        if (isAdded) {
            logTagD(this::class.java.simpleName, "已经添加了, 不能重复添加, remove")
            currentAty.supportFragmentManager.beginTransaction()
                .remove(this)
                .commitNow()
        }
        try {
            show(currentAty.supportFragmentManager, tag)
        } catch (exp: IllegalStateException) {
            logTagW(this::class.java.simpleName, "showError: ${exp.message}")
            doWithoutCatch(this::class.java.simpleName, "Dialog-showError") {
                showAllowingStateLoss(currentAty.supportFragmentManager, tag)
            }
        }
    }

    private fun showAllowingStateLoss(manager: FragmentManager, tag: String) {
        //由于父类方法中mDismissed，mShownByMe不可直接访问，所以此处采用反射修改他们的值
        val dialogFragmentClass = DialogFragment::class.java
        val mDismissed = dialogFragmentClass.getDeclaredField("mDismissed")
        mDismissed.isAccessible = true
        mDismissed.set(this, false)

        val mShownByMe = dialogFragmentClass.getDeclaredField("mShownByMe")
        mShownByMe.isAccessible = true
        mShownByMe.set(this, true)

        manager.commitNow(true) {
            add(this@BaseDialog, tag)
        }
    }

    private fun setDialogCenter(){
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            logTagV(TAG, "适配StudioSPlus")
            dialog?.window?.apply {
                val params = attributes
                params?.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    private fun setDialogBottom() {
        dialog?.window?.apply {
            val params = attributes
            val bottomMargin = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, 30f, context.resources.displayMetrics
            ).toInt()
            params?.y = bottomMargin

            attributes = params
            setGravity(Gravity.BOTTOM)
        }
    }


}