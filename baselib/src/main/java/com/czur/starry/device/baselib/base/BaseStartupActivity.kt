package com.czur.starry.device.baselib.base

import android.content.Intent
import android.os.Bundle
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.launch

/**
 * Created by 陈丰尧 on 2/25/21
 */
abstract class BaseStartupActivity : NoNavActivity() {
    companion object {
        private const val TAG = "BaseStartupActivity"
        const val KEY_NEXT_STEP_DATA = "nextData"
    }

    val preData: Bundle?
        get() = intent.getBundleExtra(KEY_NEXT_STEP_DATA)

    fun startFlowAndFinish(flowId: Int) {
        setResult(RESULT_OK, Intent().apply {
            putExtra(RESULT_FLOW, flowId)
        })
        finish()
    }

    /**
     * 跳过
     */
    fun skipThisStep() {
        setResult(RESULT_CANCELED)
        finish()
    }

    /**
     * 上一步
     */
    fun moveToPreStep() {
        setResult(RESULT_PRE)
        finish()
    }

    /**
     * 下一步
     */
    fun moveToNextStep(bundle: Bundle? = null) {
        val resultData = Intent()
        logTagD(TAG, "下一步")
        bundle?.let {
            logTagD(TAG, "添加Data")
            resultData.putExtra(KEY_NEXT_STEP_DATA, bundle)
        }
        setResult(RESULT_OK, resultData)
        launch {
            finish()
        }
    }
}