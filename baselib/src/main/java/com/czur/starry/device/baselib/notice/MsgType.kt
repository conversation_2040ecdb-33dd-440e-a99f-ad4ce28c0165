package com.czur.starry.device.baselib.notice

/**
 * Created by 陈丰尧 on 4/7/21
 */

/**
 * 消息类型,分为大类 和 二级类型, 均使用int类型表示
 */
class MsgType(val type: Int, val sub: Int) {
    companion object {
        const val SYNC = 1                  // 需要重新拉取数据
        const val MEET = 1 shl 1        // 会议相关
        const val USER_INFO = 1 shl 2   // 用户状态变化
        const val MODULE_BOOT = 1 shl 3 // 模块启动
        const val VOICE_RECOGNITION = 1 shl 4 // 语音识别
        const val NOTIFY_MSG = 1 shl 5 // 通知消息
        const val GET_DATA = 1 shl 6    // 获取同步消息
        const val VOICE_NOTIFY = 1 shl 7 // 语音通知其他模块
        const val COMMON = 1 shl 8      // 通用模块
        const val WRITE_PAD = 1 shl 9   // 手写板模块
        const val SCREEN_SHOT = 1 shl 10   // 截图消息

        /****SYNC****/
        const val SYNC_CONTACT = 1              // 同步联系人
        const val SYNC_COMPANY = 1 shl 1    // 同步企业信息
        const val SYNC_SCHEDULE = 1 shl 2   // 同步日程
        const val SYNC_NOTICE = 1 shl 3     // 同步通知面板
        const val SYNC_RECORD = 1 shl 4     // 同步最近记录
        const val SYNC_PARTY_TRANS_CODE = 1 shl 5 // 同步党建转码
        const val COMMON_PERIPHERAL_USB_CHANGE = 1 shl 6 // 外设USB模式变化


        /****MEET****/
        const val MEET_CALL_VIDEO = 1               // 有新的来电
        const val MEET_STOP = 1 shl 1             // 会议结束
        const val MEET_CMD = 1 shl 2            // 控制会议
        const val MEET_USER_LIST = 1 shl 3      // 用户列表更新
        const val MEET_START = 1 shl 4          // 开始会议
        const val MEET_CMD_FROM_REMOTE = 1 shl 5  // 从服务器来的指令
        const val MEET_NETTY_ONLINE = 1 shl 7   // netty重启完毕
        const val MEET_CHECK_MEETING_LIST = 1 shl 8 // 检查来电
        const val MEETING_USER_UPDATE = 1 shl 9     // 更新单独的用户信息
        const val MEETING_CONFLICT = 1 shl 10       // 视频会议冲突
        const val MEETING_REJECT_CALL_PAGE = 1 shl 11    // 拒绝弹出来电页面
        const val MEETING_STATUS = 1 shl 12       // 会议状态


        /****USER_INFO****/
        const val USER_INFO_LOGIN = 1
        const val USER_INFO_LOGOUT = 1 shl 1
        const val USER_INFO_OTHER_DEVICE_LOGIN = 1 shl 2

        /****MODULE_BOOT****/
        const val MODULE_BOOT_LAUNCHER = 1  // Launcher启动

        /****VOICE_RECOGNITION****/
        const val VOICE_BOOT = 1 shl 1 //启动模块
        const val VOICE_ADJUST = 1 shl 2 //调节配置
        const val VOICE_MEET = 1 shl 3 //会议模块

        /****NOTIFY_MSG****/
        const val NOTIFY_MSG_LATEST = 1 shl 1 // 最新一条消息
        const val NOTIFY_READ = 1 shl 2         // 标记消息为已读

        /****VOICE_NOTIFY****/
        const val VOICE_NOTIFY_BRIGHTNESS = 1   // 调整画面亮度
        const val VOICE_NOTIFY_OPERATOR_PIC_SIZE = 1 shl 1     // 调整画面尺寸

        /****COMMON****/
        const val COMMON_TOAST = 1  // 显示全局Toast
        const val COMMON_BYOM_REQUEST = 1 shl 1 // 宜享BYOM请求
        const val COMMON_BYOM_IP = 1 shl 2 // 宜享BYOM请求

        /****WRITE_PAD****/
        const val WRITE_CLIENT_CONN = 1 // 客户端连接

        const val VOICE_CONTACT_ACTION = "com.czur.device.notice.voice.contact"
        const val VOICE_MEETING_ACTION = "com.czur.device.notice.voice.meeting"
        const val VOICE_NEWCALL_ACTION = "com.czur.device.notice.voice.newcall"
        const val VOICE_NOTICE_CENTER_ACTION = "com.czur.device.notice.voice.notice_center"

        //快速会议
        const val VOICE_MEETING_QUICK_ACTION = "com.czur.device.notice.voice.quick_meeting"

        //加入会议
        const val VOICE_MEETING_JOIN_ACTION = "com.czur.device.notice.voice.join_meeting"

        const val VOICE_SETTING_ACTION = "com.czur.device.notice.voice.settings"
        const val VOICE_FILE_ACTION = "com.czur.device.notice.voice.file"
        const val VOICE_LOGIN_ACTION = "com.czur.device.notice.voice.login"
        const val VOICE_SETTING_TAB = "settingTab"
        const val VOICE_FILE_TAB = "fileTab"
        const val VOICE_LOGIN_TAB = "loginTab"
        const val VOICE_PACKAGE_NAME = "startPackageName"
        const val VOICE_CLASS_NAME = "saveClassName"
        const val VOICE_ACTION_EXIT = "com.czur.starry.exit"

        /****GET_DATA****/
        const val HAS_CAN_JOIN_MEETING = 1      // 是否有可加入会议

        fun getByKey(key: Long): MsgType {
            val type = key ushr 32
            val sub = key shl 32 ushr 32
            return MsgType(type.toInt(), sub.toInt())
        }
    }

    // 关注这个大类下的所有项目
    constructor(type: Int) : this(type, Int.MAX_VALUE)

    // 将type转换为Long类型, 作为Key
    val typeKey by lazy { getKey() }

    /**
     * 类型能否匹配上
     *
     */
    fun match(type: Int, subType: Int): Boolean {
        return this.type and type != 0
                && this.sub and subType != 0
    }

    override fun equals(other: Any?): Boolean {
        if (other is MsgType) {
            return other.type == this.type && other.sub == this.sub
        }
        return super.equals(other)
    }

    override fun toString(): String {
        val sb = StringBuilder("type=( ")
        if (type and SYNC != 0) {
            sb.append("SYNC - ${getSyncSubName()} ")
        }
        if (type and MEET != 0) {
            sb.append("MEET - ${getMeetSubName()} ")
        }
        if (type and USER_INFO != 0) {
            sb.append("USER_INFO - ${getUserInfoSubName()} ")
        }
        if (type and MODULE_BOOT != 0) {
            sb.append("MODULE_BOOT -${getModuleBootSubName()} ")
        }
        if (type and VOICE_RECOGNITION != 0) {
            sb.append("VOICE_RECOGNITION -${getVoiceRecogitionSubName()} ")
        }

        if (type and VOICE_NOTIFY != 0) {
            sb.append("VOICE-NOTIFY -${getVoiceNotifySubName()} ")
        }

        if (type and NOTIFY_MSG != 0) {
            sb.append("NOTIFY_MSG -${getNotifyMsgName()}")
        }

        if (type and GET_DATA != 0) {
            sb.append("GET_DATA -${getGetDataName()}")
        }

        if (type and COMMON != 0) {
            sb.append("COMMON -${getCommonName()}")
        }

        if (type and WRITE_PAD != 0) {
            sb.append("WRITE_PAD -${getWritePadName()}")
        }

        if (type and SCREEN_SHOT != 0) {
            sb.append("SCREEN_SHOT -${getScreenShotName()}")
        }

        if (type and (SYNC or MEET or USER_INFO or MODULE_BOOT or VOICE_RECOGNITION or NOTIFY_MSG or GET_DATA or COMMON or WRITE_PAD) == 0) {
            sb.append("UNKNOWN ")
        }
        sb.append(")")
        return sb.toString()
    }

    private fun getWritePadName(): String {
        return buildString {
            append("[ ")
            if (sub and WRITE_CLIENT_CONN != 0) {
                append("客户端连接成功")
            }
            append("]")
        }
    }

    private fun getScreenShotName(): String {
        return buildString {
            append("[ ")
            if (sub and COMMON_TOAST != 0) {
                append("截屏成功")
            }
            append("]")
        }
    }

    private fun getCommonName(): String {
        return buildString {
            append("[ ")
            if (sub and COMMON_TOAST != 0) {
                append("显示全局Toast")
            }
            if (sub and COMMON_BYOM_REQUEST != 0) {
                append("宜享BYOM请求")
            }
            append("]")
        }
    }

    private fun getVoiceNotifySubName(): String {
        val sb = StringBuilder("[ ")
        if (sub and VOICE_NOTIFY_BRIGHTNESS != 0) {
            sb.append("调整画面亮度")
        }

        if (sub and VOICE_NOTIFY_OPERATOR_PIC_SIZE != 0) {
            sb.append("调整画面尺寸")
        }

        return sb.append("]").toString()
    }

    private fun getVoiceRecogitionSubName(): String {
        val sb = StringBuilder("[ ")
        if (sub and VOICE_BOOT != 0) {
            sb.append("语音启动模块 ")
        }
        if (sub and VOICE_ADJUST != 0) {
            sb.append("语音调节配置 ")
        }
        if (sub and VOICE_MEET != 0) {
            sb.append("语音会议 ")
        }

        return sb.append("]").toString()
    }

    private fun getModuleBootSubName(): String {
        val sb = StringBuilder("[ ")
        if (sub and MODULE_BOOT_LAUNCHER != 0) {
            sb.append("Launcher ")
        }
        return sb.append("]").toString()
    }

    private fun getUserInfoSubName(): String {
        val sb = StringBuilder("[ ")
        if (sub and USER_INFO_LOGIN != 0) {
            sb.append("登录 ")
        }
        if (sub and USER_INFO_LOGOUT != 0) {
            sb.append("登出 ")
        }
        if (sub and USER_INFO_OTHER_DEVICE_LOGIN != 0) {
            sb.append("被其他用户顶掉 ")
        }
        return sb.append("]").toString()
    }

    private fun getMeetSubName(): String = buildString {
        append("[ ")
        if (sub and MEET_CALL_VIDEO != 0) {
            append("新的来电 ")
        }
        if (sub and MEET_STOP != 0) {
            append("会议停止 ")
        }
        if (sub and MEET_CMD != 0) {
            append("会议CMD ")
        }
        if (sub and MEET_USER_LIST != 0) {
            append("用户列表 ")
        }
        if (sub and MEETING_USER_UPDATE != 0) {
            append("更新单个用户 ")
        }
        if (sub and MEET_START != 0) {
            append("开始会议 ")
        }
        if (sub and MEET_CMD_FROM_REMOTE != 0) {
            append("从服务器来的指令 ")
        }
        if (sub and MEET_CHECK_MEETING_LIST != 0) {
            append("检查会议列表 ")
        }
        if (sub and MEETING_CONFLICT != 0) {
            append("视频会议冲突 ")
        }
        if (sub and MEETING_REJECT_CALL_PAGE != 0) {
            append("拒绝来电页面 ")
        }
        if (sub and MEETING_STATUS != 0) {
            append("会议状态 ")
        }
        append("]")
    }


    private fun getSyncSubName(): String {
        val sb = StringBuilder("[ ")

        if (sub and SYNC_CONTACT != 0) {
            sb.append("联系人 ")
        }
        if (sub and SYNC_COMPANY != 0) {
            sb.append("企业联系人 ")
        }
        if (sub and SYNC_SCHEDULE != 0) {
            sb.append("日程 ")
        }
        if (sub and SYNC_NOTICE != 0) {
            sb.append("通知 ")
        }
        if (sub and SYNC_RECORD != 0) {
            sb.append("最近记录 ")
        }
        if (sub and SYNC_PARTY_TRANS_CODE != 0) {
            sb.append("党建视频转码 ")
        }
        if (sub and COMMON_PERIPHERAL_USB_CHANGE != 0) {
            sb.append("外设USB模式变化 ")
        }
        return sb.append("]").toString()
    }

    private fun getNotifyMsgName(): String {
        val sb = StringBuilder("[ ")

        if (sub and NOTIFY_MSG_LATEST != 0) {
            sb.append("最新一条消息 ")
        }
        if (sub and NOTIFY_READ != 0) {
            sb.append("标记消息已读 ")
        }
        return sb.append("]").toString()
    }

    private fun getGetDataName(): String {
        val sb = StringBuilder("[ ")

        if (sub and HAS_CAN_JOIN_MEETING != 0) {
            sb.append("是否有可加入会议 ")
        }
        return sb.append("]").toString()
    }

    override fun hashCode(): Int {
        var result = type
        result = 31 * result + sub
        return result
    }

    private fun getKey(): Long {
        return (type.toLong() shl 32) or sub.toLong()
    }
}
