package com.czur.starry.device.baselib.utils.data

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.utils.inMainThread
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

/**
 * Created by 陈丰尧 on 2021/6/10
 * 用来代理StateFlow实现对LiveData中值的 set/get方法
 * @param stateFlow 要代理的StateFlow
 * @param def: 默认值: 如果为null,则代表认为 liveData中的value永远不可能为null,
 *              在使用get方法时, 会直接返回LiveData中的value
 */
class StateFlowDelegate<T>(
    private val stateFlow: MutableStateFlow<T>,
    private val def: T? = null,
    private val setHookBlock: ((value: T) -> T)? = null
) :
    ReadWriteProperty<Any, T> {

    /**
     * 简化类型转换操作
     * 有可能抛出类型转换异常
     */
    constructor(stateFlow: StateFlow<T>, def: T? = null, setHookBlock: ((value: T) -> T)? = null) : this(
        stateFlow as MutableStateFlow,
        def,
        setHookBlock
    )

    override fun getValue(thisRef: Any, property: KProperty<*>): T {
        val result = stateFlow.value
        return if (result != null || def == null) {
            result!!
        } else {
            def
        }
    }

    override fun setValue(thisRef: Any, property: KProperty<*>, value: T) {
        val realValue = setHookBlock?.invoke(value) ?: value
        stateFlow.value = realValue
    }

}