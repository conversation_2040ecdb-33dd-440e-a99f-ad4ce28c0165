package com.czur.starry.device.baselib.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.czur.starry.device.baselib.R;


/**
 * Created by 陈丰尧 on 2/25/21
 * 简易的StatusBar, 只显示Wifi信息
 */
public class SimpleStatusBar extends FrameLayout {
    public SimpleStatusBar(@NonNull Context context) {
        super(context);
        init();
    }

    public SimpleStatusBar(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SimpleStatusBar(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public SimpleStatusBar(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init(){
        inflate(getContext(), R.layout.widget_simple_status_bar, this);
    }


}
