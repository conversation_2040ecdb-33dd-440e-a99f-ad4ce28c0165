package com.czur.starry.device.baselib.utils.gson

import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter

open class IntTypeAdapter : TypeAdapter<Int?>() {

    override fun write(out: JsonWriter, value: Int?) {
        if (value == null) {
            out.nullValue()
        } else {
            out.value(value)
        }
    }

    override fun read(`in`: JsonReader): Int? {
        return when (`in`.peek()) {
            JsonToken.BOOLEAN -> if (`in`.nextBoolean()) 1 else 0
            JsonToken.NULL -> {
                `in`.nextNull()
                null
            }
            JsonToken.NUMBER -> `in`.nextInt()
            JsonToken.STRING -> stringToInt(`in`.nextString())
            else -> null
        }
    }

    private fun stringToInt(str: String?): Int = try {
        str?.toInt() ?: 0
    } catch (e: Exception) {
        0
    }
}