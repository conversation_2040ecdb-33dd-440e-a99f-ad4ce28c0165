package com.czur.starry.device.baselib.tips

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.*
import com.wanglu.lib.BasePopup
import com.wanglu.lib.WPopParams
import com.wanglu.lib.WPopupDirection

/**
 * Created by 陈丰尧 on 2021/10/12
 */

private const val TAG = "FloatTipHoverListener"

class FloatTipHoverListener(private val tips: String, private val bgColor: Int) :
    View.OnHoverListener {
    companion object {
        // 指针进入View 1.5秒后 弹出通知
        const val TIPS_DELAY = 0L//1 * ONE_SECOND + 500L
        const val TIPS_CLICK_THRESHOLD = 2 * ONE_SECOND
        private const val CHECK_DELAY = 500L
        private const val KEY_CODE_SHOW = 1
        private const val KEY_CODE_CHECK = 2

        private const val BG_COLOR_WHITE = 0
        private const val BG_COLOR_BLUE = 1
        private const val BG_COLOR_LIGHT_BLUE = 2
    }

    var viewId: Int = View.NO_ID

    private val floatHandler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val view = msg.obj as? View ?: return
            when (msg.what) {
                KEY_CODE_SHOW -> {
                    removeMessages(KEY_CODE_CHECK)
                    viewId = view.id
                    if (!tipsPopup.isShowing()) {
                        // 设置tip
                        val customTip = view.getTag(R.id.tag_float_tip_custom) as? String ?: tips
                        if (customTip.isEmpty() || !view.isVisible || !view.isEnabled) {
                            // 不显示float
                            return
                        }
                        val tv = tipsPopup.getContentView() as TextView
                        tv.text = customTip
                        if (screenHeight != 0) {
                            // 能获取屏幕高度时, 弹出时计算弹出位置
                            atDown =
                                view.screenY < (screenHeight / 3F * 2)  // 控件在在屏幕2/3之上, 提示显示在下方, 否则显示在上方
                        }
                        if (atDown) {
                            tipsPopup.showAsDropDown(view)
                        } else {
                            // 显示在控件上方
                            tipsPopup.showAtDirectionByViewAlignLeft(
                                view,
                                WPopupDirection.TOP
                            )
                        }
                        TipsPool.updatePopup(this@FloatTipHoverListener)
                    }
                    val checkMsg = mkCheckMsg(view)
                    sendMessageDelayed(checkMsg, CHECK_DELAY)
                }

                KEY_CODE_CHECK -> {
                    if (!view.isVisible || !view.isEnabled) {
                        TipsPool.removePop(this@FloatTipHoverListener)
                        removeMessages(KEY_CODE_SHOW)
                        removeMessages(KEY_CODE_CHECK)
                    } else {
                        // view 可见状态, 每500ms检测一次
                        val checkMsg = mkCheckMsg(view)
                        sendMessageDelayed(checkMsg, CHECK_DELAY)
                    }
                }

            }
        }

        private fun mkCheckMsg(v: View): Message {
            val checkMsg = obtainMessage()
            checkMsg.what = KEY_CODE_CHECK
            checkMsg.obj = v
            return checkMsg
        }
    }
    private lateinit var context: Context
    private var atDown: Boolean = true
    private var screenHeight = 0

    internal val tipsPopup: BasePopup by lazy(LazyThreadSafetyMode.NONE) {
        val bgId = when (bgColor) {
            BG_COLOR_WHITE -> R.layout.baselib_pop_tip
            BG_COLOR_BLUE -> R.layout.baselib_pop_tip_blue
            BG_COLOR_LIGHT_BLUE -> R.layout.baselib_pop_tip_light_blue
            else -> R.layout.baselib_pop_tip_blue
        }
        BasePopup(
            WPopParams(
                bgId,
                context,
                false,
                cancelable = true,
                width = ViewGroup.LayoutParams.WRAP_CONTENT,
                height = ViewGroup.LayoutParams.WRAP_CONTENT,
                focusable = false
            )
        )

    }

    private var lastOutTime = 0L

    override fun onHover(v: View, event: MotionEvent): Boolean {
        if (screenHeight == 0) {
            context = v.context
            screenHeight = getScreenHeight()
        }
        when (event.action) {
            // 进入/ 移动
            MotionEvent.ACTION_HOVER_ENTER -> {
                floatHandler.removeMessages(KEY_CODE_SHOW)
                if (System.currentTimeMillis() - lastOutTime < 250) {
                    // 点击事件导致的, 屏蔽掉
                    return false
                }
                val msg = floatHandler.obtainMessage()
                msg.what = KEY_CODE_SHOW
                msg.obj = v
                floatHandler.sendMessageDelayed(msg, TIPS_DELAY)
            }
            MotionEvent.ACTION_HOVER_EXIT -> {
                lastOutTime = System.currentTimeMillis()
                floatHandler.removeMessages(KEY_CODE_SHOW)
                floatHandler.removeMessages(KEY_CODE_CHECK)
                if (tipsPopup.isShowing()) {
                    tipsPopup.dismiss()
                    TipsPool.removePop(this)
                }
            }
        }
        return false
    }

    fun removeAllHandlerMsg() {
        floatHandler.removeMessages(KEY_CODE_SHOW)
        floatHandler.removeMessages(KEY_CODE_CHECK)
    }

}