package com.czur.starry.device.baselib.network.core.interceptor

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_INVALID_TOKEN
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_OTHER_DEVICE_LOGIN
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.MsgType.Companion.USER_INFO
import com.czur.starry.device.baselib.notice.MsgType.Companion.USER_INFO_OTHER_DEVICE_LOGIN
import com.czur.starry.device.baselib.notice.NoticeHandler

import kotlinx.coroutines.runBlocking
import okhttp3.*
import org.json.JSONObject

/**
 * Created by 陈丰尧 on 2021/7/15
 * 如果Token过期,自动刷新Token
 */
class TokenInterceptor : Interceptor {

    companion object {
        private const val TAG = "TokenInterceptor"

        private const val KEY_CODE = "code"

        private const val LOGIN_URL = "/starry/api/starry/login"
        private const val LOGIN_URL_DIRECT = "/api/starry/login"
    }

    private val loginURL: String
        get() = if (Constants.BASE_HOST.contains("/starry")) {
            LOGIN_URL
        } else {
            LOGIN_URL_DIRECT
        }


    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        val responseStr = response.peekBody(1024L * 1024L * 1024L).string()
        val resCode = if (response.code == 200) try {
            getResCode(responseStr)
        } catch (exp: Exception) {
            logTagW(TAG, "解析返回code失败: $responseStr", tr = exp)
            return response
        } else response.code

        // 是否被其他设备顶掉
        return when (resCode) {
            RESULT_CODE_OTHER_DEVICE_LOGIN -> {
                logTagD(TAG, "被其他用户顶掉")
                NoticeHandler.sendMessage(MsgType(USER_INFO, USER_INFO_OTHER_DEVICE_LOGIN))
                response
            }
            RESULT_CODE_INVALID_TOKEN -> refreshToken(response, request, chain)
            else -> response // 不需要更新Token, 返回之前的结果
        }

    }

    /**
     * 刷新Token, 刷新的方式是利用保存的用户名和密码,进行重新登陆
     */
    private fun refreshToken(
        response: Response,
        request: Request,
        chain: Interceptor.Chain
    ): Response {
        logTagD(TAG, "Token需要更新")
        // 需要刷新Token
        val userName = UserHandler.accountNo.toString()
        val pwd = UserHandler.loginPassword ?: ""
        if (userName.length < 6 || pwd.isBlank()) {
            // 没有用户名/ 密码 登录不了
            // 正常情况下, 不会存在有token 但是账号密码没有的情况
            logTagW(TAG, "没有用户名/密码")
            return response
        }

        // 可以重新登录,刷新Token
        val loginRequest = mkLoginRequest(request.url, userName, pwd)
        val loginResponse = chain.proceed(loginRequest)
        // 检查Token是否刷新成功
        val token = loginResponse.body.use {
            getTokenFromJson(it?.string() ?: "{}")
        }

        if (token.isNotEmpty()) {
            // token刷新成功,更新Token
            logTagV(TAG, "新Token:${token}")
            UserHandler.token = token
        } else {
            // token刷新失败, 将之前结果直接返回
            // 如果是网络问题, 则会抛出异常
            // 没有抛出异常,并且token刷新失败, 只能是本地保存的账号无法登录
            // 所以清理本地的登录信息
            runBlocking {
                logTagW(TAG, "更新Token失败, 自动退出登录")
                UserHandler.logout()
            }
            return response
        }


        logTagD(TAG, "Token已更新, 重新请求")
        // 重新请求一遍
        return chain.proceed(request)
    }

    private fun getTokenFromJson(json: String): String {
        return try {
            val jsonObj = JSONObject(json)
            jsonObj.getJSONObject("data").getString("token")
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 构造激活 Request
     */
    private fun mkLoginRequest(oldUrl: HttpUrl, userName: String, pwd: String): Request {
        val url = HttpUrl.Builder()
            .scheme(oldUrl.scheme)
            .host(oldUrl.host)
            .encodedPath(loginURL)
            .build()

        val formBody = FormBody.Builder()
            .add("username", userName)
            .add("pwd", pwd)
            .build()

        val requestBuilder = Request.Builder()

        return requestBuilder
            .post(formBody)
            .url(url)
            .build()
    }


    /**
     * 获取网络接口的返回code
     * @return 网络接口的Code @see ResCode
     */
    private fun getResCode(responseStr: String): Int {
        if (responseStr.isBlank()) {
            return -1
        }
        val json = JSONObject(responseStr)
        return json.getInt(KEY_CODE)
    }
}