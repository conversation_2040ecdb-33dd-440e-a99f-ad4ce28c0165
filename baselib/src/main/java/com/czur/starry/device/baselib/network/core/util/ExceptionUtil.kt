package com.czur.starry.device.baselib.network.core.util

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_ACCOUNT_NOT_MATCH
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_ACTIVE_FAIL
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_APPLY_FLE_LOCK_FAIL
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_ERR_USER_PWD
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_INVALID_COUNTRY
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_INVALID_TOKEN
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_MAINLAND_DEVICE_IN_OVERSEA
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_MANY_MAIL_REQUEST
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_MSG_TIME_OUT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_NO_INTERNET
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_NO_NET_CONNECT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_REGISTER_OVER_MAX_LIMIT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_REGISTER_TIME_OUT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_SEND_SMS_ERROR
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_SMS_REQUEST_TOO_MUCH
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_VERIFICATION_CODE_ERROR
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_VERIFICATION_CODE_HAS_SEND
import com.czur.starry.device.baselib.network.core.exception.MiaoAccountNotMatchExp
import com.czur.starry.device.baselib.network.core.exception.MiaoActiveFailExp
import com.czur.starry.device.baselib.network.core.exception.MiaoApplyFileLockExp
import com.czur.starry.device.baselib.network.core.exception.MiaoHttpException
import com.czur.starry.device.baselib.network.core.exception.MiaoMainlandRejectExp
import com.czur.starry.device.baselib.network.core.exception.MiaoManyMailRequestExp
import com.czur.starry.device.baselib.network.core.exception.MiaoMsgTimeOutExp
import com.czur.starry.device.baselib.network.core.exception.MiaoNetException
import com.czur.starry.device.baselib.network.core.exception.MiaoNoLoginExp
import com.czur.starry.device.baselib.network.core.exception.MiaoRegisterOverLimitExp
import com.czur.starry.device.baselib.network.core.exception.MiaoRegisterTimeOutExp
import com.czur.starry.device.baselib.network.core.exception.MiaoSMSRequestToMuch
import com.czur.starry.device.baselib.network.core.exception.MiaoSendSMSExp
import com.czur.starry.device.baselib.network.core.exception.MiaoSendSMSRepeat
import com.czur.starry.device.baselib.network.core.exception.MiaoUnKnownExp
import com.czur.starry.device.baselib.network.core.exception.MiaoUserCountryCodeErrorExp
import com.czur.starry.device.baselib.network.core.exception.MiaoUserPwdErrorExp
import com.czur.starry.device.baselib.network.core.exception.MiaoVerificationCodeException

/**
 * Created by 陈丰尧 on 2021/7/19
 */
/**
 * 检查并抛出异常
 */
fun checkAndThrowException(miaoHttpEntity: MiaoHttpEntity<*>) {
    if (miaoHttpEntity.isSuccess) {
        // 请求成功
        return
    }

    throw createMiaoHttpException(miaoHttpEntity)
}

/**
 * 根据返回的错误码, 创建对应的异常
 */
fun createMiaoHttpException(miaoHttpEntity: MiaoHttpEntity<*>): MiaoHttpException {
    return when (miaoHttpEntity.code) {
        // 网络错误
        RESULT_CODE_NO_INTERNET, RESULT_CODE_NO_NET_CONNECT ->
            MiaoNetException(miaoHttpEntity.code, miaoHttpEntity.msg)
        // 验证码错误
        RESULT_CODE_VERIFICATION_CODE_ERROR ->
            MiaoVerificationCodeException()
        // 一天发送短信次数过多
        RESULT_CODE_SMS_REQUEST_TOO_MUCH ->
            MiaoSMSRequestToMuch()

        RESULT_CODE_MANY_MAIL_REQUEST -> MiaoManyMailRequestExp()   // 邮件过多
        // 用户名密码错误
        RESULT_CODE_ERR_USER_PWD ->
            MiaoUserPwdErrorExp()

        RESULT_CODE_INVALID_TOKEN, ResCode.RESULT_CODE_NO_LOGIN ->
            // token过期了, 正常框架会去重新登录,来刷新Token
            // 所以token刷新失败, 就表示需要重新登录
            MiaoNoLoginExp()

        RESULT_CODE_SEND_SMS_ERROR -> MiaoSendSMSExp()
        RESULT_CODE_VERIFICATION_CODE_HAS_SEND -> MiaoSendSMSRepeat()
        RESULT_CODE_REGISTER_OVER_MAX_LIMIT -> MiaoRegisterOverLimitExp()
        RESULT_CODE_MSG_TIME_OUT -> MiaoMsgTimeOutExp()
        RESULT_CODE_APPLY_FLE_LOCK_FAIL -> MiaoApplyFileLockExp()
        RESULT_CODE_MAINLAND_DEVICE_IN_OVERSEA -> MiaoMainlandRejectExp()
        RESULT_CODE_REGISTER_TIME_OUT -> MiaoRegisterTimeOutExp()
        RESULT_CODE_ACTIVE_FAIL -> MiaoActiveFailExp()
        RESULT_CODE_INVALID_COUNTRY -> MiaoUserCountryCodeErrorExp()
        RESULT_CODE_ACCOUNT_NOT_MATCH -> MiaoAccountNotMatchExp()
        else -> MiaoUnKnownExp(miaoHttpEntity.code, miaoHttpEntity.msg)
    }
}



