package com.czur.starry.device.baselib.utils

import android.content.Context
import android.media.AudioManager
import android.media.AudioManager.MODE_IN_CALL
import android.media.AudioManager.MODE_IN_COMMUNICATION
import android.media.MediaPlayer
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.KEY_ONLY_USB_PERIPHERAL_USE_MIC
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp

/**
 * Created by 陈丰尧 on 2021/11/8
 */
private const val TAG = "AudioUtil"

class AudioUtil {

    companion object {
        const val VOLUME_NORMAL = 1F
        const val VOLUME_QUITE = 0.2F
        const val VOLUME_MUTE = 0F

        private const val PROP_KEY_VOIP_PROC = "sys.czur.voipproc"
        private const val PROP_KEY_VOICE_CALL = "sys.czur.voicecall" // 是否占用麦克风
    }

    // 音频管理
    private val audioManager by lazy {
        CZURAtyManager.appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    private val defAfChangeListener by lazy {
        AudioManager.OnAudioFocusChangeListener {
            logTagV(TAG, "音频焦点改变:${getAudioFocusName(it)}")
        }
    }

    // 音频焦点改变监听
    private lateinit var afChangeListener: AudioManager.OnAudioFocusChangeListener

    /**
     * 麦克风是否被占用
     */
    fun isMicInUse(): Boolean {
        if (getBooleanSystemProp(KEY_ONLY_USB_PERIPHERAL_USE_MIC, false)) {
            return false    // 只有USB外设使用麦克风时，不判断系统麦克风是否被占用
        }
        return audioManager.mode == MODE_IN_CALL
                || audioManager.mode == MODE_IN_COMMUNICATION
                || getBooleanSystemProp(PROP_KEY_VOICE_CALL, false)
    }

    /**
     * 获取占用麦克风应用的PID
     */
    fun getUseMicPid(): List<Int> {
        if (getBooleanSystemProp(KEY_ONLY_USB_PERIPHERAL_USE_MIC, false)) {
            // 只有USB外设使用麦克风时，不判断系统麦克风是否被占用
            return emptyList()
        }
        val voipPids = getStringSystemProp(PROP_KEY_VOIP_PROC, "0").split(",")
            .filter {
                it != "0"
            }
            .map {
                it.toInt()
            }
        return voipPids
    }

    /**
     * 请求音频焦点
     */
    fun requestAudioFocus(
        streamType: Int = AudioManager.STREAM_MUSIC,
        durationHint: Int = AudioManager.AUDIOFOCUS_GAIN,
        focusListener: (AudioManager.OnAudioFocusChangeListener)? = null
    ): Int {
        logTagV(TAG, "请求音频焦点")
        logTagV(TAG, "请求音频焦点${focusListener == null}")
        val listener = focusListener ?: defAfChangeListener
        afChangeListener = listener

        val result = audioManager.requestAudioFocus(
            listener,
            streamType,
            durationHint
        )

        if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            logTagV(TAG, "音频焦点请求成功")
        } else {
            logTagW(TAG, "音频焦点请求失败")
        }
        return result
    }

    /**
     * 释放音频焦点
     */
    fun abandonAudioFocus(focusListener: (AudioManager.OnAudioFocusChangeListener)? = null) {
        logTagV(TAG, "释放音频焦点")
        doWithoutCatch {
            var listener = focusListener
            if (listener == null) {
                if (::afChangeListener.isInitialized) {
                    listener = afChangeListener
                }
            }
            listener?.let {
                audioManager.abandonAudioFocus(it)
            }
        }

    }

    fun getAudioFocusName(audioFocus: Int): String {
        return when (audioFocus) {
            AudioManager.AUDIOFOCUS_GAIN -> "AUDIOFOCUS_GAIN"
            AudioManager.AUDIOFOCUS_LOSS -> "AUDIOFOCUS_LOSS"
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> "AUDIOFOCUS_LOSS_TRANSIENT"
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> "AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK"
            else -> "其他:${audioFocus}"
        }
    }

}

fun MediaPlayer.setVolume(volume: Float) = setVolume(volume, volume)