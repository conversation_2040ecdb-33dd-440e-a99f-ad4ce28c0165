package com.czur.starry.device.baselib.utils.keyboard

import android.view.WindowInsets
import androidx.lifecycle.LifecycleOwner
import com.czur.starry.device.baselib.base.BaseActivity
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.base.v2.fragment.CZBaseFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import kotlinx.coroutines.flow.MutableStateFlow


/**
 * Created by 陈丰尧 on 2021/12/27
 */
class SoftKeyboardStateHelper(
    activity: CZBaseAty
) {

    private var lastDecorFitsSystemWindows: Boolean? = null


    private val currentImeHeightFlow = MutableStateFlow(0)

    private var onSoftKeyboardOpened: ((keyboardHeightInPx: Int) -> Unit)? = null
    private var onSoftKeyboardClosed: (() -> Unit)? = null

    constructor(baseFragment: CZBaseFragment) : this(baseFragment.requireActivity() as CZBaseAty) {
        lastDecorFitsSystemWindows =
            baseFragment.requireActivity().window.decorView.fitsSystemWindows
        baseFragment.viewLifecycleOwner.lifecycle.addObserver(object : AutoRemoveLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                lastDecorFitsSystemWindows?.let {
                    // 恢复Activity的设置
                    baseFragment.requireActivity().window.decorView.fitsSystemWindows = it
                }
            }
        })
    }

    init {
        activity.window.setDecorFitsSystemWindows(false)

        activity.launch {
            currentImeHeightFlow.collect { imeHeight ->
                if (imeHeight > 0) {
                    notifyOnSoftKeyboardOpened(imeHeight)
                } else {
                    notifyOnSoftKeyboardClosed()
                }
            }
        }

        activity.window.decorView.setOnApplyWindowInsetsListener { _, insets ->
            val imeInsets = insets.getInsets(WindowInsets.Type.ime())

            currentImeHeightFlow.value = imeInsets.bottom

            insets
        }
    }

    fun doOnImeOpen(action: (keyboardHeightInPx: Int) -> Unit) {
        onSoftKeyboardOpened = action
    }

    fun doOnImeClose(action: () -> Unit) {
        onSoftKeyboardClosed = action
    }

    private fun notifyOnSoftKeyboardOpened(keyboardHeightInPx: Int) {
        onSoftKeyboardOpened?.invoke(keyboardHeightInPx)
    }

    private fun notifyOnSoftKeyboardClosed() {
        onSoftKeyboardClosed?.invoke()
    }
}
