package com.czur.starry.device.baselib.widget

import android.content.Context
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.text.method.TransformationMethod
import android.util.AttributeSet
import android.widget.EditText
import androidx.appcompat.widget.AppCompatImageView
import com.czur.starry.device.baselib.R

/**
 * Created by 陈丰尧 on 2021/11/2
 */
class EyeView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    companion object {
        private const val MODE_HIDE = 0
        private const val MODE_SHOW = 1
    }

    private var editText: EditText? = null

    private var mode: Int = MODE_HIDE
        set(value) {
            field = value
            updateUI()
        }

    init {
        updateUI()
        setOnClickListener {
            mode = if (mode == MODE_HIDE) {
                MODE_SHOW
            } else {
                MODE_HIDE
            }
        }
    }

    private fun updateUI() {
        if (mode == MODE_SHOW) {
            setImageResource(R.drawable.base_lib_ic_eye_open)
            editText?.changeEditTextMethod(HideReturnsTransformationMethod.getInstance())
        } else {
            setImageResource(R.drawable.base_lib_ic_eye_close)
            editText?.changeEditTextMethod(PasswordTransformationMethod.getInstance())
        }
    }

    private fun EditText.changeEditTextMethod( method: TransformationMethod) {
        val index = selectionStart // 记录光标位置
        transformationMethod = method // 切换密码显示模式
        setSelection(index)            // 恢复光标位置
    }

    fun bindToEditText(editText: EditText) {
        this.editText = editText
        updateUI()
    }
}