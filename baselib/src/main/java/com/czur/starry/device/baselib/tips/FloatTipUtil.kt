package com.czur.starry.device.baselib.tips

import android.view.View
import android.widget.TextView
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.doWithEllipsisCount

/**
 * Created by 陈丰尧 on 2021/10/12
 */
fun View.setCustomFloatTip(tip: String) {
    setTag(R.id.tag_float_tip_custom, tip)
}

fun View.getFloatTip(): String? {
    return getTag(R.id.tag_float_tip_custom)?.toString()
        ?: getTag(R.id.tag_float_tip_xml)?.toString()
}

/**
 * 同步设置文字和提示信息
 * 当文字显示不全时,    会设置tip为[text]
 * 当文字可以显示完全时, 会设置tip为[defTip]
 */
fun TextView.setTextAndTip(text: String, defTip: String = "") {
    setText(text)
    doWithEllipsisCount({ setCustomFloatTip(text) }, { setCustomFloatTip(defTip) })
}