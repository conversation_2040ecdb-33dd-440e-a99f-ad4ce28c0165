package com.czur.starry.device.baselib.base.v2.aty

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import java.lang.reflect.ParameterizedType
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 2023/6/7
 * 使用ViewBinding功能的Activity基类
 */
open class CZViewBindingAty<VB : ViewBinding> : CZBusinessAty() {
    protected var binding: VB by Delegates.notNull()

    final override fun setCusContentView() {
        binding = generateViewBinding()
        setContentView(binding.root)
    }

    final override fun initViews() {
        super.initViews()
        binding.initBindingViews()
    }

    protected open fun VB.initBindingViews() {}

    protected open fun generateViewBinding(): VB {
        val type = this::class.java.genericSuperclass as? ParameterizedType
            ?: throw IllegalArgumentException("没有指定ViewBinding的泛型")
        val vbClazz = type.actualTypeArguments.find {
            // 找到泛型声明为 实现了 ViewBinding接口的类型
            (it as Class<*>).genericInterfaces[0] == ViewBinding::class.java
        } as? Class<*> ?: throw IllegalArgumentException("ViewBinding class not found")

        val method = vbClazz.getMethod(
            "inflate",
            LayoutInflater::class.java,
            ViewGroup::class.java,
            Boolean::class.java
        )
        val viewGroup = findViewById<ViewGroup>(android.R.id.content)
        return method.invoke(null, layoutInflater, viewGroup, viewGroup != null) as VB
    }

}