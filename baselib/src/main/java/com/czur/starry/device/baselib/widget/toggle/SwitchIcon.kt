package com.czur.starry.device.baselib.widget.toggle

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.widget.CircleView

/**
 * Created by 陈丰尧 on 1/12/21
 */
class SwitchIcon @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val DEF_OFF_COLOR = 0xFF678DF0.toInt()    //OFF 颜色
        private const val DEF_ON_COLOR = 0xFF4bb6f4.toInt()     //ON 颜色
    }

    private var offBgColor = DEF_OFF_COLOR //OFF 颜色
    private var onBgColor = DEF_ON_COLOR //ON 颜色
    private var currentColor = offBgColor //当前颜色初始值
        set(value) {
            field = value
            invalidate()
        }
    private val defDuration = 300L // 默认动画时长

    private val paint = Paint()

    private var circleXOFF = 0f
    private var circleXON = 0f

    private lateinit var onAnimSet: AnimatorSet
    private lateinit var offAnimSet: AnimatorSet

    private var l: OnClickListener? = null

    // 参数1: switch的状态
    // 参数2: 是否是用户点击
    private var switchListener: ((isOn: Boolean, fromUser: Boolean) -> Unit)? = null
    private var userSwitchListener: ((isOn: Boolean) -> Unit)? = null
    private var initFinish = false

    // 阻止通过用户点击 来改变状态
    var blockUserSwitch: ((willStatus: Boolean) -> Boolean)? = null

    private var currentAnim: AnimatorSet? = null
    private var switchLeft = -1F

    private var isOn = false
        set(value) {
            if (field != value) {
                field = value
                startAnim() //开始动画
            }
            skipOneAnim = false
        }

    private var skipOneAnim = false

    private val switchOFF: LinearLayout by lazy {
        findViewById(R.id.switchOFF)
    }
    private val switchON: LinearLayout by lazy {
        findViewById(R.id.switchON)
    }
    private val switchCircleView: CircleView by lazy {
        findViewById(R.id.switchCircleView)
    }

    init {
        background = null // 清除掉背景颜色

        val ta = context.obtainStyledAttributes(attrs, R.styleable.SwitchIcon)

        onBgColor = ta.getColor(R.styleable.SwitchIcon_onBgColor, DEF_ON_COLOR)
        offBgColor = ta.getColor(R.styleable.SwitchIcon_offBgColor, DEF_OFF_COLOR)
        currentColor = offBgColor
        ta.recycle()

        paint.isAntiAlias = true
        paint.style = Paint.Style.FILL
        inflate(context, R.layout.widget_switch, this)

        super.setOnClickListener {
            val needBlock = blockUserSwitch?.invoke(!isOn) ?: false
            if (needBlock || currentAnim?.isRunning == true) {
                // 阻止用户改变Switch状态
                return@setOnClickListener
            }

            isOn = !isOn
            // 用户设定的点击事件
            l?.onClick(this)
            switchListener?.let {
                it(isOn, true)
            }
            userSwitchListener?.let {
                it(isOn)
            }
        }

        if (isOn) {
            switchOFF.alpha = 0f
        } else {
            switchON.alpha = 0f
        }
    }

    override fun setOnClickListener(l: OnClickListener?) {
        this.l = l
    }

    /**
     * 设置改变的监听
     */
    fun setOnSwitchChange(l: (isOn: Boolean, fromClick: Boolean) -> Unit) {
        switchListener = l
    }

    fun setOnUserSwitchChange(l: (isOn: Boolean) -> Unit) {
        userSwitchListener = l
    }

    /**
     * 设置开关
     */
    fun setSwitchOn(isOn: Boolean, skipAnim: Boolean = false) {
        if (this.isOn != isOn) {
            skipOneAnim = skipAnim
            this.isOn = isOn
            switchListener?.let {
                it(isOn, false)
            }
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        if (switchLeft < 0) {
            switchLeft = switchCircleView.x
        }
        val switchRight = width - switchLeft - switchCircleView.width
        circleXOFF = switchLeft.coerceAtMost(switchRight)
        circleXON = switchLeft.coerceAtLeast(switchRight)
        initAnim() // 有了尺寸数据后,再更新动画
        initFinish = true
        if (isOn) {

            // 证明开关的初始状态改变了
            // 改变组件的初始状态
            switchCircleView.x = circleXON
            switchON.alpha = 1f
            switchOFF.alpha = 0f
            currentColor = onBgColor

        }
    }


    override fun dispatchDraw(canvas: Canvas) {
        val r = height / 2f // 圆角矩形的圆角半径
        // 绘制背景的圆角矩形
        paint.color = currentColor
        canvas.drawRoundRect(0f, 0f, width.toFloat(), height.toFloat(), r, r, paint)
        super.dispatchDraw(canvas)
    }

    private fun initAnim() {
        onAnimSet = makeAnim(true)
        offAnimSet = makeAnim(false)
    }

    fun value(): Boolean = isOn

    /**
     * 播放动画
     */
    private fun startAnim() {
        if (initFinish) {
            currentAnim = if (isOn) {
                offAnimSet.cancel()
                onAnimSet
            } else {
                onAnimSet.cancel()
                offAnimSet
            }.also { anim ->
                if (skipOneAnim) {
                    anim.duration = 0
                } else {
                    anim.duration = defDuration
                }
                anim.start()
            }
        }
    }

    private fun makeAnim(on: Boolean): AnimatorSet {

        val animSet = AnimatorSet()
        animSet.duration = defDuration
        if (on) {
            // 背景颜色动画
            val colorAnim = ObjectAnimator.ofArgb(this, "currentColor", offBgColor, onBgColor)

            // 滑块动画
            val xAnim = ObjectAnimator.ofFloat(switchCircleView, "x", circleXOFF, circleXON)

            // 文字动画
            val onAnim = ObjectAnimator.ofFloat(switchON, "alpha", 0f, 1f)
            onAnim.interpolator = DecelerateInterpolator()

            val offAnim = ObjectAnimator.ofFloat(switchOFF, "alpha", 1f, 0f)
            animSet.playTogether(colorAnim, xAnim, onAnim, offAnim)
        } else {
            val colorAnim = ObjectAnimator.ofArgb(this, "currentColor", onBgColor, offBgColor)

            val xAnim = ObjectAnimator.ofFloat(switchCircleView, "x", circleXON, circleXOFF)
            val onAnim = ObjectAnimator.ofFloat(switchON, "alpha", 1f, 0f)

            val offAnim = ObjectAnimator.ofFloat(switchOFF, "alpha", 0f, 1f)
            offAnim.interpolator = DecelerateInterpolator()
            animSet.playTogether(colorAnim, xAnim, onAnim, offAnim)
        }

        return animSet
    }

}