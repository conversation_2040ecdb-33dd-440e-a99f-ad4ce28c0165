package com.czur.starry.device.baselib.utils

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.ShapeDrawable
import android.os.SystemClock
import android.view.ActionMode
import android.view.GestureDetector
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.IntRange
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.common.Constants.CLICK_DEBOUNCE
import com.czur.starry.device.baselib.tips.setCustomFloatTip
import com.czur.starry.device.baselib.widget.OnRecyclerItemClickListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 3/3/21
 */
private const val TAG = "UIUtil"
private const val EMPTY_STR = ""

// recyclerView 点击事件
fun RecyclerView.doOnItemClick(
    doubleClickOnSame: Boolean = false,
    listener: (vh: RecyclerView.ViewHolder, view: View) -> Boolean
) {
    addOnItemTouchListener(
        OnRecyclerItemClickListener().setOnClickListener(
            doubleClickOnSame,
            listener
        )
    )
}

/**
 * RecyclerView 添加鼠标右键的点击事件
 */
fun RecyclerView.doOnItemRightClick(listener: (vh: RecyclerView.ViewHolder, view: View) -> Boolean) {
    addOnItemTouchListener(OnRecyclerItemClickListener().setOnRightClickListener(listener))
}

/**
 * RecyclerView 添加长按事件
 */
fun RecyclerView.doOnItemLongClick(listener: (vh: RecyclerView.ViewHolder, view: View) -> Boolean) {
    addOnItemTouchListener(OnRecyclerItemClickListener().setOnLongClickListener(listener))
}

/**
 * 给RecyclerView添加Drawable分割线
 */
fun RecyclerView.addItemDecorationDrawable(orientation: Int, drawableID: Int) {
    addItemDecoration(DividerItemDecoration(context, orientation).apply {
        setDrawable(
            ResourcesCompat.getDrawable(resources, drawableID, null)!!
        )
    })
}

/**
 * 给RecyclerView添加Shape分割线
 * @param orientation 方向 [RecyclerView.VERTICAL] or [RecyclerView.HORIZONTAL]
 * @param size 分割线的大小
 */
fun RecyclerView.addItemDecoration(orientation: Int, size: Int) {
    val drawable = ShapeDrawable().apply {
        paint.color = Color.TRANSPARENT
        if (orientation == RecyclerView.VERTICAL) {
            intrinsicHeight = size
            intrinsicWidth = 1
        } else {
            intrinsicHeight = 1
            intrinsicWidth = size
        }
    }
    addItemDecoration(DividerItemDecoration(context, orientation).apply {
        setDrawable(drawable)
    })
}

/**
 * 关闭默认的supportsChangeAnimations
 * 可以屏蔽刷新单个Item时, Item的高亮动画
 */
fun RecyclerView.closeDefChangeAnimations() {
    (itemAnimator as? DefaultItemAnimator)?.supportsChangeAnimations = false
}

/**
 * RecyclerView 添加没有数据的布局
 * 注意: 需要放在 setAdapter之后调用
 * 并且只能调用一次, 有更换Adapter的情况则不要用, 还不支持
 */
fun RecyclerView.addEmptyView(emptyLayout: Int) {
    val parentView = parent
    val emptyView =
        LayoutInflater.from(context).inflate(emptyLayout, parentView as ViewGroup, false)
    when (parentView) {
        is ConstraintLayout -> {
            val layoutParams =
                ConstraintLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                    .apply {
                        leftToLeft = id
                        topToTop = id
                        rightToRight = id
                        bottomToBottom = id
                    }

            parentView.addView(emptyView, layoutParams)
        }

        is FrameLayout -> {
            val layoutParams: FrameLayout.LayoutParams =
                (parentView.layoutParams as FrameLayout.LayoutParams).apply {
                    gravity = Gravity.CENTER
                    this.setMargins(leftMargin, topMargin, rightMargin, bottomMargin)
                }
            parentView.addView(emptyView, layoutParams)
        }

        else -> {
            TODO("还不支持这种ViewGroup")
        }
    }
    emptyView.gone()

    val adapterObserver = object : AdapterDataObserver() {
        private fun updateCount() {
            if (adapter?.itemCount == 0) {
                emptyView.show()
            } else {
                emptyView.gone()
            }
        }

        override fun onChanged() {
            super.onChanged()
            updateCount()
        }

        override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
            super.onItemRangeInserted(positionStart, itemCount)
            updateCount()
        }

        override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
            super.onItemRangeRemoved(positionStart, itemCount)
            updateCount()
        }

        override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
            super.onItemRangeMoved(fromPosition, toPosition, itemCount)
            updateCount()
        }
    }

    adapter?.registerAdapterDataObserver(adapterObserver) ?: kotlin.run {
        NotImpl("必须在SetAdapter后调用")
    }
}

fun View.show() {
    visibility = View.VISIBLE
}

fun View.invisible(invisible: Boolean = true) {
    visibility = if (invisible) {
        View.INVISIBLE
    } else {
        View.VISIBLE
    }
}

fun View.gone(gone: Boolean = true) {
    visibility = if (gone) {
        View.GONE
    } else {
        View.VISIBLE
    }
}

private var View.triggerTime: Long
    get() = if (getTag(R.id.tag_trigger_time) != null) getTag(R.id.tag_trigger_time) as Long else 0L
    set(value) = setTag(R.id.tag_trigger_time, value)

/**
 * 防止连点的点击事件
 */
fun View.setOnDebounceClickListener(
    debounceTime: Long = CLICK_DEBOUNCE,
    action: (view: View) -> Unit
) {
    setOnClickListener {
        if (SystemClock.elapsedRealtime() - triggerTime > debounceTime) {
            triggerTime = SystemClock.elapsedRealtime()
            action(it)
        }
    }
}

fun View.setOnSuspendClickListener(cs: CoroutineScope, action: suspend (view: View) -> Unit) {
    var job: Job? = null
    setOnClickListener {
        if (job != null) {
            if (job?.isActive == true || job?.isCompleted == false) {
                logTagI(TAG, "点击事件被拦截了")
                return@setOnClickListener
            }
            job = null
        }
        job = cs.launch {
            action(it)
            job = null
        }
    }
}

/**
 * 通过Touch的方式来实现点击
 * 这个方法通常是因为click监听有冲突,所以一般不会在使用click监听了
 * 在使用这个方法时, view的click事件会不好用
 */
@SuppressLint("ClickableViewAccessibility")
fun View.setDebounceTouchClickListener(
    debounceTime: Long = CLICK_DEBOUNCE,
    action: (view: View) -> Unit
) {
    val gestureDetector =
        GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapUp(e: MotionEvent): Boolean {
                if (SystemClock.elapsedRealtime() - triggerTime > debounceTime) {
                    triggerTime = SystemClock.elapsedRealtime()
                    action(this@setDebounceTouchClickListener)
                }
                return true
            }

            override fun onDown(e: MotionEvent): Boolean = true
        })
    setOnTouchListener { _, event ->
        gestureDetector.onTouchEvent(event)
    }
}


fun View.performTouch() {
    val downTime = System.currentTimeMillis()
    val eventTime = System.currentTimeMillis() + 100
    val x = 0.0f
    val y = 0.0f
    // metaState 是设备状态的集合，这里不需要，所以设置为0
    val metaState = 0

    val motionEvent = MotionEvent.obtain(
        downTime,
        eventTime,
        MotionEvent.ACTION_DOWN,
        x, // 触摸x位置
        y, // 触摸y位置
        metaState
    )
    dispatchTouchEvent(motionEvent)

    // 必须生成一个 ACTION_UP 动作才能使触摸事件完整
    val upEvent = MotionEvent.obtain(
        downTime,
        eventTime,
        MotionEvent.ACTION_UP,
        x,
        y,
        metaState
    )
    dispatchTouchEvent(upEvent)
}


/**
 * 设置带圆角的background
 */
fun View.setCornersBg(radius: Float, color: Int) {
    val gradientDrawable = GradientDrawable()
    gradientDrawable.cornerRadius = radius
    gradientDrawable.setColor(color)
    background = gradientDrawable
}

/**
 * 显示并切换到指定的图片
 */
fun ImageView.show(@DrawableRes imgRes: Int) {
    setImageResource(imgRes)
    show()
}

/**
 * 根据表达式设定不同的图片资源
 * @param trueRes       表达式为 true   时设定的图片
 * @param falseRes      表达式为 false  时设定的图片
 * @param condition     设定图片的条件
 */
fun ImageView.setImgResCondition(
    @DrawableRes trueRes: Int,
    @DrawableRes falseRes: Int,
    condition: Boolean,
) {
    setImgResCondition(trueRes, falseRes) { condition }
}

fun ImageView.setImgAndTipsCondition(
    @DrawableRes trueRes: Int,
    @DrawableRes falseRes: Int,
    trueStr: String,
    falseStr: String,
    condition: Boolean,
) {
    setImgResCondition(trueRes, falseRes) { condition }
    setTipsCondition(trueStr, falseStr) { condition }
}

/**
 * 根据表达式设定不同的图片资源
 * @param trueRes       表达式为 true   时设定的图片
 * @param falseRes      表达式为 false  时设定的图片
 * @param condition     设定图片的条件代码块
 */
fun ImageView.setImgResCondition(
    @DrawableRes trueRes: Int,
    @DrawableRes falseRes: Int,
    condition: () -> Boolean,
) {
    if (condition()) {
        setImageResource(trueRes)
    } else {
        setImageResource(falseRes)
    }
}

fun View.setTipsCondition(
    trueStr: String,
    falseStr: String,
    condition: () -> Boolean
) {
    if (condition()) {
        setCustomFloatTip(trueStr)
    } else {
        setCustomFloatTip(falseStr)
    }
}

fun ImageView.setImgResWithLiveData(
    @DrawableRes trueRes: Int,
    @DrawableRes falseRes: Int,
    conditionLiveData: LiveData<Boolean>,
    owner: LifecycleOwner,
) {
    conditionLiveData.observe(owner) {
        setImgResCondition(trueRes, falseRes, it)
    }
}

fun ImageView.setImgAndTipsWithLiveData(
    @DrawableRes trueRes: Int,
    @DrawableRes falseRes: Int,
    trueStr: String,
    falseStr: String,
    conditionLiveData: LiveData<Boolean>,
    owner: LifecycleOwner,
) {
    conditionLiveData.observe(owner) {
        setImgAndTipsCondition(trueRes, falseRes, trueStr, falseStr, it)
    }
}

private const val MOCK_COLOR = 0x00_FF_FF_FF

/**
 * 改变颜色的Alpha值
 */
fun changeColorAlpha(alpha: Float, srcColor: Int): Int {
    val alphaValue = (0xFF * alpha).toInt()
    return changeColorAlpha(alphaValue, srcColor)
}

/**
 * 改变颜色的Alpha值
 */
fun changeColorAlpha(@IntRange(from = 0, to = 255) alpha: Int, srcColor: Int): Int {
    val colorRGB = srcColor and MOCK_COLOR
    val alphaValue = alpha shl 24
    return alphaValue or colorRGB
}

/**
 * 在View中获取LifecycleOwner对象
 * 如果没有找到, 则会使用当前View所依附的Activity/Service(必须是AppCompatActivity/LifecycleService)
 * 这个方法应该配合 viewTreeObserver.addOnGlobalLayoutListener() 使用
 * 否则只能获取到Activity对象,而不能获取到最近的Fragment
 */
fun View.requireLifecycleOwner(): LifecycleOwner =
    findViewTreeLifecycleOwner() ?: context as LifecycleOwner

/**
 * 根据TextView是否能全部显示,来执行不同方法
 */
fun TextView.doWithEllipsisCount(
    hasEllipsisBlock: (tv: TextView) -> Unit,
    hasNoEllipsisCountBlock: (tv: TextView) -> Unit
) {
    post {
        layout?.let {
            val lineCount = it.lineCount
            if (lineCount > 0 && it.getEllipsisCount(lineCount - 1) > 0) {
                hasEllipsisBlock(this)
            } else {
                hasNoEllipsisCountBlock(this)
            }
        }

    }
}

/**
 * 为TextView添加/取消下划线
 * 默认是添加下划线
 */
fun TextView.showUnderLine(showUnderLine: Boolean = true) {
    paintFlags = if (showUnderLine) {
        paintFlags or Paint.UNDERLINE_TEXT_FLAG
    } else {
        paintFlags and Paint.UNDERLINE_TEXT_FLAG.inv()
    }
}

/**
 * 清空TextView输入的文字
 */
fun TextView.clearContentText() {
    text = EMPTY_STR
}


/**
 * 变黑色TextView输入的文字颜色
 */
fun TextView.darkContentTextColor() {
    if (text.isNullOrEmpty()) {
        text = hint?.toString() ?: ""
    }
    setTextColor(0xFF000000.toInt())
    clearFocus()
}


/**
 * 当EditText失去焦点时调用
 * 注意: 此方法会调用 setOnFocusChangeListener
 *      所以如果有针对焦点的其他操作, 请不要使用该方法
 */
fun EditText.doOnLoseFocus(block: (EditText) -> Unit) {
    setOnFocusChangeListener { view, hasFocus ->
        if (!hasFocus) {
            block(view as? EditText ?: this)
        }
    }
}

fun EditText.doOnHasFocus(block: () -> Unit) {
    setOnFocusChangeListener { _, hasFocus ->
        if (hasFocus) {
            block()
        }
    }
}

/**
 * 设置ActionDownListener
 * 同时会监听键盘的回车键
 */
fun EditText.setOnActionDoneListener(
    imeActionId: Int = EditorInfo.IME_ACTION_DONE,
    block: () -> Boolean
) {
    setOnEditorActionListener { _, actionId, event ->
        if (actionId == imeActionId  // 输入法软键盘的发送按钮
            || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
        ) {
            block()
        } else {
            false
        }
    }
}

fun EditText.disableCopyAndPaste() {
    //长按EditText会弹出菜单项，所以不创建菜单项就可以实现禁用复制和粘贴的功能
    val callback = object : ActionMode.Callback {
        override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            //创建菜单项view，返回false表示不创建，事件结束
            return false
        }

        override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            //菜单项更新，返回false表示没有更新，事件结束
            return false
        }

        override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
            //选择菜单项的某一项，返回true表示拦截点击事件，事件结束
            return true
        }

        override fun onDestroyActionMode(mode: ActionMode?) {
            //菜单项view销毁，什么也不做
        }

    }
    //禁用复制
    this.customSelectionActionModeCallback = callback
}

/**
 * 将光标移动到末尾
 */
fun EditText.moveSelectionToLast() {
    val text = text?.toString() ?: ""
    if (text.isEmpty()) return
    setSelection(text.length)
}

/**
 * 修改光标颜色
 * @param color 光标颜色
 */
fun EditText.setCursorColor(color: Int) {
    try {
        val cursorDrawable = textCursorDrawable ?: AppCompatResources.getDrawable(
            context,
            R.drawable.drawable_base_cursor
        )
        cursorDrawable ?: return
        cursorDrawable.colorFilter = PorterDuffColorFilter(
            color,
            PorterDuff.Mode.SRC_IN
        )
        setTextCursorDrawable(cursorDrawable)
    } catch (exp: Throwable) {
        logTagW(TAG, "修改光标颜色失败", tr = exp)
    }
}

fun View.getLocationOnScreenRect(): Rect {
    val location = IntArray(2)
    getLocationOnScreen(location)
    return Rect(location[0], location[1], location[0] + width, location[1] + height)
}

fun View.getLocationOnScreenX(): Int {
    val location = IntArray(2)
    getLocationOnScreen(location)
    return location[0]
}

fun View.getLocationOnScreenY(): Int {
    val location = IntArray(2)
    getLocationOnScreen(location)
    return location[1]
}

val View.screenY: Int
    get() = getLocationOnScreenY()
val View.screenX: Int
    get() = getLocationOnScreenX()

/**
 * 同步设置View的启用状态和通明度
 * 启用状态的通明度固定为1
 * 禁用状态的通明度 默认为 20%
 */
fun View.setEnableAndAlpha(isEnable: Boolean, disableAlpha: Float = 0.3F) {
    this.isEnabled = isEnabled
    alpha = if (isEnable) {
        1F
    } else {
        disableAlpha
    }
}