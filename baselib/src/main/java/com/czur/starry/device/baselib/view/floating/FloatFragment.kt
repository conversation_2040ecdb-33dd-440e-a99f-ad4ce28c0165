package com.czur.starry.device.baselib.view.floating

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.graphics.Bitmap
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewPropertyAnimator
import android.view.ViewTreeObserver
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.FrameLayout.LayoutParams.UNSPECIFIED_GRAVITY
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.aty.CZBaseAty
import com.czur.starry.device.baselib.base.v2.fragment.floating.IDismissible
import com.czur.starry.device.baselib.common.Constants.ANIM_DURATION_SHORT
import com.czur.starry.device.baselib.databinding.FragmentFloatBinding
import com.czur.starry.device.baselib.tips.TipsPool
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.keyboard.hideKeyboard
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.BaseFragment
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlin.math.max

// 当前正在显示的FloatTag
private val showingFloatTags = mutableSetOf<String>()

/**
 * Created by 陈丰尧 on 4/28/21
 * 浮动页面
 * 只能点击该Fragment中间的内容, 点击其他地方都会隐藏掉
 */
@Deprecated("使用CZVBFloatingFragment代替")
abstract class FloatFragment(
    val bgDark: Boolean = false,
    private val outSideDismiss: Boolean = true,
    private val outSideClick: Boolean = false,  // float外部是否可以点击到其他内容
    private val inputMode: Int? = null,
    private val showMode: FloatShowMode = FloatShowMode.NORMAL,
    private val containerID: Int? = null,    // 想要加入的指定容器, 如果不指定,则会浮在最上层
    private val blockOutSideClickBeforeAnim: Boolean = false,   // 动画之前屏蔽点击事件
    private val bgImg: Bitmap? = null    // 背景图片
) :
    BaseFragment(),
    ViewTreeObserver.OnGlobalLayoutListener,
    IDismissible {
    companion object {
        private const val LOCATION_BY_GRAVITY = Float.MIN_VALUE

        /**
         * 判断给定的Tag是否正在显示
         */
        fun isTagShowing(showTag: String): Boolean {
            return showingFloatTags.contains(showTag)
        }
    }

    private val logTag: String by lazy { this::class.java.simpleName }
    open var floatTag: String? = null // 可以指定ShowTag
    private val showTag: String
        get() = floatTag ?: logTag

    private var params: ByViewParams? = null
    private var byScreenParams: ByScreenParams? = null

    protected lateinit var subView: View
        private set
    protected val container: ViewGroup
        get() = subView as ViewGroup

    private var anim = AnimDirection.NONE  // 进入的动画
    private var enterAnimFinish = false // 进入动画是否结束

    private var locationX = 0F  // 浮窗的位置 X轴
    private var locationY = 0F  // 浮窗的位置 Y轴

    // 进入动画的差值器
    private val enterInterpolator = DecelerateInterpolator()

    // 退出动画的差值器
    private val outInterpolator = AccelerateInterpolator()

    // 退出动画的监听
    private val endListener = object : AnimatorListenerAdapter() {
        override fun onAnimationEnd(animation: Animator) {
            removeSelf()
        }
    }

    // 消失的回调
    var dismissListener: (() -> Unit)? = null

    // 弹出Float之前的InputMode
    private var lastInputMode: Int? = null

    var isDismissing = false
    var hasDismiss = false

    // subView 是否初始化完成
    var isSubViewInstall = false

    private var dismissWatchJob: Job? = null

    fun setOnDismissListener(dismissListener: (() -> Unit)): FloatFragment {
        this.dismissListener = dismissListener
        return this
    }

    private lateinit var outBinding: FragmentFloatBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        beforeCreateView()

        inputMode?.let {
            lastInputMode = requireActivity().window.attributes.softInputMode
            requireActivity().window.setSoftInputMode(it)
        }

        if (mContainer == null) {
            outBinding = FragmentFloatBinding.inflate(inflater, container, false)
            subView = inflater.inflate(getLayoutId(), outBinding.root as ViewGroup, false)
            outBinding.root.addView(subView)
            subView.invisible() // 先让View不可见
            subView.isClickable = true
            subView.viewTreeObserver.addOnGlobalLayoutListener(this)
            mContainer = outBinding.root
        } else {
            if (mContainer!!.parent != null) {
                (mContainer!!.parent as ViewGroup).removeView(mContainer!!)
            }
        }
        isSubViewInstall = true
        return mContainer
    }

    override fun initView() {
        super.initView()
        // 点击外部会dismiss
        if (!outSideClick) {
            outBinding.floatOutSide.setOnClickListener {
                if (outSideDismiss && (!blockOutSideClickBeforeAnim || enterAnimFinish)) {
                    logTagV(logTag, "点击外部, 消去弹窗")
                    dismiss()
                }
            }
        } else {
            // 设定背景不能被点击, 来点击到其他位置
            outBinding.floatOutSide.isClickable = false
        }

        outBinding.floatDarkBg.setOnGenericMotionListener { _, event ->
            event.action == MotionEvent.ACTION_SCROLL
        }
    }

    /**
     * 将Fragment从画面中移除
     */
    private fun removeSelf() {
        doWithoutCatch(logTag, "removeSelf") {
            val activity = requireActivity()
            activity.supportFragmentManager.beginTransaction()
                .remove(this)
                .commitAllowingStateLoss()
        }
        // 移除后, 调用消失回调
        doWithoutCatch {
            showingFloatTags.remove(showTag)
            dismissListener?.invoke()
        }
        hasDismiss = true
        isDismissing = false

        dismissWatchJob?.cancel()
        dismissWatchJob = null
    }

    /**
     * 不指定位置信息, 则会在屏幕中间
     * 允许子类指定默认无参的显示方式
     */
    open fun show(targetAty: CZBaseAty = CZURAtyManager.currentActivity()) {
        show(ByScreenParams.center())
    }

    /**
     * 显示FloatFragment
     * 不指定位置信息
     */
    fun show(anim: AnimDirection, targetAty: CZBaseAty = CZURAtyManager.currentActivity()) {
        if (showMode == FloatShowMode.SINGLE) {
            if (showingFloatTags.contains(showTag)) {
                logTagD(logTag, "${showTag}已经显示了, 不再显示")
                return
            }
        }

        showingFloatTags.add(showTag)
        // 对话框显示的时候, 手动清空所有FloatTips
        TipsPool.clearPop()

        this.anim = anim
        logTagV(logTag, "activity:${targetAty}")
        val trans = targetAty.supportFragmentManager.beginTransaction()
        if (showMode == FloatShowMode.REPEAT) {
            logTagV(logTag, "只显示一个FloatFragment,floatTag:${showTag}")
            val removeFragment =
                targetAty.supportFragmentManager.findFragmentByTag(showTag)
            logTagD(logTag, "remove:${removeFragment == null}")
            removeFragment?.let {
                logTagV(logTag, "remove之前的Fragment")
                trans.remove(it)
            }
        }

        logTagV(logTag, "添加Fragment:TAG:${showTag}")
        val targetContainerId = containerID ?: android.R.id.content
        trans
            .add(targetContainerId, this, showTag)
            .commitAllowingStateLoss()
        // 调用onShow生命周期
        showTime = System.currentTimeMillis()
        onShow()
    }

    /**
     * 显示FloatFragment
     */
    fun show(byScreenParams: ByScreenParams, anim: AnimDirection = AnimDirection.SCALE) {
        this.byScreenParams = byScreenParams
        show(anim)
    }

    /**
     * 显示FloatFragment
     * @param byViewParams 位置信息
     */
    fun show(byViewParams: ByViewParams, anim: AnimDirection = AnimDirection.BOTTOM) {
        this.params = byViewParams
        show(anim)
    }

    /**
     * 当Float显示时的回调
     */
    protected open fun onShow() {}
    protected open fun onDismiss() {}

    /**
     * 取消显示
     * 如果指定了动画效果, 则会按照进入的动画方向,原路返回
     */
    override fun dismiss() {
        logTagD(logTag, "dismiss")
        showingFloatTags.remove(showTag)
        if (isDismissing) {
            logTagW(logTag, "正在dismiss 忽略")
            return
        }
        isDismissing = true
        view?.let {
            hideKeyboard(it.windowToken)
        }
        // 禁用点击事件
        doWithoutCatch(tag = logTag, "subView 异常") {
            subView.isEnabled = false
            outBinding.floatOutSide.isEnabled = false
            if (bgDark || bgImg != null) {
                dismissDarkBg()
            }

            when (this.anim) {
                AnimDirection.LEFT -> subView.animate()
                    .x(-subView.width.toFloat())
                    .defOut()

                AnimDirection.TOP -> subView.animate()
                    .y(-subView.height.toFloat())
                    .defOut()

                AnimDirection.RIGHT -> subView.animate()
                    .x(outBinding.floatOutSide.width.toFloat())
                    .defOut()

                AnimDirection.BOTTOM -> subView.animate()
                    .y(outBinding.floatOutSide.height.toFloat())
                    .defOut()

                AnimDirection.SCALE -> subView.animate()
                    .scaleX(0.4F)
                    .scaleY(0.4f)
                    .z(0f)
                    .alpha(0.5F)
                    .defOut()

                AnimDirection.NONE -> removeSelf()
            }
        }
        // 保存inputMode
        lastInputMode?.let {
            requireActivity().window.setSoftInputMode(it)
        }
        onDismiss()
    }


    // View已经加载(宽高数据已经获取)
    override fun onGlobalLayout() {
        if (bgDark || bgImg != null) {
            // 使用动画显示背景
            showDarkBg()
        }
        // 取消监听
        subView.viewTreeObserver.removeOnGlobalLayoutListener(this)
        params?.let {
            // 如果有位置信息, 则先计算位置信息
            calculateLocationByView(it)
        }
        byScreenParams?.let {
            calculateLocationByScreen(it)
        }
        // 计算完位置信息后,显示出subView
        subView.post {
            subView.show()
            // 将浮窗移动到指定位置
            doWithoutCatch {
                moveToLocation()
            }
        }
    }

    /**
     * 播放背景动画
     */
    private fun showDarkBg() {
        bgImg?.let {
            outBinding.imgDarkFront.show()
            outBinding.floatDarkBg.setImageBitmap(it)
        }
        if (!bgDark) {
            outBinding.floatDarkBg.background = null
        }
        outBinding.floatDarkBg.show()
        outBinding.floatDarkBg.alpha = 0F
        outBinding.floatDarkBg.animate()
            .alpha(1F)
            .duration = ANIM_DURATION_SHORT
    }

    private fun dismissDarkBg() {
        outBinding.floatDarkBg.animate()
            .alpha(0F)
            .duration = ANIM_DURATION_SHORT
    }

    /**
     * 移动到指定位置
     * 如果指定了动画的方向, 这会由指定方向移动到默认位置
     * 如果没有指定动画方向[AnimDirection.NONE] 则不使用动画效果
     */
    private fun moveToLocation() {
        when (anim) {
            AnimDirection.LEFT -> {
                subView.x = -subView.width.toFloat()
                if (locationY != LOCATION_BY_GRAVITY) {
                    subView.y = locationY
                }
                subView.animate()
                    .x(locationX)
                    .defIn()
            }

            AnimDirection.TOP -> {
                if (locationX != LOCATION_BY_GRAVITY) {
                    subView.x = locationX
                }
                subView.y = -subView.height.toFloat()
                subView.animate().y(locationY).defIn()
            }

            AnimDirection.RIGHT -> {
                subView.x = outBinding.floatOutSide.width.toFloat()
                if (locationY != LOCATION_BY_GRAVITY) {
                    subView.y = locationY
                }
                subView.animate().x(locationX).defIn()

            }

            AnimDirection.BOTTOM -> {
                if (locationX != LOCATION_BY_GRAVITY) {
                    subView.x = locationX
                }
                subView.y = outBinding.floatOutSide.height.toFloat()
                subView.animate().y(locationY).defIn()
            }

            AnimDirection.SCALE -> {// 缩放动画
                if (locationX != LOCATION_BY_GRAVITY) {
                    subView.x = locationX
                }
                if (locationY != LOCATION_BY_GRAVITY) {
                    subView.y = locationY
                }

                subView.scaleX = 0.4F
                subView.scaleY = 0.4F
                subView.translationZ = -subView.elevation
                subView.alpha = 0.5F
                subView.animate().scaleX(1F)
                    .scaleY(1F)
                    .alpha(1F)
                    .translationZ(0F)
                    .defIn()

            }

            AnimDirection.NONE -> {
                enterAnimFinish = true   // 没有动画, 直接标记为完成
                subView.x = locationX
                subView.y = locationY
            }
        }
    }

    private fun calculateLocationByScreen(byScreenParams: ByScreenParams) {
        // 居中时 margin属性 计算在 左侧/上方 (可以为负)
        locationX = when (byScreenParams.gravityHorizontal) {
            ByScreenParams.GravityHorizontal.LEFT -> byScreenParams.marginX.toFloat()
            ByScreenParams.GravityHorizontal.RIGHT -> requireView().width - subView.width - byScreenParams.marginX.toFloat()
            ByScreenParams.GravityHorizontal.CENTER -> {
                val layoutParams = subView.layoutParams as FrameLayout.LayoutParams
                layoutParams.addGravity(Gravity.CENTER_HORIZONTAL)
                layoutParams.leftMargin = byScreenParams.marginX
                subView.layoutParams = layoutParams
                LOCATION_BY_GRAVITY
            }
        }
        locationY = when (byScreenParams.gravityVertical) {
            ByScreenParams.GravityVertical.TOP -> byScreenParams.marginY.toFloat()
            ByScreenParams.GravityVertical.BOTTOM -> requireView().height - subView.height - byScreenParams.marginY.toFloat()
            ByScreenParams.GravityVertical.CENTER -> {
                val layoutParams = subView.layoutParams as FrameLayout.LayoutParams
                layoutParams.addGravity(Gravity.CENTER_VERTICAL)
                layoutParams.topMargin = byScreenParams.marginY
                subView.layoutParams = layoutParams
                LOCATION_BY_GRAVITY
            }
        }
    }

    /**
     * 计算实际的偏移量
     */
    private fun calculateLocationByView(byViewParams: ByViewParams) {
        var dx = 0F
        var dy = 0F

        // 获取View的坐标
        val intArr = IntArray(2)
        byViewParams.view.getLocationInWindow(intArr)
        val viewX = intArr[0]
        val viewY = intArr[1]

        // 计算x轴的实际位置
        if (byViewParams.location in listOf(
                ByViewParams.Location.TOP,
                ByViewParams.Location.BOTTOM,
            )
        ) {
//             如果位置是在 目标view的 [上/下],则通过对齐方式(左/右)计算x轴偏移量
            dx = when (byViewParams.align) {
                ByViewParams.Align.LEFT -> viewX.toFloat() + byViewParams.marginX
                ByViewParams.Align.RIGHT -> viewX.toFloat() + byViewParams.view.width - subView.width - byViewParams.marginX
                ByViewParams.Align.CENTER_HORIZONTAL -> {
                    // 与指定View水平居中对齐
                    val centerX = (viewX.toFloat() + viewX.toFloat() + byViewParams.view.width) / 2F
                    max(0F, centerX - (subView.width / 2F)) // 不能超出屏幕
                }

                else -> dx
            }

            dy = when (byViewParams.location) {
                ByViewParams.Location.TOP -> viewY.toFloat() - subView.height - byViewParams.marginY
                ByViewParams.Location.BOTTOM -> viewY.toFloat() + byViewParams.view.height + byViewParams.marginY
                else -> dy
            }
        } else {
            // 指定了位置是在 目标View的 [左/右], 则忽略x轴的对齐方式
            dx = when (byViewParams.location) {
                ByViewParams.Location.LEFT -> viewX.toFloat() - subView.width - byViewParams.marginX
                ByViewParams.Location.RIGHT -> viewX.toFloat() + byViewParams.view.width + byViewParams.marginX
                else -> dx
            }

            dy = when (byViewParams.align) {
                ByViewParams.Align.TOP -> viewY.toFloat() + byViewParams.marginY
                ByViewParams.Align.BOTTOM -> viewY.toFloat() + byViewParams.view.height - subView.height - byViewParams.marginY
                else -> dy
            }
        }

        // 根据dx和dy来更新subview的位置
        locationX = dx
        locationY = dy
    }


    /**
     * 设置退出动画的默认参数
     */
    private fun ViewPropertyAnimator.defOut(): ViewPropertyAnimator {
        dismissWatchJob = launch {
            delay(ANIM_DURATION_SHORT + 50)
            if (isDismissing) {
                logTagW(logTag, "isDismiss值错误 直接remove")
                removeSelf()
            }
        }
        return setDuration(ANIM_DURATION_SHORT)
            .setInterpolator(outInterpolator)
            .setListener(endListener)
    }

    /**
     * 设置进入动画的默认参数
     */
    private fun ViewPropertyAnimator.defIn(): ViewPropertyAnimator {
        return setDuration(ANIM_DURATION_SHORT)
            .setListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                    enterAnimFinish = true
                }

                override fun onAnimationCancel(animation: Animator) {
                    enterAnimFinish = false
                }

                override fun onAnimationRepeat(animation: Animator) {
                }

            })
            .setInterpolator(enterInterpolator)
    }


    /**
     * 动画的方向
     *  从 屏幕的哪个方向进入(退出时也会从这个方向退出)
     */
    enum class AnimDirection {
        LEFT, TOP, RIGHT, BOTTOM, // 飞入动画
        SCALE,                    // 缩放动画
        NONE                      // 不使用动画
    }

    open class ShowParams {
        var marginY = 0             // 与View在Y轴的间隔
        var marginX = 0              // 与View在X轴的间隔
    }

    class ByScreenParams : ShowParams() {
        enum class GravityHorizontal {
            LEFT, RIGHT, CENTER
        }

        enum class GravityVertical {
            TOP, BOTTOM, CENTER
        }

        var gravityHorizontal = GravityHorizontal.LEFT
        var gravityVertical = GravityVertical.TOP

        companion object {
            /**
             * 在屏幕居中显示
             */
            fun center() = ByScreenParams().apply {
                gravityHorizontal = GravityHorizontal.CENTER
                gravityVertical = GravityVertical.CENTER
            }

            /**
             * 在屏幕上方水平居中
             */
            fun centerHorizontal() = ByScreenParams().apply {
                gravityHorizontal = GravityHorizontal.CENTER
                gravityVertical = GravityVertical.TOP
            }

            /**
             * 在屏幕右上角显示
             */
            fun topRight(offSetRight: Int = 0, offSetTop: Int = 0) = ByScreenParams().apply {
                gravityHorizontal = GravityHorizontal.RIGHT
                gravityVertical = GravityVertical.TOP
                marginY = offSetTop
                marginX = offSetRight
            }

            /**
             * 在屏幕左上角显示
             */
            fun topLeft(offSetLeft: Int = 0, offSetTop: Int = 0) = ByScreenParams().apply {
                gravityHorizontal = GravityHorizontal.LEFT
                gravityVertical = GravityVertical.TOP
                marginY = offSetTop
                marginX = offSetLeft
            }

            /**
             * 在屏幕左下角显示
             */
            fun bottomLeft(offSetLeft: Int = 0, offSetBottom: Int = 0) = ByScreenParams().apply {
                gravityHorizontal = GravityHorizontal.LEFT
                gravityVertical = GravityVertical.BOTTOM
                marginY = offSetBottom
                marginX = offSetLeft
            }
        }
    }

    /**
     * 位置信息的数据类,相对于指定View
     * @param view: 相对于那个View显示
     */
    class ByViewParams(val view: View) : ShowParams() {
        /**
         * 相对目标的位置
         */
        enum class Location {
            LEFT, TOP, RIGHT, BOTTOM, NONE
        }

        /**
         * 对齐方式
         */
        enum class Align {
            LEFT, TOP, RIGHT, BOTTOM, NONE, CENTER_HORIZONTAL
        }

        var location = Location.NONE // 相对View的位置
        var align = Align.NONE      // 与View的对齐方式
    }


    private fun FrameLayout.LayoutParams.addGravity(gravity: Int) {
        if (this.gravity == UNSPECIFIED_GRAVITY) {
            this.gravity = gravity
        } else {
            this.gravity = this.gravity or gravity
        }
    }
}