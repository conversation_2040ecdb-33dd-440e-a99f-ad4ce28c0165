package com.czur.starry.device.baselib.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.NICKNAME_LENGTH
import com.czur.starry.device.baselib.utils.getShowLength
import com.czur.starry.device.baselib.utils.subShowStrByLength

/**
 * Created by 陈丰尧 on 2022/3/2
 */
class NickNameTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private val ellipsisStr: String by lazy {
        context.resources.getText(R.string.symbol_ellipsis).toString()
    }

    override fun setText(text: CharSequence?, type: BufferType?) {
        if (text.isNullOrEmpty() || type != BufferType.NORMAL) {
            super.setText(text, type)
            return
        }

        val str = text.toString()
        val length = str.getShowLength()
        if (length <= NICKNAME_LENGTH) {
            // 字符比较小
            super.setText(text, type)
            return
        }

        val showText =
            str.subShowStrByLength(NICKNAME_LENGTH) + ellipsisStr
        super.setText(showText, type)
    }
}