package com.czur.starry.device.baselib.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.databinding.BaselibItemWordBinding
import com.czur.starry.device.baselib.databinding.BaselibWidgetWordindexBinding
import com.czur.starry.device.baselib.utils.IndexUtils.INDEX_WORDS
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxLayoutManager
import com.noober.background.drawable.DrawableCreator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val DISABLE_ALPHA = 0.2f

class WordIndexView : FrameLayout {
    var colorText = Color.WHITE
    var colorBg = Color.parseColor("#33ffffff")
    private var chooseListener: ((chooseChar: Char) -> Unit)? = null

    val adapter = WordAdapter()

    private val scope = MainScope()
    private lateinit var binding: BaselibWidgetWordindexBinding

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.BaseLib_WordItem)
        colorText = ta.getColor(R.styleable.BaseLib_WordItem_baselib_tv_color, colorText)
        colorBg = ta.getColor(R.styleable.BaseLib_WordItem_baselib_bg_color, colorBg)
        ta.recycle()
        init(context)
    }

    private fun init(context: Context) {
        binding = BaselibWidgetWordindexBinding.inflate(LayoutInflater.from(context), this, true)
        initView()
    }


    fun setWords(words: Collection<Char>) {
        scope.launch {
            val eq = withContext(Dispatchers.IO) {
                val raw = adapter.words.toList()
                if (raw.size != words.size) {
                    return@withContext false
                }
                val wordsList = words.toList()
                for (i in raw.indices) {
                    if (raw[i] != wordsList[i]) {
                        return@withContext false
                    }
                }
                true
            }

            if (!eq) {
                adapter.words = words
            }
        }
    }

    fun doOnChoose(listener: (Char) -> Unit) {
        this.chooseListener = listener
    }

    private fun initView() {
        binding.rvWords.layoutManager = FlexboxLayoutManager(this.context, FlexDirection.COLUMN)
        adapter.setItemStyle(colorText, colorBg)
        binding.rvWords.adapter = adapter

        binding.rvWords.doOnItemClick { vh, view ->
            val pos = vh.adapterPosition
            val c = INDEX_WORDS[pos]
            chooseListener?.let {
                if (adapter.isEnablePosition(pos)) {
                    it(c)
                }
            }
            true
        }
    }

    class WordAdapter
        : RecyclerView.Adapter<WordAdapter.ViewHolder>() {
        var words: Collection<Char> = emptyList()
            set(value) {
                field = value
                notifyDataSetChanged()
            }

        var colorTv = Color.WHITE
        var colorBg = Color.parseColor("#33ffffff")

        fun setItemStyle(colorTv: Int, colorBg: Int) {
            this.colorBg = colorBg
            this.colorTv = colorTv
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

            val itemBinding = BaselibItemWordBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            itemBinding.tvWords.setTextColor(colorTv)
            itemBinding.root.background = DrawableCreator.Builder()
                .setCornersRadius(15F)
                .setSolidColor(colorBg)
                .build()
            return ViewHolder(itemBinding)
        }

        override fun getItemCount(): Int {
            return INDEX_WORDS.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.tvWords.text = INDEX_WORDS[position].toString()

            if (isEnablePosition(position)) {
                holder.itemView.alpha = 1.0f
            } else {
                holder.itemView.alpha = DISABLE_ALPHA
            }
        }

        fun isEnablePosition(position: Int) = words.contains(INDEX_WORDS[position])

        class ViewHolder(private var binding: BaselibItemWordBinding) : RecyclerView.ViewHolder(binding.root) {
            var tvWords = binding.tvWords
        }
    }
}