package com.czur.starry.device.baselib.utils

import android.os.SystemClock
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.selects.select
import java.util.ArrayDeque


/**
 * Created by 陈丰尧 on 2021/8/6
 */

/**
 * throttle操作符
 * 在指定时间间隔内, 只取第一个
 * @param periodMillis 指定时间间隔
 */
fun <T> Flow<T>.throttleFirst(periodMillis: Long): Flow<T> {
    require(periodMillis > 0) { "时间间隔必须设定的大于0" }
    return flow {
        var lastTime = 0L
        collect { value ->
            val currentTime = SystemClock.elapsedRealtime()
            if (currentTime - lastTime >= periodMillis) {
                lastTime = currentTime
                emit(value)
            }
        }
    }
}

/**
 * 计数, 从现在开始向前的[timeInterval]ms 内, 一共有多少个数据流过
 */
fun <T> Flow<T>.countInTimeInterval(timeInterval: Long): Flow<Int> {
    require(timeInterval > 0)
    val timeList = ArrayDeque<Long>()
    return flow {
        collect {
            val currentTime = SystemClock.elapsedRealtime()
            while (timeList.isNotEmpty()) {
                val oldTime = timeList.peek() ?: break
                if (currentTime - oldTime > timeInterval) {
                    timeList.poll()
                } else {
                    break
                }
            }
            timeList.add(currentTime)
            emit(timeList.size)
        }
    }
}



fun <T> Flow<T>.collectByConsumerCapacity(withScope: CoroutineScope): Flow<List<T>> = flow {
    val channel = Channel<T>(Channel.UNLIMITED)
    val producerJob = withScope.launch {
        <EMAIL> { value ->
            channel.send(value)
        }
        channel.close()
    }

    val buffer = mutableListOf<T>()
    while (!channel.isClosedForReceive) {
        select {
            channel.onReceive { value ->
                buffer.add(value)
                while (true) {
                    val nextValue = channel.tryReceive().getOrNull() ?: break
                    buffer.add(nextValue)
                }
                if (buffer.isNotEmpty()) {
                    emit(buffer.toList())
                }
                buffer.clear()
            }
        }
    }
    producerJob.join()
}

/**
 * 添加事件之间的最小时间间隔
 * 不会让事件丢失, 只会添加延迟
 */
fun <T> Flow<T>.minimumInterval(periodMillis: Long): Flow<T> {
    require(periodMillis > 0) { "时间间隔必须设定的大于0" }
    return flow {
        var lastTime = 0L
        collect { value ->
            val currentTime = SystemClock.elapsedRealtime()
            val delayTime = periodMillis - (currentTime - lastTime)
            if (delayTime > 0) {
                delay(delayTime)
            }
            lastTime = SystemClock.elapsedRealtime()
            emit(value)
        }
    }
}

/**
 * 自定义防抖操作符
 * 1. 当有值时, 立刻发送,然后在指定时间窗口内忽略其他值
 * 2. 时间窗口结束后, 如果没有后续的值, 则会补发最后一个被忽略的值,用来保证最后结果的正确
 * 3. 如果触发了补发的逻辑,那么需要再次进入时间窗口, 并屏蔽时间窗口内的其他值
 * 整体的逻辑与sample操作符类似, 但是sample是丢弃值, 而这个是补发值
 * @param timeoutMillis 防抖时间窗口(毫秒)
 */
fun <T> Flow<T>.sampleWithTrailing(timeoutMillis: Long): Flow<T> = channelFlow {
    require(timeoutMillis > 0) { "防抖时间窗口必须大于0" }

    var lastValue: T? = null
    var hasLastValue = false
    var debounceJob: Job? = null

    // 发送值并启动防抖窗口的辅助函数
    suspend fun sendAndStartDebounce(value: T) {
        send(value)
        // 取消之前的防抖任务
        debounceJob?.cancel()
        // 启动新的防抖窗口
        debounceJob = launch {
            delay(timeoutMillis)
            // 窗口结束时如果有lastValue则发射它
            if (hasLastValue) {
                @Suppress("UNCHECKED_CAST")
                val temp = lastValue as T
                lastValue = null
                hasLastValue = false
                // 递归调用以确保补发后也启动新的防抖窗口
                sendAndStartDebounce(temp)
            }
        }
    }

    collect { value ->
        if (debounceJob?.isActive == true) {
            // 防抖窗口内，只更新lastValue
            lastValue = value
            hasLastValue = true
        } else {
            // 不在防抖窗口内，立即发送并启动窗口
            sendAndStartDebounce(value)
        }
    }

    // 确保流完成时清理资源
    awaitClose { debounceJob?.cancel() }
}