package com.czur.starry.device.baselib.utils.hardware

import com.czur.starry.device.baselib.common.KEY_USE_CAMERA_PID
import com.czur.starry.device.baselib.handler.CZPropHandler
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp

/**
 * Created by 陈丰尧 on 2024/5/23
 */
object CameraUtil {
    private const val TAG = "CameraUtil"

    /**
     * 判断摄像头是否正在使用
     */
    suspend fun isCameraInUse(): Boolean {
        return getUseCameraPkg().isNotEmpty()
    }

    /**
     * 获取正在使用摄像头的应用包名
     */
    suspend fun getUseCameraPkg(): String {
        return CZPropHandler.getProp(CZPropHandler.PROP_KEY_CAMERA_USE_PKG, "")
    }

    fun getUseCameraPids(): List<Int> {
        return getStringSystemProp(KEY_USE_CAMERA_PID, "")
            .split(",")
            .filter {
                it != "0"
            }
            .mapNotNull { it.toIntOrNull() }
    }
}