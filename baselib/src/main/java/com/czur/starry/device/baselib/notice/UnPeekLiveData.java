package com.czur.starry.device.baselib.notice;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by 陈丰尧 on 4/26/21
 * 只发一次性事件
 */
public class UnPeekLiveData<T> extends LiveData<T> {

    protected boolean isAllowNullValue;

    private final ConcurrentHashMap<Integer, Boolean> observers = new ConcurrentHashMap<>();

    /**
     * 保存外部传入的Observer与代理Observer之间的映射关系
     */
    private final ConcurrentHashMap<Integer, Integer> observerMap = new ConcurrentHashMap<>();

    /**
     * 这里会持有永久性注册的Observer对象，因为是永久性注册的，必须调用remove才会注销，所有这里持有Observer对象不存在内存泄漏问题，
     * 因为一旦泄漏了，只能说明是业务使用方没有remove
     */
    private final ConcurrentHashMap<Integer, Observer> foreverObservers = new ConcurrentHashMap<>();

    private Observer<T> createProxyObserver(@NonNull Observer originalObserver, @NonNull Integer storeId) {
        return t -> {
            if (!observers.get(storeId)) {
                observers.put(storeId, true);
                if (t != null || isAllowNullValue) {
                    originalObserver.onChanged(t);
                }
            }
        };
    }

    @Override
    public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer) {
        if (owner instanceof Fragment && ((Fragment) owner).getViewLifecycleOwner() != null) {
            /**
             * 2020年起，Fragment中LiveData传入的LifeCycleOwner从fragment.this改进为getViewLifeCycleOwner。这样设计，主要是为了解决getView()的生命长度比fragment短（仅存活于onCreateView之后和onDestroyView之前），
             * 导致某些时候fragment其他成员还活着，但getView()为null的生命周期安全问题，
             * 也即，在Fragment的场景下，请使用getViewLifeCycleOwner来作为liveData的订阅者。（并且注意，对getViewLifeCycleOwner的使用应在onCreateView之后和onDestroyView之前）
             */
            owner = ((Fragment) owner).getViewLifecycleOwner();
        }

        Integer storeId = System.identityHashCode(observer);
        observe(storeId, owner, observer);
    }

    @Override
    public void observeForever(@NonNull Observer<? super T> observer) {
        Integer storeId = System.identityHashCode(observer);
        observeForever(storeId, observer);
    }

    private void observe(@NonNull Integer storeId,
                         @NonNull LifecycleOwner owner,
                         @NonNull Observer<? super T> observer) {

        if (observers.get(storeId) == null) {
            observers.put(storeId, true);
        }

        Observer registerObserver;
        if (observerMap.get(storeId) == null) {
            registerObserver = createProxyObserver(observer, storeId);
            // 保存外部Observer以及内部代理Observer的映射关系
            observerMap.put(storeId, System.identityHashCode(registerObserver));
        } else {
            // 通过反射拿到真正注册到LiveData中的Observer
            Integer registerObserverStoreId = observerMap.get(storeId);
            registerObserver = LiveDataUtil.getObserver(this, registerObserverStoreId);
            if (registerObserver == null) {
                registerObserver = createProxyObserver(observer, storeId);
                // 保存外部Observer以及内部代理Observer的映射关系
                observerMap.put(storeId, System.identityHashCode(registerObserver));
            }
        }

        super.observe(owner, registerObserver);
    }

    private void observeForever(@NonNull Integer storeId, @NonNull Observer<? super T> observer) {

        if (observers.get(storeId) == null) {
            observers.put(storeId, true);
        }

        Observer registerObserver = foreverObservers.get(storeId);
        if (registerObserver == null) {
            registerObserver = createProxyObserver(observer, storeId);
            foreverObservers.put(storeId, registerObserver);
        }

        super.observeForever(registerObserver);
    }

    @Override
    public void removeObserver(@NonNull Observer<? super T> observer) {
        Integer storeId = System.identityHashCode(observer);
        Observer registerObserver = foreverObservers.remove(storeId);
        if (registerObserver == null && observerMap.containsKey(storeId)) {
            // 反射拿到真正注册到LiveData中的observer
            Integer registerObserverStoreId = observerMap.remove(storeId);
            registerObserver = LiveDataUtil.getObserver(this, registerObserverStoreId);
        }

        if (registerObserver != null) {
            observers.remove(storeId);
        }

        super.removeObserver(registerObserver != null ? registerObserver : observer);
    }

    /**
     * 重写的 setValue 方法，默认不接收 null
     * 可通过 Builder 配置允许接收
     * 可通过 Builder 配置消息延时清理的时间
     * <p>
     * override setValue, do not receive null by default
     * You can configure to allow receiving through Builder
     * And also, You can configure the delay time of message clearing through Builder
     *
     * @param value
     */
    @Override
    public void setValue(T value) {
        if (value != null || isAllowNullValue) {
            for (Map.Entry<Integer, Boolean> entry : observers.entrySet()) {
                entry.setValue(false);
            }
            super.setValue(value);
        }
    }

    @Override
    public void postValue(T value) {
        if (value != null || isAllowNullValue) {
            for (Map.Entry<Integer, Boolean> entry : observers.entrySet()) {
                entry.setValue(false);
            }
            super.postValue(value);
        }
    }

    public void clear() {
        super.setValue(null);
    }
}
