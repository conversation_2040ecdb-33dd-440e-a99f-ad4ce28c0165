package com.czur.starry.device.baselib.notice

import android.net.Uri
import com.czur.starry.device.baselib.utils.CONTENT

/**
 * Created by 陈丰尧 on 2021/8/9
 */
object NoticeConstant {
    const val AUTHORITY = "com.czur.starry.device.noticecenter.notice.NoticeProvider"
    val URI:Uri = Uri.Builder()
        .scheme(CONTENT)
        .authority(AUTHORITY)
        .build()

    const val KEY_MSG_TYPE = "noticeKeyType"
    const val KEY_BLOB = "noticeKeyBlob"
    const val URI_KEY_MSG = "msgData"

    // 默认开启数据压缩
    const val ZIP_ENABLE = true

    const val BOOT_SUCCESS = 1
}