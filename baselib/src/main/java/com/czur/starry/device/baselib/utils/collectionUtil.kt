package com.czur.starry.device.baselib.utils

import android.util.SparseIntArray
import androidx.core.util.containsKey

/**
 * Created by 陈丰尧 on 2021/8/6
 */

/**
 * 集合中是否有指定元素
 */
inline fun <T> Iterable<T>.has(predicate: (T) -> <PERSON><PERSON>an): Bo<PERSON>an {
    return firstOrNull(predicate) != null
}

inline fun SparseIntArray.getOrPut(key: Int, defaultValue: () -> Int): Int {
    return if (!containsKey(key)) {
        val answer = defaultValue()
        put(key, answer)
        answer
    } else {
        get(key)
    }
}

/**
 * 集合中相邻元素两两计算, 返回新的集合
 */
inline fun <T, R> List<T>.calculateAdjacent(block: (first: T, second: T) -> R): List<R> {
    if (size < 2) return emptyList()
    val result = mutableListOf<R>()
    for (i in 1 until size) {
        val first = get(i - 1)
        val second = get(i)
        val r = block(first, second)
        result.add(r)
    }
    return result.toList()
}

inline fun <T> MutableList<T>.moveToFirst(block: (item: T) -> Boolean) {
    val index = indexOfFirst(block)
    if (index > 0) {
        val target = removeAt(index)
        add(0, target)
    }
}