package com.czur.starry.device.baselib.widget

import android.animation.ValueAnimator
import android.animation.ValueAnimator.INFINITE
import android.animation.ValueAnimator.REVERSE
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.lifecycle.LifecycleOwner
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.createFloatFrameLockAnim
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.requireLifecycleOwner
import kotlin.math.min

/**
 * Created by 陈丰尧 on 1/12/21
 */
private const val ANIM_FPS = 15 // 动画帧率限制为15帧

class CircleView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private var paintColor = Color.WHITE
    var circleColor: Int
        get() = paintColor
        set(value) {
            paintColor = value
            paint.color = paintColor
            invalidate()
        }
    private val paint = Paint()

    private val breathEffect: Boolean
    private val alphaAnim: ValueAnimator by lazy {
        createFloatFrameLockAnim(ANIM_FPS, 1F, 0F) {
            alpha = it.animatedValue as Float
        }.apply {
            duration = ONE_SECOND
            repeatCount = INFINITE
            repeatMode = REVERSE
            interpolator = AccelerateDecelerateInterpolator()
        }
    }


    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CircleView)
        paintColor = ta.getColor(R.styleable.CircleView_circleColor, Color.WHITE)

        breathEffect = ta.getBoolean(R.styleable.CircleView_breathEffect, false)

        ta.recycle()

        paint.isAntiAlias = true
        paint.color = paintColor
        paint.style = Paint.Style.FILL

        if (breathEffect) {
            alphaAnim.start()

            requireLifecycleOwner().lifecycle.addObserver(object:AutoRemoveLifecycleObserver{
                override fun onStart(owner: LifecycleOwner) {
                    super.onStart(owner)
                    alphaAnim.resume()
                }
                override fun onResume(owner: LifecycleOwner) {
                    super.onResume(owner)
                    alphaAnim.resume()
                }

                override fun onStop(owner: LifecycleOwner) {
                    super.onStop(owner)
                    alphaAnim.pause()
                }
                override fun onDestroy(owner: LifecycleOwner) {
                    alphaAnim.cancel()
                }
            })
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = measuredWidth
        val height = measuredHeight
        // 宽高的最小值为半径
        val diameter = min(width, height)
        setMeasuredDimension(diameter, diameter)
    }

    override fun onDraw(canvas: Canvas) {
        val r = width / 2f //圆的半径
        canvas.drawCircle(width / 2f, height / 2f, r, paint)
    }
}