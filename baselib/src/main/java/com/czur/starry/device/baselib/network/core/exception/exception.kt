package com.czur.starry.device.baselib.network.core.exception

import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_ACTIVE_FAIL
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_APPLY_FLE_LOCK_FAIL
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_ERR_USER_PWD
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_INVALID_COUNTRY
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_MAINLAND_DEVICE_IN_OVERSEA
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_MEETING_OVER_LIMIT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_MSG_TIME_OUT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_NO_LOGIN
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_NO_NET_CONNECT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_REGISTER_OVER_MAX_LIMIT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_REGISTER_TIME_OUT
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_SEND_SMS_ERROR
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_SMS_REQUEST_TOO_MUCH
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_VERIFICATION_CODE_ERROR
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_VERIFICATION_CODE_HAS_SEND

/**
 * Created by 陈丰尧 on 2021/7/19
 */
open class MiaoHttpException(val code: Int, msg: String) : RuntimeException(msg) {
    override fun toString(): String {
        return "网络异常:${code}-${message}"
    }
}

/**
 * 网络异常
 */
class MiaoNetException(code: Int, msg: String) : MiaoHttpException(code, msg) {
    // 是否是 没有网络连接
    val isNoConnect: Boolean
        get() = code == RESULT_CODE_NO_NET_CONNECT
}

/**
 * 验证码错误
 */
class MiaoVerificationCodeException :
    MiaoHttpException(RESULT_CODE_VERIFICATION_CODE_ERROR, "验证码错误")

class MiaoSMSRequestToMuch : MiaoHttpException(RESULT_CODE_SMS_REQUEST_TOO_MUCH, "短信请求太多，一天只有5次")

/**
 * 用户名/密码错误
 */
class MiaoUserPwdErrorExp : MiaoHttpException(RESULT_CODE_ERR_USER_PWD, "用户名/密码错误")

/**
 * 未知错误
 */
class MiaoUnKnownExp(code: Int, msg: String) : MiaoHttpException(code, "未知错误:${msg}")

/**
 * 激活失败
 */
class MiaoActiveFailExp : MiaoHttpException(RESULT_CODE_ACTIVE_FAIL, "激活失败")

/**
 * 没有登录
 */
class MiaoNoLoginExp : MiaoHttpException(RESULT_CODE_NO_LOGIN, "没有登录")

/**
 * 验证码发送失败
 */
class MiaoSendSMSExp : MiaoHttpException(RESULT_CODE_SEND_SMS_ERROR, "发送短信失败")

/**
 * 验证码已经发送过了
 */
class MiaoSendSMSRepeat :
    MiaoHttpException(RESULT_CODE_VERIFICATION_CODE_HAS_SEND, "验证码获取频繁")


/**
 * 注册超时
 */
class MiaoRegisterTimeOutExp : MiaoHttpException(RESULT_CODE_REGISTER_TIME_OUT, "注册超时")

/**
 * 注册次数超过限制
 */
class MiaoRegisterOverLimitExp : MiaoHttpException(RESULT_CODE_REGISTER_OVER_MAX_LIMIT, "注册次数超过限制")

/**
 * 会议超过了最大方数限制
 */
class MiaoMeetingOverLimitExp : MiaoHttpException(RESULT_CODE_MEETING_OVER_LIMIT, "超过最大方数限制")

/**
 * 消息已过期
 */
class MiaoMsgTimeOutExp : MiaoHttpException(RESULT_CODE_MSG_TIME_OUT, "消息已过期")

class MiaoApplyFileLockExp : MiaoHttpException(RESULT_CODE_APPLY_FLE_LOCK_FAIL, "申请文件锁失败")

class MiaoMainlandRejectExp : MiaoHttpException(RESULT_CODE_MAINLAND_DEVICE_IN_OVERSEA, "国内设备在海外访问")

/**
 * 非注册区域登陆
 */
class MiaoUserCountryCodeErrorExp : MiaoHttpException(RESULT_CODE_INVALID_COUNTRY, "非本地区注册账号")

/**
 * 邮件请求过多
 */
class MiaoManyMailRequestExp : MiaoHttpException(RESULT_CODE_SMS_REQUEST_TOO_MUCH, "邮件请求过多")
class MiaoAccountNotMatchExp : MiaoHttpException(RESULT_CODE_VERIFICATION_CODE_HAS_SEND, "账号不匹配")