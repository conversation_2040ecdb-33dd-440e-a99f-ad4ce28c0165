package com.czur.starry.device.baselib.base.listener

import androidx.emoji2.bundled.BundledEmojiCompatConfig
import androidx.emoji2.text.EmojiCompat
import com.czur.czurutils.base.CZURApplication
import com.czur.czurutils.log.CZURLogStyle
import com.czur.czurutils.log.initLogUtil
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.CZUR_LOG_DIR_NAME
import com.czur.starry.device.baselib.utils.ONE_KB_SI
import com.czur.starry.device.baselib.utils.logEnableAll
import com.czur.starry.device.baselib.utils.logEnableD
import com.czur.starry.device.baselib.utils.logEnableE
import com.czur.starry.device.baselib.utils.logEnableI
import com.czur.starry.device.baselib.utils.logEnableV
import com.czur.starry.device.baselib.utils.logEnableW
import com.czur.starry.device.baselib.utils.saveLog
import java.io.File

open class StarryApp : CZURApplication() {

    open val singleLogFileSize = 512 * ONE_KB_SI
    open val logFileCount = 5
    open val commonLogTag = "CZURLog"

    override fun onCreate() {
        super.onCreate()
        CZURAtyManager.appContext = this
        registerActivityLifecycleCallbacks(CZURAtyManager)
        EmojiCompat.init(BundledEmojiCompatConfig(this))

        initLogUtil(this) {
            configTag = commonLogTag
            // log开关
            logOutputSwitch {
//                consoleShowV = logEnableV
//                consoleShowI = logEnableI
//                consoleShowD = logEnableD
//                consoleShowW = logEnableW
//                consoleShowE = logEnableE
//                consoleShowAll = logEnableAll

                consoleShowV = true
                consoleShowI = true
                consoleShowD = true
                consoleShowW = true
                consoleShowE = true
                consoleShowAll = true

                fileSaveAll = saveLog
            }
            // log样式
            logStyle {
                showArgIndex = false
                showHeader = false
                showBorder = CZURLogStyle.BorderStyle.JSON_ONLY
            }
            logSaveConfig {
                maxFileCount = logFileCount
                maxFileSizeInByte = singleLogFileSize
                logFileName = "CZLog"
                logFileExtension = "log"
                additionalStorageCrashStack = true
                customLogSaveDir = File(getExternalFilesDir(null),"${CZUR_LOG_DIR_NAME}/${com.czur.czurutils.getProcessName()}")
                logFileHead {
                    addFileHead = false
                }
            }
        }
    }
}