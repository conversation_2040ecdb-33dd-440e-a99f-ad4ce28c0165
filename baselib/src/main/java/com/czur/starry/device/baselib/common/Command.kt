package com.czur.starry.device.baselib.common

/**
 * 语音指令
 */
object CommandID {


    const val PERSONAL = 10001 //个人中心
    const val CONTACTS = 10002 //联系人
    const val MEETTING_RECORDER = 10003 //会议记录
    const val MEETTING_DIAL = 11003 //拨号
    const val MEETTING_COMPANY = 11004 //企业联系人
    const val MEETTING_QUICK = 11005 //快速会议
    const val MEETTING_JOIN = 11006 //加入会议
    const val FAVORITE = 10004 //收藏
    const val APP_STORE = 10005 //应用商店
    const val SCHEDULE = 10006 //日程
    const val MESSAGE_CENTER = 10007 //消息中心
    const val FILE_MAMAGER = 10008 //文件管理,云文件
    const val MOBLIE_STORAGE = 10009 //移动存储
    const val SETTINGS = 10010 //设置,WIFI设置
    const val HDMI = 10011 //HDMI
    const val HOME_PAGE = 10012 //首页
    const val WIFI_AP = 10201 //无线投屏
    const val AUTO_FOCUS = 10014 //自动对焦
    const val MANUAL_FOCUS = 10015 //手动对焦
//    const val DATE_SCHEDULE = 10015 //查看日期日程
    const val FILE_MAMAGER_LOCAL = 10016 //查看本地文件
    const val FILE_MAMAGER_MEETING = 10017 //查看会议录音
    const val FILE_MAMAGER_DOWNLOAD = 10040 //查看下载文件
    const val FILE_MAMAGER_PAINTSCREEN = 10041 //查看截图文件

    const val SETTING_WIRED = 10019 //有线网络
    const val SETTING_NETWORK_SPEED = 10020 //网络速度
    const val SETTING_FOCUS = 10021 //对焦设置
    const val SETTING_PIC_SIZE = 10022 //画面尺寸
    const val SETTING_KEYSTONE = 10023 //梯形矫正
    const val SETTING_BRIGHTNESS = 10024 //亮度调节
    const val SETTING_SOUND = 10025 //声音
    const val SETTING_TOUCH_PAD = 10026 //触控板
    const val SETTING_BLE_MOU = 10027 //蓝牙鼠键
    const val SETTING_LANGUAGE = 10028 //语言
    const val SETTING_TIME_ZONE = 10029 //时区
    const val SETTING_SYSTEM_INFO = 10030 //系统信息
    const val SETTING_UPGRADE = 10031 //升级
    const val SETTING_RECOVERY = 10032 //恢复出厂
    const val SETTING_FEEDBACK = 10033 //意见反馈
    const val SETTING_CLEAN_SPACE = 10035 //清理空间

    const val LOG_IN = 10034 //登陆

    const val INCREASE_VOLUME = 10101 //增加音量
    const val DECREASE_VOLUME = 10102 //减小音量
    const val MAX_VOLUME = 10103 //最大音量
    const val MIN_VOLUME = 10104 //最小音量
    const val MEDI_VOLUME = 10134 //中等音量
    const val BRIGHTNESS = 10105 //标准亮度
    const val BRIGHTNESS_HIGH = 10106 //高亮
    const val VIEW_INCREASE = 10107 //调大画面
    const val VIEW_DECREASE = 10108 //调小画面
    const val VIEW_MAX = 10109 //最大画面
    const val VIEW_MIN = 10110 //最小画面

    const val VIDEO_MEETING = 20001 //邀请会议
    const val INVIATE_FINISH = 40001 //开始会议
    const val EXIT_MEETING = 40002 //退出会议/挂断
    const val ANSWER_MEETING = 40004 //接收会议邀请
    const val REMOVE_MEETING = 40003 //从会议列表删除联系人
    const val EXIT = 30001 //退出
    const val SLEEP = 30002 //息屏

    const val WAKE_UP = 90001//唤醒
    const val NO_MATCH = -1001//没有匹配
    const val MY_SELF = "self"//添加自己


    const val MEETING_ADD_CONTACT = 0   //添加人员到会议列表
    const val MEETING_START = 1         //启动会议
    const val MEETING_REMOVE_CONTACT = 2//删除人员从会议列表
    const val MEETING_EXIT = 3          //退出会议
}