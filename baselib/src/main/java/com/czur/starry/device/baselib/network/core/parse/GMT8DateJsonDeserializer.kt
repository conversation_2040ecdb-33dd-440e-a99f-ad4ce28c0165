package com.czur.starry.device.baselib.network.core.parse

import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by 陈丰尧 on 2021/7/17
 * 将字符串格式的服务器时间, 转换成Date类型
 * 返回值为UTC时间
 */
private const val TIME_ZONE_OFFSET_IN_MILLIONS = 8 * ONE_HOUR

abstract class GMT8DateJsonDeserializer : JsonDeserializer<Date> {
    abstract val datePattern: String
    private val timeZone = TimeZone.getTimeZone("Etc/GMT-8")


    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Date {
        return json?.let {
            val sdf = SimpleDateFormat(datePattern, Locale.ROOT)
            sdf.timeZone = timeZone
            val serverDateUTC = sdf.parse(it.asString)!!.time
            Date(serverDateUTC)
        } ?: Date()
    }
}