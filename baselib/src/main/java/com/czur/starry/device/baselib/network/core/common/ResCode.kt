package com.czur.starry.device.baselib.network.core.common

/**
 * Created by 陈丰尧 on 2021/7/16
 * 网络状态常量
 */

object ResCode {
    const val RESULT_CODE_SUCCESS = 1000                // 请求成功

    // 服务器错误
    const val RESULT_CODE_SERVER_INTERNAL_ERROR = 1001 // 服务器内部错误

    // 客户端错误
    const val RESULT_CODE_INVALID_TOKEN = 1002          // 无效Token
    const val RESULT_CODE_BAD_ACCESS = 1003             // 不允许访问(通常是激活相关的问题)
    const val RESULT_CODE_ERR_USER_PWD = 1004           // 错误的用户名和密码
    const val RESULT_CODE_USER_INACTIVE = 1005         // 不是活跃用户
    const val RESULT_CODE_MAINLAND_DEVICE_IN_OVERSEA = 1007 // 国内设备在海外
    const val RESULT_CODE_VERIFICATION_CODE_ERROR = 2014    // 验证码错误
    const val RESULT_CODE_ACCOUNT_NOT_MATCH = 2015      // 账号不匹配
    const val RESULT_CODE_SMS_REQUEST_TOO_MUCH = 2017       // 短信请求过多, 一天只有5次机会
    const val RESULT_CODE_VERIFICATION_CODE_HAS_SEND = 2019 // 验证码已发送
    const val RESULT_CODE_REGISTER_TIME_OUT = 2021          // 注册超时, 申请的会议号已经被释放了
    const val RESULT_CODE_REGISTER_OVER_MAX_LIMIT = 2034    // 超过了最大注册次数
    const val RESULT_CODE_MEETING_OVER_LIMIT = 2031     // 超过最大方数限制
    const val RESULT_CODE_SEND_SMS_ERROR = 2041         // 发送验证码失败
    const val RESULT_CODE_MSG_TIME_OUT = 2042           // 消息已过期
    const val RESULT_CODE_SHARE_OCCUPIED = 2044         // 分享被占用
    const val RESULT_CODE_APPLY_FLE_LOCK_FAIL = 2048   // 申请文件锁失败
    const val RESULT_CODE_AUDIO_TEXT_TRANSLATE_DURATION_ERROR = 2057   // 语音转写失败
    const val RESULT_CODE_INVALID_COUNTRY = 4002       // 非注册区域登陆

    // 环境错误
    const val RESULT_CODE_NO_INTERNET = 10001           // 有链接 没网络
    const val RESULT_CODE_NO_NET_CONNECT = 10002           // 没有网络连接

    // 激活失败
    const val RESULT_CODE_ACTIVE_FAIL = 10003           // 激活失败

    // 没有登录
    const val RESULT_CODE_NO_LOGIN = 10004

    // 其他设备登录, 需要顶掉
    const val RESULT_CODE_OTHER_DEVICE_LOGIN = 1111

    // 视频会议
    const val RESULT_CODE_MEETING_PWD_ERROR = 6005      // 会议密码有误
    const val RESULT_CODE_MEETING_FINISH = 6007         // 会议已结束
    const val RESULT_CODE_MEETING_PWD_ERROR_TOO_MUCH = 6008 // 密码错误次数过多
    const val RESULT_CODE_MEETING_BEEN_LOCKED = 6002    // 会议已锁定
    const val RESULT_CODE_MEETING_CODE_ABSENCE = 6001   // 会议不存在

    const val RESULT_CODE_MANY_MAIL_REQUEST = 6015      // 邮件请求过多
}