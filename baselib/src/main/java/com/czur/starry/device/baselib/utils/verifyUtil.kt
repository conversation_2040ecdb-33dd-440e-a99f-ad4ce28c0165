package com.czur.starry.device.baselib.utils

import android.graphics.BitmapFactory
import android.text.InputFilter
import android.widget.EditText
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.widget.CharsFilter
import com.czur.starry.device.baselib.widget.EmojiFilter
import com.czur.starry.device.baselib.widget.FirstZeroFilter
import com.czur.starry.device.baselib.widget.NoSpaceFilter
import com.czur.starry.device.baselib.widget.ShowTextLength
import com.czur.starry.device.baselib.widget.showTextLength
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2021/7/29
 * 进行各种校验的工具类
 */
const val ACCOUNT_LENGTH_DEVICE = 6     // 设备号6位
const val ACCOUNT_LENGTH_DEVICE_PARTY_BUILDING = 7  // 党建版本账号长度7位
const val ACCOUNT_LENGTH_PHONE_CN = 11  // 手机号11位
const val NICKNAME_LENGTH = 14          // 昵称长度(中文占2个字符)
const val PWD_LENGTH_LOWER_LIMIT = 8    // 密码长度下限
const val PWD_LENGTH_UPPER_LIMIT = 20   // 密码长度上限

const val ACCOUNT_LENGTH_LOWER_LIMIT = ACCOUNT_LENGTH_DEVICE        // 会议账号最小长度
const val ACCOUNT_LENGTH_UPPER_LIMIT = ACCOUNT_LENGTH_PHONE_CN      // 会议账号最大长度

/**
 * 当前设备的账号长度
 */
val currentDeviceAccountLength: Int
    get() = when (Constants.versionIndustry) {
        VersionIndustry.UNIVERSAL -> ACCOUNT_LENGTH_DEVICE
        VersionIndustry.PARTY_BUILDING -> ACCOUNT_LENGTH_DEVICE_PARTY_BUILDING
        else -> ACCOUNT_LENGTH_DEVICE
    }

/**
 * 判断字符串长度是否符合账号信息
 */
fun String.isAccountLength(): Boolean =
    length in ACCOUNT_LENGTH_LOWER_LIMIT..ACCOUNT_LENGTH_UPPER_LIMIT

fun String.isDeviceAccountLength(): Boolean = length == currentDeviceAccountLength

fun String.isPwdLength(): Boolean = length in PWD_LENGTH_LOWER_LIMIT..PWD_LENGTH_UPPER_LIMIT

private const val REGEX_EMAIL = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$"


/**
 * 校验是否是电子邮箱
 */
fun String.isEmail(): Boolean {
    if (isNullOrBlank()) return false
    val regex = REGEX_EMAIL.toRegex()
    return matches(regex)
}

/**
 * 校验手机号是否符合规则
 * @param areaCode : 地区码, 不同地区手机号规则不一样
 */
fun String.isPhoneNumber(areaCode: String = "+86"): Boolean {
    if (isNullOrBlank()) {
        return false
    }
    val regex = when (areaCode) {
        "+86" -> "^1\\d{10}$"
        else -> "\\s+"
    }.toRegex()
    return matches(regex)
}

/**
 * 判断字符串是否是验证码
 * 只判断长度是6个字符
 */
fun String.isVerificationCode(): Boolean {
    return this.length == 6
}

/**
 * 校验密码是否符合规则
 * 21.3.1: 于洋: 密码至少包含 大小写字母和数字, 长度介于8~16位之间
 * 21.8.20:于洋: 密码至少包含 大小写字母和数字, 长度介于8~20位之间
 * 22.7.20:于洋: 密码至少包含 字母和数字, 长度介于8~20位之间
 */
fun String.isPwdOK(): Boolean {
    val regex =
        "^(?=.*[a-zA-Z])(?=.*[0-9]).{${PWD_LENGTH_LOWER_LIMIT},${PWD_LENGTH_UPPER_LIMIT}}$".toRegex()
    return matches(regex)
}

/**
 * 给EditText 添加昵称长度的filter
 * 昵称长度: @see [NICKNAME_LENGTH]
 */
fun EditText.addNickNameFilter() {
    showTextLength = NICKNAME_LENGTH    // 昵称长度限制
    addEmojiFilter()                    // 昵称禁止输入Emoji
}

fun EditText.addEmojiFilter() {
    filters = if (filters.isEmpty()) {
        arrayOf(EmojiFilter())
    } else {
        val newFilters = filters.filter {
            it !is EmojiFilter
        }.toTypedArray()
        arrayOf(*newFilters, EmojiFilter())
    }
}

fun EditText.addFirstZeroFilter() {
    filters = if (filters.isEmpty()) {
        arrayOf(FirstZeroFilter())
    } else {
        val newFilters = filters.filter {
            it !is FirstZeroFilter
        }.toTypedArray()
        arrayOf(*newFilters, FirstZeroFilter())
    }
}

/**
 * 添加过滤指定字符的Filter
 */
fun EditText.addCharsFilter(chars: List<Char>) {
    filters = if (filters.isEmpty()) {
        arrayOf(CharsFilter(chars))
    } else {
        val newFilters = filters.filter {
            it !is CharsFilter
        }.toTypedArray()
        arrayOf(*newFilters, CharsFilter(chars))
    }
}


/**
 * 添加禁止输入空格的过滤器
 */
fun EditText.addNoSpaceFilter() {
    filters = if (filters.isEmpty()) {
        arrayOf(NoSpaceFilter())
    } else {
        val newFilters = filters.filter {
            it !is NoSpaceFilter
        }.toTypedArray()
        arrayOf(*newFilters, NoSpaceFilter())
    }
}

/**
 * 添加登录输入框的长度限制
 * 根据版本不同, 长度也不同
 */
fun EditText.addLoginAccountLengthFilter() {
    val length = currentDeviceAccountLength
    addMaxLengthFilter(length)
}

fun EditText.addAccountLengthFilter() {
    if (filters.isEmpty()) {
        filters = arrayOf(InputFilter.LengthFilter(ACCOUNT_LENGTH_UPPER_LIMIT))
    } else {
        val lengthFilterIndex = filters.indexOfFirst {
            it is InputFilter.LengthFilter
        }
        if (lengthFilterIndex >= 0) {
            filters[lengthFilterIndex] = InputFilter.LengthFilter(ACCOUNT_LENGTH_UPPER_LIMIT)
            filters = filters
        } else {
            filters = arrayOf(*filters, InputFilter.LengthFilter(ACCOUNT_LENGTH_UPPER_LIMIT))
        }
    }
}

private fun EditText.addMaxLengthFilter(maxLength: Int) {
    if (filters.isEmpty()) {
        filters = arrayOf(InputFilter.LengthFilter(maxLength))
    } else {
        val lengthFilterIndex = filters.indexOfFirst {
            it is InputFilter.LengthFilter
        }
        if (lengthFilterIndex >= 0) {
            filters[lengthFilterIndex] = InputFilter.LengthFilter(maxLength)
            filters = filters
        } else {
            filters = arrayOf(*filters, InputFilter.LengthFilter(maxLength))
        }
    }
}


fun getNickNameFilterArr(): Array<InputFilter> =
    arrayOf(ShowTextLength(NICKNAME_LENGTH), EmojiFilter())

/**
 * 判断文件是否是合法的图片
 */
suspend fun File.isValidImage(): Boolean = withContext(Dispatchers.IO) {
    try {
        if (!exists() || !isFile) return@withContext false

        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeFile(absolutePath, options)
        options.outWidth > 0 && options.outHeight > 0
    } catch (e: Exception) {
        false
    }
}

/**
 * 校验异常
 */
class VerifyException(message: String) : Exception(message)

