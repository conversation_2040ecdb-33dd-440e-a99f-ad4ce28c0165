package com.czur.starry.device.baselib.notice

import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.*
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.Channel.Factory.UNLIMITED

/**
 * Created by 陈丰尧 on 4/7/21
 */
object NoticeHandler {
    private const val TAG = "NoticeHandler"
    const val SYNC_TIME_OUT = 4 * ONE_SECOND

    // 协程
    private val job = Job()
    private val scope = CoroutineScope(job)

    private val receiver by lazy {
        val contentResolver = CZURAtyManager.appContext.contentResolver
        contentResolver
    }

    // 给ContentObserver使用的Handler
    private val handler: Handler by lazy {
        Handler(Looper.getMainLooper())
    }

    private val observableMap = mutableMapOf<Long, NoticeContentObserver>()
    private val simpleLock = SimpleLock()

    private val willDelObserverChannel = Channel<NoticeContentObserver>(capacity = UNLIMITED)


    init {
        scope.launch {
            while (isActive) {
                val willDelObserver = willDelObserverChannel.receive()
                delay(2 * ONE_SECOND)
                simpleLock.write {
                    if (willDelObserver.isCallBackEmpty()) {
                        logTagD(TAG, "取消注册contentObserver")
                        receiver.unregisterContentObserver(willDelObserver)
                        observableMap.remove(willDelObserver.typeKey)
                    } else {
                        logTagV(TAG, "还有callback, 不取消注册")
                    }

                }
            }
        }
    }

    /**
     * 发送一条消息
     * @param type          消息类型
     * @param msgBuilder    提供一个NoticeMsg的作用域,方便构建消息
     */
    fun sendMessage(type: MsgType, msgBuilder: suspend NoticeMsg.() -> Unit) {
        scope.launch {
            val noticeMsg = NoticeMsg()
            noticeMsg.msgBuilder()
            sendMessage(type, noticeMsg)
        }
    }

    /**
     * 发送消息
     * @param type: 消息类型
     * @param msg: 消息体,可以不传
     */
    fun sendMessage(type: MsgType, msg: NoticeMsg = NoticeMsg()) {
        logTagD(TAG, "sendMessage: type:${type}")
        scope.launch {
            try {
                val cv = makeContentValues(type, msg)
                receiver.insert(NoticeConstant.URI, cv)
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
    }

    /**
     * 注册接收消息, 一个客户端可以注册多次回调
     * 但是同一种类型,实际上只会向消息中心注册一次, Client端会维护同一类型的多种回调
     * @param type: 关心的消息类型
     */
    fun register(type: MsgType, owner: LifecycleOwner? = null, listener: (msg: NoticeMsg) -> Unit) {
        logTagD(TAG, "注册回调")
        scope.launch(Dispatchers.Default) {
            val typeKey = type.typeKey
            val noticeContentObserver = simpleLock.write {
                observableMap.getOrPut(typeKey) {
                    logTagD(TAG, "消息类型:${type}第一次注册")
                    val notifyUri = Uri.Builder()
                        .scheme(CONTENT)
                        .authority(NoticeConstant.AUTHORITY)
                        .appendPath(typeKey.toString()) // notify key
                        .build()
                    // 注册回调
                    val observer =
                        NoticeContentObserver(scope, typeKey, handler)
                    receiver.registerContentObserver(
                        notifyUri,
                        true,
                        observer
                    )
                    observer

                }
            }

            if (owner != null) {
                val values = simpleLock.read {
                    observableMap.values.toList()
                }
                if (!values.has {
                        it.hasThisOwner(owner)
                    }) {
                    logTagD(TAG, "该owner没有之前添加过监听")
                    addLifecycleObserver(owner)
                }
            }

            // 添加回调接口
            noticeContentObserver.addWrapper(CallBackWrapper(owner, listener))
        }

    }


    /**
     * 同步获取数据
     */
    suspend fun getData(type: MsgType, timeout: Long = SYNC_TIME_OUT): NoticeMsg? {
        return withContext(Dispatchers.Default) {
            try {
                val cursor =
                    receiver.query(
                        NoticeConstant.URI,
                        null,
                        null,
                        arrayOf("${type.typeKey}", timeout.toString()), // 超时时间
                        null
                    )
                val noticeMsg: NoticeMsg? = cursor?.use {
                    it.toEntityBlob()
                }
                noticeMsg
            } catch (exp: Exception) {
                logTagE(TAG, "getData失败", tr = exp)
                null
            }
        }
    }

    /**
     * 注册一个同步消息
     */
    fun registerSync(
        type: MsgType,
        owner: LifecycleOwner? = null,
        listener: suspend (msg: NoticeMsg) -> NoticeMsg?
    ) {
        register(type, owner) {
            scope.launch {
                // 收到消息
                // 获取同步请求的ID
                val msgID = it.msgId
                // 业务端获取数据
                val result = listener(it) ?: NoticeMsg()
                result.isBackMsg = true
                result.msgId = msgID
                sendMessage(type, result)
            }
        }
    }


    /**
     * 取消注册, 针对没有指定LifeOwner的情况
     * 如果使用LifeOwner, 会自动跟随生命周期取消注册
     */
    fun unRegister(type: MsgType, listener: (msg: NoticeMsg) -> Unit) {
        val noticeContentObserver = simpleLock.read {
            observableMap[type.typeKey]
        } ?: return
        val needUnRegister = noticeContentObserver.unRegisterByListener(listener)
        if (needUnRegister) {
            logTagD(TAG, "取消注册contentObserver")
            receiver.unregisterContentObserver(noticeContentObserver)
            simpleLock.write {
                observableMap.remove(type.typeKey)
            }
        }
    }

    /**
     * 取消注册所有监听, 在程序退出前使用
     */
    fun clearAll() = doWithoutCatch {
        logTagD(TAG, "取消注册所有监听")
        scope.launch {
            simpleLock.read {
                observableMap.values.forEach {
                    receiver.unregisterContentObserver(it)
                }
            }

            simpleLock.write {
                observableMap.clear()
            }
        }

    }

    private suspend fun addLifecycleObserver(owner: LifecycleOwner) =
        withContext(Dispatchers.Main) {
            // 第一次注册这个Owner, 对这个Owner添加生命周期回调
            logTagD(TAG, "添加监听,监控生命周期${owner.javaClass.simpleName}-${owner}")
            owner.lifecycle.addObserver(object : LifecycleEventObserver {
                override fun onStateChanged(
                    source: LifecycleOwner,
                    event: Lifecycle.Event
                ) {
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        logTagD(
                            TAG,
                            "lifecycleOwner:${source.javaClass.simpleName}-${source} 已经Destroy"
                        )
                        // 应当销毁所有该owner对象绑定的
                        val willDestroyCallBacks = simpleLock.read {
                            observableMap.values.filter {
                                it.hasThisOwner(source)
                            }
                        }
                        logTagV(TAG, "需要取消注册数量:${willDestroyCallBacks.size}")

                        try {
                            willDestroyCallBacks.forEach {
                                val destroy = it.unRegisterByOwner(source)
                                if (destroy) {
                                    // 这个wrapper里已经没有要回调的对象了
                                    // 就跟Service取消注册
                                    logTagD(TAG, "发送到销毁flow中")
                                    scope.launch {
                                        willDelObserverChannel.send(it)
                                    }
                                }
                            }
                        } catch (e: NullPointerException) {
                            logTagD(
                                TAG,
                                "addLifecycleObserver Null Pointer Exception:${e.message}"
                            )
                        }

                        // 取消生命周期的回调
                        source.lifecycle.removeObserver(this)
                    }
                }
            })
        }
}

