package com.czur.starry.device.baselib.utils.data

import android.annotation.SuppressLint
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.utils.inMainThread
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

/**
 * Created by 陈丰尧 on 2021/6/10
 * 用来代理LiveData实现对LiveData中值的 set/get方法
 * @param liveData 要代理的LiveData
 * @param def: 默认值: 如果为null,则代表认为 liveData中的value永远不可能为null,
 *              在使用get方法时, 会直接返回LiveData中的value
 */
class LiveDataDelegate<T>(
    private val liveData: MutableLiveData<T>,
    private val def: T? = null,
    private val setHookBlock: ((value: T) -> T)? = null
) :
    ReadWriteProperty<Any, T> {

    /**
     * 简化类型转换操作
     * 有可能抛出类型转换异常
     */
    constructor(liveData: LiveData<T>, def: T? = null, setHookBlock: ((value: T) -> T)? = null) : this(
        liveData as MutableLiveData,
        def,
        setHookBlock
    )

    override fun getValue(thisRef: Any, property: KProperty<*>): T {
        val result = liveData.value
        return if (result != null || def == null) {
            result!!
        } else {
            def
        }
    }

    @SuppressLint("NullSafeMutableLiveData")
    override fun setValue(thisRef: Any, property: KProperty<*>, value: T) {
        val realValue = setHookBlock?.invoke(value) ?: value
        if (inMainThread()) {
            // 自动做线程切换
            liveData.value = realValue
        } else {
            liveData.postValue(realValue)
        }
    }

}