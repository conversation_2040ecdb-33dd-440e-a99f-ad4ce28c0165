package com.czur.starry.device.baselib.widget

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.Gravity
import android.view.PointerIcon
import androidx.appcompat.widget.AppCompatButton
import com.czur.starry.device.baselib.R
import com.czur.starry.device.baselib.utils.changeColorAlpha
import com.noober.background.drawable.DrawableCreator

class CommonButton @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatButton(context, attrs, defStyleAttr) {

    enum class Theme(
        val code: Int,
        val textColor: Int,
        val bgColor: Int
    ) {
        DARK(1, 0xFFFFFFFF.toInt(), 0x33FFFFFF),
        LIGHT(2, 0xFFFFFFFF.toInt(), 0xFF4BB6F4.toInt()),
        BLUE(3, 0xFFFFFFFF.toInt(), 0xFF5879FC.toInt()),
        WHITE(4, 0xFFEF4B4C.toInt(), 0xFFFFFFFF.toInt()),
        RED(5, 0xFFFFFFFF.toInt(), 0xFFEF4B4C.toInt()),
        WHITE2(6, 0xFF5879fc.toInt(), 0xFFFFFFFF.toInt()),
        DARK2(7,0xFF5e5e5e.toInt(),0xFFF2F2F2.toInt()),
        DARK3(8,0xFFFFFFFF.toInt(),0x7F000000.toInt()),
        BLUE2(9,0xFFFFFFFF.toInt(),0xFF046CFD.toInt()),
        GREY(10,0xFF3D3D3D.toInt(),0xFFE7E7E7.toInt());

        companion object {
            fun createByCode(code: Int): Theme {
                return values().find { it.code == code } ?: DARK
            }
        }
    }


    lateinit var theme: Theme       // lateinit 不能删除, init代码块会在setEnabled方法后被调用
        private set

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CommonButton)
        val themeCode = ta.getInt(R.styleable.CommonButton_baselib_theme, Theme.DARK.code)
        initStyle()
        theme = Theme.createByCode(themeCode)
        pointerIcon = PointerIcon.getSystemIcon(context, PointerIcon.TYPE_ARROW) // 防止出现小手
        ta.recycle()
        changeStyle()
    }

    fun changeTheme(theme: Theme) {
        this.theme = theme
        changeStyle()
    }

    private fun changeStyle() {
        background = DrawableCreator.Builder()
            .setCornersRadius(10f)
            .setSolidColor(theme.bgColor).build()

        setTextColor(theme.textColor)
        changeEnableStyle()
    }

    private fun initStyle() {
        gravity = Gravity.CENTER
        setTypeface(null, Typeface.BOLD)
    }


    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        changeEnableStyle()
    }

    private fun changeEnableStyle() {
        if (this::theme.isInitialized) {
            alpha = if (isEnabled) 1F else 0.5F
        }
    }


}