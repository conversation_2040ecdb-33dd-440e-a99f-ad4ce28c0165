package com.czur.starry.device.baselib.utils.fw.proxy

import android.media.AudioDeviceInfo
import android.media.SystemManager
import android.media.SystemManager.OnSystemChangeListener
import android.provider.Settings
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.doWithoutCatch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/6/13
 * SystemManager的代理
 * SystemManager: czur自己定制的接口类, 在Android升级时,可能不会第一时间实装
 * 将用到的方法代理出来,并捕获异常, 可以在SystemManager没有适配完成时,保证应用不会崩溃, 影响其他功能的调试
 * 同时将一些参数变成枚举,方便调用
 */
class SystemManagerProxy(onSystemChangeListener: OnSystemChangeListener? = null) {
    companion object {
        private const val TAG = "SystemManagerProxy"

        private const val TRACK_MODE_GET = -1   // 获取当前追踪模式

        // 参考文档: https://czurtech.feishu.cn/docx/XA1CdYB9goT51rxL0gecdid8n8X
        const val PERF_CONSTRAINT_NONE = 0  // 无性能约束
        const val PERF_CONSTRAINT_HDMI = 1  // HDMI性能约束
        const val PERF_CONSTRAINT_CAST = 2  // 投屏性能约束
        const val PERF_CONSTRAINT_VIDEO = 4 // 视频性能约束

        // 回调分类
        const val CALLBACK_CATEGORY_GADGET = 3
        const val CALLBACK_CATEGORY_HDMIOUT_STATE = 5

        private const val ADB_SETTING_ON: Int = 1
        private const val ADB_SETTING_OFF: Int = 0
    }

    enum class TrackMode(val modeValue: Int) {
        TRACK_MODE_OFF(2),            // 关闭追踪模式
        TRACK_MODE_VISION(0),         // 智能人像画面
        TRACK_MODE_VISION_AND_DOA(1)  // 智能人像画面+声源定位
    }

    enum class PerfConstraintScene(val perfValue: Int) {
        HDMI(PERF_CONSTRAINT_HDMI),  // HDMI性能约束
        CAST(PERF_CONSTRAINT_CAST),  // 投屏性能约束
        VIDEO(PERF_CONSTRAINT_VIDEO)  // 视频性能约束
    }

    /**
     * 显示模式
     */
    enum class LooksMode(val modeValue: Int) {
        LOOKS_MODE_6500K(1),
        LOOKS_MODE_8880K(0),
        LOOKS_MODE_NOT_SUPPORTED(-1)
    }

    /**
     * 光机移动方向
     */
    enum class MachMoveDirection(val direction: Int) {
        FORWARD(1),  // 向前
        BACKWARD(0)  // 向后
    }

    /**
     * 控制USB模式
     */
    enum class ControlUSBMode(val modeValue: Int) {
        USB_MODE_IS_GADGET(-1),         // 获取是否是gadget模式，返回0表示不是，非0表示是
        USB_MODE_OTG(0),                // 保留,没有使用
        USB_MODE_ENABLE_ADB(1),         // 设置设备作为USB的从设备，并启动ADB
        USB_MODE_HOST(2),               // 设置设备作为USB的主机, 并停止ADB
        USB_MODE_EN_GADGET(3),          // 使能gadget模式，具体是否开启ADB取决于debuggable 重启后恢复
        USB_MODE_EN_GADGET_STORE(4),    // 使能gadget模式，具体是否开启ADB取决于debuggable 重启后不恢复
        USB_MODE_DIS_GADGET(5)          // 停止gadget功能，退回到HOST模式或者由debuggable确定是否还开启ADB
    }

    /**
     * USB外设模式的状态信息
     */
    enum class USBModeState(val state: Int) {
        USB_GADGET_MODE_OFF(0),         // USB外设模式关闭
        USB_GADGET_MODE_ON(1),          // USB外设模式开启
        USB_GADGET_CONNECTED(2),        // USB线连接
        USB_GADGET_DISCONNECTED(3),     // USB线断开
        USB_GADGET_STREAM_ON(4),        // 数据流使用中
        USB_GADGET_STREAM_OFF(5),       // 数据流未使用
    }

    private var systemManager: SystemManager? = null

    init {
        try {
            // SystemManager 现在必须要有一个Listener
            systemManager = CZURAtyManager.appContext.getSystemService(SystemManager::class.java)
            if (onSystemChangeListener != null) {
                systemManager?.addListener(onSystemChangeListener)
            } else {
                logTagW(TAG, "onSystemChangeListener is null,add default listener")
                val listener =
                    OnSystemChangeListener { category, eventID, para1, extend1, extend2 ->
                        logTagV(
                            TAG,
                            "category: $category, eventID: $eventID, para1: $para1, extend1: $extend1, extend2: $extend2"
                        )
                    }
                systemManager?.addListener(listener)
            }
        } catch (tr: Throwable) {
            logTagE(TAG, "SystemManager 初始化失败", tr = tr)
        }

    }

    val BCSH_6500K: IntArray
        get() = try {
            SystemManager.BCSH_6500K
        } catch (e: Throwable) {
            logTagW(TAG, "BCSH_6500K 获取失败", tr = e)
            intArrayOf(58, 50, 60, 50)
        }

    val BCSH_8880K: IntArray
        get() = try {
            SystemManager.BCSH_8880K
        } catch (e: Throwable) {
            logTagW(TAG, "BCSH_8880K 获取失败", tr = e)
            intArrayOf(53, 50, 59, 44)
        }


    fun getEthernetMac(): String {
        return try {
            systemManager!!.ethernetMac.lowercase().trim()
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager getEthernetMac 调用失败", tr = e)
            ""
        }
    }

    fun getDlp3439LooksMode(): LooksMode {
        if (Constants.starryHWInfo.series == Q2Series) {
            return LooksMode.LOOKS_MODE_NOT_SUPPORTED   // Q2不支持,直接返回
        }
        return try {
            val modeValue = systemManager!!.dlp3439LooksMode
            logTagV(TAG, "systemManager#getDlp3439LooksMode:${modeValue}")
            when (modeValue) {
                SystemManager.LOOKS_MODE_6500K -> LooksMode.LOOKS_MODE_6500K
                SystemManager.LOOKS_MODE_8880K -> LooksMode.LOOKS_MODE_8880K
                else -> LooksMode.LOOKS_MODE_NOT_SUPPORTED
            }
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager getDlp3439LooksMode 调用失败", tr = e)
            LooksMode.LOOKS_MODE_NOT_SUPPORTED
        }
    }

    fun saveDlp3439Looks(value: Int): Int = try {
        systemManager!!.saveDlp3439Looks(value)
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager saveDlp3439Looks 调用失败", tr = e)
        -1
    }

    suspend fun saveDlp3439Looks(value: Int, looksMode: LooksMode): Int = try {
        logTagV(TAG, "saveDlp3439Looks:value:${value} - looksMOde:${looksMode}")
        withContext(Dispatchers.IO) {
            systemManager!!.saveDlp3439Looks(value, looksMode.modeValue)
        }
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager saveDlp3439Looks 调用失败", tr = e)
        -1
    }

    fun perfCpuGov(): Int {
        logTagV(TAG, "perfCpuGov")
        return try {
            systemManager!!.perfCpuGov()
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager perfCpuGov 调用失败", tr = e)
            -1
        }
    }

    fun restoreCpuGov(): Int {
        logTagV(TAG, "restoreCpuGov")
        return try {
            systemManager!!.restoreCpuGov()
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager restoreCpuGov 调用失败", tr = e)
            -1
        }
    }

    /**
     * 设置追踪模式
     * @param trackMode 追踪模式
     * @see TrackMode
     */
    fun setTrackMode(trackMode: TrackMode) {
        logTagV(TAG, "setTrackMode: $trackMode")
        doWithoutCatch(TAG, "setTrackMode 调用失败") {
            systemManager!!.setTrackMode(trackMode.modeValue)
        }
    }

    /**
     * 获取当前追踪模式
     */
    fun getTrackMode(): TrackMode {
        return try {
            when (systemManager!!.setTrackMode(TRACK_MODE_GET)) {
                TrackMode.TRACK_MODE_OFF.modeValue -> TrackMode.TRACK_MODE_OFF
                TrackMode.TRACK_MODE_VISION.modeValue -> TrackMode.TRACK_MODE_VISION
                TrackMode.TRACK_MODE_VISION_AND_DOA.modeValue -> TrackMode.TRACK_MODE_VISION_AND_DOA
                else -> TrackMode.TRACK_MODE_OFF
            }
        } catch (e: Throwable) {
            logTagW(TAG, "getTrackMode", tr = e)
            TrackMode.TRACK_MODE_OFF
        }
    }

    /**
     * 开启算法收集
     * @param audio 是否开启音频算法收集
     * @param camera 是否开启摄像头算法收集 默认和音频一致
     */
    fun enableAlgoCollection(audio: Boolean, camera: Boolean = audio) {
        try {
            logTagD(TAG, "enableAlgoCollection: audio=$audio, camera=$camera")
            systemManager!!.enableAlgoCollection(audio, camera)
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager enableAlgoCollection 调用失败", tr = e)

        }
    }

    /**
     * 获取当前性能约束
     */
    fun getPerfConstraint(): Int {
        return try {
            systemManager!!.perfConstraint
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager getPerfConstraintError 调用失败", tr = e)
            PERF_CONSTRAINT_NONE
        }.also {
            logTagD(TAG, "getPerfConstraint: $it")
        }
    }

    /**
     * 是否需要按照性能约束进行限制
     */
    fun needPerfConstraint(scene: PerfConstraintScene): Boolean {
        val perfConstraint = getPerfConstraint()
        return perfConstraint and scene.perfValue != 0  // 因为返回值可能是多个场景的复合结果，所以需要用位运算判断
    }

    /**
     * 控制光机移动 异步API
     * @param direction 移动方向
     * @param step 移动步数
     */
    fun opticalMachMove(direction: MachMoveDirection, step: Int): Int =
        try {
            systemManager!!.opticalMachMove(direction.direction, step)
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager opticalMachMove 调用失败", tr = e)
            -1
        }

    /**
     * 光机自动对焦
     */
    fun opticalMachAutoFocus(): Int = try {
        systemManager!!.opticalMachAutoFocus()
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager opticalMachAutoFocus 调用失败", tr = e)
        -1
    }

    /**
     * 光机重置自动对焦
     */
    fun opticalMachResetAutoFocus(): Int = try {
        systemManager!!.opticalMachResetAutoFocus()
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager opticalMachResetAutoFocus 调用失败", tr = e)
        -1
    }

    /**
     * 控制USB模式(外设模式)
     */
    private suspend fun controlUsbMode(mode: ControlUSBMode): Int {
        logTagD(TAG, "controlUsbMode: $mode")
        return try {
            withContext(Dispatchers.Default) {
                systemManager!!.controlUsbMode(mode.modeValue)
            }
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager controlUsbMode 调用失败", tr = e)
            -1
        }
    }

    /**
     * 获取是否是gadget模式
     */
    fun isGadgetMode(): Boolean {
        return getGadgetMode() != USBModeState.USB_GADGET_MODE_OFF
    }

    /**
     * 获取gadget模式
     */
    fun getGadgetMode(): USBModeState {
        return try {
            logTagV(TAG, "getGadgetMode Start")
            val modeValue =
                systemManager!!.controlUsbMode(ControlUSBMode.USB_MODE_IS_GADGET.modeValue).also {
                    logTagD(TAG, "isGadgetMode Value: $it")
                }
            USBModeState.entries.find { it.state == modeValue } ?: USBModeState.USB_GADGET_MODE_OFF
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager isGadgetMode 调用失败", tr = e)
            USBModeState.USB_GADGET_MODE_OFF
        }
    }

    /**
     * 开启gadget模式
     */
    suspend fun setGadgetMode(enable: Boolean) {
        logTagD(TAG, "setGadgetMode: $enable")
        if (enable) {
            controlUsbMode(ControlUSBMode.USB_MODE_EN_GADGET_STORE)
        } else {
            controlUsbMode(ControlUSBMode.USB_MODE_DIS_GADGET)
        }
        // 发送USB状态变化的消息
        logTagV(TAG, "发送USB状态变化的消息")
        NoticeHandler.sendMessage(MsgType(MsgType.SYNC, MsgType.COMMON_PERIPHERAL_USB_CHANGE))
    }

    /**
     * 开启ADB
     */
    suspend fun enableADB(enable: Boolean = true) {
        logTagD(TAG, "enableADB enable: $enable")
        withContext(Dispatchers.Default) {
            // 需要先设置USB模式
            if (enable) {
                controlUsbMode(ControlUSBMode.USB_MODE_ENABLE_ADB)
            } else {
                val gadgetMode = getGadgetMode()
                if (gadgetMode == USBModeState.USB_GADGET_MODE_OFF) {
                    controlUsbMode(ControlUSBMode.USB_MODE_HOST)
                }
            }

            Settings.Global.putInt(
                globalAppCtx.contentResolver,
                Settings.Global.ADB_ENABLED, if (enable) ADB_SETTING_ON else ADB_SETTING_OFF
            )
        }
    }

    /**
     * Update Audio Policy when ent BYOM.
     * @param enterByom  true, enter byom; false, exit byom.
     */
    fun updateAudioPolicyByByom(enterByom: Boolean): Int {
        logTagD(TAG, "updateAudioPolicyByByom: $enterByom")
        return systemManager?.updateAudioPolicyByByom(enterByom) ?: -1
    }

    fun getHdmiOutState(): Boolean = try {
        val state = systemManager!!.hdmiOutState
        state
    } catch (e: Throwable) {
        logTagW(TAG, "qyD SystemManager getHdmiOutStateError 调用失败", tr = e)
        false
    }

    // CEC部分
    // 打开 本机 联动 外接设备
    /**
     * 获取 打开本机 时是否联动 外接设备
     * @return true: 联动 false: 不联动
     */
    fun getCECStateBootScreenByDevice(): Boolean = try {
        systemManager!!.isHdmiCecOutStartState
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager getHdmiCecOutState 调用失败", tr = e)
        false
    }

    /**
     * 设置 打开本机 时是否联动 外接设备
     * @param enable true: 联动 false: 不联动
     * @return 0: 成功 -1: 失败
     */
    fun setCECStateBootScreenByDevice(enable: Boolean): Int = try {
        systemManager!!.setHdmiCecOutStartState(enable)
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager setCECEnable 调用失败", tr = e)
        -1
    }

    // 关闭 本机 联动 外接设备
    /**
     * 获取 关闭本机 时是否联动 外接设备
     * @return true: 联动 false: 不联动
     */
    fun getCECStateShutdownScreenByDevice(): Boolean = try {
        systemManager!!.isHdmiCecOutShutDownState
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager getHdmiCecOutShutdownState 调用失败", tr = e)
        false
    }

    /**
     * 设置 关闭本机 时是否联动 外接设备
     * @param enable true: 联动 false: 不联动
     * @return 0: 成功 -1: 失败
     */
    fun setCECStateShutdownScreenByDevice(enable: Boolean): Int = try {
        systemManager!!.setHdmiCecOutShutDownState(enable)
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager setHdmiCecOutShutdownState 调用失败", tr = e)
        -1
    }

    // 打开 外接设备 联动 本机
    /**
     * 获取 打开外接设备 时是否联动 本机
     * @return true: 联动 false: 不联动
     */
    fun getCECStateBootDeviceByScreen(): Boolean = try {
        systemManager!!.isHdmiCecInStartState
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager getHdmiCecInStartState 调用失败", tr = e)
        false
    }

    /**
     * 设置 打开外接设备 时是否联动 本机
     * @param enable true: 联动 false: 不联动
     * @return 0: 成功 -1: 失败
     */
    fun setCECStateBootDeviceByScreen(enable: Boolean): Int = try {
        systemManager!!.setHdmiCecInStartState(enable)
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager setHdmiCecInStartState 调用失败", tr = e)
        -1
    }

    // 关闭 外接设备 联动 本机
    /**
     * 获取 关闭外接设备 时是否联动 本机
     * @return true: 联动 false: 不联动
     * @return 0: 成功 -1: 失败
     */
    fun getCECStateShutdownDeviceByScreen(): Boolean = try {
        systemManager!!.isHdmiCecInShutDownState
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager getHdmiCecInShutDownState 调用失败", tr = e)
        false
    }

    /**
     * 设置 关闭外接设备 时是否联动 本机
     * @param enable true: 联动 false: 不联动
     * @return 0: 成功 -1: 失败
     */
    fun setCECStateShutdownDeviceByScreen(enable: Boolean): Int = try {
        systemManager!!.setHdmiCecInShutDownState(enable)
    } catch (e: Throwable) {
        logTagW(TAG, "SystemManager setHdmiCecInShutDownState 调用失败", tr = e)
        -1
    }

    /**
     * 设置音频输出设备
     * @param device 音频设备
     * @return true: 成功 false: 失败
     */
    fun setPreferredAudioDevice(device: AudioDeviceInfo): Boolean {
        return try {
            val inout = if (device.isSink) 0 else 1
            logTagD(TAG, "setPreferredAudioDevice param:${inout} - ${device.type} - 0")
            val res = systemManager!!.setPreferredAudioDevice(inout, device.type, 0)
            logTagD(TAG, "setPreferredAudioDevice res:${res}")
            return res == 0
        } catch (e: Throwable) {
            logTagW(TAG, "SystemManager setPreferredAudioDevice 调用失败", tr = e)
            false
        }
    }
}