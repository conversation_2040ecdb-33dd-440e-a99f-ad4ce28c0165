package com.czur.starry.device.baselib.network.core.interceptor

import android.content.Intent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.BASE_HOST
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_E_SHARE_MAC
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.common.ResCode.RESULT_CODE_SUCCESS
import com.czur.starry.device.baselib.network.core.exception.MiaoActiveFailExp
import com.czur.starry.device.baselib.network.core.util.addParams
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import com.czur.starry.device.baselib.utils.prop.starryDeviceID
import com.google.gson.Gson
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response

/**
 * Created by 陈丰尧 on 2021/7/14
 * 为网络请求添加激活秘钥
 * 如果没有激活秘钥, 则自动请求激活信息
 */
class ActiveInterceptor : Interceptor {
    companion object {
        private const val TAG = "ActiveInterceptor"
        private const val ACTIVE_URL = "/starry/api/starry/register/v2/active"
        private const val ACTIVE_URL_DIRECT = "/api/starry/register/v2/active"

        private const val PARAM_KEY_SECRET = "secret"
        private const val PARAM_KEY_ACTIVE_SN = "sn"
        private const val PARAM_KEY_VOUCHER = "voucher"
        private const val PARAM_KEY_MAC = "mac"
    }

    private val activeURL: String
        get() = if (Constants.BASE_HOST.contains("/starry")) {
            ACTIVE_URL
        } else {
            ACTIVE_URL_DIRECT
        }

    // 不需要激活信息的接口
    private val noNeedActiveUrl = listOf(
        "/api/starry/register/restoreSetting",
        "/api/starry/register/active",
        "/starry/api/starry/register/restoreSetting",
        "/starry/api/starry/register/active",
        ACTIVE_URL,
        ACTIVE_URL_DIRECT
    )

    private val gson by lazy { Gson() }

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }


    override fun intercept(chain: Interceptor.Chain): Response {
        val oldReq = chain.request()
        if (oldReq.url.encodedPath in noNeedActiveUrl) {
            logTagV(TAG, "不需要激活接口:url${oldReq.url.encodedPath}")
            // 添加默认的secret信息
            val newReq = oldReq.addParams(PARAM_KEY_SECRET to Constants.DEF_SECRET)
            return chain.proceed(newReq)
        }

        if (starryDeviceID.isEmpty()) {
            logTagE(TAG, "需要激活, 但是starryDeviceID为空, 禁止激活")
            return chain.proceed(oldReq)
        }

        // 其他接口
        var secret = UserHandler.secret
        if (secret.isEmpty()) {
            logTagV(TAG, "需要激活:${oldReq.url.encodedPath}")
            // 需要激活
            val ethMac = systemManager.getEthernetMac()
            val activeRequest = mkActiveRequest(oldReq.url, ethMac)

            val response = chain.proceed(activeRequest)

            val result = response.body?.string() ?: throw MiaoActiveFailExp()
            val activeEntity = try {
                gson.fromJson(result, ActiveEntity::class.java)
            } catch (tr: Throwable) {
                logTagE(TAG, "激活失败(解析服务器数据失败):result:${result}", tr = tr)
                throw MiaoActiveFailExp()
            }
            if (!activeEntity.isSuccess) {
                logTagE(TAG, "激活失败")
                throw MiaoActiveFailExp()
            }
            // 激活成功, 存储信息
            setStringSystemProp(KEY_LAUNCHER_E_SHARE_MAC, ethMac)
            secret = activeEntity.data.secret
            // 存储secret信息
            UserHandler.secret = secret

            // 激活EShare
            activeEntity.data.screenActiveCode?.let { code ->
                activeEShare(code)
            }

            response.body?.close()
        }

        // 继续网络请求
        return chain.proceed(oldReq.addParams(PARAM_KEY_SECRET to secret))
    }

    private fun activeEShare(activeCode: String) {
        logTagD(TAG, "激活EShare,code:${activeCode}")
        // 不考虑数据恢复的情况
        logTagD(TAG, "设置混投开关")
        setBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, true)
        UserHandler.eShareActiveCode = activeCode
        val intent = Intent("com.eshare.action.activate_with_key").apply {
            putExtra("lickey", activeCode)
        }
        val context = CZURAtyManager.appContext
        sendBroadcastAsUser(context, intent, "com.ecloud.eshare.server")
    }

    /**
     * 构造激活 Request
     */
    private fun mkActiveRequest(oldUrl: HttpUrl, ethMac: String): Request {
        val host = BASE_HOST.substringBefore("/")
        val url = HttpUrl.Builder()
            .scheme(oldUrl.scheme)
            .host(host)
            .encodedPath(activeURL)
            .addQueryParameter(PARAM_KEY_ACTIVE_SN, Constants.SERIAL)
            .addQueryParameter(PARAM_KEY_SECRET, Constants.DEF_SECRET)
            .addQueryParameter(PARAM_KEY_VOUCHER, starryDeviceID)
            .addQueryParameter(PARAM_KEY_MAC, ethMac)
            .build()
        val requestBuilder = Request.Builder()
        return requestBuilder
            .get()
            .url(url)
            .build()
    }
}

/**
 * 激活接口的返回数据类型
 */
data class ActiveEntity(
    val code: Int,
    val msg: String,
    val data: ActiveData,
) {
    val isSuccess: Boolean
        get() = code == RESULT_CODE_SUCCESS
}

data class ActiveData(
    val secret: String,
    val screenActiveCode: String? // 有可能没有
)