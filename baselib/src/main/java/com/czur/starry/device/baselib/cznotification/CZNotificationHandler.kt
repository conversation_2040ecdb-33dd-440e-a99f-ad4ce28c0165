package com.czur.starry.device.baselib.cznotification

import android.content.ContentValues
import android.graphics.Bitmap
import android.net.Uri
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.CONTENT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.ObjectOutputStream

/**
 * Created by 陈丰尧 on 2024/8/13
 */
class CZNotificationHandler {
    companion object {
        private const val TAG = "CZNotificationHandler"

        const val CZ_NOTIFICATION_PROVIDER_AUTHORITY =
            "com.czur.starry.device.launcher.notice.CZNotificationProvider"

        const val CZ_NOTIFICATION_KEY_PKG_NAME = "cz_notification_pkg_name"
        const val CZ_NOTIFICATION_KEY_ICON = "cz_notification_icon"
        const val CZ_NOTIFICATION_KEY_TITLE = "cz_notification_title"
        const val CZ_NOTIFICATION_KEY_PENDING_INTENT = "cz_notification_pending_intent"
        const val CZ_NOTIFICATION_KEY_REMOVE_AFTER_EXECUTION =
            "cz_notification_remove_after_execution"

        const val CZ_DEL_WHERE_ID = "id"
        const val CZ_DEL_WHERE_PKG_NAME = "pkgName"
    }

    private val uri = Uri.Builder()
        .scheme(CONTENT)
        .authority(CZ_NOTIFICATION_PROVIDER_AUTHORITY)
        .build()

    /**
     * 显示通知
     */
    suspend fun showNotification(
        icon: Bitmap,
        title: String,
        intentWrapper: CZNotificationIntentWrapper,
        removeAfterExecution: Boolean,
    ): Int {
        return withContext(Dispatchers.Default) {

            val bitmapByteArray = async {
                ByteArrayOutputStream().use {
                    icon.compress(Bitmap.CompressFormat.PNG, 100, it)
                    it.toByteArray()
                }
            }

            val intentByteArray = async {
                ByteArrayOutputStream().use {
                    ObjectOutputStream(it).writeObject(intentWrapper)
                    it.toByteArray()
                }
            }

            val contentValue = ContentValues().apply {
                put(CZ_NOTIFICATION_KEY_PKG_NAME, globalAppCtx.packageName)
                put(CZ_NOTIFICATION_KEY_ICON, bitmapByteArray.await())
                put(CZ_NOTIFICATION_KEY_TITLE, title)
                put(CZ_NOTIFICATION_KEY_PENDING_INTENT, intentByteArray.await())
                put(CZ_NOTIFICATION_KEY_REMOVE_AFTER_EXECUTION, removeAfterExecution)
            }

            val result = globalAppCtx.contentResolver.insert(uri, contentValue)
            val idString = (result?.path ?: "").replace("/", "")
            idString.toIntOrNull() ?: -1
        }
    }

    /**
     * 移除通知
     */
    suspend fun removeNotification(id: Int) {
        withContext(Dispatchers.Default) {
            logTagD(TAG, "移除通知: $id")
            globalAppCtx.contentResolver.delete(uri, CZ_DEL_WHERE_ID, arrayOf(id.toString()))
        }
    }

    suspend fun clearAllPkgNotification(pkgName: String) {
        withContext(Dispatchers.Default) {
            globalAppCtx.contentResolver.delete(uri, CZ_DEL_WHERE_PKG_NAME, arrayOf(pkgName))
        }
    }
}