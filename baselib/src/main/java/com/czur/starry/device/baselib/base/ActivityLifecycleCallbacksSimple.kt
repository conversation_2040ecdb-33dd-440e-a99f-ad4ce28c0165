package com.czur.starry.device.baselib.base

import android.app.Activity
import android.app.Application
import android.os.Bundle

/**
 * Created by 陈丰尧 on 1/23/21
 * ActivityLifecycleCallbacks 的简单实现
 * 这样不用重写所有方法
 */
class ActivityLifecycleCallbacksSimple:Application.ActivityLifecycleCallbacks {
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityResumed(activity: Activity) {
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
    }
}