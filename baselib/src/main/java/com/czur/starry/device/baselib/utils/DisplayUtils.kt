package com.czur.starry.device.baselib.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Rect
import android.os.Build
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.fw.proxy.ScreenCaptureProxy
import com.czur.starry.device.baselib.utils.fw.proxy.SurfaceControlProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

private const val TAG = "DisplayUtils"

private const val SCREEN_HEIGHT = 1080
private const val SCREEN_WIDTH = 1920

/**
 * 获取屏幕高度
 */
fun getScreenHeight(): Int {
    return SCREEN_HEIGHT
}

fun getScreenWidth(): Int = SCREEN_WIDTH

fun getTopControlBarHeight(): Int {
    return when (Constants.starryHWInfo.model) {
        StarryModel.StudioModel.StudioSPlus -> 120
        else -> 0
    }
}

/**
 * 获取屏幕截图
 * @param targetConfig 目标Bitmap的Config, 如果为null, 则不进行转换
 */
suspend fun takeScreenShot(targetConfig: Bitmap.Config? = null): Bitmap? =
    withContext(Dispatchers.IO) {
        val screenshotBmp = when (Build.VERSION.SDK_INT) {
            Build.VERSION_CODES.S -> {
                logTagV(TAG, "takeScreenShot: use SurfaceControl")
                SurfaceControlProxy.takeScreenshot() ?: takeScreenshotByCmd()
            }

            Build.VERSION_CODES.UPSIDE_DOWN_CAKE -> {
                logTagV(TAG, "takeScreenShot: use ScreenCapture")
                ScreenCaptureProxy.takeScreenshot(Rect(0, getTopControlBarHeight(), getScreenWidth(), getScreenHeight() + getTopControlBarHeight())) ?: takeScreenshotByCmd()
            }

            else -> {
                logTagV(TAG, "takeScreenShot: use cmd sdk=${Build.VERSION.SDK_INT}")
                takeScreenshotByCmd()
            }
        } ?: return@withContext null


        if (targetConfig == null) {
            screenshotBmp
        } else {
            screenshotBmp.transformationConfig(targetConfig)
        }
    }

private fun takeScreenshotByCmd(): Bitmap? {
    val saveFile = File(globalAppCtx.cacheDir, "screenshot.png")
    val shellCmd = "screencap -p ${saveFile.absolutePath}"
    val process = Runtime.getRuntime().exec(shellCmd)
    process.waitFor()
    return BitmapFactory.decodeFile(saveFile.absolutePath).also {
        saveFile.delete()
    }
}

