package com.czur.starry.device.baselib.notice

import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagV

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*

/**
 * Created by 陈丰尧 on 2021/8/9
 */
class NoticeContentObserver(
    val scope: CoroutineScope,
    val typeKey: Long,
    handler: Handler
) : ContentObserver(handler) {
    companion object {
        private const val TAG = "NoticeContentObserver"
    }

    private val callBackWrappers = mutableListOf<CallBackWrapper>()
    private val ownerSet = mutableSetOf<LifecycleOwner>()

    // 输出Log用的
    private val typeMsg: String by lazy {
        MsgType.getByKey(typeKey).toString()
    }


    fun addWrapper(wrapper: CallBackWrapper) {
        callBackWrappers.add(wrapper)
        wrapper.owner?.let {
            ownerSet.add(it)
        }
    }

    /**
     * 是否有这个Owner对象
     */
    fun hasThisOwner(checkOwner: LifecycleOwner): Boolean {
        return ownerSet.contains(checkOwner)
    }

    /**
     * 通过LifecycleOwner来取消注册
     * @return 取消注册后, 这个Observer是否已经为空,需要被销毁
     */
    fun unRegisterByOwner(owner: LifecycleOwner): Boolean {
        // 本地没有注册过带livecycleOwner的回调
        if (ownerSet.isEmpty()) return false

        callBackWrappers.removeIf {
            it.owner == owner
        }
        ownerSet.remove(owner)
        // 检查这个CallBack还有没有存在的必要
        return callBackWrappers.isEmpty()
    }

    fun isCallBackEmpty(): Boolean {
        return callBackWrappers.isEmpty()
    }

    /**
     * 通过Listener来取消注册
     * @return 取消注册后, 这个Observer是否已经为空,需要被销毁
     */
    fun unRegisterByListener(listener: (msg: NoticeMsg) -> Unit): Boolean {
        callBackWrappers.removeIf {
            val needRemove = it.listener == listener
            if (needRemove) {
                ownerSet.remove(it.owner)
            }
            needRemove
        }
        return callBackWrappers.isEmpty()
    }

    override fun onChange(selfChange: Boolean, uri: Uri?) {
        super.onChange(selfChange, uri)
        if (uri == null) {
            return
        }
        logTagV(TAG, "接收新的消息:${typeMsg}")
        scope.launch {
            // 进行回调
            callBackAll(uri)
        }
    }

    /**
     * 将NoticeMsg回调给所有的Listener
     * 1. 从Uri中解析出NoticeMsg对象
     * 2. 将NoticeMsg对象发给所有监听
     */
    private suspend fun callBackAll(uri: Uri) {
        withContext(Dispatchers.Default) {
            // 通过base64 获取NoticeMsg
            val noticeMsg = uri.getQueryParameter(NoticeConstant.URI_KEY_MSG)?.let {
                base64ToNoticeMsg(it)
            } ?: NoticeMsg()
            // 每一个回调都在一个独立的协程中
            callBackWrappers.forEach {
                scope.launch {
                    it.callback(noticeMsg)
                }
            }
        }
    }
}