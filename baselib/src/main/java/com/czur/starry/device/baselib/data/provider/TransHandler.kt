package com.czur.starry.device.baselib.data.provider

import android.content.ContentValues
import android.content.Intent
import android.content.UriMatcher
import android.os.Environment
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.MsgType.Companion.USER_INFO_LOGOUT
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.PathWrapper
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.makeUri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Created by 宋清君 on 2/25/25
 * 记录翻译状态的工具类
 */
object TransHandler : SPContentHandler() {

    private const val TAG = "TransHandler"

    const val KEY_IS_TRANSLATING = "vendor.czur.transcriptionIsTranslating"          // 是否正在翻译
    const val KEY_IS_STOP_TRANS = "vendor.czur.transcription.isStopTrans"          // 停止翻译
    const val KEY_IS_SHOW_SUBTITLES = "vendor.czur.transcription.isShowSubtitles"          // 显示ai字幕
    const val KEY_AI_TRANS_FILE_NAME = "KEY_AI_TRANS_FILE_NAME"          // AI互译文件名

    const val KEY_GENERATE_MEETING_MINUTES = "generateMeetingMinutes"
    const val KEY_TRANS_TARGET = "KEY_TRANS_TARGET"
    const val KEY_TRANS_SOURCE = "KEY_TRANS_SOURCE"
//
//    const val SHOW_TYPE_SELF = 0// 展示给自己
//    const val SHOW_TYPE_BOTH = 1// 展示给自己和对方
//    const val DEFAULT_SHOW_TYPE = SHOW_TYPE_SELF// 显示方式默认值

    const val GENERATE_MEETING_MINUTES_YES = 0 //
    const val GENERATE_MEETING_MINUTES_NO = 1 //
    const val DEFAULT_GENERATE_MEETING_MINUTES = GENERATE_MEETING_MINUTES_YES // 显示内容默认值

    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
            KEY_IS_TRANSLATING to LiveTrans(isTranslatingLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_IS_STOP_TRANS to LiveTrans(stopTransLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_IS_SHOW_SUBTITLES to LiveTrans(showSubtitlesLive as MutableLiveData) {
                it.toBoolean()
            }
        )
    }
    override val authority: String =
        "com.czur.starry.device.transcription.provider.TranscriptionProvider"


    val isTranslatingLive by lazy { createLive { isTranslating } }
    var isTranslating: Boolean
        set(value) {
            setValue(KEY_IS_TRANSLATING, value)
        }
        get() = getValue(KEY_IS_TRANSLATING, false)

    // true: 停止翻译  作为开关使用,请在收到true以后,重置为false
    val stopTransLive by lazy { createLive { stopTrans } }
    var stopTrans: Boolean
        set(value) {
            setValue(KEY_IS_STOP_TRANS, value)
        }
        get() = getValue(KEY_IS_STOP_TRANS, false)

    // true: 显示ai字幕
    val showSubtitlesLive by lazy { createLive { showSubtitles } }
    var showSubtitles: Boolean
        set(value) {
            setValue(KEY_IS_SHOW_SUBTITLES, value)
        }
        get() = getValue(KEY_IS_SHOW_SUBTITLES, true)

}

