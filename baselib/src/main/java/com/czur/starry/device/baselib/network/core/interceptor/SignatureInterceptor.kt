package com.czur.starry.device.baselib.network.core.interceptor

import com.czur.starry.device.baselib.network.core.util.MiaoHttpUtils
import com.czur.starry.device.baselib.network.core.util.addParams
import com.czur.starry.device.baselib.network.core.util.getParams
import okhttp3.Interceptor
import okhttp3.Response

/**
 * Created by 陈丰尧 on 2021/7/14
 * 对数据请求进行签名的拦截器
 * 通过请求的参数,来构建签名信息, 然后添加到请求中
 */
class SignatureInterceptor : Interceptor {
    companion object{
        private const val PARAM_KEY_SIGN = "sign"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val params = request.getParams()
        val sign = MiaoHttpUtils.generateSignature(params)
        val signRequest = request.addParams(PARAM_KEY_SIGN to sign)
        return chain.proceed(signRequest)
    }
}