package com.czur.starry.device.baselib.base.v2.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import java.lang.reflect.ParameterizedType
import kotlin.properties.Delegates

/**
 * Created by 陈丰尧 on 2023/6/8
 */
abstract class CZViewBindingFragment<VB : ViewBinding> : CZBusinessFragment() {
    var binding: VB by Delegates.notNull()
        protected set

    final override fun createCusContentView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = generateViewBinding(container)
        return binding.root
    }

    final override fun initViews() {
        super.initViews()
        binding.initBindingViews()
    }

    protected open fun VB.initBindingViews() {}

    /**
     * 生成ViewBinding
     */
    private fun generateViewBinding(rootView: ViewGroup?): VB {
        val type = this::class.java.genericSuperclass as? ParameterizedType
            ?: throw IllegalArgumentException("没有指定ViewBinding的泛型")
        val vbClazz = type.actualTypeArguments.find {
            // 找到泛型声明为 实现了 ViewBinding接口的类型
            (it as Class<*>).genericInterfaces[0] == ViewBinding::class.java
        } as? Class<*> ?: throw IllegalArgumentException("ViewBinding class not found")

        val method = vbClazz.getMethod(
            "inflate",
            LayoutInflater::class.java,
            ViewGroup::class.java,
            Boolean::class.java
        )
        return method.invoke(null, layoutInflater, rootView, false) as VB
    }
}